# DeepSeekR1 Midend优化对比

## 概述

本项目现在支持两个版本的DeepSeekR1编译：
1. **原始版本** - 使用基础的midend优化pass
2. **增强版本** - 添加了额外的矩阵乘法优化pass

## 新增的优化Pass

在最新提交中，我们添加了以下优化pass：
- `matmul-transpose-b-vectorization`: 转置B矩阵的向量化优化
- `batchmatmul-transpose-b-vectorization`: 批量矩阵乘法转置B向量化
- `matmul-optimize`: 基础矩阵乘法优化
- `batchmatmul-tile-optimize`: 批量矩阵乘法分块优化

## 构建和运行

### 1. 构建项目
```bash
cd /home/<USER>/Personal_Projects/OSPP/buddy-mlir
mkdir -p build && cd build
cmake .. && make -j$(nproc)
```

### 2. 构建两个版本
```bash
# 构建原始版本
make buddy-deepseek-r1-original-run

# 构建增强版本
make buddy-deepseek-r1-run
```

### 3. 运行性能对比
```bash
# 使用对比脚本
cd /home/<USER>/Personal_Projects/OSPP/buddy-mlir
bash examples/BuddyDeepSeekR1/compare_optimizations.sh
```

## 生成的文件

### MLIR中间文件
- `forward-midend-original.mlir`: 原始优化的forward MLIR
- `forward-midend-optimized.mlir`: 增强优化的forward MLIR
- `subgraph-midend-original.mlir`: 原始优化的subgraph MLIR
- `subgraph-midend-optimized.mlir`: 增强优化的subgraph MLIR

### 目标文件
- `forward-original.o` / `forward.o`: 原始/增强版本的forward目标文件
- `subgraph-original.o` / `subgraph.o`: 原始/增强版本的subgraph目标文件

### 可执行文件
- `buddy-deepseek-r1-original-run`: 原始优化版本
- `buddy-deepseek-r1-run`: 增强优化版本

## 性能测试

### 手动性能测试
```bash
cd build

# 测试原始版本
echo "Testing original version..."
time ./examples/BuddyDeepSeekR1/buddy-deepseek-r1-original-run

# 测试增强版本
echo "Testing optimized version..."
time ./examples/BuddyDeepSeekR1/buddy-deepseek-r1-run
```

### MLIR分析
```bash
# 比较MLIR文件差异
cd build
diff examples/BuddyDeepSeekR1/forward-midend-original.mlir \
     examples/BuddyDeepSeekR1/forward-midend-optimized.mlir | head -50

# 统计优化操作数量
echo "Original vector operations:"
grep -c "vector\." examples/BuddyDeepSeekR1/forward-midend-original.mlir

echo "Optimized vector operations:"
grep -c "vector\." examples/BuddyDeepSeekR1/forward-midend-optimized.mlir
```

## 预期改进

新增的优化pass应该带来以下改进：

1. **向量化优化**: 通过SIMD指令提升矩阵乘法性能
2. **转置优化**: 优化QKV计算中的转置操作
3. **分块优化**: 改善缓存局部性，减少内存访问延迟
4. **批量处理**: 提升批量矩阵乘法的效率

## 针对QKV融合的优化

这些新增的pass特别适合你已实现的QKV融合优化：
- QKV融合将3个小矩阵乘法合并为1个大矩阵乘法
- `batchmatmul-tile-optimize`对大矩阵进行分块，提升缓存效率
- `matmul-transpose-b-vectorization`优化attention计算中的转置操作
- `matmul-optimize`提供基础的矩阵乘法性能优化

## 故障排除

如果构建失败：
1. 确保buddy-opt工具已正确构建
2. 检查所有优化pass是否可用：`./build/bin/buddy-opt --help | grep matmul`
3. 如果某个pass不可用，可以从CMakeLists.txt中临时移除

## 下一步

1. 运行性能测试，量化优化效果
2. 分析生成的MLIR代码，验证优化是否正确应用
3. 根据结果调整优化参数（向量大小、分块大小等）
4. 考虑添加更多针对QKV融合的专用优化
