; NOTE: Assertions have been autogenerated by utils/update_llc_test_checks.py
; RUN: llc -enable-machine-outliner -mtriple=amdgcn-adm-amdhsa < %s | FileCheck %s

; NOTE: Machine outliner doesn't run.
@x = dso_local global i32 0, align 4

define dso_local i32 @check_boundaries() #0 {
; CHECK-LABEL: check_boundaries:
; CHECK:       check_boundaries$local:
; CHECK-NEXT:    .type check_boundaries$local,@function
; CHECK-NEXT:    .cfi_startproc
; CHECK-NEXT:  ; %bb.0:
; CHECK-NEXT:    s_waitcnt vmcnt(0) expcnt(0) lgkmcnt(0)
; CHECK-NEXT:    s_mov_b32 s8, s33
; CHECK-NEXT:    s_mov_b32 s33, s32
; CHECK-NEXT:    s_addk_i32 s32, 0x600
; CHECK-NEXT:    v_mov_b32_e32 v4, 0
; CHECK-NEXT:    v_mov_b32_e32 v0, 1
; CHECK-NEXT:    v_mov_b32_e32 v1, 2
; CHECK-NEXT:    v_mov_b32_e32 v2, 3
; CHECK-NEXT:    v_mov_b32_e32 v3, 4
; CHECK-NEXT:    buffer_store_dword v4, off, s[0:3], s33
; CHECK-NEXT:    buffer_store_dword v0, off, s[0:3], s33 offset:4
; CHECK-NEXT:    buffer_store_dword v1, off, s[0:3], s33 offset:8
; CHECK-NEXT:    buffer_store_dword v2, off, s[0:3], s33 offset:12
; CHECK-NEXT:    buffer_store_dword v3, off, s[0:3], s33 offset:16
; CHECK-NEXT:    s_mov_b64 s[4:5], 0
; CHECK-NEXT:    s_and_saveexec_b64 s[6:7], s[4:5]
; CHECK-NEXT:    s_xor_b64 s[4:5], exec, s[6:7]
; CHECK-NEXT:    s_cbranch_execz .LBB0_2
; CHECK-NEXT:  ; %bb.1:
; CHECK-NEXT:    buffer_store_dword v0, off, s[0:3], s33 offset:4
; CHECK-NEXT:    buffer_store_dword v1, off, s[0:3], s33 offset:8
; CHECK-NEXT:    buffer_store_dword v2, off, s[0:3], s33 offset:12
; CHECK-NEXT:    buffer_store_dword v3, off, s[0:3], s33 offset:16
; CHECK-NEXT:  .LBB0_2: ; %Flow
; CHECK-NEXT:    s_andn2_saveexec_b64 s[4:5], s[4:5]
; CHECK-NEXT:    s_cbranch_execz .LBB0_4
; CHECK-NEXT:  ; %bb.3:
; CHECK-NEXT:    v_mov_b32_e32 v0, 1
; CHECK-NEXT:    buffer_store_dword v0, off, s[0:3], s33 offset:12
; CHECK-NEXT:  .LBB0_4:
; CHECK-NEXT:    s_or_b64 exec, exec, s[4:5]
; CHECK-NEXT:    v_mov_b32_e32 v0, 0
; CHECK-NEXT:    s_mov_b32 s32, s33
; CHECK-NEXT:    s_mov_b32 s33, s8
; CHECK-NEXT:    s_waitcnt vmcnt(0)
; CHECK-NEXT:    s_setpc_b64 s[30:31]
  %1 = alloca i32, align 4, addrspace(5)
  %2 = alloca i32, align 4, addrspace(5)
  %3 = alloca i32, align 4, addrspace(5)
  %4 = alloca i32, align 4, addrspace(5)
  %5 = alloca i32, align 4, addrspace(5)
  store i32 0, i32 addrspace(5)* %1, align 4
  store i32 0, i32 addrspace(5)* %2, align 4
  %6 = load i32, i32 addrspace(5)* %2, align 4
  %7 = icmp ne i32 %6, 0
  br i1 %7, label %9, label %8

  store i32 1, i32 addrspace(5)* %2, align 4
  store i32 2, i32 addrspace(5)* %3, align 4
  store i32 3, i32 addrspace(5)* %4, align 4
  store i32 4, i32 addrspace(5)* %5, align 4
  br label %10

  store i32 1, i32 addrspace(5)* %4, align 4
  br label %10

  %11 = load i32, i32 addrspace(5)* %2, align 4
  %12 = icmp ne i32 %11, 0
  br i1 %12, label %14, label %13

  store i32 1, i32 addrspace(5)* %2, align 4
  store i32 2, i32 addrspace(5)* %3, align 4
  store i32 3, i32 addrspace(5)* %4, align 4
  store i32 4, i32 addrspace(5)* %5, align 4
  br label %15

  store i32 1, i32 addrspace(5)* %4, align 4
  br label %15

  ret i32 0
}

define dso_local i32 @main() #0 {
; CHECK-LABEL: main:
; CHECK:       main$local:
; CHECK-NEXT:    .type main$local,@function
; CHECK-NEXT:    .cfi_startproc
; CHECK-NEXT:  ; %bb.0:
; CHECK-NEXT:    s_waitcnt vmcnt(0) expcnt(0) lgkmcnt(0)
; CHECK-NEXT:    s_mov_b32 s6, s33
; CHECK-NEXT:    s_mov_b32 s33, s32
; CHECK-NEXT:    s_addk_i32 s32, 0x600
; CHECK-NEXT:    v_mov_b32_e32 v0, 0
; CHECK-NEXT:    s_getpc_b64 s[4:5]
; CHECK-NEXT:    s_add_u32 s4, s4, x@rel32@lo+4
; CHECK-NEXT:    s_addc_u32 s5, s5, x@rel32@hi+12
; CHECK-NEXT:    v_mov_b32_e32 v2, 1
; CHECK-NEXT:    v_mov_b32_e32 v3, 2
; CHECK-NEXT:    v_mov_b32_e32 v4, 3
; CHECK-NEXT:    v_mov_b32_e32 v5, 4
; CHECK-NEXT:    buffer_store_dword v0, off, s[0:3], s33
; CHECK-NEXT:    buffer_store_dword v2, off, s[0:3], s33 offset:4
; CHECK-NEXT:    buffer_store_dword v3, off, s[0:3], s33 offset:8
; CHECK-NEXT:    buffer_store_dword v4, off, s[0:3], s33 offset:12
; CHECK-NEXT:    buffer_store_dword v5, off, s[0:3], s33 offset:16
; CHECK-NEXT:    v_mov_b32_e32 v0, s4
; CHECK-NEXT:    v_mov_b32_e32 v1, s5
; CHECK-NEXT:    flat_store_dword v[0:1], v2
; CHECK-NEXT:    ;;#ASMSTART
; CHECK-NEXT:    ;;#ASMEND
; CHECK-NEXT:    buffer_store_dword v2, off, s[0:3], s33 offset:4
; CHECK-NEXT:    buffer_store_dword v3, off, s[0:3], s33 offset:8
; CHECK-NEXT:    buffer_store_dword v4, off, s[0:3], s33 offset:12
; CHECK-NEXT:    v_mov_b32_e32 v0, 0
; CHECK-NEXT:    buffer_store_dword v5, off, s[0:3], s33 offset:16
; CHECK-NEXT:    s_mov_b32 s32, s33
; CHECK-NEXT:    s_mov_b32 s33, s6
; CHECK-NEXT:    s_waitcnt vmcnt(0) lgkmcnt(0)
; CHECK-NEXT:    s_setpc_b64 s[30:31]
  %1 = alloca i32, align 4, addrspace(5)
  %2 = alloca i32, align 4, addrspace(5)
  %3 = alloca i32, align 4, addrspace(5)
  %4 = alloca i32, align 4, addrspace(5)
  %5 = alloca i32, align 4, addrspace(5)

  store i32 0, i32 addrspace(5)* %1, align 4
  store i32 0, i32* @x, align 4
  store i32 1, i32 addrspace(5)* %2, align 4
  store i32 2, i32 addrspace(5)* %3, align 4
  store i32 3, i32 addrspace(5)* %4, align 4
  store i32 4, i32 addrspace(5)* %5, align 4
  store i32 1, i32* @x, align 4
  call void asm sideeffect "", "~{memory},~{dirflag},~{fpsr},~{flags}"()
  store i32 1, i32 addrspace(5)* %2, align 4
  store i32 2, i32 addrspace(5)* %3, align 4
  store i32 3, i32 addrspace(5)* %4, align 4
  store i32 4, i32 addrspace(5)* %5, align 4
  ret i32 0
}

attributes #0 = { noredzone nounwind ssp uwtable "frame-pointer"="all" }
