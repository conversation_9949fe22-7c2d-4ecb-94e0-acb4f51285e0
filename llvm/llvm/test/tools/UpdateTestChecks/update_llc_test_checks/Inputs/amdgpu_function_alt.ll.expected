; NOTE: Assertions have been autogenerated by utils/update_llc_test_checks.py UTC_ARGS: --tool cat --default-march amdgcn --version 4
; RUN: cat %S/amdgpu_function_alt.s | FileCheck --check-prefixes=CHECK %s

define float @sample(float %x) {
; CHECK-LABEL: sample:
; CHECK:         s_waitcnt vmcnt(0) expcnt(0) lgkmcnt(0)
; CHECK-NEXT:    v_mul_f32_e32 v0, v0, v0
; CHECK-NEXT:    s_setpc_b64 s[30:31]
  %y = fmul float %x, %x
  ret float %y
}
