; NOTE: Assertions have been autogenerated by utils/update_llc_test_checks.py
; RUN: llc -mtriple=lanai -stop-after=finalize-isel -debug-only=isel -o /dev/null %s 2>&1 | FileCheck %s

define i64 @i64_test(i64 %i) nounwind readnone {
; CHECK-LABEL: i64_test:
; CHECK:       SelectionDAG has 22 nodes:
; CHECK-NEXT:    t0: ch,glue = EntryToken
; CHECK-NEXT:    t5: i32,ch = LDW_RI<Mem:(load (s32) from %fixed-stack.0)> TargetFrameIndex:i32<-2>, TargetConstant:i32<0>, TargetConstant:i32<0>, t0
; CHECK-NEXT:    t7: i32 = ADD_I_LO TargetFrameIndex:i32<0>, TargetConstant:i32<0>
; CHECK-NEXT:    t29: i32 = OR_I_LO disjoint t7, TargetConstant:i32<4>
; CHECK-NEXT:    t22: i32,ch = LDW_RI<Mem:(dereferenceable load (s32) from %ir.loc + 4, basealign 8)> t29, TargetConstant:i32<0>, TargetConstant:i32<0>, t0
; CHECK-NEXT:    t24: i32 = ADD_R t5, t22, TargetConstant:i32<0>
; CHECK-NEXT:    t3: i32,ch = LDW_RI<Mem:(load (s32) from %fixed-stack.1, align 8)> TargetFrameIndex:i32<-1>, TargetConstant:i32<0>, TargetConstant:i32<0>, t0
; CHECK-NEXT:    t19: i32,ch = LDW_RI<Mem:(dereferenceable load (s32) from %ir.loc, align 8)> TargetFrameIndex:i32<0>, TargetConstant:i32<0>, TargetConstant:i32<0>, t0
; CHECK-NEXT:    t27: i32 = ADD_R t3, t19, TargetConstant:i32<0>
; CHECK-NEXT:    t30: i32,glue = SFSUB_F_RR t24, t5
; CHECK-NEXT:    t31: i32 = SCC TargetConstant:i32<4>, t30:1
; CHECK-NEXT:    t28: i32 = ADD_R t27, t31, TargetConstant:i32<0>
; CHECK-NEXT:    t15: ch,glue = CopyToReg t0, Register:i32 $rv, t28
; CHECK-NEXT:    t17: ch,glue = CopyToReg t15, Register:i32 $r9, t24, t15:1
; CHECK-NEXT:    t18: ch = RET Register:i32 $rv, Register:i32 $r9, t17, t17:1
; CHECK-EMPTY:
  %loc = alloca i64
  %j = load i64, i64 * %loc
  %r = add i64 %i, %j
  ret i64 %r
}

define i64 @i32_test(i32 %i) nounwind readnone {
; CHECK-LABEL: i32_test:
; CHECK:       SelectionDAG has 14 nodes:
; CHECK-NEXT:    t0: ch,glue = EntryToken
; CHECK-NEXT:    t21: i32,ch = CopyFromReg t0, Register:i32 $r0
; CHECK-NEXT:    t13: ch,glue = CopyToReg t0, Register:i32 $rv, t21
; CHECK-NEXT:    t3: i32,ch = LDW_RI<Mem:(load (s32) from %fixed-stack.0, align 8)> TargetFrameIndex:i32<-1>, TargetConstant:i32<0>, TargetConstant:i32<0>, t0
; CHECK-NEXT:    t6: i32,ch = LDW_RI<Mem:(dereferenceable load (s32) from %ir.loc)> TargetFrameIndex:i32<0>, TargetConstant:i32<0>, TargetConstant:i32<0>, t0
; CHECK-NEXT:    t7: i32 = ADD_R t3, t6, TargetConstant:i32<0>
; CHECK-NEXT:    t15: ch,glue = CopyToReg t13, Register:i32 $r9, t7, t13:1
; CHECK-NEXT:    t16: ch = RET Register:i32 $rv, Register:i32 $r9, t15, t15:1
; CHECK-EMPTY:
  %loc = alloca i32
  %j = load i32, i32 * %loc
  %r = add i32 %i, %j
  %ext = zext i32 %r to i64
  ret i64 %ext
}

define i64 @i16_test(i16 %i) nounwind readnone {
; CHECK-LABEL: i16_test:
; CHECK:       SelectionDAG has 19 nodes:
; CHECK-NEXT:    t0: ch,glue = EntryToken
; CHECK-NEXT:    t33: i32,ch = CopyFromReg t0, Register:i32 $r0
; CHECK-NEXT:    t14: ch,glue = CopyToReg t0, Register:i32 $rv, t33
; CHECK-NEXT:    t1: i32 = ADD_I_LO TargetFrameIndex:i32<-1>, TargetConstant:i32<0>
; CHECK-NEXT:    t21: i32 = OR_I_LO disjoint t1, TargetConstant:i32<2>
; CHECK-NEXT:    t23: i32,ch = LDHz_RI<Mem:(load (s16) from %fixed-stack.0 + 2, basealign 4)> t21, TargetConstant:i32<0>, TargetConstant:i32<0>, t0
; CHECK-NEXT:    t22: i32,ch = LDHz_RI<Mem:(dereferenceable load (s16) from %ir.loc)> TargetFrameIndex:i32<0>, TargetConstant:i32<0>, TargetConstant:i32<0>, t0
; CHECK-NEXT:    t24: i32 = ADD_R t23, t22, TargetConstant:i32<0>
; CHECK-NEXT:    t27: i32 = AND_I_HI t24, TargetConstant:i32<0>
; CHECK-NEXT:    t16: ch,glue = CopyToReg t14, Register:i32 $r9, t27, t14:1
; CHECK-NEXT:    t28: i32 = TargetConstant<65535>
; CHECK-NEXT:    t17: ch = RET Register:i32 $rv, Register:i32 $r9, t16, t16:1
; CHECK-EMPTY:
  %loc = alloca i16
  %j = load i16, i16 * %loc
  %r = add i16 %i, %j
  %ext = zext i16 %r to i64
  ret i64 %ext
}

define i64 @i8_test(i8 %i) nounwind readnone {
; CHECK-LABEL: i8_test:
; CHECK:       SelectionDAG has 20 nodes:
; CHECK-NEXT:    t0: ch,glue = EntryToken
; CHECK-NEXT:    t33: i32,ch = CopyFromReg t0, Register:i32 $r0
; CHECK-NEXT:    t14: ch,glue = CopyToReg t0, Register:i32 $rv, t33
; CHECK-NEXT:    t1: i32 = ADD_I_LO TargetFrameIndex:i32<-1>, TargetConstant:i32<0>
; CHECK-NEXT:    t21: i32 = OR_I_LO disjoint t1, TargetConstant:i32<3>
; CHECK-NEXT:    t23: i32,ch = LDBz_RI<Mem:(load (s8) from %fixed-stack.0 + 3, basealign 4)> t21, TargetConstant:i32<0>, TargetConstant:i32<0>, t0
; CHECK-NEXT:    t22: i32,ch = LDBz_RI<Mem:(dereferenceable load (s8) from %ir.loc)> TargetFrameIndex:i32<0>, TargetConstant:i32<0>, TargetConstant:i32<0>, t0
; CHECK-NEXT:    t24: i32 = ADD_R t23, t22, TargetConstant:i32<0>
; CHECK-NEXT:    t26: i32 = SLI TargetConstant:i32<255>
; CHECK-NEXT:    t27: i32 = AND_R t24, t26, TargetConstant:i32<0>
; CHECK-NEXT:    t16: ch,glue = CopyToReg t14, Register:i32 $r9, t27, t14:1
; CHECK-NEXT:    t17: ch = RET Register:i32 $rv, Register:i32 $r9, t16, t16:1
; CHECK-EMPTY:
  %loc = alloca i8
  %j = load i8, i8 * %loc
  %r = add i8 %i, %j
  %ext = zext i8 %r to i64
  ret i64 %ext
}
