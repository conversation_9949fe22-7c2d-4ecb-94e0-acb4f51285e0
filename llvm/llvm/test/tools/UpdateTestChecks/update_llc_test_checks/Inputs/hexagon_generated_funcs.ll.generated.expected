; NOTE: Assertions have been autogenerated by utils/update_llc_test_checks.py UTC_ARGS: --include-generated-funcs
; RUN: llc -enable-machine-outliner -mtriple=hexagon-unknown-linux < %s | FileCheck %s

; NOTE: Machine outliner doesn't run.
@x = global i32 0, align 4

define dso_local i32 @check_boundaries() #0 {
  %1 = alloca i32, align 4
  %2 = alloca i32, align 4
  %3 = alloca i32, align 4
  %4 = alloca i32, align 4
  %5 = alloca i32, align 4
  store i32 0, i32* %1, align 4
  store i32 0, i32* %2, align 4
  %6 = load i32, i32* %2, align 4
  %7 = icmp ne i32 %6, 0
  br i1 %7, label %9, label %8

  store i32 1, i32* %2, align 4
  store i32 2, i32* %3, align 4
  store i32 3, i32* %4, align 4
  store i32 4, i32* %5, align 4
  br label %10

  store i32 1, i32* %4, align 4
  br label %10

  %11 = load i32, i32* %2, align 4
  %12 = icmp ne i32 %11, 0
  br i1 %12, label %14, label %13

  store i32 1, i32* %2, align 4
  store i32 2, i32* %3, align 4
  store i32 3, i32* %4, align 4
  store i32 4, i32* %5, align 4
  br label %15

  store i32 1, i32* %4, align 4
  br label %15

  ret i32 0
}

define dso_local i32 @main() #0 {
  %1 = alloca i32, align 4
  %2 = alloca i32, align 4
  %3 = alloca i32, align 4
  %4 = alloca i32, align 4
  %5 = alloca i32, align 4

  store i32 0, i32* %1, align 4
  store i32 0, i32* @x, align 4
  store i32 1, i32* %2, align 4
  store i32 2, i32* %3, align 4
  store i32 3, i32* %4, align 4
  store i32 4, i32* %5, align 4
  store i32 1, i32* @x, align 4
  call void asm sideeffect "", "~{memory},~{dirflag},~{fpsr},~{flags}"()
  store i32 1, i32* %2, align 4
  store i32 2, i32* %3, align 4
  store i32 3, i32* %4, align 4
  store i32 4, i32* %5, align 4
  ret i32 0
}

attributes #0 = { noredzone nounwind ssp uwtable "frame-pointer"="all" }
; CHECK-LABEL: check_boundaries:
; CHECK:         .cfi_startproc
; CHECK-NEXT:  // %bb.0:
; CHECK-NEXT:    {
; CHECK-NEXT:     r0 = #0
; CHECK-NEXT:     allocframe(#24)
; CHECK-NEXT:    }
; CHECK-NEXT:    .cfi_def_cfa r30, 8
; CHECK-NEXT:    .cfi_offset r31, -4
; CHECK-NEXT:    .cfi_offset r30, -8
; CHECK-NEXT:    {
; CHECK-NEXT:     memw(r29+#4) = #0
; CHECK-NEXT:    }
; CHECK-NEXT:    {
; CHECK-NEXT:     memw(r29+#8) = #0
; CHECK-NEXT:     memw(r29+#8) = #1
; CHECK-NEXT:    }
; CHECK-NEXT:    {
; CHECK-NEXT:     r1 = memw(r29+#8)
; CHECK-NEXT:     memw(r29+#12) = #2
; CHECK-NEXT:    }
; CHECK-NEXT:    {
; CHECK-NEXT:     memw(r29+#16) = #3
; CHECK-NEXT:     memw(r29+#20) = #4
; CHECK-NEXT:    }
; CHECK-NEXT:    {
; CHECK-NEXT:     p0 = cmp.eq(r1,#0)
; CHECK-NEXT:     if (p0.new) memw(r29+#16) = #3
; CHECK-NEXT:     if (p0.new) memw(r29+#12) = #2
; CHECK-NEXT:    }
; CHECK-NEXT:    {
; CHECK-NEXT:     if (p0) memw(r29+#20) = #4
; CHECK-NEXT:     if (p0) memw(r29+#8) = #1
; CHECK-NEXT:    }
; CHECK-NEXT:    {
; CHECK-NEXT:     if (!p0) memw(r29+#16) = #1
; CHECK-NEXT:    }
; CHECK-NEXT:    {
; CHECK-NEXT:     r31:30 = dealloc_return(r30):raw
; CHECK-NEXT:    }
;
; CHECK-LABEL: main:
; CHECK:         .cfi_startproc
; CHECK-NEXT:  // %bb.0:
; CHECK-NEXT:    {
; CHECK-NEXT:     r0 = ##x
; CHECK-NEXT:     allocframe(#24)
; CHECK-NEXT:    }
; CHECK-NEXT:    .cfi_def_cfa r30, 8
; CHECK-NEXT:    .cfi_offset r31, -4
; CHECK-NEXT:    .cfi_offset r30, -8
; CHECK-NEXT:    {
; CHECK-NEXT:     memw(r29+#4) = #0
; CHECK-NEXT:     memw(r0+#0) = #1
; CHECK-NEXT:    }
; CHECK-NEXT:    {
; CHECK-NEXT:     memw(r29+#8) = #1
; CHECK-NEXT:     memw(r29+#12) = #2
; CHECK-NEXT:    }
; CHECK-NEXT:    {
; CHECK-NEXT:     memw(r29+#16) = #3
; CHECK-NEXT:     memw(r29+#20) = #4
; CHECK-NEXT:    }
; CHECK-NEXT:    //# InlineAsm Start
; CHECK-NEXT:    //# InlineAsm End
; CHECK-NEXT:    {
; CHECK-NEXT:     r0 = #0
; CHECK-NEXT:     memw(r29+#8) = #1
; CHECK-NEXT:     memw(r29+#12) = #2
; CHECK-NEXT:    }
; CHECK-NEXT:    {
; CHECK-NEXT:     memw(r29+#16) = #3
; CHECK-NEXT:     memw(r29+#20) = #4
; CHECK-NEXT:    }
; CHECK-NEXT:    {
; CHECK-NEXT:     r31:30 = dealloc_return(r30):raw
; CHECK-NEXT:    }
