; NOTE: Assertions have been autogenerated by utils/update_llc_test_checks.py
; RUN: llc --enable-machine-outliner --mtriple=loongarch32-unknown-linux < %s | FileCheck %s
@x = dso_local global i32 0, align 4

define dso_local i32 @check_boundaries() #0 {
; CHECK-LABEL: check_boundaries:
; CHECK:       # %bb.0:
; CHECK-NEXT:    addi.w $sp, $sp, -32
; CHECK-NEXT:    .cfi_def_cfa_offset 32
; CHECK-NEXT:    st.w $ra, $sp, 28 # 4-byte Folded Spill
; CHECK-NEXT:    st.w $fp, $sp, 24 # 4-byte Folded Spill
; CHECK-NEXT:    .cfi_offset 1, -4
; CHECK-NEXT:    .cfi_offset 22, -8
; CHECK-NEXT:    addi.w $fp, $sp, 32
; CHECK-NEXT:    .cfi_def_cfa 22, 0
; CHECK-NEXT:    st.w $zero, $fp, -12
; CHECK-NEXT:    st.w $zero, $fp, -16
; CHECK-NEXT:    ori $a0, $zero, 1
; CHECK-NEXT:    beqz $zero, .LBB0_3
; CHECK-NEXT:  # %bb.1:
; CHECK-NEXT:    st.w $a0, $fp, -24
; CHECK-NEXT:    ld.w $a0, $fp, -16
; CHECK-NEXT:    beqz $a0, .LBB0_4
; CHECK-NEXT:  .LBB0_2:
; CHECK-NEXT:    ori $a0, $zero, 1
; CHECK-NEXT:    st.w $a0, $fp, -24
; CHECK-NEXT:    b .LBB0_5
; CHECK-NEXT:  .LBB0_3:
; CHECK-NEXT:    st.w $a0, $fp, -16
; CHECK-NEXT:    ori $a0, $zero, 2
; CHECK-NEXT:    st.w $a0, $fp, -20
; CHECK-NEXT:    ori $a0, $zero, 3
; CHECK-NEXT:    st.w $a0, $fp, -24
; CHECK-NEXT:    ori $a0, $zero, 4
; CHECK-NEXT:    st.w $a0, $fp, -28
; CHECK-NEXT:    ld.w $a0, $fp, -16
; CHECK-NEXT:    bnez $a0, .LBB0_2
; CHECK-NEXT:  .LBB0_4:
; CHECK-NEXT:    ori $a0, $zero, 1
; CHECK-NEXT:    st.w $a0, $fp, -16
; CHECK-NEXT:    ori $a0, $zero, 2
; CHECK-NEXT:    st.w $a0, $fp, -20
; CHECK-NEXT:    ori $a0, $zero, 3
; CHECK-NEXT:    st.w $a0, $fp, -24
; CHECK-NEXT:    ori $a0, $zero, 4
; CHECK-NEXT:    st.w $a0, $fp, -28
; CHECK-NEXT:  .LBB0_5:
; CHECK-NEXT:    move $a0, $zero
; CHECK-NEXT:    ld.w $fp, $sp, 24 # 4-byte Folded Reload
; CHECK-NEXT:    ld.w $ra, $sp, 28 # 4-byte Folded Reload
; CHECK-NEXT:    addi.w $sp, $sp, 32
; CHECK-NEXT:    ret
  %1 = alloca i32, align 4
  %2 = alloca i32, align 4
  %3 = alloca i32, align 4
  %4 = alloca i32, align 4
  %5 = alloca i32, align 4
  store i32 0, ptr %1, align 4
  store i32 0, ptr %2, align 4
  %6 = load i32, ptr %2, align 4
  %7 = icmp ne i32 %6, 0
  br i1 %7, label %9, label %8

  store i32 1, ptr %2, align 4
  store i32 2, ptr %3, align 4
  store i32 3, ptr %4, align 4
  store i32 4, ptr %5, align 4
  br label %10

  store i32 1, ptr %4, align 4
  br label %10

  %11 = load i32, ptr %2, align 4
  %12 = icmp ne i32 %11, 0
  br i1 %12, label %14, label %13

  store i32 1, ptr %2, align 4
  store i32 2, ptr %3, align 4
  store i32 3, ptr %4, align 4
  store i32 4, ptr %5, align 4
  br label %15

  store i32 1, ptr %4, align 4
  br label %15

  ret i32 0
}

define dso_local i32 @main() #0 {
; CHECK-LABEL: main:
; CHECK:       # %bb.0:
; CHECK-NEXT:    addi.w $sp, $sp, -32
; CHECK-NEXT:    .cfi_def_cfa_offset 32
; CHECK-NEXT:    st.w $ra, $sp, 28 # 4-byte Folded Spill
; CHECK-NEXT:    st.w $fp, $sp, 24 # 4-byte Folded Spill
; CHECK-NEXT:    .cfi_offset 1, -4
; CHECK-NEXT:    .cfi_offset 22, -8
; CHECK-NEXT:    addi.w $fp, $sp, 32
; CHECK-NEXT:    .cfi_def_cfa 22, 0
; CHECK-NEXT:    st.w $zero, $fp, -12
; CHECK-NEXT:    pcalau12i $a0, %pc_hi20(x)
; CHECK-NEXT:    ori $a1, $zero, 1
; CHECK-NEXT:    st.w $a1, $fp, -16
; CHECK-NEXT:    ori $a2, $zero, 2
; CHECK-NEXT:    st.w $a2, $fp, -20
; CHECK-NEXT:    ori $a3, $zero, 3
; CHECK-NEXT:    st.w $a3, $fp, -24
; CHECK-NEXT:    ori $a4, $zero, 4
; CHECK-NEXT:    st.w $a4, $fp, -28
; CHECK-NEXT:    st.w $a1, $a0, %pc_lo12(x)
; CHECK-NEXT:    #APP
; CHECK-NEXT:    #NO_APP
; CHECK-NEXT:    st.w $a1, $fp, -16
; CHECK-NEXT:    st.w $a2, $fp, -20
; CHECK-NEXT:    st.w $a3, $fp, -24
; CHECK-NEXT:    st.w $a4, $fp, -28
; CHECK-NEXT:    move $a0, $zero
; CHECK-NEXT:    ld.w $fp, $sp, 24 # 4-byte Folded Reload
; CHECK-NEXT:    ld.w $ra, $sp, 28 # 4-byte Folded Reload
; CHECK-NEXT:    addi.w $sp, $sp, 32
; CHECK-NEXT:    ret
  %1 = alloca i32, align 4
  %2 = alloca i32, align 4
  %3 = alloca i32, align 4
  %4 = alloca i32, align 4
  %5 = alloca i32, align 4

  store i32 0, ptr %1, align 4
  store i32 0, ptr @x, align 4
  store i32 1, ptr %2, align 4
  store i32 2, ptr %3, align 4
  store i32 3, ptr %4, align 4
  store i32 4, ptr %5, align 4
  store i32 1, ptr @x, align 4
  call void asm sideeffect "", "~{memory},~{dirflag},~{fpsr},~{flags}"()
  store i32 1, ptr %2, align 4
  store i32 2, ptr %3, align 4
  store i32 3, ptr %4, align 4
  store i32 4, ptr %5, align 4
  ret i32 0
}

attributes #0 = { noredzone nounwind ssp uwtable "frame-pointer"="all" }
