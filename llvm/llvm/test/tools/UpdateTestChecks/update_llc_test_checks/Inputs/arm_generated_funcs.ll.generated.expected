; NOTE: Assertions have been autogenerated by utils/update_llc_test_checks.py UTC_ARGS: --include-generated-funcs
; RUN: llc -enable-machine-outliner -mtriple=arm-unknown-linux < %s | FileCheck %s
@x = global i32 0, align 4

define dso_local i32 @check_boundaries() #0 {
  %1 = alloca i32, align 4
  %2 = alloca i32, align 4
  %3 = alloca i32, align 4
  %4 = alloca i32, align 4
  %5 = alloca i32, align 4
  store i32 0, i32* %1, align 4
  store i32 0, i32* %2, align 4
  %6 = load i32, i32* %2, align 4
  %7 = icmp ne i32 %6, 0
  br i1 %7, label %9, label %8

  store i32 1, i32* %2, align 4
  store i32 2, i32* %3, align 4
  store i32 3, i32* %4, align 4
  store i32 4, i32* %5, align 4
  br label %10

  store i32 1, i32* %4, align 4
  br label %10

  %11 = load i32, i32* %2, align 4
  %12 = icmp ne i32 %11, 0
  br i1 %12, label %14, label %13

  store i32 1, i32* %2, align 4
  store i32 2, i32* %3, align 4
  store i32 3, i32* %4, align 4
  store i32 4, i32* %5, align 4
  br label %15

  store i32 1, i32* %4, align 4
  br label %15

  ret i32 0
}

define dso_local i32 @main() #0 {
  %1 = alloca i32, align 4
  %2 = alloca i32, align 4
  %3 = alloca i32, align 4
  %4 = alloca i32, align 4
  %5 = alloca i32, align 4

  store i32 0, i32* %1, align 4
  store i32 0, i32* @x, align 4
  store i32 1, i32* %2, align 4
  store i32 2, i32* %3, align 4
  store i32 3, i32* %4, align 4
  store i32 4, i32* %5, align 4
  store i32 1, i32* @x, align 4
  call void asm sideeffect "", "~{memory},~{dirflag},~{fpsr},~{flags}"()
  store i32 1, i32* %2, align 4
  store i32 2, i32* %3, align 4
  store i32 3, i32* %4, align 4
  store i32 4, i32* %5, align 4
  ret i32 0
}

attributes #0 = { noredzone nounwind ssp uwtable "frame-pointer"="none" }
; CHECK-LABEL: check_boundaries:
; CHECK:       @ %bb.0:
; CHECK-NEXT:    sub sp, sp, #20
; CHECK-NEXT:    mov r0, #0
; CHECK-NEXT:    cmp r0, #0
; CHECK-NEXT:    str r0, [sp, #12]
; CHECK-NEXT:    str r0, [sp, #16]
; CHECK-NEXT:    beq .LBB0_2
; CHECK-NEXT:  @ %bb.1:
; CHECK-NEXT:    mov r0, #1
; CHECK-NEXT:    str r0, [sp, #4]
; CHECK-NEXT:    b .LBB0_3
; CHECK-NEXT:  .LBB0_2:
; CHECK-NEXT:    mov r1, lr
; CHECK-NEXT:    bl OUTLINED_FUNCTION_0
; CHECK-NEXT:    mov lr, r1
; CHECK-NEXT:  .LBB0_3:
; CHECK-NEXT:    ldr r0, [sp, #12]
; CHECK-NEXT:    cmp r0, #0
; CHECK-NEXT:    beq .LBB0_5
; CHECK-NEXT:  @ %bb.4:
; CHECK-NEXT:    mov r0, #1
; CHECK-NEXT:    str r0, [sp, #4]
; CHECK-NEXT:    b .LBB0_6
; CHECK-NEXT:  .LBB0_5:
; CHECK-NEXT:    mov r1, lr
; CHECK-NEXT:    bl OUTLINED_FUNCTION_0
; CHECK-NEXT:    mov lr, r1
; CHECK-NEXT:  .LBB0_6:
; CHECK-NEXT:    mov r0, #0
; CHECK-NEXT:    add sp, sp, #20
; CHECK-NEXT:    mov pc, lr
;
; CHECK-LABEL: main:
; CHECK:       @ %bb.0:
; CHECK-NEXT:    sub sp, sp, #20
; CHECK-NEXT:    ldr r0, .LCPI1_0
; CHECK-NEXT:    mov r1, #1
; CHECK-NEXT:    mov r2, #3
; CHECK-NEXT:    mov r3, #4
; CHECK-NEXT:    str r1, [sp, #12]
; CHECK-NEXT:    str r1, [r0]
; CHECK-NEXT:    mov r0, #0
; CHECK-NEXT:    str r0, [sp, #16]
; CHECK-NEXT:    mov r0, #2
; CHECK-NEXT:    str r0, [sp, #8]
; CHECK-NEXT:    str r2, [sp, #4]
; CHECK-NEXT:    str r3, [sp]
; CHECK-NEXT:    @APP
; CHECK-NEXT:    @NO_APP
; CHECK-NEXT:    str r0, [sp, #8]
; CHECK-NEXT:    mov r0, #0
; CHECK-NEXT:    str r1, [sp, #12]
; CHECK-NEXT:    str r2, [sp, #4]
; CHECK-NEXT:    str r3, [sp]
; CHECK-NEXT:    add sp, sp, #20
; CHECK-NEXT:    mov pc, lr
; CHECK-NEXT:    .p2align 2
; CHECK-NEXT:  @ %bb.1:
; CHECK-NEXT:  .LCPI1_0:
; CHECK-NEXT:    .long x
;
; CHECK-LABEL: OUTLINED_FUNCTION_0:
; CHECK:       @ %bb.0:
; CHECK-NEXT:    mov r0, #2
; CHECK-NEXT:    str r0, [sp, #8]
; CHECK-NEXT:    mov r0, #1
; CHECK-NEXT:    str r0, [sp, #12]
; CHECK-NEXT:    mov r0, #3
; CHECK-NEXT:    str r0, [sp, #4]
; CHECK-NEXT:    mov r0, #4
; CHECK-NEXT:    str r0, [sp]
; CHECK-NEXT:    mov pc, lr
