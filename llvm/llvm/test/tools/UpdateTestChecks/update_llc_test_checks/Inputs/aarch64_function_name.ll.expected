; NOTE: Assertions have been autogenerated by utils/update_llc_test_checks.py
; Check that we accept functions with '$' in the name.

; RUN: llc -mtriple=aarch64-unknown-linux < %s | FileCheck --check-prefix=LINUX %s
; RUN: llc -mtriple=aarch64-apple-darwin < %s | FileCheck --check-prefix=DARWIN %s
; RUN: llc -mtriple=arm64-apple-macosx < %s | FileCheck --check-prefix=DARWIN %s

define hidden i32 @"_Z54bar$ompvariant$bar"() {
; LINUX-LABEL: _Z54bar$ompvariant$bar:
; LINUX:       // %bb.0: // %entry
; LINUX-NEXT:    mov w0, #2 // =0x2
; LINUX-NEXT:    ret
;
; DARWIN-LABEL: _Z54bar$ompvariant$bar:
; DARWIN:       ; %bb.0: ; %entry
; DARWIN-NEXT:    mov w0, #2 ; =0x2
; DARWIN-NEXT:    ret
entry:
  ret i32 2
}
