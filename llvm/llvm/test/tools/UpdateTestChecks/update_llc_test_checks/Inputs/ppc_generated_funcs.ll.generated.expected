; NOTE: Assertions have been autogenerated by utils/update_llc_test_checks.py UTC_ARGS: --include-generated-funcs
; RUN: llc -enable-machine-outliner -mtriple=ppc32-unknown-linux < %s | FileCheck %s
; RUN: llc -enable-machine-outliner -mtriple=powerpc-ibm-aix-xcoff -mcpu=ppc < %s | FileCheck %s -check-prefix=AIX

; NOTE: Machine outliner doesn't run.
@x = global i32 0, align 4

define dso_local i32 @check_boundaries() #0 {
  %1 = alloca i32, align 4
  %2 = alloca i32, align 4
  %3 = alloca i32, align 4
  %4 = alloca i32, align 4
  %5 = alloca i32, align 4
  store i32 0, i32* %1, align 4
  store i32 0, i32* %2, align 4
  %6 = load i32, i32* %2, align 4
  %7 = icmp ne i32 %6, 0
  br i1 %7, label %9, label %8

  store i32 1, i32* %2, align 4
  store i32 2, i32* %3, align 4
  store i32 3, i32* %4, align 4
  store i32 4, i32* %5, align 4
  br label %10

  store i32 1, i32* %4, align 4
  br label %10

  %11 = load i32, i32* %2, align 4
  %12 = icmp ne i32 %11, 0
  br i1 %12, label %14, label %13

  store i32 1, i32* %2, align 4
  store i32 2, i32* %3, align 4
  store i32 3, i32* %4, align 4
  store i32 4, i32* %5, align 4
  br label %15

  store i32 1, i32* %4, align 4
  br label %15

  ret i32 0
}

define dso_local i32 @main() #0 {
  %1 = alloca i32, align 4
  %2 = alloca i32, align 4
  %3 = alloca i32, align 4
  %4 = alloca i32, align 4
  %5 = alloca i32, align 4

  store i32 0, i32* %1, align 4
  store i32 0, i32* @x, align 4
  store i32 1, i32* %2, align 4
  store i32 2, i32* %3, align 4
  store i32 3, i32* %4, align 4
  store i32 4, i32* %5, align 4
  store i32 1, i32* @x, align 4
  call void asm sideeffect "", "~{memory},~{dirflag},~{fpsr},~{flags}"()
  store i32 1, i32* %2, align 4
  store i32 2, i32* %3, align 4
  store i32 3, i32* %4, align 4
  store i32 4, i32* %5, align 4
  ret i32 0
}

attributes #0 = { noredzone nounwind ssp uwtable "frame-pointer"="all" }
; CHECK-LABEL: check_boundaries:
; CHECK:       # %bb.0:
; CHECK-NEXT:    stwu 1, -32(1)
; CHECK-NEXT:    stw 31, 28(1)
; CHECK-NEXT:    .cfi_def_cfa_offset 32
; CHECK-NEXT:    .cfi_offset r31, -4
; CHECK-NEXT:    mr 31, 1
; CHECK-NEXT:    .cfi_def_cfa_register r31
; CHECK-NEXT:    li 4, 0
; CHECK-NEXT:    li 3, 1
; CHECK-NEXT:    stw 4, 24(31)
; CHECK-NEXT:    li 4, 2
; CHECK-NEXT:    li 5, 3
; CHECK-NEXT:    li 6, 4
; CHECK-NEXT:    cmplwi 3, 0
; CHECK-NEXT:    stw 3, 20(31)
; CHECK-NEXT:    stw 4, 16(31)
; CHECK-NEXT:    stw 5, 12(31)
; CHECK-NEXT:    stw 6, 8(31)
; CHECK-NEXT:    beq 0, .LBB0_2
; CHECK-NEXT:  # %bb.1:
; CHECK-NEXT:    stw 3, 12(31)
; CHECK-NEXT:    b .LBB0_3
; CHECK-NEXT:  .LBB0_2:
; CHECK-NEXT:    stw 3, 20(31)
; CHECK-NEXT:    stw 4, 16(31)
; CHECK-NEXT:    stw 5, 12(31)
; CHECK-NEXT:    stw 6, 8(31)
; CHECK-NEXT:  .LBB0_3:
; CHECK-NEXT:    li 3, 0
; CHECK-NEXT:    lwz 31, 28(1)
; CHECK-NEXT:    addi 1, 1, 32
; CHECK-NEXT:    blr
;
; CHECK-LABEL: main:
; CHECK:       # %bb.0:
; CHECK-NEXT:    stwu 1, -32(1)
; CHECK-NEXT:    stw 31, 28(1)
; CHECK-NEXT:    .cfi_def_cfa_offset 32
; CHECK-NEXT:    .cfi_offset r31, -4
; CHECK-NEXT:    mr 31, 1
; CHECK-NEXT:    .cfi_def_cfa_register r31
; CHECK-NEXT:    li 3, 0
; CHECK-NEXT:    stw 3, 24(31)
; CHECK-NEXT:    li 3, 1
; CHECK-NEXT:    li 4, 2
; CHECK-NEXT:    li 5, 3
; CHECK-NEXT:    li 6, 4
; CHECK-NEXT:    lis 7, x@ha
; CHECK-NEXT:    stw 3, 20(31)
; CHECK-NEXT:    stw 4, 16(31)
; CHECK-NEXT:    stw 5, 12(31)
; CHECK-NEXT:    stw 6, 8(31)
; CHECK-NEXT:    stw 3, x@l(7)
; CHECK-NEXT:    #APP
; CHECK-NEXT:    #NO_APP
; CHECK-NEXT:    stw 3, 20(31)
; CHECK-NEXT:    li 3, 0
; CHECK-NEXT:    stw 4, 16(31)
; CHECK-NEXT:    stw 5, 12(31)
; CHECK-NEXT:    stw 6, 8(31)
; CHECK-NEXT:    lwz 31, 28(1)
; CHECK-NEXT:    addi 1, 1, 32
; CHECK-NEXT:    blr
;
; AIX-LABEL: check_boundaries:
; AIX:       # %bb.0:
; AIX-NEXT:    stw 31, -4(1)
; AIX-NEXT:    stwu 1, -48(1)
; AIX-NEXT:    mr 31, 1
; AIX-NEXT:    li 4, 0
; AIX-NEXT:    li 3, 1
; AIX-NEXT:    stw 4, 40(31)
; AIX-NEXT:    li 4, 2
; AIX-NEXT:    li 5, 3
; AIX-NEXT:    li 6, 4
; AIX-NEXT:    cmplwi 3, 0
; AIX-NEXT:    stw 3, 36(31)
; AIX-NEXT:    stw 4, 32(31)
; AIX-NEXT:    stw 5, 28(31)
; AIX-NEXT:    stw 6, 24(31)
; AIX-NEXT:    beq 0, L..BB0_2
; AIX-NEXT:  # %bb.1:
; AIX-NEXT:    stw 3, 28(31)
; AIX-NEXT:    b L..BB0_3
; AIX-NEXT:  L..BB0_2:
; AIX-NEXT:    stw 3, 36(31)
; AIX-NEXT:    stw 4, 32(31)
; AIX-NEXT:    stw 5, 28(31)
; AIX-NEXT:    stw 6, 24(31)
; AIX-NEXT:  L..BB0_3:
; AIX-NEXT:    li 3, 0
; AIX-NEXT:    addi 1, 1, 48
; AIX-NEXT:    lwz 31, -4(1)
; AIX-NEXT:    blr
;
; AIX-LABEL: main:
; AIX:       # %bb.0:
; AIX-NEXT:    stw 31, -4(1)
; AIX-NEXT:    stwu 1, -48(1)
; AIX-NEXT:    lwz 4, L..C0(2) # @x
; AIX-NEXT:    mr 31, 1
; AIX-NEXT:    li 3, 0
; AIX-NEXT:    stw 3, 40(31)
; AIX-NEXT:    li 3, 1
; AIX-NEXT:    li 5, 2
; AIX-NEXT:    li 6, 3
; AIX-NEXT:    li 7, 4
; AIX-NEXT:    stw 3, 36(31)
; AIX-NEXT:    stw 5, 32(31)
; AIX-NEXT:    stw 6, 28(31)
; AIX-NEXT:    stw 7, 24(31)
; AIX-NEXT:    stw 3, 0(4)
; AIX-NEXT:    #APP
; AIX-NEXT:    #NO_APP
; AIX-NEXT:    stw 3, 36(31)
; AIX-NEXT:    li 3, 0
; AIX-NEXT:    stw 5, 32(31)
; AIX-NEXT:    stw 6, 28(31)
; AIX-NEXT:    stw 7, 24(31)
; AIX-NEXT:    addi 1, 1, 48
; AIX-NEXT:    lwz 31, -4(1)
; AIX-NEXT:    blr
