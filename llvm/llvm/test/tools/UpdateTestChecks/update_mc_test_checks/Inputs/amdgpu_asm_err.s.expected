// NOTE: Assertions have been autogenerated by utils/update_mc_test_checks.py
// RUN: not llvm-mc -triple=amdgcn -show-encoding %s 2>&1 | FileCheck --check-prefixes=CHECKA %s
// RUN: not llvm-mc -triple=amdgcn %s 2>&1 | FileCheck --check-prefixes=CHECKB %s

v_bfrev_b32 v5, v299
// CHECKA: :[[@LINE-1]]:17: error: register index is out of range
// CHECKB: :[[@LINE-2]]:17: error: register index is out of range

v_bfrev_b32 v5, v1
// CHECKA: v_bfrev_b32_e32 v5, v1                  ; encoding: [0x01,0x71,0x0a,0x7e]
// CHECKB: v_bfrev_b32_e32 v5, v1
