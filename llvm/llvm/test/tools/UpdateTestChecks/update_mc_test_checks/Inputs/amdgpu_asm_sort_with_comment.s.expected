// NOTE: Assertions have been autogenerated by utils/update_mc_test_checks.py UTC_ARGS: --sort
// RUN: llvm-mc -triple=amdgcn -show-encoding %s 2>&1 | FileCheck --check-prefixes=CHECK %s

v_bfrev_b32 v1, v1
// CHECK: v_bfrev_b32_e32 v1, v1                  ; encoding: [0x01,0x71,0x02,0x7e]
// This is comment B

// This is comment C
v_bfrev_b32 v2, v1
// CHECK: v_bfrev_b32_e32 v2, v1                  ; encoding: [0x01,0x71,0x04,0x7e]

v_bfrev_b32 v5, v1 //This is comment A
// CHECK: v_bfrev_b32_e32 v5, v1                  ; encoding: [0x01,0x71,0x0a,0x7e]
