# NOTE: Assertions have been autogenerated by utils/update_mc_test_checks.py
# RUN: llvm-mc -triple=amdgcn -mcpu=tonga -disassemble -show-encoding %s 2>&1 | FileCheck -check-prefixes=CHECK,CHECKA %s
# RUN: llvm-mc -triple=amdgcn -mcpu=gfx1100 -disassemble -show-encoding %s 2>&1 | FileCheck -check-prefixes=CHECK,CHECKB %s

0x00,0x00,0x00,0x7e
# CHECK: v_nop                                   ; encoding: [0x00,0x00,0x00,0x7e]

0x01,0x71,0x0a,0x7e
# CHECKA: v_movrelsd_b32_e32 v5, v1               ; encoding: [0x01,0x71,0x0a,0x7e]
# CHECKB: v_bfrev_b32_e32 v5, v1                  ; encoding: [0x01,0x71,0x0a,0x7e]
