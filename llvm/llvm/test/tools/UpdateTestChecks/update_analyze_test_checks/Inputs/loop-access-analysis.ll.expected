; NOTE: Assertions have been autogenerated by utils/update_analyze_test_checks.py
; RUN: opt -passes='print<access-info>' < %s -disable-output 2>&1 | FileCheck %s

define void @laa(ptr nocapture readonly %Base1, ptr nocapture readonly %Base2, ptr %Dest) {
; CHECK-LABEL: 'laa'
; CHECK-NEXT:    loop:
; CHECK-NEXT:      Memory dependences are safe with run-time checks
; CHECK-NEXT:      Dependences:
; CHECK-NEXT:      Run-time memory checks:
; CHECK-NEXT:      Check 0:
; CHECK-NEXT:        Comparing group ([[GRP1:0x[0-9a-f]+]]):
; CHECK-NEXT:          %gep.Dest = getelementptr inbounds float, ptr %Dest, i64 %iv
; CHECK-NEXT:          %gep.Dest = getelementptr inbounds float, ptr %Dest, i64 %iv
; CHECK-NEXT:        Against group ([[GRP2:0x[0-9a-f]+]]):
; CHECK-NEXT:          %select = select i1 %cmp, ptr %gep.1, ptr %gep.2
; CHECK-NEXT:      Check 1:
; CHECK-NEXT:        Comparing group ([[GRP1]]):
; CHECK-NEXT:          %gep.Dest = getelementptr inbounds float, ptr %Dest, i64 %iv
; CHECK-NEXT:          %gep.Dest = getelementptr inbounds float, ptr %Dest, i64 %iv
; CHECK-NEXT:        Against group ([[GRP3:0x[0-9a-f]+]]):
; CHECK-NEXT:          %select = select i1 %cmp, ptr %gep.1, ptr %gep.2
; CHECK-NEXT:      Grouped accesses:
; CHECK-NEXT:        Group [[GRP1]]:
; CHECK-NEXT:          (Low: %Dest High: (400 + %Dest))
; CHECK-NEXT:            Member: {%Dest,+,4}<nuw><%loop>
; CHECK-NEXT:            Member: {%Dest,+,4}<nuw><%loop>
; CHECK-NEXT:        Group [[GRP2]]:
; CHECK-NEXT:          (Low: %Base1 High: (400 + %Base1))
; CHECK-NEXT:            Member: {%Base1,+,4}<nw><%loop>
; CHECK-NEXT:        Group [[GRP3]]:
; CHECK-NEXT:          (Low: %Base2 High: (400 + %Base2))
; CHECK-NEXT:            Member: {%Base2,+,4}<nw><%loop>
; CHECK-EMPTY:
; CHECK-NEXT:      Non vectorizable stores to invariant address were not found in loop.
; CHECK-NEXT:      SCEV assumptions:
; CHECK-EMPTY:
; CHECK-NEXT:      Expressions re-written:
;
entry:
  br label %loop

loop:
  %iv = phi i64 [ 0, %entry ], [ %iv.next, %loop ]
  %gep.Dest = getelementptr inbounds float, ptr %Dest, i64 %iv
  %l.Dest = load float, ptr %gep.Dest
  %cmp = fcmp une float %l.Dest, 0.0
  %gep.1 = getelementptr inbounds float, ptr %Base1, i64 %iv
  %gep.2 = getelementptr inbounds float, ptr %Base2, i64 %iv
  %select = select i1 %cmp, ptr %gep.1, ptr %gep.2
  %sink = load float, ptr %select, align 4
  store float %sink, ptr %gep.Dest, align 4
  %iv.next = add nuw nsw i64 %iv, 1
  %exitcond.not = icmp eq i64 %iv.next, 100
  br i1 %exitcond.not, label %exit, label %loop

exit:
  ret void
}

define void @test_brace_escapes(ptr noundef %arr) {
; CHECK-LABEL: 'test_brace_escapes'
; CHECK-NEXT:    loop.1:
; CHECK-NEXT:      Report: could not determine number of loop iterations
; CHECK-NEXT:      Dependences:
; CHECK-NEXT:      Run-time memory checks:
; CHECK-NEXT:      Grouped accesses:
; CHECK-EMPTY:
; CHECK-NEXT:      Non vectorizable stores to invariant address were not found in loop.
; CHECK-NEXT:      SCEV assumptions:
; CHECK-EMPTY:
; CHECK-NEXT:      Expressions re-written:
; CHECK-NEXT:    loop.2:
; CHECK-NEXT:      Memory dependences are safe with run-time checks
; CHECK-NEXT:      Dependences:
; CHECK-NEXT:      Run-time memory checks:
; CHECK-NEXT:      Check 0:
; CHECK-NEXT:        Comparing group ([[GRP4:0x[0-9a-f]+]]):
; CHECK-NEXT:          %gep.iv.2 = getelementptr inbounds ptr, ptr %arr, i64 %iv.2
; CHECK-NEXT:        Against group ([[GRP5:0x[0-9a-f]+]]):
; CHECK-NEXT:          %gep.iv.1 = getelementptr inbounds ptr, ptr %arr, i64 %iv.1
; CHECK-NEXT:      Grouped accesses:
; CHECK-NEXT:        Group [[GRP4]]:
; CHECK-NEXT:          (Low: {(64 + %arr),+,64}<%loop.1> High: {(8064 + %arr),+,64}<%loop.1>)
; CHECK-NEXT:            Member: {{\{\{}}(64 + %arr),+,64}<%loop.1>,+,8}<%loop.2>
; CHECK-NEXT:        Group [[GRP5]]:
; CHECK-NEXT:          (Low: %arr High: (8000 + %arr))
; CHECK-NEXT:            Member: {%arr,+,8}<nuw><%loop.2>
; CHECK-EMPTY:
; CHECK-NEXT:      Non vectorizable stores to invariant address were not found in loop.
; CHECK-NEXT:      SCEV assumptions:
; CHECK-EMPTY:
; CHECK-NEXT:      Expressions re-written:
;
entry:
  br label %loop.1

loop.1:
  %iv = phi i64 [ %iv.next, %loop.1 ], [ 8, %entry ]
  %arr.addr.0.i = phi ptr [ %incdec.ptr.i, %loop.1 ], [ %arr, %entry ]
  %incdec.ptr.i = getelementptr inbounds ptr, ptr %arr.addr.0.i, i64 1
  %0 = load ptr, ptr %arr.addr.0.i, align 8
  %tobool.not.i = icmp eq ptr %0, null
  %iv.next = add i64 %iv, 8
  br i1 %tobool.not.i, label %loop.1.exit, label %loop.1

loop.1.exit:
  %iv.lcssa = phi i64 [ %iv, %loop.1 ]
  br label %loop.2

loop.2:
  %iv.1 = phi i64 [ 0, %loop.1.exit ], [ %iv.1.next, %loop.2 ]
  %iv.2 = phi i64 [ %iv.lcssa, %loop.1.exit ], [ %iv.2.next, %loop.2 ]
  %gep.iv.1 = getelementptr inbounds ptr, ptr %arr, i64 %iv.1
  %l.1 = load ptr, ptr %gep.iv.1, align 8
  %iv.2.next = add nsw i64 %iv.2, 1
  %gep.iv.2 = getelementptr inbounds ptr, ptr %arr, i64 %iv.2
  store ptr %l.1, ptr %gep.iv.2, align 8
  %iv.1.next = add nuw nsw i64 %iv.1, 1
  %cmp = icmp ult i64 %iv.1.next, 1000
  br i1 %cmp, label %loop.2, label %exit

exit:
  ret void
}

