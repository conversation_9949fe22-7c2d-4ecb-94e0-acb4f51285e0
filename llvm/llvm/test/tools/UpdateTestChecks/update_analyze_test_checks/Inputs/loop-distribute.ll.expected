; NOTE: Assertions have been autogenerated by utils/update_analyze_test_checks.py
; RUN: opt -passes=loop-distribute -enable-loop-distribute \
; RUN:   -debug-only=loop-distribute -disable-output 2>&1 %s | FileCheck %s

define void @ldist(i1 %cond, ptr %A, ptr %B, ptr %C) {
; CHECK-LABEL: 'ldist'
; CHECK-NEXT:  LDist: Found a candidate loop: for.body
; CHECK-NEXT:  LDist: Backward dependences:
; CHECK-NEXT:    Unknown:
; CHECK-NEXT:        %lv3 = load i16, ptr %c.sink, align 2 ->
; CHECK-NEXT:        store i16 %add, ptr %c.sink, align 1
; CHECK-NEXT:    Unknown:
; CHECK-NEXT:        %lv3 = load i16, ptr %c.sink, align 2 ->
; CHECK-NEXT:        store i16 %add, ptr %c.sink, align 1
; CHECK-NEXT:  LDist: Seeded partitions:
; CHECK-NEXT:  LDist: Partition 0:
; CHECK-NEXT:    for.body: %lv = load i16, ptr %A, align 1
; CHECK-NEXT:  LDist: Partition 1:
; CHECK-NEXT:    for.body: store i16 %lv, ptr %A, align 1
; CHECK-NEXT:  LDist: Partition 2:
; CHECK-NEXT:    if.then: %lv2 = load i16, ptr %A, align 1
; CHECK-NEXT:  LDist: Partition 3: (cycle)
; CHECK-NEXT:    if.end: %lv3 = load i16, ptr %c.sink, align 2
; CHECK-NEXT:    if.end: store i16 %add, ptr %c.sink, align 1
; CHECK-NEXT:  LDist: Merged partitions:
; CHECK-NEXT:  LDist: Partition 0:
; CHECK-NEXT:    for.body: %lv = load i16, ptr %A, align 1
; CHECK-NEXT:    for.body: store i16 %lv, ptr %A, align 1
; CHECK-NEXT:    if.then: %lv2 = load i16, ptr %A, align 1
; CHECK-NEXT:  LDist: Partition 1: (cycle)
; CHECK-NEXT:    if.end: %lv3 = load i16, ptr %c.sink, align 2
; CHECK-NEXT:    if.end: store i16 %add, ptr %c.sink, align 1
; CHECK-NEXT:  LDist: Populated partitions:
; CHECK-NEXT:  LDist: Partition 0:
; CHECK-NEXT:    for.body: %lv = load i16, ptr %A, align 1
; CHECK-NEXT:    for.body: store i16 %lv, ptr %A, align 1
; CHECK-NEXT:    if.then: %lv2 = load i16, ptr %A, align 1
; CHECK-NEXT:    for.body: br i1 %cond, label %if.then, label %if.end
; CHECK-NEXT:    if.then: br label %if.end
; CHECK-NEXT:    if.end: br i1 %tobool.not, label %for.end.loopexit, label %for.body
; CHECK-NEXT:    if.end: %tobool.not = icmp eq i16 %iv.next, 1000
; CHECK-NEXT:    if.end: %iv.next = add nuw nsw i16 %iv, 1
; CHECK-NEXT:    for.body: %iv = phi i16 [ 0, %entry ], [ %iv.next, %if.end ]
; CHECK-NEXT:  LDist: Partition 1: (cycle)
; CHECK-NEXT:    if.end: %lv3 = load i16, ptr %c.sink, align 2
; CHECK-NEXT:    if.end: store i16 %add, ptr %c.sink, align 1
; CHECK-NEXT:    for.body: br i1 %cond, label %if.then, label %if.end
; CHECK-NEXT:    if.then: br label %if.end
; CHECK-NEXT:    if.end: br i1 %tobool.not, label %for.end.loopexit, label %for.body
; CHECK-NEXT:    if.end: %tobool.not = icmp eq i16 %iv.next, 1000
; CHECK-NEXT:    if.end: %iv.next = add nuw nsw i16 %iv, 1
; CHECK-NEXT:    for.body: %iv = phi i16 [ 0, %entry ], [ %iv.next, %if.end ]
; CHECK-NEXT:    if.end: %add = add i16 %lv3, 10
; CHECK-NEXT:    if.end: %c.sink = phi ptr [ %B, %if.then ], [ %C, %for.body ]
; CHECK-NEXT:  LDist: Distributing loop: for.body
; CHECK-NEXT:  LDist: Pointers:
; CHECK-NEXT:  Check 0:
; CHECK-NEXT:    Comparing group ([[GRP1:0x[0-9a-f]+]]):
; CHECK-NEXT:    ptr %A
; CHECK-NEXT:    ptr %A
; CHECK-NEXT:    Against group ([[GRP2:0x[0-9a-f]+]]):
; CHECK-NEXT:    ptr %C
; CHECK-NEXT:    ptr %C
; CHECK-NEXT:  Check 1:
; CHECK-NEXT:    Comparing group ([[GRP1]]):
; CHECK-NEXT:    ptr %A
; CHECK-NEXT:    ptr %A
; CHECK-NEXT:    Against group ([[GRP3:0x[0-9a-f]+]]):
; CHECK-NEXT:    ptr %B
; CHECK-NEXT:    ptr %B
; CHECK-NEXT:  LDist: After removing unused Instrs:
; CHECK-NEXT:  LDist: Partition 0:
; CHECK-NEXT:  for.body.ldist1: ; preds = %if.end.ldist1, %for.body.ph.ldist1
; CHECK-NEXT:    %iv.ldist1 = phi i16 [ 0, %for.body.ph.ldist1 ], [ %iv.next.ldist1, %if.end.ldist1 ]
; CHECK-NEXT:    %lv.ldist1 = load i16, ptr %A, align 1, !alias.scope !0, !noalias !3
; CHECK-NEXT:    store i16 %lv.ldist1, ptr %A, align 1, !alias.scope !0, !noalias !3
; CHECK-NEXT:    br i1 %cond, label %if.then.ldist1, label %if.end.ldist1
; CHECK-EMPTY:
; CHECK-NEXT:  if.then.ldist1: ; preds = %for.body.ldist1
; CHECK-NEXT:    %lv2.ldist1 = load i16, ptr %A, align 1, !alias.scope !0, !noalias !3
; CHECK-NEXT:    br label %if.end.ldist1
; CHECK-EMPTY:
; CHECK-NEXT:  if.end.ldist1: ; preds = %if.then.ldist1, %for.body.ldist1
; CHECK-NEXT:    %iv.next.ldist1 = add nuw nsw i16 %iv.ldist1, 1
; CHECK-NEXT:    %tobool.not.ldist1 = icmp eq i16 %iv.next.ldist1, 1000
; CHECK-NEXT:    br i1 %tobool.not.ldist1, label %for.body.ph, label %for.body.ldist1
; CHECK-NEXT:  LDist: Partition 1:
; CHECK-NEXT:  for.body: ; preds = %if.end, %for.body.ph
; CHECK-NEXT:    %iv = phi i16 [ 0, %for.body.ph ], [ %iv.next, %if.end ]
; CHECK-NEXT:    br i1 %cond, label %if.then, label %if.end
; CHECK-EMPTY:
; CHECK-NEXT:  if.then: ; preds = %for.body
; CHECK-NEXT:    br label %if.end
; CHECK-EMPTY:
; CHECK-NEXT:  if.end: ; preds = %if.then, %for.body
; CHECK-NEXT:    %c.sink = phi ptr [ %B, %if.then ], [ %C, %for.body ]
; CHECK-NEXT:    %lv3 = load i16, ptr %c.sink, align 2
; CHECK-NEXT:    %add = add i16 %lv3, 10
; CHECK-NEXT:    store i16 %add, ptr %c.sink, align 1
; CHECK-NEXT:    %iv.next = add nuw nsw i16 %iv, 1
; CHECK-NEXT:    %tobool.not = icmp eq i16 %iv.next, 1000
; CHECK-NEXT:    br i1 %tobool.not, label %for.end.loopexit.loopexit6, label %for.body
;
entry:
  br label %for.body

for.body:                                         ; preds = %if.end, %entry
  %iv = phi i16 [ 0, %entry ], [ %iv.next, %if.end ]
  %lv = load i16, ptr %A, align 1
  store i16 %lv, ptr %A, align 1
  br i1 %cond, label %if.then, label %if.end

if.then:                                          ; preds = %for.body
  %lv2 = load i16, ptr %A, align 1
  br label %if.end

if.end:                                           ; preds = %if.then, %for.body
  %c.sink = phi ptr [ %B, %if.then ], [ %C, %for.body ]
  %lv3 = load i16, ptr %c.sink
  %add = add i16 %lv3, 10
  store i16 %add, ptr %c.sink, align 1
  %iv.next = add nuw nsw i16 %iv, 1
  %tobool.not = icmp eq i16 %iv.next, 1000
  br i1 %tobool.not, label %for.end.loopexit, label %for.body

for.end.loopexit:                                 ; preds = %if.end
  ret void
}
