## Basic test checking that update_analyze_test_checks.py works correctly
# RUN: cp -f %S/Inputs/branch-probability-analysis.ll %t.ll && %update_analyze_test_checks %t.ll
# RUN: diff -u %t.ll %S/Inputs/branch-probability-analysis.ll.expected
## Check that running the script again does not change the result:
# RUN: %update_analyze_test_checks %t.ll
# RUN: diff -u %t.ll %S/Inputs/branch-probability-analysis.ll.expected
