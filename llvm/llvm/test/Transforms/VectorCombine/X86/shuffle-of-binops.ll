; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 4
; RUN: opt < %s -passes=vector-combine -S -mtriple=x86_64-- -mattr=SSE2 | FileCheck %s --check-prefixes=CHECK,SSE
; RUN: opt < %s -passes=vector-combine -S -mtriple=x86_64-- -mattr=AVX2 | FileCheck %s --check-prefixes=CHECK,AVX

declare void @use(<4 x i32>)

; Shuffle is much cheaper than fdiv. FMF are intersected.

define <4 x float> @shuf_fdiv_v4f32_yy(<4 x float> %x, <4 x float> %y, <4 x float> %z) {
; CHECK-LABEL: define <4 x float> @shuf_fdiv_v4f32_yy(
; CHECK-SAME: <4 x float> [[X:%.*]], <4 x float> [[Y:%.*]], <4 x float> [[Z:%.*]]) #[[ATTR0:[0-9]+]] {
; CHECK-NEXT:    [[TMP2:%.*]] = shufflevector <4 x float> [[X]], <4 x float> [[Z]], <4 x i32> <i32 1, i32 3, i32 5, i32 7>
; CHECK-NEXT:    [[TMP3:%.*]] = shufflevector <4 x float> [[Y]], <4 x float> poison, <4 x i32> <i32 1, i32 3, i32 1, i32 3>
; CHECK-NEXT:    [[R:%.*]] = fdiv arcp <4 x float> [[TMP2]], [[TMP3]]
; CHECK-NEXT:    ret <4 x float> [[R]]
;
  %b0 = fdiv fast <4 x float> %x, %y
  %b1 = fdiv arcp <4 x float> %z, %y
  %r = shufflevector <4 x float> %b0, <4 x float> %b1, <4 x i32> <i32 1, i32 3, i32 5, i32 7>
  ret <4 x float> %r
}

; Common operand is op0 of the binops.

define <4 x i32> @shuf_add_v4i32_xx(<4 x i32> %x, <4 x i32> %y, <4 x i32> %z) {
; CHECK-LABEL: define <4 x i32> @shuf_add_v4i32_xx(
; CHECK-SAME: <4 x i32> [[X:%.*]], <4 x i32> [[Y:%.*]], <4 x i32> [[Z:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:    [[B0:%.*]] = add <4 x i32> [[X]], [[Y]]
; CHECK-NEXT:    [[B1:%.*]] = add <4 x i32> [[X]], [[Z]]
; CHECK-NEXT:    [[R2:%.*]] = shufflevector <4 x i32> [[B0]], <4 x i32> [[B1]], <4 x i32> <i32 poison, i32 poison, i32 6, i32 0>
; CHECK-NEXT:    ret <4 x i32> [[R2]]
;
  %b0 = add <4 x i32> %x, %y
  %b1 = add <4 x i32> %x, %z
  %r = shufflevector <4 x i32> %b0, <4 x i32> %b1, <4 x i32> <i32 poison, i32 poison, i32 6, i32 0>
  ret <4 x i32> %r
}

; For commutative instructions, common operand may be swapped (SSE - expensive fmul vs AVX - cheap fmul)

define <4 x float> @shuf_fmul_v4f32_xx_swap(<4 x float> %x, <4 x float> %y, <4 x float> %z) {
; SSE-LABEL: define <4 x float> @shuf_fmul_v4f32_xx_swap(
; SSE-SAME: <4 x float> [[X:%.*]], <4 x float> [[Y:%.*]], <4 x float> [[Z:%.*]]) #[[ATTR0]] {
; SSE-NEXT:    [[TMP1:%.*]] = shufflevector <4 x float> [[Y]], <4 x float> [[Z]], <4 x i32> <i32 0, i32 3, i32 4, i32 7>
; SSE-NEXT:    [[TMP2:%.*]] = shufflevector <4 x float> [[X]], <4 x float> poison, <4 x i32> <i32 0, i32 3, i32 0, i32 3>
; SSE-NEXT:    [[R:%.*]] = fmul <4 x float> [[TMP1]], [[TMP2]]
; SSE-NEXT:    ret <4 x float> [[R]]
;
; AVX-LABEL: define <4 x float> @shuf_fmul_v4f32_xx_swap(
; AVX-SAME: <4 x float> [[X:%.*]], <4 x float> [[Y:%.*]], <4 x float> [[Z:%.*]]) #[[ATTR0]] {
; AVX-NEXT:    [[B0:%.*]] = fmul <4 x float> [[X]], [[Y]]
; AVX-NEXT:    [[B1:%.*]] = fmul <4 x float> [[Z]], [[X]]
; AVX-NEXT:    [[R:%.*]] = shufflevector <4 x float> [[B0]], <4 x float> [[B1]], <4 x i32> <i32 0, i32 3, i32 4, i32 7>
; AVX-NEXT:    ret <4 x float> [[R]]
;
  %b0 = fmul <4 x float> %x, %y
  %b1 = fmul <4 x float> %z, %x
  %r = shufflevector <4 x float> %b0, <4 x float> %b1, <4 x i32> <i32 0, i32 3, i32 4, i32 7>
  ret <4 x float> %r
}

; For commutative instructions, common operand may be swapped.

define <2 x i64> @shuf_and_v2i64_yy_swap(<2 x i64> %x, <2 x i64> %y, <2 x i64> %z) {
; CHECK-LABEL: define <2 x i64> @shuf_and_v2i64_yy_swap(
; CHECK-SAME: <2 x i64> [[X:%.*]], <2 x i64> [[Y:%.*]], <2 x i64> [[Z:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:    [[B0:%.*]] = and <2 x i64> [[X]], [[Y]]
; CHECK-NEXT:    [[B1:%.*]] = and <2 x i64> [[Y]], [[Z]]
; CHECK-NEXT:    [[R:%.*]] = shufflevector <2 x i64> [[B0]], <2 x i64> [[B1]], <2 x i32> <i32 3, i32 0>
; CHECK-NEXT:    ret <2 x i64> [[R]]
;
  %b0 = and <2 x i64> %x, %y
  %b1 = and <2 x i64> %y, %z
  %r = shufflevector <2 x i64> %b0, <2 x i64> %b1, <2 x i32> <i32 3, i32 0>
  ret <2 x i64> %r
}

; non-commutative binop, but common op0

define <4 x i32> @shuf_shl_v4i32_xx(<4 x i32> %x, <4 x i32> %y, <4 x i32> %z) {
; CHECK-LABEL: define <4 x i32> @shuf_shl_v4i32_xx(
; CHECK-SAME: <4 x i32> [[X:%.*]], <4 x i32> [[Y:%.*]], <4 x i32> [[Z:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:    [[TMP1:%.*]] = shufflevector <4 x i32> [[X]], <4 x i32> poison, <4 x i32> <i32 3, i32 1, i32 1, i32 2>
; CHECK-NEXT:    [[TMP2:%.*]] = shufflevector <4 x i32> [[Y]], <4 x i32> [[Z]], <4 x i32> <i32 3, i32 1, i32 1, i32 6>
; CHECK-NEXT:    [[R:%.*]] = shl <4 x i32> [[TMP1]], [[TMP2]]
; CHECK-NEXT:    ret <4 x i32> [[R]]
;
  %b0 = shl <4 x i32> %x, %y
  %b1 = shl <4 x i32> %x, %z
  %r = shufflevector <4 x i32> %b0, <4 x i32> %b1, <4 x i32> <i32 3, i32 1, i32 1, i32 6>
  ret <4 x i32> %r
}

; common operand, but not commutable (SSE - expensive vector shift vs AVX2 - cheap vector shift)

define <4 x i32> @shuf_shl_v4i32_xx_swap(<4 x i32> %x, <4 x i32> %y, <4 x i32> %z) {
; SSE-LABEL: define <4 x i32> @shuf_shl_v4i32_xx_swap(
; SSE-SAME: <4 x i32> [[X:%.*]], <4 x i32> [[Y:%.*]], <4 x i32> [[Z:%.*]]) #[[ATTR0]] {
; SSE-NEXT:    [[TMP1:%.*]] = shufflevector <4 x i32> [[X]], <4 x i32> [[Z]], <4 x i32> <i32 3, i32 2, i32 2, i32 5>
; SSE-NEXT:    [[TMP2:%.*]] = shufflevector <4 x i32> [[Y]], <4 x i32> [[X]], <4 x i32> <i32 3, i32 2, i32 2, i32 5>
; SSE-NEXT:    [[R:%.*]] = shl <4 x i32> [[TMP1]], [[TMP2]]
; SSE-NEXT:    ret <4 x i32> [[R]]
;
; AVX-LABEL: define <4 x i32> @shuf_shl_v4i32_xx_swap(
; AVX-SAME: <4 x i32> [[X:%.*]], <4 x i32> [[Y:%.*]], <4 x i32> [[Z:%.*]]) #[[ATTR0]] {
; AVX-NEXT:    [[B0:%.*]] = shl <4 x i32> [[X]], [[Y]]
; AVX-NEXT:    [[B1:%.*]] = shl <4 x i32> [[Z]], [[X]]
; AVX-NEXT:    [[R:%.*]] = shufflevector <4 x i32> [[B0]], <4 x i32> [[B1]], <4 x i32> <i32 3, i32 2, i32 2, i32 5>
; AVX-NEXT:    ret <4 x i32> [[R]]
;
  %b0 = shl <4 x i32> %x, %y
  %b1 = shl <4 x i32> %z, %x
  %r = shufflevector <4 x i32> %b0, <4 x i32> %b1, <4 x i32> <i32 3, i32 2, i32 2, i32 5>
  ret <4 x i32> %r
}

; negative test - mismatched opcodes

define <2 x i64> @shuf_sub_add_v2i64_yy(<2 x i64> %x, <2 x i64> %y, <2 x i64> %z) {
; CHECK-LABEL: define <2 x i64> @shuf_sub_add_v2i64_yy(
; CHECK-SAME: <2 x i64> [[X:%.*]], <2 x i64> [[Y:%.*]], <2 x i64> [[Z:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:    [[B0:%.*]] = sub <2 x i64> [[X]], [[Y]]
; CHECK-NEXT:    [[B1:%.*]] = add <2 x i64> [[Z]], [[Y]]
; CHECK-NEXT:    [[R:%.*]] = shufflevector <2 x i64> [[B0]], <2 x i64> [[B1]], <2 x i32> <i32 3, i32 0>
; CHECK-NEXT:    ret <2 x i64> [[R]]
;
  %b0 = sub <2 x i64> %x, %y
  %b1 = add <2 x i64> %z, %y
  %r = shufflevector <2 x i64> %b0, <2 x i64> %b1, <2 x i32> <i32 3, i32 0>
  ret <2 x i64> %r
}

; type change via shuffle

define <8 x float> @shuf_fmul_v4f32_xx_type(<4 x float> %x, <4 x float> %y, <4 x float> %z) {
; CHECK-LABEL: define <8 x float> @shuf_fmul_v4f32_xx_type(
; CHECK-SAME: <4 x float> [[X:%.*]], <4 x float> [[Y:%.*]], <4 x float> [[Z:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:    [[B0:%.*]] = fmul <4 x float> [[X]], [[Y]]
; CHECK-NEXT:    [[B1:%.*]] = fmul <4 x float> [[Z]], [[X]]
; CHECK-NEXT:    [[R:%.*]] = shufflevector <4 x float> [[B0]], <4 x float> [[B1]], <8 x i32> <i32 0, i32 3, i32 4, i32 7, i32 0, i32 1, i32 1, i32 6>
; CHECK-NEXT:    ret <8 x float> [[R]]
;
  %b0 = fmul <4 x float> %x, %y
  %b1 = fmul <4 x float> %z, %x
  %r = shufflevector <4 x float> %b0, <4 x float> %b1, <8 x i32> <i32 0, i32 3, i32 4, i32 7, i32 0, i32 1, i32 1, i32 6>
  ret <8 x float> %r
}

; negative test - uses

define <4 x i32> @shuf_lshr_v4i32_yy_use1(<4 x i32> %x, <4 x i32> %y, <4 x i32> %z) {
; CHECK-LABEL: define <4 x i32> @shuf_lshr_v4i32_yy_use1(
; CHECK-SAME: <4 x i32> [[X:%.*]], <4 x i32> [[Y:%.*]], <4 x i32> [[Z:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:    [[B0:%.*]] = lshr <4 x i32> [[X]], [[Y]]
; CHECK-NEXT:    call void @use(<4 x i32> [[B0]])
; CHECK-NEXT:    [[B1:%.*]] = lshr <4 x i32> [[Z]], [[Y]]
; CHECK-NEXT:    [[R:%.*]] = shufflevector <4 x i32> [[B0]], <4 x i32> [[B1]], <4 x i32> <i32 0, i32 2, i32 4, i32 6>
; CHECK-NEXT:    ret <4 x i32> [[R]]
;
  %b0 = lshr <4 x i32> %x, %y
  call void @use(<4 x i32> %b0)
  %b1 = lshr <4 x i32> %z, %y
  %r = shufflevector <4 x i32> %b0, <4 x i32> %b1, <4 x i32> <i32 0, i32 2, i32 4, i32 6>
  ret <4 x i32> %r
}

; negative test - uses

define <4 x i32> @shuf_mul_v4i32_yy_use2(<4 x i32> %x, <4 x i32> %y, <4 x i32> %z) {
; CHECK-LABEL: define <4 x i32> @shuf_mul_v4i32_yy_use2(
; CHECK-SAME: <4 x i32> [[X:%.*]], <4 x i32> [[Y:%.*]], <4 x i32> [[Z:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:    [[B0:%.*]] = mul <4 x i32> [[X]], [[Y]]
; CHECK-NEXT:    [[B1:%.*]] = mul <4 x i32> [[Z]], [[Y]]
; CHECK-NEXT:    call void @use(<4 x i32> [[B1]])
; CHECK-NEXT:    [[R:%.*]] = shufflevector <4 x i32> [[B0]], <4 x i32> [[B1]], <4 x i32> <i32 1, i32 3, i32 5, i32 7>
; CHECK-NEXT:    ret <4 x i32> [[R]]
;
  %b0 = mul <4 x i32> %x, %y
  %b1 = mul <4 x i32> %z, %y
  call void @use(<4 x i32> %b1)
  %r = shufflevector <4 x i32> %b0, <4 x i32> %b1, <4 x i32> <i32 1, i32 3, i32 5, i32 7>
  ret <4 x i32> %r
}

; non-matching operands (not commutable)

define <4 x float> @shuf_fdiv_v4f32_no_common_op(<4 x float> %x, <4 x float> %y, <4 x float> %z, <4 x float> %w) {
; CHECK-LABEL: define <4 x float> @shuf_fdiv_v4f32_no_common_op(
; CHECK-SAME: <4 x float> [[X:%.*]], <4 x float> [[Y:%.*]], <4 x float> [[Z:%.*]], <4 x float> [[W:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:    [[TMP1:%.*]] = shufflevector <4 x float> [[X]], <4 x float> [[Z]], <4 x i32> <i32 1, i32 3, i32 5, i32 7>
; CHECK-NEXT:    [[TMP2:%.*]] = shufflevector <4 x float> [[Y]], <4 x float> [[W]], <4 x i32> <i32 1, i32 3, i32 5, i32 7>
; CHECK-NEXT:    [[R:%.*]] = fdiv <4 x float> [[TMP1]], [[TMP2]]
; CHECK-NEXT:    ret <4 x float> [[R]]
;
  %b0 = fdiv <4 x float> %x, %y
  %b1 = fdiv <4 x float> %z, %w
  %r = shufflevector <4 x float> %b0, <4 x float> %b1, <4 x i32> <i32 1, i32 3, i32 5, i32 7>
  ret <4 x float> %r
}

; negative test - binops may be relatively cheap

define <16 x i16> @shuf_and_v16i16_yy_expensive_shuf(<16 x i16> %x, <16 x i16> %y, <16 x i16> %z) {
; CHECK-LABEL: define <16 x i16> @shuf_and_v16i16_yy_expensive_shuf(
; CHECK-SAME: <16 x i16> [[X:%.*]], <16 x i16> [[Y:%.*]], <16 x i16> [[Z:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:    [[B0:%.*]] = and <16 x i16> [[X]], [[Y]]
; CHECK-NEXT:    [[B1:%.*]] = and <16 x i16> [[Y]], [[Z]]
; CHECK-NEXT:    [[R:%.*]] = shufflevector <16 x i16> [[B0]], <16 x i16> [[B1]], <16 x i32> <i32 15, i32 22, i32 25, i32 13, i32 28, i32 0, i32 poison, i32 3, i32 0, i32 30, i32 3, i32 7, i32 9, i32 19, i32 2, i32 22>
; CHECK-NEXT:    ret <16 x i16> [[R]]
;
  %b0 = and <16 x i16> %x, %y
  %b1 = and <16 x i16> %y, %z
  %r = shufflevector <16 x i16> %b0, <16 x i16> %b1, <16 x i32> <i32 15, i32 22, i32 25, i32 13, i32 28, i32 0, i32 poison, i32 3, i32 0, i32 30, i32 3, i32 7, i32 9, i32 19, i32 2, i32 22>
  ret <16 x i16> %r
}

; negative test - don't fold shuffle(divrem(x,y),divrem(z,w)) if mask contains poison (PR89390)

define <4 x i32> @shuf_srem_v4i32_poison(<4 x i32> %a0, <4 x i32> %a1) {
; CHECK-LABEL: define <4 x i32> @shuf_srem_v4i32_poison(
; CHECK-SAME: <4 x i32> [[A0:%.*]], <4 x i32> [[A1:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:    [[SREM0:%.*]] = srem <4 x i32> [[A1]], [[A0]]
; CHECK-NEXT:    [[SREM1:%.*]] = srem <4 x i32> splat (i32 1), [[A0]]
; CHECK-NEXT:    [[R:%.*]] = shufflevector <4 x i32> [[SREM0]], <4 x i32> [[SREM1]], <4 x i32> <i32 0, i32 poison, i32 6, i32 3>
; CHECK-NEXT:    ret <4 x i32> [[R]]
;
  %srem0 = srem <4 x i32> %a1, %a0
  %srem1 = srem <4 x i32> <i32 1, i32 1, i32 1, i32 1>, %a0
  %r = shufflevector <4 x i32> %srem0, <4 x i32> %srem1, <4 x i32> <i32 0, i32 poison, i32 6, i32 3>
  ret <4 x i32> %r
}

