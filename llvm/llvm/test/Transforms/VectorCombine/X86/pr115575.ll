; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 4
; RUN: opt < %s -passes=vector-combine -S -mtriple=x86_64-- | FileCheck %s

define <2 x i8> @PR115575(i8 %x) {
; CHECK-LABEL: define <2 x i8> @PR115575(
; CHECK-SAME: i8 [[X:%.*]]) {
; CHECK-NEXT:    [[INS:%.*]] = insertelement <2 x i8> poison, i8 [[X]], i32 3
; CHECK-NEXT:    [[BO:%.*]] = sdiv <2 x i8> [[INS]], <i8 5, i8 2>
; CHECK-NEXT:    ret <2 x i8> [[BO]]
;
  %ins = insertelement <2 x i8> poison, i8 %x, i32 3
  %bo = sdiv <2 x i8> %ins, <i8 5, i8 2>
  ret <2 x i8> %bo
}
