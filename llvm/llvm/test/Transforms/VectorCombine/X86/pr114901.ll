; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 4
; RUN: opt < %s -passes=vector-combine -S -mtriple=x86_64-- -mattr=sse2 | FileCheck %s --check-prefixes=SSE
; RUN: opt < %s -passes=vector-combine -S -mtriple=x86_64-- -mattr=avx2 | FileCheck %s --check-prefixes=AVX

; PR114901 - ensure that the ASHR node doesn't commute the operands.
define i1 @PR114901(<4 x i32> %a) {
; SSE-LABEL: define i1 @PR114901(
; SSE-SAME: <4 x i32> [[A:%.*]]) #[[ATTR0:[0-9]+]] {
; SSE-NEXT:    [[E1:%.*]] = extractelement <4 x i32> [[A]], i32 1
; SSE-NEXT:    [[E3:%.*]] = extractelement <4 x i32> [[A]], i32 3
; SSE-NEXT:    [[CMP1:%.*]] = icmp sgt i32 [[E1]], -8
; SSE-NEXT:    [[CMP3:%.*]] = icmp sgt i32 [[E3]], 42
; SSE-NEXT:    [[R:%.*]] = ashr i1 [[CMP3]], [[CMP1]]
; SSE-NEXT:    ret i1 [[R]]
;
; AVX-LABEL: define i1 @PR114901(
; AVX-SAME: <4 x i32> [[A:%.*]]) #[[ATTR0:[0-9]+]] {
; AVX-NEXT:    [[TMP1:%.*]] = icmp sgt <4 x i32> [[A]], <i32 poison, i32 -8, i32 poison, i32 42>
; AVX-NEXT:    [[SHIFT:%.*]] = shufflevector <4 x i1> [[TMP1]], <4 x i1> poison, <4 x i32> <i32 poison, i32 3, i32 poison, i32 poison>
; AVX-NEXT:    [[TMP2:%.*]] = ashr <4 x i1> [[SHIFT]], [[TMP1]]
; AVX-NEXT:    [[R:%.*]] = extractelement <4 x i1> [[TMP2]], i64 1
; AVX-NEXT:    ret i1 [[R]]
;
  %e1 = extractelement <4 x i32> %a, i32 1
  %e3 = extractelement <4 x i32> %a, i32 3
  %cmp1 = icmp sgt i32 %e1, 4294967288
  %cmp3 = icmp sgt i32 %e3, 42
  %r = ashr i1 %cmp3, %cmp1
  ret i1 %r
}

define i1 @PR114901_flip(<4 x i32> %a) {
; SSE-LABEL: define i1 @PR114901_flip(
; SSE-SAME: <4 x i32> [[A:%.*]]) #[[ATTR0]] {
; SSE-NEXT:    [[E1:%.*]] = extractelement <4 x i32> [[A]], i32 1
; SSE-NEXT:    [[E3:%.*]] = extractelement <4 x i32> [[A]], i32 3
; SSE-NEXT:    [[CMP1:%.*]] = icmp sgt i32 [[E1]], -8
; SSE-NEXT:    [[CMP3:%.*]] = icmp sgt i32 [[E3]], 42
; SSE-NEXT:    [[R:%.*]] = ashr i1 [[CMP1]], [[CMP3]]
; SSE-NEXT:    ret i1 [[R]]
;
; AVX-LABEL: define i1 @PR114901_flip(
; AVX-SAME: <4 x i32> [[A:%.*]]) #[[ATTR0]] {
; AVX-NEXT:    [[TMP1:%.*]] = icmp sgt <4 x i32> [[A]], <i32 poison, i32 -8, i32 poison, i32 42>
; AVX-NEXT:    [[SHIFT:%.*]] = shufflevector <4 x i1> [[TMP1]], <4 x i1> poison, <4 x i32> <i32 poison, i32 3, i32 poison, i32 poison>
; AVX-NEXT:    [[TMP2:%.*]] = ashr <4 x i1> [[TMP1]], [[SHIFT]]
; AVX-NEXT:    [[R:%.*]] = extractelement <4 x i1> [[TMP2]], i64 1
; AVX-NEXT:    ret i1 [[R]]
;
  %e1 = extractelement <4 x i32> %a, i32 1
  %e3 = extractelement <4 x i32> %a, i32 3
  %cmp1 = icmp sgt i32 %e1, 4294967288
  %cmp3 = icmp sgt i32 %e3, 42
  %r = ashr i1 %cmp1, %cmp3
  ret i1 %r
}
