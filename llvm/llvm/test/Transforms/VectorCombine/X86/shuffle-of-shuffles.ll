; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 4
; RUN: opt < %s -passes=vector-combine -S -mtriple=x86_64-- -mattr=sse2 | FileCheck %s --check-prefixes=CHECK,SSE
; RUN: opt < %s -passes=vector-combine -S -mtriple=x86_64-- -mattr=avx2 | FileCheck %s --check-prefixes=CHECK,AVX

; fold to identity

define <8 x i32> @concat_extract_subvectors(<8 x i32> %x) {
; CHECK-LABEL: define <8 x i32> @concat_extract_subvectors(
; CHECK-SAME: <8 x i32> [[X:%.*]]) #[[ATTR0:[0-9]+]] {
; CHECK-NEXT:    ret <8 x i32> [[X]]
;
  %lo = shufflevector <8 x i32> %x, <8 x i32> poison, <4 x i32> <i32 0, i32 1, i32 2, i32 3>
  %hi = shufflevector <8 x i32> %x, <8 x i32> poison, <4 x i32> <i32 4, i32 5, i32 6, i32 7>
  %concat = shufflevector <4 x i32> %lo, <4 x i32> %hi, <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
  ret <8 x i32> %concat
}

; negative test - shuffle contains undef

define <8 x i32> @concat_extract_subvectors_undef(<8 x i32> %x) {
; CHECK-LABEL: define <8 x i32> @concat_extract_subvectors_undef(
; CHECK-SAME: <8 x i32> [[X:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:    [[LO:%.*]] = shufflevector <8 x i32> [[X]], <8 x i32> undef, <4 x i32> <i32 0, i32 1, i32 2, i32 8>
; CHECK-NEXT:    [[HI:%.*]] = shufflevector <8 x i32> [[X]], <8 x i32> undef, <4 x i32> <i32 4, i32 5, i32 6, i32 8>
; CHECK-NEXT:    [[CONCAT:%.*]] = shufflevector <4 x i32> [[LO]], <4 x i32> [[HI]], <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
; CHECK-NEXT:    ret <8 x i32> [[CONCAT]]
;
  %lo = shufflevector <8 x i32> %x, <8 x i32> undef, <4 x i32> <i32 0, i32 1, i32 2, i32 8>
  %hi = shufflevector <8 x i32> %x, <8 x i32> undef, <4 x i32> <i32 4, i32 5, i32 6, i32 8>
  %concat = shufflevector <4 x i32> %lo, <4 x i32> %hi, <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
  ret <8 x i32> %concat
}

; shuffle contains poison

define <8 x i32> @concat_extract_subvectors_poison(<8 x i32> %x) {
; CHECK-LABEL: define <8 x i32> @concat_extract_subvectors_poison(
; CHECK-SAME: <8 x i32> [[X:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:    ret <8 x i32> [[X]]
;
  %lo = shufflevector <8 x i32> %x, <8 x i32> poison, <4 x i32> <i32 0, i32 1, i32 2, i32 8>
  %hi = shufflevector <8 x i32> %x, <8 x i32> poison, <4 x i32> <i32 4, i32 5, i32 6, i32 8>
  %concat = shufflevector <4 x i32> %lo, <4 x i32> %hi, <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
  ret <8 x i32> %concat
}

; broadcast loads are free on AVX (and blends are much cheap than general 2-operand shuffles)

define  <4 x double> @blend_broadcasts_v4f64(ptr %p0, ptr %p1)  {
; SSE-LABEL: define <4 x double> @blend_broadcasts_v4f64(
; SSE-SAME: ptr [[P0:%.*]], ptr [[P1:%.*]]) #[[ATTR0]] {
; SSE-NEXT:    [[LD0:%.*]] = load <4 x double>, ptr [[P0]], align 32
; SSE-NEXT:    [[LD1:%.*]] = load <4 x double>, ptr [[P1]], align 32
; SSE-NEXT:    [[BLEND:%.*]] = shufflevector <4 x double> [[LD0]], <4 x double> [[LD1]], <4 x i32> <i32 0, i32 4, i32 4, i32 0>
; SSE-NEXT:    ret <4 x double> [[BLEND]]
;
; AVX-LABEL: define <4 x double> @blend_broadcasts_v4f64(
; AVX-SAME: ptr [[P0:%.*]], ptr [[P1:%.*]]) #[[ATTR0]] {
; AVX-NEXT:    [[LD0:%.*]] = load <4 x double>, ptr [[P0]], align 32
; AVX-NEXT:    [[LD1:%.*]] = load <4 x double>, ptr [[P1]], align 32
; AVX-NEXT:    [[BCST0:%.*]] = shufflevector <4 x double> [[LD0]], <4 x double> undef, <4 x i32> zeroinitializer
; AVX-NEXT:    [[BCST1:%.*]] = shufflevector <4 x double> [[LD1]], <4 x double> undef, <4 x i32> zeroinitializer
; AVX-NEXT:    [[BLEND:%.*]] = shufflevector <4 x double> [[BCST0]], <4 x double> [[BCST1]], <4 x i32> <i32 0, i32 5, i32 6, i32 3>
; AVX-NEXT:    ret <4 x double> [[BLEND]]
;
  %ld0 = load <4 x double>, ptr %p0, align 32
  %ld1 = load <4 x double>, ptr %p1, align 32
  %bcst0 = shufflevector <4 x double> %ld0, <4 x double> undef, <4 x i32> zeroinitializer
  %bcst1 = shufflevector <4 x double> %ld1, <4 x double> undef, <4 x i32> zeroinitializer
  %blend = shufflevector <4 x double> %bcst0, <4 x double> %bcst1, <4 x i32> <i32 0, i32 5, i32 6, i32 3>
  ret <4 x double> %blend
}

define <2 x float> @PR86068(<2 x float> %a0, <2 x float> %a1) {
; CHECK-LABEL: define <2 x float> @PR86068(
; CHECK-SAME: <2 x float> [[A0:%.*]], <2 x float> [[A1:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:    [[S2:%.*]] = shufflevector <2 x float> [[A1]], <2 x float> [[A0]], <2 x i32> <i32 1, i32 3>
; CHECK-NEXT:    ret <2 x float> [[S2]]
;
  %s1 = shufflevector <2 x float> %a1, <2 x float> poison, <2 x i32> <i32 1, i32 poison>
  %s2 = shufflevector <2 x float> %s1, <2 x float> %a0, <2 x i32> <i32 0, i32 3>
  ret <2 x float> %s2
}
