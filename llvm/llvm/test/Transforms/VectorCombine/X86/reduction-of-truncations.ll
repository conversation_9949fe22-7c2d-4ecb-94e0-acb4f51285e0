; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt < %s -S --passes=vector-combine -mtriple=x86_64-- -mcpu=x86-64    | FileCheck %s --check-prefixes=CHECK,X64
; RUN: opt < %s -S --passes=vector-combine -mtriple=x86_64-- -mcpu=x86-64-v2 | FileCheck %s --check-prefixes=CHECK,X64
; RUN: opt < %s -S --passes=vector-combine -mtriple=x86_64-- -mcpu=x86-64-v3 | FileCheck %s --check-prefixes=CHECK,X64
; RUN: opt < %s -S --passes=vector-combine -mtriple=x86_64-- -mcpu=x86-64-v4 | FileCheck %s --check-prefixes=CHECK,AVX512

;
; Fold reduce(trunc(X)) -> trunc(reduce(X)) if more cost efficient
;

; Cheap AVX512 v8i64 -> v8i32 truncation
define i32 @reduce_add_trunc_v8i64_i32(<8 x i64> %a0)  {
; X64-LABEL: @reduce_add_trunc_v8i64_i32(
; X64-NEXT:    [[TMP1:%.*]] = call i64 @llvm.vector.reduce.add.v8i64(<8 x i64> [[A0:%.*]])
; X64-NEXT:    [[RED:%.*]] = trunc i64 [[TMP1]] to i32
; X64-NEXT:    ret i32 [[RED]]
;
; AVX512-LABEL: @reduce_add_trunc_v8i64_i32(
; AVX512-NEXT:    [[TR:%.*]] = trunc <8 x i64> [[A0:%.*]] to <8 x i32>
; AVX512-NEXT:    [[RED:%.*]] = tail call i32 @llvm.vector.reduce.add.v8i32(<8 x i32> [[TR]])
; AVX512-NEXT:    ret i32 [[RED]]
;
  %tr = trunc <8 x i64> %a0 to <8 x i32>
  %red = tail call i32 @llvm.vector.reduce.add.v8i32(<8 x i32> %tr)
  ret i32 %red
}
declare i32 @llvm.vector.reduce.add.v8i32(<8 x i32>)

; No legal vXi8 multiplication so vXi16 is always cheaper
define i8 @reduce_mul_trunc_v16i16_i8(<16 x i16> %a0)  {
; CHECK-LABEL: @reduce_mul_trunc_v16i16_i8(
; CHECK-NEXT:    [[TMP1:%.*]] = call i16 @llvm.vector.reduce.mul.v16i16(<16 x i16> [[A0:%.*]])
; CHECK-NEXT:    [[RED:%.*]] = trunc i16 [[TMP1]] to i8
; CHECK-NEXT:    ret i8 [[RED]]
;
  %tr = trunc <16 x i16> %a0 to <16 x i8>
  %red = tail call i8 @llvm.vector.reduce.mul.v16i8(<16 x i8> %tr)
  ret i8 %red
}
declare i8 @llvm.vector.reduce.mul.v16i8(<16 x i8>)

define i8 @reduce_or_trunc_v8i32_i8(<8 x i32> %a0)  {
; CHECK-LABEL: @reduce_or_trunc_v8i32_i8(
; CHECK-NEXT:    [[TMP1:%.*]] = call i32 @llvm.vector.reduce.or.v8i32(<8 x i32> [[A0:%.*]])
; CHECK-NEXT:    [[RED:%.*]] = trunc i32 [[TMP1]] to i8
; CHECK-NEXT:    ret i8 [[RED]]
;
  %tr = trunc <8 x i32> %a0 to <8 x i8>
  %red = tail call i8 @llvm.vector.reduce.or.v8i32(<8 x i8> %tr)
  ret i8 %red
}
declare i8 @llvm.vector.reduce.or.v8i8(<8 x i8>)

define i8 @reduce_xor_trunc_v16i64_i8(<16 x i64> %a0)  {
; CHECK-LABEL: @reduce_xor_trunc_v16i64_i8(
; CHECK-NEXT:    [[TMP1:%.*]] = call i64 @llvm.vector.reduce.xor.v16i64(<16 x i64> [[A0:%.*]])
; CHECK-NEXT:    [[RED:%.*]] = trunc i64 [[TMP1]] to i8
; CHECK-NEXT:    ret i8 [[RED]]
;
  %tr = trunc <16 x i64> %a0 to <16 x i8>
  %red = tail call i8 @llvm.vector.reduce.xor.v16i8(<16 x i8> %tr)
  ret i8 %red
}
declare i8 @llvm.vector.reduce.xor.v16i8(<16 x i8>)

; Truncation source has other uses - OK to truncate reduction
define i16 @reduce_and_trunc_v16i64_i16(<16 x i64> %a0)  {
; CHECK-LABEL: @reduce_and_trunc_v16i64_i16(
; CHECK-NEXT:    [[TMP1:%.*]] = call i64 @llvm.vector.reduce.and.v16i64(<16 x i64> [[A0:%.*]])
; CHECK-NEXT:    [[RED:%.*]] = trunc i64 [[TMP1]] to i16
; CHECK-NEXT:    call void @use_v16i64(<16 x i64> [[A0]])
; CHECK-NEXT:    ret i16 [[RED]]
;
  %tr = trunc <16 x i64> %a0 to <16 x i16>
  %red = tail call i16 @llvm.vector.reduce.and.v16i16(<16 x i16> %tr)
  call void @use_v16i64(<16 x i64> %a0)
  ret i16 %red
}
declare i16 @llvm.vector.reduce.and.v16i16(<16 x i16>)

; Negative Test: vXi16 multiply is much cheaper than vXi64
define i16 @reduce_mul_trunc_v8i64_i16(<8 x i64> %a0)  {
; CHECK-LABEL: @reduce_mul_trunc_v8i64_i16(
; CHECK-NEXT:    [[TR:%.*]] = trunc <8 x i64> [[A0:%.*]] to <8 x i16>
; CHECK-NEXT:    [[RED:%.*]] = tail call i16 @llvm.vector.reduce.mul.v8i16(<8 x i16> [[TR]])
; CHECK-NEXT:    ret i16 [[RED]]
;
  %tr = trunc <8 x i64> %a0 to <8 x i16>
  %red = tail call i16 @llvm.vector.reduce.mul.v8i16(<8 x i16> %tr)
  ret i16 %red
}
declare i16 @llvm.vector.reduce.mul.v8i16(<8 x i16>)

; Negative Test: min/max reductions can't use pre-truncated types.
define i8 @reduce_smin_trunc_v16i16_i8(<16 x i16> %a0)  {
; CHECK-LABEL: @reduce_smin_trunc_v16i16_i8(
; CHECK-NEXT:    [[TR:%.*]] = trunc <16 x i16> [[A0:%.*]] to <16 x i8>
; CHECK-NEXT:    [[RED:%.*]] = tail call i8 @llvm.vector.reduce.smin.v16i8(<16 x i8> [[TR]])
; CHECK-NEXT:    ret i8 [[RED]]
;
  %tr = trunc <16 x i16> %a0 to <16 x i8>
  %red = tail call i8 @llvm.vector.reduce.smin.v16i8(<16 x i8> %tr)
  ret i8 %red
}
declare i8 @llvm.vector.reduce.smin.v16i8(<16 x i8>)

; Negative Test: Truncation has other uses.
define i16 @reduce_and_trunc_v16i64_i16_multiuse(<16 x i64> %a0)  {
; CHECK-LABEL: @reduce_and_trunc_v16i64_i16_multiuse(
; CHECK-NEXT:    [[TR:%.*]] = trunc <16 x i64> [[A0:%.*]] to <16 x i16>
; CHECK-NEXT:    [[RED:%.*]] = tail call i16 @llvm.vector.reduce.and.v16i16(<16 x i16> [[TR]])
; CHECK-NEXT:    call void @use_v16i16(<16 x i16> [[TR]])
; CHECK-NEXT:    ret i16 [[RED]]
;
  %tr = trunc <16 x i64> %a0 to <16 x i16>
  %red = tail call i16 @llvm.vector.reduce.and.v16i16(<16 x i16> %tr)
  call void @use_v16i16(<16 x i16> %tr)
  ret i16 %red
}

declare void @use_v16i64(<16 x i64>)
declare void @use_v16i16(<16 x i16>)

