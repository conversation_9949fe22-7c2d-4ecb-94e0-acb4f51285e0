; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -S -mtriple=riscv64 -mattr=+v,+m,+zvfh %s -passes=vector-combine | FileCheck %s --check-prefixes=ALL,VEC-COMBINE,VEC-COMBINE-64
; RUN: opt -S -mtriple=riscv64 -mattr=+v,+m,+zvfh %s | FileCheck %s --check-prefixes=ALL,NO-VEC-COMBINE
; RUN: opt -S -mtriple=riscv32 -mattr=+v,+m,+zvfh %s -passes=vector-combine | FileCheck %s --check-prefixes=ALL,VEC-COMBINE,VEC-COMBINE-32
; RUN: opt -S -mtriple=riscv32 -mattr=+v,+m,+zvfh %s | FileCheck %s --check-prefixes=ALL,NO-VEC-COMBINE

declare <vscale x 1 x i64> @llvm.vp.add.nxv1i64(<vscale x 1 x i64>, <vscale x 1 x i64>, <vscale x 1 x i1>, i32)
declare <vscale x 1 x i64> @llvm.vp.sub.nxv1i64(<vscale x 1 x i64>, <vscale x 1 x i64>, <vscale x 1 x i1>, i32)
declare <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64>, <vscale x 1 x i64>, <vscale x 1 x i1>, i32)
declare <vscale x 1 x i64> @llvm.vp.sdiv.nxv1i64(<vscale x 1 x i64>, <vscale x 1 x i64>, <vscale x 1 x i1>, i32)
declare <vscale x 1 x i64> @llvm.vp.udiv.nxv1i64(<vscale x 1 x i64>, <vscale x 1 x i64>, <vscale x 1 x i1>, i32)
declare <vscale x 1 x i64> @llvm.vp.srem.nxv1i64(<vscale x 1 x i64>, <vscale x 1 x i64>, <vscale x 1 x i1>, i32)
declare <vscale x 1 x i64> @llvm.vp.urem.nxv1i64(<vscale x 1 x i64>, <vscale x 1 x i64>, <vscale x 1 x i1>, i32)
declare <vscale x 1 x i64> @llvm.vp.ashr.nxv1i64(<vscale x 1 x i64>, <vscale x 1 x i64>, <vscale x 1 x i1>, i32)
declare <vscale x 1 x i64> @llvm.vp.lshr.nxv1i64(<vscale x 1 x i64>, <vscale x 1 x i64>, <vscale x 1 x i1>, i32)
declare <vscale x 1 x i64> @llvm.vp.shl.nxv1i64(<vscale x 1 x i64>, <vscale x 1 x i64>, <vscale x 1 x i1>, i32)
declare <vscale x 1 x i64> @llvm.vp.or.nxv1i64(<vscale x 1 x i64>, <vscale x 1 x i64>, <vscale x 1 x i1>, i32)
declare <vscale x 1 x i64> @llvm.vp.and.nxv1i64(<vscale x 1 x i64>, <vscale x 1 x i64>, <vscale x 1 x i1>, i32)
declare <vscale x 1 x i64> @llvm.vp.xor.nxv1i64(<vscale x 1 x i64>, <vscale x 1 x i64>, <vscale x 1 x i1>, i32)
declare <vscale x 1 x i64> @llvm.vp.smin.nxv1i64(<vscale x 1 x i64>, <vscale x 1 x i64>, <vscale x 1 x i1>, i32)
declare <vscale x 1 x i64> @llvm.vp.smax.nxv1i64(<vscale x 1 x i64>, <vscale x 1 x i64>, <vscale x 1 x i1>, i32)
declare <vscale x 1 x i64> @llvm.vp.umin.nxv1i64(<vscale x 1 x i64>, <vscale x 1 x i64>, <vscale x 1 x i1>, i32)
declare <vscale x 1 x i64> @llvm.vp.umax.nxv1i64(<vscale x 1 x i64>, <vscale x 1 x i64>, <vscale x 1 x i1>, i32)
declare <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float>, <vscale x 1 x float>, <vscale x 1 x i1>, i32)
declare <vscale x 1 x float> @llvm.vp.fsub.nxv1f32(<vscale x 1 x float>, <vscale x 1 x float>, <vscale x 1 x i1>, i32)
declare <vscale x 1 x float> @llvm.vp.fdiv.nxv1f32(<vscale x 1 x float>, <vscale x 1 x float>, <vscale x 1 x i1>, i32)
declare <vscale x 1 x float> @llvm.vp.frem.nxv1f32(<vscale x 1 x float>, <vscale x 1 x float>, <vscale x 1 x i1>, i32)
declare <vscale x 1 x float> @llvm.vp.copysign.nxv1f32(<vscale x 1 x float>, <vscale x 1 x float>, <vscale x 1 x i1>, i32)
declare <vscale x 1 x float> @llvm.vp.minnum.nxv1f32(<vscale x 1 x float>, <vscale x 1 x float>, <vscale x 1 x i1>, i32)
declare <vscale x 1 x float> @llvm.vp.maxnum.nxv1f32(<vscale x 1 x float>, <vscale x 1 x float>, <vscale x 1 x i1>, i32)
declare <vscale x 8 x i8> @llvm.vp.add.nxv8i8(<vscale x 8 x i8>, <vscale x 8 x i8>, <vscale x 8 x i1>, i32)
declare <vscale x 8 x i8> @llvm.vp.mul.nxv8i8(<vscale x 8 x i8>, <vscale x 8 x i8>, <vscale x 8 x i1>, i32)
declare <vscale x 8 x half> @llvm.vp.fadd.nxv8f16(<vscale x 8 x half>, <vscale x 8 x half>, <vscale x 8 x i1>, i32)
declare <1 x i64> @llvm.vp.add.v1i64(<1 x i64>, <1 x i64>, <1 x i1>, i32)
declare <4 x i64> @llvm.vp.add.v4i64(<4 x i64>, <4 x i64>, <4 x i1>, i32)
declare <1 x i64> @llvm.vp.mul.v1i64(<1 x i64>, <1 x i64>, <1 x i1>, i32)
declare <4 x i64> @llvm.vp.mul.v4i64(<4 x i64>, <4 x i64>, <4 x i1>, i32)

define <vscale x 1 x i64> @add_nxv1i64_allonesmask(<vscale x 1 x i64> %x, i64 %y, i32 zeroext %evl) {
; VEC-COMBINE-64-LABEL: @add_nxv1i64_allonesmask(
; VEC-COMBINE-64-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-64-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-64-NEXT:    [[TMP1:%.*]] = add i64 [[Y:%.*]], 42
; VEC-COMBINE-64-NEXT:    [[DOTSPLATINSERT:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[TMP1]], i64 0
; VEC-COMBINE-64-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[DOTSPLATINSERT]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-64-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP2]], <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-64-NEXT:    ret <vscale x 1 x i64> [[TMP3]]
;
; NO-VEC-COMBINE-LABEL: @add_nxv1i64_allonesmask(
; NO-VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; NO-VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; NO-VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.add.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; NO-VEC-COMBINE-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; NO-VEC-COMBINE-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
; VEC-COMBINE-32-LABEL: @add_nxv1i64_allonesmask(
; VEC-COMBINE-32-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-32-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-32-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; VEC-COMBINE-32-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-32-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.add.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-32-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; VEC-COMBINE-32-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
  %splat = insertelement <vscale x 1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 1 x i1> %splat, <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
  %1 = insertelement <vscale x 1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <vscale x 1 x i64> %1, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x i64> @llvm.vp.add.nxv1i64(<vscale x 1 x i64> %2, <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %x, <vscale x 1 x i64> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %4
}

define <vscale x 1 x i64> @add_nxv1i64_anymask(<vscale x 1 x i64> %x, i64 %y, <vscale x 1 x i1> %mask, i32 zeroext %evl) {
; ALL-LABEL: @add_nxv1i64_anymask(
; ALL-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; ALL-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.add.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK:%.*]], i32 [[EVL:%.*]])
; ALL-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; ALL-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
  %1 = insertelement <vscale x 1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <vscale x 1 x i64> %1, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x i64> @llvm.vp.add.nxv1i64(<vscale x 1 x i64> %2, <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %x, <vscale x 1 x i64> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %4
}

define <vscale x 1 x i64> @sub_nxv1i64_allonesmask(<vscale x 1 x i64> %x, i64 %y, i32 zeroext %evl) {
; VEC-COMBINE-64-LABEL: @sub_nxv1i64_allonesmask(
; VEC-COMBINE-64-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-64-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-64-NEXT:    [[TMP1:%.*]] = sub i64 [[Y:%.*]], 42
; VEC-COMBINE-64-NEXT:    [[DOTSPLATINSERT:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[TMP1]], i64 0
; VEC-COMBINE-64-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[DOTSPLATINSERT]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-64-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP2]], <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-64-NEXT:    ret <vscale x 1 x i64> [[TMP3]]
;
; NO-VEC-COMBINE-LABEL: @sub_nxv1i64_allonesmask(
; NO-VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; NO-VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; NO-VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.sub.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; NO-VEC-COMBINE-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; NO-VEC-COMBINE-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
; VEC-COMBINE-32-LABEL: @sub_nxv1i64_allonesmask(
; VEC-COMBINE-32-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-32-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-32-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; VEC-COMBINE-32-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-32-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.sub.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-32-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; VEC-COMBINE-32-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
  %splat = insertelement <vscale x 1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 1 x i1> %splat, <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
  %1 = insertelement <vscale x 1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <vscale x 1 x i64> %1, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x i64> @llvm.vp.sub.nxv1i64(<vscale x 1 x i64> %2, <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %x, <vscale x 1 x i64> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %4
}

define <vscale x 1 x i64> @sub_nxv1i64_anymask(<vscale x 1 x i64> %x, i64 %y, <vscale x 1 x i1> %mask, i32 zeroext %evl) {
; ALL-LABEL: @sub_nxv1i64_anymask(
; ALL-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; ALL-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.sub.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK:%.*]], i32 [[EVL:%.*]])
; ALL-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; ALL-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
  %1 = insertelement <vscale x 1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <vscale x 1 x i64> %1, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x i64> @llvm.vp.sub.nxv1i64(<vscale x 1 x i64> %2, <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %x, <vscale x 1 x i64> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %4
}

define <vscale x 1 x i64> @mul_nxv1i64_allonesmask(<vscale x 1 x i64> %x, i64 %y, i32 zeroext %evl) {
; VEC-COMBINE-64-LABEL: @mul_nxv1i64_allonesmask(
; VEC-COMBINE-64-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-64-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-64-NEXT:    [[TMP1:%.*]] = mul i64 [[Y:%.*]], 42
; VEC-COMBINE-64-NEXT:    [[DOTSPLATINSERT:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[TMP1]], i64 0
; VEC-COMBINE-64-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[DOTSPLATINSERT]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-64-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP2]], <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-64-NEXT:    ret <vscale x 1 x i64> [[TMP3]]
;
; NO-VEC-COMBINE-LABEL: @mul_nxv1i64_allonesmask(
; NO-VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; NO-VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; NO-VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; NO-VEC-COMBINE-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; NO-VEC-COMBINE-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
; VEC-COMBINE-32-LABEL: @mul_nxv1i64_allonesmask(
; VEC-COMBINE-32-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-32-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-32-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; VEC-COMBINE-32-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-32-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-32-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; VEC-COMBINE-32-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
  %splat = insertelement <vscale x 1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 1 x i1> %splat, <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
  %1 = insertelement <vscale x 1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <vscale x 1 x i64> %1, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %2, <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %x, <vscale x 1 x i64> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %4
}

define <vscale x 1 x i64> @mul_nxv1i64_anymask(<vscale x 1 x i64> %x, i64 %y, <vscale x 1 x i1> %mask, i32 zeroext %evl) {
; ALL-LABEL: @mul_nxv1i64_anymask(
; ALL-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; ALL-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK:%.*]], i32 [[EVL:%.*]])
; ALL-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; ALL-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
  %1 = insertelement <vscale x 1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <vscale x 1 x i64> %1, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %2, <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %x, <vscale x 1 x i64> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %4
}

define <vscale x 1 x i64> @sdiv_nxv1i64_allonesmask(<vscale x 1 x i64> %x, i64 %y, i32 zeroext %evl) {
; VEC-COMBINE-64-LABEL: @sdiv_nxv1i64_allonesmask(
; VEC-COMBINE-64-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-64-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-64-NEXT:    [[TMP1:%.*]] = sdiv i64 [[Y:%.*]], 42
; VEC-COMBINE-64-NEXT:    [[DOTSPLATINSERT:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[TMP1]], i64 0
; VEC-COMBINE-64-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[DOTSPLATINSERT]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-64-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP2]], <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-64-NEXT:    ret <vscale x 1 x i64> [[TMP3]]
;
; NO-VEC-COMBINE-LABEL: @sdiv_nxv1i64_allonesmask(
; NO-VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; NO-VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; NO-VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.sdiv.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; NO-VEC-COMBINE-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; NO-VEC-COMBINE-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
; VEC-COMBINE-32-LABEL: @sdiv_nxv1i64_allonesmask(
; VEC-COMBINE-32-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-32-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-32-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; VEC-COMBINE-32-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-32-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.sdiv.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-32-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; VEC-COMBINE-32-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
  %splat = insertelement <vscale x 1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 1 x i1> %splat, <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
  %1 = insertelement <vscale x 1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <vscale x 1 x i64> %1, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x i64> @llvm.vp.sdiv.nxv1i64(<vscale x 1 x i64> %2, <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %x, <vscale x 1 x i64> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %4
}

define <vscale x 1 x i64> @sdiv_nxv1i64_anymask(<vscale x 1 x i64> %x, i64 %y, <vscale x 1 x i1> %mask, i32 zeroext %evl) {
; ALL-LABEL: @sdiv_nxv1i64_anymask(
; ALL-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; ALL-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.sdiv.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK:%.*]], i32 [[EVL:%.*]])
; ALL-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; ALL-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
  %1 = insertelement <vscale x 1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <vscale x 1 x i64> %1, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x i64> @llvm.vp.sdiv.nxv1i64(<vscale x 1 x i64> %2, <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %x, <vscale x 1 x i64> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %4
}

define <vscale x 1 x i64> @sdiv_nxv1i64_unspeculatable(i64 %x, i64 %y, i32 zeroext %evl) {
; ALL-LABEL: @sdiv_nxv1i64_unspeculatable(
; ALL-NEXT:    [[MASK_HEAD:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; ALL-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[MASK_HEAD]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[X_HEAD:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[X:%.*]], i64 0
; ALL-NEXT:    [[X_SPLAT:%.*]] = shufflevector <vscale x 1 x i64> [[X_HEAD]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[Y_HEAD:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[X]], i64 0
; ALL-NEXT:    [[Y_SPLAT:%.*]] = shufflevector <vscale x 1 x i64> [[Y_HEAD]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[RES:%.*]] = call <vscale x 1 x i64> @llvm.vp.sdiv.nxv1i64(<vscale x 1 x i64> [[X_SPLAT]], <vscale x 1 x i64> [[Y_SPLAT]], <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; ALL-NEXT:    ret <vscale x 1 x i64> [[RES]]
;
  %mask.head = insertelement <vscale x 1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 1 x i1> %mask.head, <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
  %x.head = insertelement <vscale x 1 x i64> poison, i64 %x, i64 0
  %x.splat = shufflevector <vscale x 1 x i64> %x.head, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %y.head = insertelement <vscale x 1 x i64> poison, i64 %x, i64 0
  %y.splat = shufflevector <vscale x 1 x i64> %y.head, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %res = call <vscale x 1 x i64> @llvm.vp.sdiv.nxv1i64(<vscale x 1 x i64> %x.splat, <vscale x 1 x i64> %y.splat, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %res
}

define <vscale x 1 x i64> @udiv_nxv1i64_allonesmask(<vscale x 1 x i64> %x, i64 %y, i32 zeroext %evl) {
; VEC-COMBINE-64-LABEL: @udiv_nxv1i64_allonesmask(
; VEC-COMBINE-64-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-64-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-64-NEXT:    [[TMP1:%.*]] = udiv i64 [[Y:%.*]], 42
; VEC-COMBINE-64-NEXT:    [[DOTSPLATINSERT:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[TMP1]], i64 0
; VEC-COMBINE-64-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[DOTSPLATINSERT]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-64-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP2]], <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-64-NEXT:    ret <vscale x 1 x i64> [[TMP3]]
;
; NO-VEC-COMBINE-LABEL: @udiv_nxv1i64_allonesmask(
; NO-VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; NO-VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; NO-VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.udiv.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; NO-VEC-COMBINE-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; NO-VEC-COMBINE-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
; VEC-COMBINE-32-LABEL: @udiv_nxv1i64_allonesmask(
; VEC-COMBINE-32-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-32-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-32-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; VEC-COMBINE-32-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-32-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.udiv.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-32-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; VEC-COMBINE-32-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
  %splat = insertelement <vscale x 1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 1 x i1> %splat, <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
  %1 = insertelement <vscale x 1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <vscale x 1 x i64> %1, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x i64> @llvm.vp.udiv.nxv1i64(<vscale x 1 x i64> %2, <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %x, <vscale x 1 x i64> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %4
}

define <vscale x 1 x i64> @udiv_nxv1i64_anymask(<vscale x 1 x i64> %x, i64 %y, <vscale x 1 x i1> %mask, i32 zeroext %evl) {
; ALL-LABEL: @udiv_nxv1i64_anymask(
; ALL-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; ALL-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.udiv.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK:%.*]], i32 [[EVL:%.*]])
; ALL-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; ALL-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
  %1 = insertelement <vscale x 1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <vscale x 1 x i64> %1, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x i64> @llvm.vp.udiv.nxv1i64(<vscale x 1 x i64> %2, <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %x, <vscale x 1 x i64> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %4
}

define <vscale x 1 x i64> @udiv_nxv1i64_unspeculatable(i64 %x, i64 %y, i32 zeroext %evl) {
; ALL-LABEL: @udiv_nxv1i64_unspeculatable(
; ALL-NEXT:    [[MASK_HEAD:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; ALL-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[MASK_HEAD]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[X_HEAD:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[X:%.*]], i64 0
; ALL-NEXT:    [[X_SPLAT:%.*]] = shufflevector <vscale x 1 x i64> [[X_HEAD]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[Y_HEAD:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[X]], i64 0
; ALL-NEXT:    [[Y_SPLAT:%.*]] = shufflevector <vscale x 1 x i64> [[Y_HEAD]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[RES:%.*]] = call <vscale x 1 x i64> @llvm.vp.udiv.nxv1i64(<vscale x 1 x i64> [[X_SPLAT]], <vscale x 1 x i64> [[Y_SPLAT]], <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; ALL-NEXT:    ret <vscale x 1 x i64> [[RES]]
;
  %mask.head = insertelement <vscale x 1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 1 x i1> %mask.head, <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
  %x.head = insertelement <vscale x 1 x i64> poison, i64 %x, i64 0
  %x.splat = shufflevector <vscale x 1 x i64> %x.head, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %y.head = insertelement <vscale x 1 x i64> poison, i64 %x, i64 0
  %y.splat = shufflevector <vscale x 1 x i64> %y.head, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %res = call <vscale x 1 x i64> @llvm.vp.udiv.nxv1i64(<vscale x 1 x i64> %x.splat, <vscale x 1 x i64> %y.splat, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %res
}

define <vscale x 1 x i64> @srem_nxv1i64_allonesmask(<vscale x 1 x i64> %x, i64 %y, i32 zeroext %evl) {
; VEC-COMBINE-64-LABEL: @srem_nxv1i64_allonesmask(
; VEC-COMBINE-64-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-64-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-64-NEXT:    [[TMP1:%.*]] = srem i64 [[Y:%.*]], 42
; VEC-COMBINE-64-NEXT:    [[DOTSPLATINSERT:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[TMP1]], i64 0
; VEC-COMBINE-64-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[DOTSPLATINSERT]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-64-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP2]], <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-64-NEXT:    ret <vscale x 1 x i64> [[TMP3]]
;
; NO-VEC-COMBINE-LABEL: @srem_nxv1i64_allonesmask(
; NO-VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; NO-VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; NO-VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.srem.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; NO-VEC-COMBINE-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; NO-VEC-COMBINE-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
; VEC-COMBINE-32-LABEL: @srem_nxv1i64_allonesmask(
; VEC-COMBINE-32-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-32-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-32-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; VEC-COMBINE-32-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-32-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.srem.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-32-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; VEC-COMBINE-32-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
  %splat = insertelement <vscale x 1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 1 x i1> %splat, <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
  %1 = insertelement <vscale x 1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <vscale x 1 x i64> %1, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x i64> @llvm.vp.srem.nxv1i64(<vscale x 1 x i64> %2, <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %x, <vscale x 1 x i64> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %4
}

define <vscale x 1 x i64> @srem_nxv1i64_anymask(<vscale x 1 x i64> %x, i64 %y, <vscale x 1 x i1> %mask, i32 zeroext %evl) {
; ALL-LABEL: @srem_nxv1i64_anymask(
; ALL-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; ALL-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.srem.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK:%.*]], i32 [[EVL:%.*]])
; ALL-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; ALL-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
  %1 = insertelement <vscale x 1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <vscale x 1 x i64> %1, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x i64> @llvm.vp.srem.nxv1i64(<vscale x 1 x i64> %2, <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %x, <vscale x 1 x i64> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %4
}

define <vscale x 1 x i64> @srem_nxv1i64_unspeculatable(i64 %x, i64 %y, i32 zeroext %evl) {
; ALL-LABEL: @srem_nxv1i64_unspeculatable(
; ALL-NEXT:    [[MASK_HEAD:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; ALL-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[MASK_HEAD]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[X_HEAD:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[X:%.*]], i64 0
; ALL-NEXT:    [[X_SPLAT:%.*]] = shufflevector <vscale x 1 x i64> [[X_HEAD]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[Y_HEAD:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[X]], i64 0
; ALL-NEXT:    [[Y_SPLAT:%.*]] = shufflevector <vscale x 1 x i64> [[Y_HEAD]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[RES:%.*]] = call <vscale x 1 x i64> @llvm.vp.srem.nxv1i64(<vscale x 1 x i64> [[X_SPLAT]], <vscale x 1 x i64> [[Y_SPLAT]], <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; ALL-NEXT:    ret <vscale x 1 x i64> [[RES]]
;
  %mask.head = insertelement <vscale x 1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 1 x i1> %mask.head, <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
  %x.head = insertelement <vscale x 1 x i64> poison, i64 %x, i64 0
  %x.splat = shufflevector <vscale x 1 x i64> %x.head, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %y.head = insertelement <vscale x 1 x i64> poison, i64 %x, i64 0
  %y.splat = shufflevector <vscale x 1 x i64> %y.head, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %res = call <vscale x 1 x i64> @llvm.vp.srem.nxv1i64(<vscale x 1 x i64> %x.splat, <vscale x 1 x i64> %y.splat, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %res
}

define <vscale x 1 x i64> @urem_nxv1i64_allonesmask(<vscale x 1 x i64> %x, i64 %y, i32 zeroext %evl) {
; VEC-COMBINE-64-LABEL: @urem_nxv1i64_allonesmask(
; VEC-COMBINE-64-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-64-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-64-NEXT:    [[TMP1:%.*]] = urem i64 [[Y:%.*]], 42
; VEC-COMBINE-64-NEXT:    [[DOTSPLATINSERT:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[TMP1]], i64 0
; VEC-COMBINE-64-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[DOTSPLATINSERT]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-64-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP2]], <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-64-NEXT:    ret <vscale x 1 x i64> [[TMP3]]
;
; NO-VEC-COMBINE-LABEL: @urem_nxv1i64_allonesmask(
; NO-VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; NO-VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; NO-VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.urem.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; NO-VEC-COMBINE-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; NO-VEC-COMBINE-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
; VEC-COMBINE-32-LABEL: @urem_nxv1i64_allonesmask(
; VEC-COMBINE-32-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-32-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-32-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; VEC-COMBINE-32-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-32-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.urem.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-32-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; VEC-COMBINE-32-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
  %splat = insertelement <vscale x 1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 1 x i1> %splat, <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
  %1 = insertelement <vscale x 1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <vscale x 1 x i64> %1, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x i64> @llvm.vp.urem.nxv1i64(<vscale x 1 x i64> %2, <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %x, <vscale x 1 x i64> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %4
}

define <vscale x 1 x i64> @urem_nxv1i64_anymask(<vscale x 1 x i64> %x, i64 %y, <vscale x 1 x i1> %mask, i32 zeroext %evl) {
; ALL-LABEL: @urem_nxv1i64_anymask(
; ALL-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; ALL-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.urem.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK:%.*]], i32 [[EVL:%.*]])
; ALL-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; ALL-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
  %1 = insertelement <vscale x 1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <vscale x 1 x i64> %1, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x i64> @llvm.vp.urem.nxv1i64(<vscale x 1 x i64> %2, <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %x, <vscale x 1 x i64> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %4
}

define <vscale x 1 x i64> @urem_nxv1i64_unspeculatable(i64 %x, i64 %y, i32 zeroext %evl) {
; ALL-LABEL: @urem_nxv1i64_unspeculatable(
; ALL-NEXT:    [[MASK_HEAD:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; ALL-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[MASK_HEAD]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[X_HEAD:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[X:%.*]], i64 0
; ALL-NEXT:    [[X_SPLAT:%.*]] = shufflevector <vscale x 1 x i64> [[X_HEAD]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[Y_HEAD:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[X]], i64 0
; ALL-NEXT:    [[Y_SPLAT:%.*]] = shufflevector <vscale x 1 x i64> [[Y_HEAD]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[RES:%.*]] = call <vscale x 1 x i64> @llvm.vp.urem.nxv1i64(<vscale x 1 x i64> [[X_SPLAT]], <vscale x 1 x i64> [[Y_SPLAT]], <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; ALL-NEXT:    ret <vscale x 1 x i64> [[RES]]
;
  %mask.head = insertelement <vscale x 1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 1 x i1> %mask.head, <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
  %x.head = insertelement <vscale x 1 x i64> poison, i64 %x, i64 0
  %x.splat = shufflevector <vscale x 1 x i64> %x.head, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %y.head = insertelement <vscale x 1 x i64> poison, i64 %x, i64 0
  %y.splat = shufflevector <vscale x 1 x i64> %y.head, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %res = call <vscale x 1 x i64> @llvm.vp.urem.nxv1i64(<vscale x 1 x i64> %x.splat, <vscale x 1 x i64> %y.splat, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %res
}

define <vscale x 1 x i64> @sdiv_nxv1i64_allonesmask_knownvl(i64 %x, i64 %y) {
; VEC-COMBINE-LABEL: @sdiv_nxv1i64_allonesmask_knownvl(
; VEC-COMBINE-NEXT:    [[TMP1:%.*]] = sdiv i64 [[X:%.*]], [[X]]
; VEC-COMBINE-NEXT:    [[DOTSPLATINSERT:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[TMP1]], i64 0
; VEC-COMBINE-NEXT:    [[RES:%.*]] = shufflevector <vscale x 1 x i64> [[DOTSPLATINSERT]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-NEXT:    ret <vscale x 1 x i64> [[RES]]
;
; NO-VEC-COMBINE-LABEL: @sdiv_nxv1i64_allonesmask_knownvl(
; NO-VEC-COMBINE-NEXT:    [[MASK_HEAD:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; NO-VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[MASK_HEAD]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[X_HEAD:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[X:%.*]], i64 0
; NO-VEC-COMBINE-NEXT:    [[X_SPLAT:%.*]] = shufflevector <vscale x 1 x i64> [[X_HEAD]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[Y_HEAD:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[X]], i64 0
; NO-VEC-COMBINE-NEXT:    [[Y_SPLAT:%.*]] = shufflevector <vscale x 1 x i64> [[Y_HEAD]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[RES:%.*]] = call <vscale x 1 x i64> @llvm.vp.sdiv.nxv1i64(<vscale x 1 x i64> [[X_SPLAT]], <vscale x 1 x i64> [[Y_SPLAT]], <vscale x 1 x i1> [[MASK]], i32 4)
; NO-VEC-COMBINE-NEXT:    ret <vscale x 1 x i64> [[RES]]
;
  %mask.head = insertelement <vscale x 1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 1 x i1> %mask.head, <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
  %x.head = insertelement <vscale x 1 x i64> poison, i64 %x, i64 0
  %x.splat = shufflevector <vscale x 1 x i64> %x.head, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %y.head = insertelement <vscale x 1 x i64> poison, i64 %x, i64 0
  %y.splat = shufflevector <vscale x 1 x i64> %y.head, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %res = call <vscale x 1 x i64> @llvm.vp.sdiv.nxv1i64(<vscale x 1 x i64> %x.splat, <vscale x 1 x i64> %y.splat, <vscale x 1 x i1> %mask, i32 4)
  ret <vscale x 1 x i64> %res
}

define <vscale x 1 x i64> @sdiv_nxv1i64_anymask_knownvl(i64 %x, i64 %y, <vscale x 1 x i1> %mask) {
; ALL-LABEL: @sdiv_nxv1i64_anymask_knownvl(
; ALL-NEXT:    [[X_HEAD:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[X:%.*]], i64 0
; ALL-NEXT:    [[X_SPLAT:%.*]] = shufflevector <vscale x 1 x i64> [[X_HEAD]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[Y_HEAD:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[X]], i64 0
; ALL-NEXT:    [[Y_SPLAT:%.*]] = shufflevector <vscale x 1 x i64> [[Y_HEAD]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[RES:%.*]] = call <vscale x 1 x i64> @llvm.vp.sdiv.nxv1i64(<vscale x 1 x i64> [[X_SPLAT]], <vscale x 1 x i64> [[Y_SPLAT]], <vscale x 1 x i1> [[MASK:%.*]], i32 4)
; ALL-NEXT:    ret <vscale x 1 x i64> [[RES]]
;
  %x.head = insertelement <vscale x 1 x i64> poison, i64 %x, i64 0
  %x.splat = shufflevector <vscale x 1 x i64> %x.head, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %y.head = insertelement <vscale x 1 x i64> poison, i64 %x, i64 0
  %y.splat = shufflevector <vscale x 1 x i64> %y.head, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %res = call <vscale x 1 x i64> @llvm.vp.sdiv.nxv1i64(<vscale x 1 x i64> %x.splat, <vscale x 1 x i64> %y.splat, <vscale x 1 x i1> %mask, i32 4)
  ret <vscale x 1 x i64> %res
}

define <vscale x 1 x i64> @udiv_nxv1i64_allonesmask_knownvl(i64 %x, i64 %y) {
; VEC-COMBINE-LABEL: @udiv_nxv1i64_allonesmask_knownvl(
; VEC-COMBINE-NEXT:    [[TMP1:%.*]] = udiv i64 [[X:%.*]], [[X]]
; VEC-COMBINE-NEXT:    [[DOTSPLATINSERT:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[TMP1]], i64 0
; VEC-COMBINE-NEXT:    [[RES:%.*]] = shufflevector <vscale x 1 x i64> [[DOTSPLATINSERT]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-NEXT:    ret <vscale x 1 x i64> [[RES]]
;
; NO-VEC-COMBINE-LABEL: @udiv_nxv1i64_allonesmask_knownvl(
; NO-VEC-COMBINE-NEXT:    [[MASK_HEAD:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; NO-VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[MASK_HEAD]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[X_HEAD:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[X:%.*]], i64 0
; NO-VEC-COMBINE-NEXT:    [[X_SPLAT:%.*]] = shufflevector <vscale x 1 x i64> [[X_HEAD]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[Y_HEAD:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[X]], i64 0
; NO-VEC-COMBINE-NEXT:    [[Y_SPLAT:%.*]] = shufflevector <vscale x 1 x i64> [[Y_HEAD]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[RES:%.*]] = call <vscale x 1 x i64> @llvm.vp.udiv.nxv1i64(<vscale x 1 x i64> [[X_SPLAT]], <vscale x 1 x i64> [[Y_SPLAT]], <vscale x 1 x i1> [[MASK]], i32 4)
; NO-VEC-COMBINE-NEXT:    ret <vscale x 1 x i64> [[RES]]
;
  %mask.head = insertelement <vscale x 1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 1 x i1> %mask.head, <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
  %x.head = insertelement <vscale x 1 x i64> poison, i64 %x, i64 0
  %x.splat = shufflevector <vscale x 1 x i64> %x.head, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %y.head = insertelement <vscale x 1 x i64> poison, i64 %x, i64 0
  %y.splat = shufflevector <vscale x 1 x i64> %y.head, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %res = call <vscale x 1 x i64> @llvm.vp.udiv.nxv1i64(<vscale x 1 x i64> %x.splat, <vscale x 1 x i64> %y.splat, <vscale x 1 x i1> %mask, i32 4)
  ret <vscale x 1 x i64> %res
}

define <vscale x 1 x i64> @udiv_nxv1i64_anymask_knownvl(i64 %x, i64 %y, <vscale x 1 x i1> %mask) {
; ALL-LABEL: @udiv_nxv1i64_anymask_knownvl(
; ALL-NEXT:    [[X_HEAD:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[X:%.*]], i64 0
; ALL-NEXT:    [[X_SPLAT:%.*]] = shufflevector <vscale x 1 x i64> [[X_HEAD]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[Y_HEAD:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[X]], i64 0
; ALL-NEXT:    [[Y_SPLAT:%.*]] = shufflevector <vscale x 1 x i64> [[Y_HEAD]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[RES:%.*]] = call <vscale x 1 x i64> @llvm.vp.udiv.nxv1i64(<vscale x 1 x i64> [[X_SPLAT]], <vscale x 1 x i64> [[Y_SPLAT]], <vscale x 1 x i1> [[MASK:%.*]], i32 4)
; ALL-NEXT:    ret <vscale x 1 x i64> [[RES]]
;
  %x.head = insertelement <vscale x 1 x i64> poison, i64 %x, i64 0
  %x.splat = shufflevector <vscale x 1 x i64> %x.head, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %y.head = insertelement <vscale x 1 x i64> poison, i64 %x, i64 0
  %y.splat = shufflevector <vscale x 1 x i64> %y.head, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %res = call <vscale x 1 x i64> @llvm.vp.udiv.nxv1i64(<vscale x 1 x i64> %x.splat, <vscale x 1 x i64> %y.splat, <vscale x 1 x i1> %mask, i32 4)
  ret <vscale x 1 x i64> %res
}

define <vscale x 1 x i64> @srem_nxv1i64_allonesmask_knownvl(i64 %x, i64 %y) {
; VEC-COMBINE-LABEL: @srem_nxv1i64_allonesmask_knownvl(
; VEC-COMBINE-NEXT:    [[TMP1:%.*]] = srem i64 [[X:%.*]], [[X]]
; VEC-COMBINE-NEXT:    [[DOTSPLATINSERT:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[TMP1]], i64 0
; VEC-COMBINE-NEXT:    [[RES:%.*]] = shufflevector <vscale x 1 x i64> [[DOTSPLATINSERT]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-NEXT:    ret <vscale x 1 x i64> [[RES]]
;
; NO-VEC-COMBINE-LABEL: @srem_nxv1i64_allonesmask_knownvl(
; NO-VEC-COMBINE-NEXT:    [[MASK_HEAD:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; NO-VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[MASK_HEAD]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[X_HEAD:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[X:%.*]], i64 0
; NO-VEC-COMBINE-NEXT:    [[X_SPLAT:%.*]] = shufflevector <vscale x 1 x i64> [[X_HEAD]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[Y_HEAD:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[X]], i64 0
; NO-VEC-COMBINE-NEXT:    [[Y_SPLAT:%.*]] = shufflevector <vscale x 1 x i64> [[Y_HEAD]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[RES:%.*]] = call <vscale x 1 x i64> @llvm.vp.srem.nxv1i64(<vscale x 1 x i64> [[X_SPLAT]], <vscale x 1 x i64> [[Y_SPLAT]], <vscale x 1 x i1> [[MASK]], i32 4)
; NO-VEC-COMBINE-NEXT:    ret <vscale x 1 x i64> [[RES]]
;
  %mask.head = insertelement <vscale x 1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 1 x i1> %mask.head, <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
  %x.head = insertelement <vscale x 1 x i64> poison, i64 %x, i64 0
  %x.splat = shufflevector <vscale x 1 x i64> %x.head, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %y.head = insertelement <vscale x 1 x i64> poison, i64 %x, i64 0
  %y.splat = shufflevector <vscale x 1 x i64> %y.head, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %res = call <vscale x 1 x i64> @llvm.vp.srem.nxv1i64(<vscale x 1 x i64> %x.splat, <vscale x 1 x i64> %y.splat, <vscale x 1 x i1> %mask, i32 4)
  ret <vscale x 1 x i64> %res
}

define <vscale x 1 x i64> @srem_nxv1i64_anymask_knownvl(i64 %x, i64 %y, <vscale x 1 x i1> %mask) {
; ALL-LABEL: @srem_nxv1i64_anymask_knownvl(
; ALL-NEXT:    [[X_HEAD:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[X:%.*]], i64 0
; ALL-NEXT:    [[X_SPLAT:%.*]] = shufflevector <vscale x 1 x i64> [[X_HEAD]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[Y_HEAD:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[X]], i64 0
; ALL-NEXT:    [[Y_SPLAT:%.*]] = shufflevector <vscale x 1 x i64> [[Y_HEAD]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[RES:%.*]] = call <vscale x 1 x i64> @llvm.vp.srem.nxv1i64(<vscale x 1 x i64> [[X_SPLAT]], <vscale x 1 x i64> [[Y_SPLAT]], <vscale x 1 x i1> [[MASK:%.*]], i32 4)
; ALL-NEXT:    ret <vscale x 1 x i64> [[RES]]
;
  %x.head = insertelement <vscale x 1 x i64> poison, i64 %x, i64 0
  %x.splat = shufflevector <vscale x 1 x i64> %x.head, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %y.head = insertelement <vscale x 1 x i64> poison, i64 %x, i64 0
  %y.splat = shufflevector <vscale x 1 x i64> %y.head, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %res = call <vscale x 1 x i64> @llvm.vp.srem.nxv1i64(<vscale x 1 x i64> %x.splat, <vscale x 1 x i64> %y.splat, <vscale x 1 x i1> %mask, i32 4)
  ret <vscale x 1 x i64> %res
}

define <vscale x 1 x i64> @urem_nxv1i64_allonesmask_knownvl(i64 %x, i64 %y) {
; VEC-COMBINE-LABEL: @urem_nxv1i64_allonesmask_knownvl(
; VEC-COMBINE-NEXT:    [[TMP1:%.*]] = urem i64 [[X:%.*]], [[X]]
; VEC-COMBINE-NEXT:    [[DOTSPLATINSERT:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[TMP1]], i64 0
; VEC-COMBINE-NEXT:    [[RES:%.*]] = shufflevector <vscale x 1 x i64> [[DOTSPLATINSERT]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-NEXT:    ret <vscale x 1 x i64> [[RES]]
;
; NO-VEC-COMBINE-LABEL: @urem_nxv1i64_allonesmask_knownvl(
; NO-VEC-COMBINE-NEXT:    [[MASK_HEAD:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; NO-VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[MASK_HEAD]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[X_HEAD:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[X:%.*]], i64 0
; NO-VEC-COMBINE-NEXT:    [[X_SPLAT:%.*]] = shufflevector <vscale x 1 x i64> [[X_HEAD]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[Y_HEAD:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[X]], i64 0
; NO-VEC-COMBINE-NEXT:    [[Y_SPLAT:%.*]] = shufflevector <vscale x 1 x i64> [[Y_HEAD]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[RES:%.*]] = call <vscale x 1 x i64> @llvm.vp.urem.nxv1i64(<vscale x 1 x i64> [[X_SPLAT]], <vscale x 1 x i64> [[Y_SPLAT]], <vscale x 1 x i1> [[MASK]], i32 4)
; NO-VEC-COMBINE-NEXT:    ret <vscale x 1 x i64> [[RES]]
;
  %mask.head = insertelement <vscale x 1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 1 x i1> %mask.head, <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
  %x.head = insertelement <vscale x 1 x i64> poison, i64 %x, i64 0
  %x.splat = shufflevector <vscale x 1 x i64> %x.head, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %y.head = insertelement <vscale x 1 x i64> poison, i64 %x, i64 0
  %y.splat = shufflevector <vscale x 1 x i64> %y.head, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %res = call <vscale x 1 x i64> @llvm.vp.urem.nxv1i64(<vscale x 1 x i64> %x.splat, <vscale x 1 x i64> %y.splat, <vscale x 1 x i1> %mask, i32 4)
  ret <vscale x 1 x i64> %res
}

define <vscale x 1 x i64> @urem_nxv1i64_anymask_knownvl(i64 %x, i64 %y, <vscale x 1 x i1> %mask) {
; ALL-LABEL: @urem_nxv1i64_anymask_knownvl(
; ALL-NEXT:    [[X_HEAD:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[X:%.*]], i64 0
; ALL-NEXT:    [[X_SPLAT:%.*]] = shufflevector <vscale x 1 x i64> [[X_HEAD]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[Y_HEAD:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[X]], i64 0
; ALL-NEXT:    [[Y_SPLAT:%.*]] = shufflevector <vscale x 1 x i64> [[Y_HEAD]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[RES:%.*]] = call <vscale x 1 x i64> @llvm.vp.urem.nxv1i64(<vscale x 1 x i64> [[X_SPLAT]], <vscale x 1 x i64> [[Y_SPLAT]], <vscale x 1 x i1> [[MASK:%.*]], i32 4)
; ALL-NEXT:    ret <vscale x 1 x i64> [[RES]]
;
  %x.head = insertelement <vscale x 1 x i64> poison, i64 %x, i64 0
  %x.splat = shufflevector <vscale x 1 x i64> %x.head, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %y.head = insertelement <vscale x 1 x i64> poison, i64 %x, i64 0
  %y.splat = shufflevector <vscale x 1 x i64> %y.head, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %res = call <vscale x 1 x i64> @llvm.vp.urem.nxv1i64(<vscale x 1 x i64> %x.splat, <vscale x 1 x i64> %y.splat, <vscale x 1 x i1> %mask, i32 4)
  ret <vscale x 1 x i64> %res
}

define <vscale x 1 x i64> @ashr_nxv1i64_allonesmask(<vscale x 1 x i64> %x, i64 %y, i32 zeroext %evl) {
; VEC-COMBINE-64-LABEL: @ashr_nxv1i64_allonesmask(
; VEC-COMBINE-64-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-64-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-64-NEXT:    [[TMP1:%.*]] = ashr i64 [[Y:%.*]], 42
; VEC-COMBINE-64-NEXT:    [[DOTSPLATINSERT:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[TMP1]], i64 0
; VEC-COMBINE-64-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[DOTSPLATINSERT]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-64-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP2]], <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-64-NEXT:    ret <vscale x 1 x i64> [[TMP3]]
;
; NO-VEC-COMBINE-LABEL: @ashr_nxv1i64_allonesmask(
; NO-VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; NO-VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; NO-VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.ashr.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; NO-VEC-COMBINE-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; NO-VEC-COMBINE-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
; VEC-COMBINE-32-LABEL: @ashr_nxv1i64_allonesmask(
; VEC-COMBINE-32-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-32-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-32-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; VEC-COMBINE-32-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-32-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.ashr.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-32-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; VEC-COMBINE-32-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
  %splat = insertelement <vscale x 1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 1 x i1> %splat, <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
  %1 = insertelement <vscale x 1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <vscale x 1 x i64> %1, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x i64> @llvm.vp.ashr.nxv1i64(<vscale x 1 x i64> %2, <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %x, <vscale x 1 x i64> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %4
}

define <vscale x 1 x i64> @ashr_nxv1i64_anymask(<vscale x 1 x i64> %x, i64 %y, <vscale x 1 x i1> %mask, i32 zeroext %evl) {
; ALL-LABEL: @ashr_nxv1i64_anymask(
; ALL-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; ALL-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.ashr.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK:%.*]], i32 [[EVL:%.*]])
; ALL-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; ALL-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
  %1 = insertelement <vscale x 1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <vscale x 1 x i64> %1, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x i64> @llvm.vp.ashr.nxv1i64(<vscale x 1 x i64> %2, <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %x, <vscale x 1 x i64> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %4
}

define <vscale x 1 x i64> @lshr_nxv1i64_allonesmask(<vscale x 1 x i64> %x, i64 %y, i32 zeroext %evl) {
; VEC-COMBINE-64-LABEL: @lshr_nxv1i64_allonesmask(
; VEC-COMBINE-64-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-64-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-64-NEXT:    [[TMP1:%.*]] = lshr i64 [[Y:%.*]], 42
; VEC-COMBINE-64-NEXT:    [[DOTSPLATINSERT:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[TMP1]], i64 0
; VEC-COMBINE-64-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[DOTSPLATINSERT]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-64-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP2]], <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-64-NEXT:    ret <vscale x 1 x i64> [[TMP3]]
;
; NO-VEC-COMBINE-LABEL: @lshr_nxv1i64_allonesmask(
; NO-VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; NO-VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; NO-VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.lshr.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; NO-VEC-COMBINE-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; NO-VEC-COMBINE-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
; VEC-COMBINE-32-LABEL: @lshr_nxv1i64_allonesmask(
; VEC-COMBINE-32-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-32-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-32-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; VEC-COMBINE-32-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-32-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.lshr.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-32-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; VEC-COMBINE-32-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
  %splat = insertelement <vscale x 1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 1 x i1> %splat, <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
  %1 = insertelement <vscale x 1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <vscale x 1 x i64> %1, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x i64> @llvm.vp.lshr.nxv1i64(<vscale x 1 x i64> %2, <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %x, <vscale x 1 x i64> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %4
}

define <vscale x 1 x i64> @lshr_nxv1i64_anymask(<vscale x 1 x i64> %x, i64 %y, <vscale x 1 x i1> %mask, i32 zeroext %evl) {
; ALL-LABEL: @lshr_nxv1i64_anymask(
; ALL-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; ALL-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.lshr.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK:%.*]], i32 [[EVL:%.*]])
; ALL-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; ALL-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
  %1 = insertelement <vscale x 1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <vscale x 1 x i64> %1, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x i64> @llvm.vp.lshr.nxv1i64(<vscale x 1 x i64> %2, <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %x, <vscale x 1 x i64> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %4
}

define <vscale x 1 x i64> @shl_nxv1i64_allonesmask(<vscale x 1 x i64> %x, i64 %y, i32 zeroext %evl) {
; VEC-COMBINE-64-LABEL: @shl_nxv1i64_allonesmask(
; VEC-COMBINE-64-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-64-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-64-NEXT:    [[TMP1:%.*]] = shl i64 [[Y:%.*]], 42
; VEC-COMBINE-64-NEXT:    [[DOTSPLATINSERT:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[TMP1]], i64 0
; VEC-COMBINE-64-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[DOTSPLATINSERT]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-64-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP2]], <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-64-NEXT:    ret <vscale x 1 x i64> [[TMP3]]
;
; NO-VEC-COMBINE-LABEL: @shl_nxv1i64_allonesmask(
; NO-VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; NO-VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; NO-VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.shl.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; NO-VEC-COMBINE-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; NO-VEC-COMBINE-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
; VEC-COMBINE-32-LABEL: @shl_nxv1i64_allonesmask(
; VEC-COMBINE-32-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-32-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-32-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; VEC-COMBINE-32-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-32-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.shl.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-32-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; VEC-COMBINE-32-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
  %splat = insertelement <vscale x 1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 1 x i1> %splat, <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
  %1 = insertelement <vscale x 1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <vscale x 1 x i64> %1, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x i64> @llvm.vp.shl.nxv1i64(<vscale x 1 x i64> %2, <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %x, <vscale x 1 x i64> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %4
}

define <vscale x 1 x i64> @shl_nxv1i64_anymask(<vscale x 1 x i64> %x, i64 %y, <vscale x 1 x i1> %mask, i32 zeroext %evl) {
; ALL-LABEL: @shl_nxv1i64_anymask(
; ALL-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; ALL-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.shl.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK:%.*]], i32 [[EVL:%.*]])
; ALL-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; ALL-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
  %1 = insertelement <vscale x 1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <vscale x 1 x i64> %1, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x i64> @llvm.vp.shl.nxv1i64(<vscale x 1 x i64> %2, <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %x, <vscale x 1 x i64> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %4
}

define <vscale x 1 x i64> @or_nxv1i64_allonesmask(<vscale x 1 x i64> %x, i64 %y, i32 zeroext %evl) {
; VEC-COMBINE-64-LABEL: @or_nxv1i64_allonesmask(
; VEC-COMBINE-64-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-64-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-64-NEXT:    [[TMP1:%.*]] = or i64 [[Y:%.*]], 42
; VEC-COMBINE-64-NEXT:    [[DOTSPLATINSERT:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[TMP1]], i64 0
; VEC-COMBINE-64-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[DOTSPLATINSERT]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-64-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP2]], <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-64-NEXT:    ret <vscale x 1 x i64> [[TMP3]]
;
; NO-VEC-COMBINE-LABEL: @or_nxv1i64_allonesmask(
; NO-VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; NO-VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; NO-VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.or.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; NO-VEC-COMBINE-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; NO-VEC-COMBINE-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
; VEC-COMBINE-32-LABEL: @or_nxv1i64_allonesmask(
; VEC-COMBINE-32-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-32-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-32-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; VEC-COMBINE-32-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-32-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.or.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-32-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; VEC-COMBINE-32-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
  %splat = insertelement <vscale x 1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 1 x i1> %splat, <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
  %1 = insertelement <vscale x 1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <vscale x 1 x i64> %1, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x i64> @llvm.vp.or.nxv1i64(<vscale x 1 x i64> %2, <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %x, <vscale x 1 x i64> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %4
}

define <vscale x 1 x i64> @or_nxv1i64_anymask(<vscale x 1 x i64> %x, i64 %y, <vscale x 1 x i1> %mask, i32 zeroext %evl) {
; ALL-LABEL: @or_nxv1i64_anymask(
; ALL-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; ALL-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.or.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK:%.*]], i32 [[EVL:%.*]])
; ALL-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; ALL-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
  %1 = insertelement <vscale x 1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <vscale x 1 x i64> %1, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x i64> @llvm.vp.or.nxv1i64(<vscale x 1 x i64> %2, <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %x, <vscale x 1 x i64> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %4
}

define <vscale x 1 x i64> @and_nxv1i64_allonesmask(<vscale x 1 x i64> %x, i64 %y, i32 zeroext %evl) {
; VEC-COMBINE-64-LABEL: @and_nxv1i64_allonesmask(
; VEC-COMBINE-64-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-64-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-64-NEXT:    [[TMP1:%.*]] = and i64 [[Y:%.*]], 42
; VEC-COMBINE-64-NEXT:    [[DOTSPLATINSERT:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[TMP1]], i64 0
; VEC-COMBINE-64-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[DOTSPLATINSERT]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-64-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP2]], <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-64-NEXT:    ret <vscale x 1 x i64> [[TMP3]]
;
; NO-VEC-COMBINE-LABEL: @and_nxv1i64_allonesmask(
; NO-VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; NO-VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; NO-VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.and.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; NO-VEC-COMBINE-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; NO-VEC-COMBINE-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
; VEC-COMBINE-32-LABEL: @and_nxv1i64_allonesmask(
; VEC-COMBINE-32-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-32-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-32-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; VEC-COMBINE-32-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-32-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.and.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-32-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; VEC-COMBINE-32-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
  %splat = insertelement <vscale x 1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 1 x i1> %splat, <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
  %1 = insertelement <vscale x 1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <vscale x 1 x i64> %1, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x i64> @llvm.vp.and.nxv1i64(<vscale x 1 x i64> %2, <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %x, <vscale x 1 x i64> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %4
}

define <vscale x 1 x i64> @and_nxv1i64_anymask(<vscale x 1 x i64> %x, i64 %y, <vscale x 1 x i1> %mask, i32 zeroext %evl) {
; ALL-LABEL: @and_nxv1i64_anymask(
; ALL-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; ALL-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.and.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK:%.*]], i32 [[EVL:%.*]])
; ALL-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; ALL-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
  %1 = insertelement <vscale x 1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <vscale x 1 x i64> %1, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x i64> @llvm.vp.and.nxv1i64(<vscale x 1 x i64> %2, <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %x, <vscale x 1 x i64> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %4
}

define <vscale x 1 x i64> @xor_nxv1i64_allonesmask(<vscale x 1 x i64> %x, i64 %y, i32 zeroext %evl) {
; VEC-COMBINE-64-LABEL: @xor_nxv1i64_allonesmask(
; VEC-COMBINE-64-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-64-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-64-NEXT:    [[TMP1:%.*]] = xor i64 [[Y:%.*]], 42
; VEC-COMBINE-64-NEXT:    [[DOTSPLATINSERT:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[TMP1]], i64 0
; VEC-COMBINE-64-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[DOTSPLATINSERT]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-64-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP2]], <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-64-NEXT:    ret <vscale x 1 x i64> [[TMP3]]
;
; NO-VEC-COMBINE-LABEL: @xor_nxv1i64_allonesmask(
; NO-VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; NO-VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; NO-VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.xor.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; NO-VEC-COMBINE-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; NO-VEC-COMBINE-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
; VEC-COMBINE-32-LABEL: @xor_nxv1i64_allonesmask(
; VEC-COMBINE-32-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-32-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-32-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; VEC-COMBINE-32-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-32-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.xor.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-32-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; VEC-COMBINE-32-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
  %splat = insertelement <vscale x 1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 1 x i1> %splat, <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
  %1 = insertelement <vscale x 1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <vscale x 1 x i64> %1, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x i64> @llvm.vp.xor.nxv1i64(<vscale x 1 x i64> %2, <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %x, <vscale x 1 x i64> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %4
}

define <vscale x 1 x i64> @xor_nxv1i64_anymask(<vscale x 1 x i64> %x, i64 %y, <vscale x 1 x i1> %mask, i32 zeroext %evl) {
; ALL-LABEL: @xor_nxv1i64_anymask(
; ALL-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; ALL-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.xor.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK:%.*]], i32 [[EVL:%.*]])
; ALL-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; ALL-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
  %1 = insertelement <vscale x 1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <vscale x 1 x i64> %1, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x i64> @llvm.vp.xor.nxv1i64(<vscale x 1 x i64> %2, <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %x, <vscale x 1 x i64> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %4
}

define <vscale x 1 x i64> @smin_nxv1i64_allonesmask(<vscale x 1 x i64> %x, i64 %y, i32 zeroext %evl) {
; ALL-LABEL: @smin_nxv1i64_allonesmask(
; ALL-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; ALL-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; ALL-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.smin.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; ALL-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; ALL-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
  %splat = insertelement <vscale x 1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 1 x i1> %splat, <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
  %1 = insertelement <vscale x 1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <vscale x 1 x i64> %1, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x i64> @llvm.vp.smin.nxv1i64(<vscale x 1 x i64> %2, <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %x, <vscale x 1 x i64> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %4
}

define <vscale x 1 x i64> @smin_nxv1i64_anymask(<vscale x 1 x i64> %x, i64 %y, <vscale x 1 x i1> %mask, i32 zeroext %evl) {
; ALL-LABEL: @smin_nxv1i64_anymask(
; ALL-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; ALL-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.smin.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK:%.*]], i32 [[EVL:%.*]])
; ALL-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; ALL-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
  %1 = insertelement <vscale x 1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <vscale x 1 x i64> %1, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x i64> @llvm.vp.smin.nxv1i64(<vscale x 1 x i64> %2, <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %x, <vscale x 1 x i64> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %4
}

define <vscale x 1 x i64> @smax_nxv1i64_allonesmask(<vscale x 1 x i64> %x, i64 %y, i32 zeroext %evl) {
; ALL-LABEL: @smax_nxv1i64_allonesmask(
; ALL-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; ALL-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; ALL-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.smax.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; ALL-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; ALL-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
  %splat = insertelement <vscale x 1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 1 x i1> %splat, <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
  %1 = insertelement <vscale x 1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <vscale x 1 x i64> %1, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x i64> @llvm.vp.smax.nxv1i64(<vscale x 1 x i64> %2, <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %x, <vscale x 1 x i64> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %4
}

define <vscale x 1 x i64> @smax_nxv1i64_anymask(<vscale x 1 x i64> %x, i64 %y, <vscale x 1 x i1> %mask, i32 zeroext %evl) {
; ALL-LABEL: @smax_nxv1i64_anymask(
; ALL-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; ALL-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.smax.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK:%.*]], i32 [[EVL:%.*]])
; ALL-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; ALL-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
  %1 = insertelement <vscale x 1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <vscale x 1 x i64> %1, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x i64> @llvm.vp.smax.nxv1i64(<vscale x 1 x i64> %2, <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %x, <vscale x 1 x i64> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %4
}

define <vscale x 1 x i64> @umin_nxv1i64_allonesmask(<vscale x 1 x i64> %x, i64 %y, i32 zeroext %evl) {
; ALL-LABEL: @umin_nxv1i64_allonesmask(
; ALL-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; ALL-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; ALL-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.umin.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; ALL-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; ALL-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
  %splat = insertelement <vscale x 1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 1 x i1> %splat, <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
  %1 = insertelement <vscale x 1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <vscale x 1 x i64> %1, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x i64> @llvm.vp.umin.nxv1i64(<vscale x 1 x i64> %2, <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %x, <vscale x 1 x i64> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %4
}

define <vscale x 1 x i64> @umax_nxv1i64_allonesmask(<vscale x 1 x i64> %x, i64 %y, i32 zeroext %evl) {
; ALL-LABEL: @umax_nxv1i64_allonesmask(
; ALL-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; ALL-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; ALL-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.umax.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; ALL-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; ALL-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
  %splat = insertelement <vscale x 1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 1 x i1> %splat, <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
  %1 = insertelement <vscale x 1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <vscale x 1 x i64> %1, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x i64> @llvm.vp.umax.nxv1i64(<vscale x 1 x i64> %2, <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %x, <vscale x 1 x i64> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %4
}

define <vscale x 1 x i64> @umax_nxv1i64_anymask(<vscale x 1 x i64> %x, i64 %y, <vscale x 1 x i1> %mask, i32 zeroext %evl) {
; ALL-LABEL: @umax_nxv1i64_anymask(
; ALL-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x i64> poison, i64 [[Y:%.*]], i64 0
; ALL-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x i64> [[TMP1]], <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x i64> @llvm.vp.umax.nxv1i64(<vscale x 1 x i64> [[TMP2]], <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> [[MASK:%.*]], i32 [[EVL:%.*]])
; ALL-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> [[X:%.*]], <vscale x 1 x i64> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; ALL-NEXT:    ret <vscale x 1 x i64> [[TMP4]]
;
  %1 = insertelement <vscale x 1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <vscale x 1 x i64> %1, <vscale x 1 x i64> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x i64> @llvm.vp.umax.nxv1i64(<vscale x 1 x i64> %2, <vscale x 1 x i64> splat (i64 42), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x i64> @llvm.vp.mul.nxv1i64(<vscale x 1 x i64> %x, <vscale x 1 x i64> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x i64> %4
}

define <vscale x 1 x float> @fadd_nxv1f32_allonesmask(<vscale x 1 x float> %x, float %y, i32 zeroext %evl) {
; VEC-COMBINE-LABEL: @fadd_nxv1f32_allonesmask(
; VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-NEXT:    [[TMP1:%.*]] = fadd float [[Y:%.*]], 4.200000e+01
; VEC-COMBINE-NEXT:    [[DOTSPLATINSERT:%.*]] = insertelement <vscale x 1 x float> poison, float [[TMP1]], i64 0
; VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x float> [[DOTSPLATINSERT]], <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> [[X:%.*]], <vscale x 1 x float> [[TMP2]], <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-NEXT:    ret <vscale x 1 x float> [[TMP3]]
;
; NO-VEC-COMBINE-LABEL: @fadd_nxv1f32_allonesmask(
; NO-VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; NO-VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x float> poison, float [[Y:%.*]], i64 0
; NO-VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x float> [[TMP1]], <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> [[TMP2]], <vscale x 1 x float> splat (float 4.200000e+01), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; NO-VEC-COMBINE-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> [[X:%.*]], <vscale x 1 x float> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; NO-VEC-COMBINE-NEXT:    ret <vscale x 1 x float> [[TMP4]]
;
  %splat = insertelement <vscale x 1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 1 x i1> %splat, <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
  %1 = insertelement <vscale x 1 x float> poison, float %y, i64 0
  %2 = shufflevector <vscale x 1 x float > %1, <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> %2, <vscale x 1 x float> splat (float 42.0), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> %x, <vscale x 1 x float> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x float> %4
}

define <vscale x 1 x float> @fadd_nxv1f32_anymask(<vscale x 1 x float> %x, float %y, <vscale x 1 x i1> %mask, i32 zeroext %evl) {
; ALL-LABEL: @fadd_nxv1f32_anymask(
; ALL-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x float> poison, float [[Y:%.*]], i64 0
; ALL-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x float> [[TMP1]], <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> [[TMP2]], <vscale x 1 x float> splat (float 4.200000e+01), <vscale x 1 x i1> [[MASK:%.*]], i32 [[EVL:%.*]])
; ALL-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> [[X:%.*]], <vscale x 1 x float> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; ALL-NEXT:    ret <vscale x 1 x float> [[TMP4]]
;
  %1 = insertelement <vscale x 1 x float> poison, float %y, i64 0
  %2 = shufflevector <vscale x 1 x float> %1, <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> %2, <vscale x 1 x float> splat (float 42.0), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> %x, <vscale x 1 x float> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x float> %4
}

define <vscale x 1 x float> @fsub_nxv1f32_allonesmask(<vscale x 1 x float> %x, float %y, i32 zeroext %evl) {
; VEC-COMBINE-LABEL: @fsub_nxv1f32_allonesmask(
; VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-NEXT:    [[TMP1:%.*]] = fsub float [[Y:%.*]], 4.200000e+01
; VEC-COMBINE-NEXT:    [[DOTSPLATINSERT:%.*]] = insertelement <vscale x 1 x float> poison, float [[TMP1]], i64 0
; VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x float> [[DOTSPLATINSERT]], <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> [[X:%.*]], <vscale x 1 x float> [[TMP2]], <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-NEXT:    ret <vscale x 1 x float> [[TMP3]]
;
; NO-VEC-COMBINE-LABEL: @fsub_nxv1f32_allonesmask(
; NO-VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; NO-VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x float> poison, float [[Y:%.*]], i64 0
; NO-VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x float> [[TMP1]], <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x float> @llvm.vp.fsub.nxv1f32(<vscale x 1 x float> [[TMP2]], <vscale x 1 x float> splat (float 4.200000e+01), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; NO-VEC-COMBINE-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> [[X:%.*]], <vscale x 1 x float> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; NO-VEC-COMBINE-NEXT:    ret <vscale x 1 x float> [[TMP4]]
;
  %splat = insertelement <vscale x 1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 1 x i1> %splat, <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
  %1 = insertelement <vscale x 1 x float> poison, float %y, i64 0
  %2 = shufflevector <vscale x 1 x float > %1, <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x float> @llvm.vp.fsub.nxv1f32(<vscale x 1 x float> %2, <vscale x 1 x float> splat (float 42.0), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> %x, <vscale x 1 x float> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x float> %4
}

define <vscale x 1 x float> @fsub_nxv1f32_anymask(<vscale x 1 x float> %x, float %y, <vscale x 1 x i1> %mask, i32 zeroext %evl) {
; ALL-LABEL: @fsub_nxv1f32_anymask(
; ALL-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x float> poison, float [[Y:%.*]], i64 0
; ALL-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x float> [[TMP1]], <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x float> @llvm.vp.fsub.nxv1f32(<vscale x 1 x float> [[TMP2]], <vscale x 1 x float> splat (float 4.200000e+01), <vscale x 1 x i1> [[MASK:%.*]], i32 [[EVL:%.*]])
; ALL-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> [[X:%.*]], <vscale x 1 x float> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; ALL-NEXT:    ret <vscale x 1 x float> [[TMP4]]
;
  %1 = insertelement <vscale x 1 x float> poison, float %y, i64 0
  %2 = shufflevector <vscale x 1 x float> %1, <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x float> @llvm.vp.fsub.nxv1f32(<vscale x 1 x float> %2, <vscale x 1 x float> splat (float 42.0), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> %x, <vscale x 1 x float> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x float> %4
}

define <vscale x 1 x float> @fdiv_nxv1f32_allonesmask(<vscale x 1 x float> %x, float %y, i32 zeroext %evl) {
; VEC-COMBINE-LABEL: @fdiv_nxv1f32_allonesmask(
; VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-NEXT:    [[TMP1:%.*]] = fdiv float [[Y:%.*]], 4.200000e+01
; VEC-COMBINE-NEXT:    [[DOTSPLATINSERT:%.*]] = insertelement <vscale x 1 x float> poison, float [[TMP1]], i64 0
; VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x float> [[DOTSPLATINSERT]], <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> [[X:%.*]], <vscale x 1 x float> [[TMP2]], <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-NEXT:    ret <vscale x 1 x float> [[TMP3]]
;
; NO-VEC-COMBINE-LABEL: @fdiv_nxv1f32_allonesmask(
; NO-VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; NO-VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x float> poison, float [[Y:%.*]], i64 0
; NO-VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x float> [[TMP1]], <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x float> @llvm.vp.fdiv.nxv1f32(<vscale x 1 x float> [[TMP2]], <vscale x 1 x float> splat (float 4.200000e+01), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; NO-VEC-COMBINE-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> [[X:%.*]], <vscale x 1 x float> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; NO-VEC-COMBINE-NEXT:    ret <vscale x 1 x float> [[TMP4]]
;
  %splat = insertelement <vscale x 1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 1 x i1> %splat, <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
  %1 = insertelement <vscale x 1 x float> poison, float %y, i64 0
  %2 = shufflevector <vscale x 1 x float > %1, <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x float> @llvm.vp.fdiv.nxv1f32(<vscale x 1 x float> %2, <vscale x 1 x float> splat (float 42.0), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> %x, <vscale x 1 x float> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x float> %4
}

define <vscale x 1 x float> @fdiv_nxv1f32_anymask(<vscale x 1 x float> %x, float %y, <vscale x 1 x i1> %mask, i32 zeroext %evl) {
; ALL-LABEL: @fdiv_nxv1f32_anymask(
; ALL-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x float> poison, float [[Y:%.*]], i64 0
; ALL-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x float> [[TMP1]], <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x float> @llvm.vp.fdiv.nxv1f32(<vscale x 1 x float> [[TMP2]], <vscale x 1 x float> splat (float 4.200000e+01), <vscale x 1 x i1> [[MASK:%.*]], i32 [[EVL:%.*]])
; ALL-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> [[X:%.*]], <vscale x 1 x float> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; ALL-NEXT:    ret <vscale x 1 x float> [[TMP4]]
;
  %1 = insertelement <vscale x 1 x float> poison, float %y, i64 0
  %2 = shufflevector <vscale x 1 x float> %1, <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x float> @llvm.vp.fdiv.nxv1f32(<vscale x 1 x float> %2, <vscale x 1 x float> splat (float 42.0), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> %x, <vscale x 1 x float> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x float> %4
}

define <vscale x 1 x float> @frem_nxv1f32_allonesmask(<vscale x 1 x float> %x, float %y, i32 zeroext %evl) {
; VEC-COMBINE-LABEL: @frem_nxv1f32_allonesmask(
; VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-NEXT:    [[TMP1:%.*]] = frem float [[Y:%.*]], 4.200000e+01
; VEC-COMBINE-NEXT:    [[DOTSPLATINSERT:%.*]] = insertelement <vscale x 1 x float> poison, float [[TMP1]], i64 0
; VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x float> [[DOTSPLATINSERT]], <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> [[X:%.*]], <vscale x 1 x float> [[TMP2]], <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-NEXT:    ret <vscale x 1 x float> [[TMP3]]
;
; NO-VEC-COMBINE-LABEL: @frem_nxv1f32_allonesmask(
; NO-VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; NO-VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x float> poison, float [[Y:%.*]], i64 0
; NO-VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x float> [[TMP1]], <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x float> @llvm.vp.frem.nxv1f32(<vscale x 1 x float> [[TMP2]], <vscale x 1 x float> splat (float 4.200000e+01), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; NO-VEC-COMBINE-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> [[X:%.*]], <vscale x 1 x float> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; NO-VEC-COMBINE-NEXT:    ret <vscale x 1 x float> [[TMP4]]
;
  %splat = insertelement <vscale x 1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 1 x i1> %splat, <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
  %1 = insertelement <vscale x 1 x float> poison, float %y, i64 0
  %2 = shufflevector <vscale x 1 x float > %1, <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x float> @llvm.vp.frem.nxv1f32(<vscale x 1 x float> %2, <vscale x 1 x float> splat (float 42.0), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> %x, <vscale x 1 x float> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x float> %4
  }

  define <vscale x 1 x float> @frem_nxv1f32_anymask(<vscale x 1 x float> %x, float %y, <vscale x 1 x i1> %mask, i32 zeroext %evl) {
; ALL-LABEL: @frem_nxv1f32_anymask(
; ALL-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x float> poison, float [[Y:%.*]], i64 0
; ALL-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x float> [[TMP1]], <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x float> @llvm.vp.frem.nxv1f32(<vscale x 1 x float> [[TMP2]], <vscale x 1 x float> splat (float 4.200000e+01), <vscale x 1 x i1> [[MASK:%.*]], i32 [[EVL:%.*]])
; ALL-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> [[X:%.*]], <vscale x 1 x float> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; ALL-NEXT:    ret <vscale x 1 x float> [[TMP4]]
;
  %1 = insertelement <vscale x 1 x float> poison, float %y, i64 0
  %2 = shufflevector <vscale x 1 x float> %1, <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x float> @llvm.vp.frem.nxv1f32(<vscale x 1 x float> %2, <vscale x 1 x float> splat (float 42.0), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> %x, <vscale x 1 x float> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x float> %4
}

define <vscale x 1 x float> @fdiv_nxv1f32_allonesmask_knownvl(<vscale x 1 x float> %x, float %y) {
; VEC-COMBINE-LABEL: @fdiv_nxv1f32_allonesmask_knownvl(
; VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-NEXT:    [[TMP1:%.*]] = fdiv float [[Y:%.*]], 4.200000e+01
; VEC-COMBINE-NEXT:    [[DOTSPLATINSERT:%.*]] = insertelement <vscale x 1 x float> poison, float [[TMP1]], i64 0
; VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x float> [[DOTSPLATINSERT]], <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> [[X:%.*]], <vscale x 1 x float> [[TMP2]], <vscale x 1 x i1> [[MASK]], i32 4)
; VEC-COMBINE-NEXT:    ret <vscale x 1 x float> [[TMP3]]
;
; NO-VEC-COMBINE-LABEL: @fdiv_nxv1f32_allonesmask_knownvl(
; NO-VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; NO-VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x float> poison, float [[Y:%.*]], i64 0
; NO-VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x float> [[TMP1]], <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x float> @llvm.vp.fdiv.nxv1f32(<vscale x 1 x float> [[TMP2]], <vscale x 1 x float> splat (float 4.200000e+01), <vscale x 1 x i1> [[MASK]], i32 4)
; NO-VEC-COMBINE-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> [[X:%.*]], <vscale x 1 x float> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 4)
; NO-VEC-COMBINE-NEXT:    ret <vscale x 1 x float> [[TMP4]]
;
  %splat = insertelement <vscale x 1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 1 x i1> %splat, <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
  %1 = insertelement <vscale x 1 x float> poison, float %y, i64 0
  %2 = shufflevector <vscale x 1 x float > %1, <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x float> @llvm.vp.fdiv.nxv1f32(<vscale x 1 x float> %2, <vscale x 1 x float> splat (float 42.0), <vscale x 1 x i1> %mask, i32 4)
  %4 = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> %x, <vscale x 1 x float> %3, <vscale x 1 x i1> %mask, i32 4)
  ret <vscale x 1 x float> %4
}

define <vscale x 1 x float> @fdiv_nxv1f32_anymask_knownvl(<vscale x 1 x float> %x, float %y, <vscale x 1 x i1> %mask) {
; ALL-LABEL: @fdiv_nxv1f32_anymask_knownvl(
; ALL-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x float> poison, float [[Y:%.*]], i64 0
; ALL-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x float> [[TMP1]], <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x float> @llvm.vp.fdiv.nxv1f32(<vscale x 1 x float> [[TMP2]], <vscale x 1 x float> splat (float 4.200000e+01), <vscale x 1 x i1> [[MASK:%.*]], i32 4)
; ALL-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> [[X:%.*]], <vscale x 1 x float> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 4)
; ALL-NEXT:    ret <vscale x 1 x float> [[TMP4]]
;
  %1 = insertelement <vscale x 1 x float> poison, float %y, i64 0
  %2 = shufflevector <vscale x 1 x float> %1, <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x float> @llvm.vp.fdiv.nxv1f32(<vscale x 1 x float> %2, <vscale x 1 x float> splat (float 42.0), <vscale x 1 x i1> %mask, i32 4)
  %4 = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> %x, <vscale x 1 x float> %3, <vscale x 1 x i1> %mask, i32 4)
  ret <vscale x 1 x float> %4
}

define <vscale x 1 x float> @frem_nxv1f32_allonesmask_knownvl(<vscale x 1 x float> %x, float %y) {
; VEC-COMBINE-LABEL: @frem_nxv1f32_allonesmask_knownvl(
; VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-NEXT:    [[TMP1:%.*]] = frem float [[Y:%.*]], 4.200000e+01
; VEC-COMBINE-NEXT:    [[DOTSPLATINSERT:%.*]] = insertelement <vscale x 1 x float> poison, float [[TMP1]], i64 0
; VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x float> [[DOTSPLATINSERT]], <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> [[X:%.*]], <vscale x 1 x float> [[TMP2]], <vscale x 1 x i1> [[MASK]], i32 4)
; VEC-COMBINE-NEXT:    ret <vscale x 1 x float> [[TMP3]]
;
; NO-VEC-COMBINE-LABEL: @frem_nxv1f32_allonesmask_knownvl(
; NO-VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; NO-VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x float> poison, float [[Y:%.*]], i64 0
; NO-VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x float> [[TMP1]], <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x float> @llvm.vp.frem.nxv1f32(<vscale x 1 x float> [[TMP2]], <vscale x 1 x float> splat (float 4.200000e+01), <vscale x 1 x i1> [[MASK]], i32 4)
; NO-VEC-COMBINE-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> [[X:%.*]], <vscale x 1 x float> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 4)
; NO-VEC-COMBINE-NEXT:    ret <vscale x 1 x float> [[TMP4]]
;
  %splat = insertelement <vscale x 1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 1 x i1> %splat, <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
  %1 = insertelement <vscale x 1 x float> poison, float %y, i64 0
  %2 = shufflevector <vscale x 1 x float > %1, <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x float> @llvm.vp.frem.nxv1f32(<vscale x 1 x float> %2, <vscale x 1 x float> splat (float 42.0), <vscale x 1 x i1> %mask, i32 4)
  %4 = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> %x, <vscale x 1 x float> %3, <vscale x 1 x i1> %mask, i32 4)
  ret <vscale x 1 x float> %4
}

  define <vscale x 1 x float> @frem_nxv1f32_anymask_knownvl(<vscale x 1 x float> %x, float %y, <vscale x 1 x i1> %mask) {
; ALL-LABEL: @frem_nxv1f32_anymask_knownvl(
; ALL-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x float> poison, float [[Y:%.*]], i64 0
; ALL-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x float> [[TMP1]], <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x float> @llvm.vp.frem.nxv1f32(<vscale x 1 x float> [[TMP2]], <vscale x 1 x float> splat (float 4.200000e+01), <vscale x 1 x i1> [[MASK:%.*]], i32 4)
; ALL-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> [[X:%.*]], <vscale x 1 x float> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 4)
; ALL-NEXT:    ret <vscale x 1 x float> [[TMP4]]
;
  %1 = insertelement <vscale x 1 x float> poison, float %y, i64 0
  %2 = shufflevector <vscale x 1 x float> %1, <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x float> @llvm.vp.frem.nxv1f32(<vscale x 1 x float> %2, <vscale x 1 x float> splat (float 42.0), <vscale x 1 x i1> %mask, i32 4)
  %4 = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> %x, <vscale x 1 x float> %3, <vscale x 1 x i1> %mask, i32 4)
  ret <vscale x 1 x float> %4
}

define <vscale x 1 x float> @copysign_nxv1f32_allonesmask(<vscale x 1 x float> %x, float %y, i32 zeroext %evl) {
; VEC-COMBINE-LABEL: @copysign_nxv1f32_allonesmask(
; VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-NEXT:    [[TMP1:%.*]] = call float @llvm.copysign.f32(float [[Y:%.*]], float 4.200000e+01)
; VEC-COMBINE-NEXT:    [[DOTSPLATINSERT:%.*]] = insertelement <vscale x 1 x float> poison, float [[TMP1]], i64 0
; VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x float> [[DOTSPLATINSERT]], <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> [[X:%.*]], <vscale x 1 x float> [[TMP2]], <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-NEXT:    ret <vscale x 1 x float> [[TMP3]]
;
; NO-VEC-COMBINE-LABEL: @copysign_nxv1f32_allonesmask(
; NO-VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; NO-VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x float> poison, float [[Y:%.*]], i64 0
; NO-VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x float> [[TMP1]], <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x float> @llvm.vp.copysign.nxv1f32(<vscale x 1 x float> [[TMP2]], <vscale x 1 x float> splat (float 4.200000e+01), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; NO-VEC-COMBINE-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> [[X:%.*]], <vscale x 1 x float> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; NO-VEC-COMBINE-NEXT:    ret <vscale x 1 x float> [[TMP4]]
;
  %splat = insertelement <vscale x 1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 1 x i1> %splat, <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
  %1 = insertelement <vscale x 1 x float> poison, float %y, i64 0
  %2 = shufflevector <vscale x 1 x float > %1, <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x float> @llvm.vp.copysign.nxv1f32(<vscale x 1 x float> %2, <vscale x 1 x float> splat (float 42.0), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> %x, <vscale x 1 x float> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x float> %4
}

define <vscale x 1 x float> @copysign_nxv1f32_anymask(<vscale x 1 x float> %x, float %y, <vscale x 1 x i1> %mask, i32 zeroext %evl) {
; ALL-LABEL: @copysign_nxv1f32_anymask(
; ALL-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x float> poison, float [[Y:%.*]], i64 0
; ALL-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x float> [[TMP1]], <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x float> @llvm.vp.copysign.nxv1f32(<vscale x 1 x float> [[TMP2]], <vscale x 1 x float> splat (float 4.200000e+01), <vscale x 1 x i1> [[MASK:%.*]], i32 [[EVL:%.*]])
; ALL-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> [[X:%.*]], <vscale x 1 x float> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; ALL-NEXT:    ret <vscale x 1 x float> [[TMP4]]
;
  %1 = insertelement <vscale x 1 x float> poison, float %y, i64 0
  %2 = shufflevector <vscale x 1 x float> %1, <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x float> @llvm.vp.copysign.nxv1f32(<vscale x 1 x float> %2, <vscale x 1 x float> splat (float 42.0), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> %x, <vscale x 1 x float> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x float> %4
}

define <vscale x 1 x float> @minnum_nxv1f32_allonesmask(<vscale x 1 x float> %x, float %y, i32 zeroext %evl) {
; VEC-COMBINE-LABEL: @minnum_nxv1f32_allonesmask(
; VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-NEXT:    [[TMP1:%.*]] = call float @llvm.minnum.f32(float [[Y:%.*]], float 4.200000e+01)
; VEC-COMBINE-NEXT:    [[DOTSPLATINSERT:%.*]] = insertelement <vscale x 1 x float> poison, float [[TMP1]], i64 0
; VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x float> [[DOTSPLATINSERT]], <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> [[X:%.*]], <vscale x 1 x float> [[TMP2]], <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-NEXT:    ret <vscale x 1 x float> [[TMP3]]
;
; NO-VEC-COMBINE-LABEL: @minnum_nxv1f32_allonesmask(
; NO-VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; NO-VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x float> poison, float [[Y:%.*]], i64 0
; NO-VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x float> [[TMP1]], <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x float> @llvm.vp.minnum.nxv1f32(<vscale x 1 x float> [[TMP2]], <vscale x 1 x float> splat (float 4.200000e+01), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; NO-VEC-COMBINE-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> [[X:%.*]], <vscale x 1 x float> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; NO-VEC-COMBINE-NEXT:    ret <vscale x 1 x float> [[TMP4]]
;
  %splat = insertelement <vscale x 1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 1 x i1> %splat, <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
  %1 = insertelement <vscale x 1 x float> poison, float %y, i64 0
  %2 = shufflevector <vscale x 1 x float > %1, <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x float> @llvm.vp.minnum.nxv1f32(<vscale x 1 x float> %2, <vscale x 1 x float> splat (float 42.0), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> %x, <vscale x 1 x float> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x float> %4
}

define <vscale x 1 x float> @minnum_nxv1f32_anymask(<vscale x 1 x float> %x, float %y, <vscale x 1 x i1> %mask, i32 zeroext %evl) {
; ALL-LABEL: @minnum_nxv1f32_anymask(
; ALL-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x float> poison, float [[Y:%.*]], i64 0
; ALL-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x float> [[TMP1]], <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x float> @llvm.vp.minnum.nxv1f32(<vscale x 1 x float> [[TMP2]], <vscale x 1 x float> splat (float 4.200000e+01), <vscale x 1 x i1> [[MASK:%.*]], i32 [[EVL:%.*]])
; ALL-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> [[X:%.*]], <vscale x 1 x float> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; ALL-NEXT:    ret <vscale x 1 x float> [[TMP4]]
;
  %1 = insertelement <vscale x 1 x float> poison, float %y, i64 0
  %2 = shufflevector <vscale x 1 x float> %1, <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x float> @llvm.vp.minnum.nxv1f32(<vscale x 1 x float> %2, <vscale x 1 x float> splat (float 42.0), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> %x, <vscale x 1 x float> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x float> %4
}

define <vscale x 1 x float> @maxnum_nxv1f32_allonesmask(<vscale x 1 x float> %x, float %y, i32 zeroext %evl) {
; VEC-COMBINE-LABEL: @maxnum_nxv1f32_allonesmask(
; VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-NEXT:    [[TMP1:%.*]] = call float @llvm.maxnum.f32(float [[Y:%.*]], float 4.200000e+01)
; VEC-COMBINE-NEXT:    [[DOTSPLATINSERT:%.*]] = insertelement <vscale x 1 x float> poison, float [[TMP1]], i64 0
; VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x float> [[DOTSPLATINSERT]], <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
; VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> [[X:%.*]], <vscale x 1 x float> [[TMP2]], <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-NEXT:    ret <vscale x 1 x float> [[TMP3]]
;
; NO-VEC-COMBINE-LABEL: @maxnum_nxv1f32_allonesmask(
; NO-VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 1 x i1> poison, i1 true, i32 0
; NO-VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 1 x i1> [[SPLAT]], <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x float> poison, float [[Y:%.*]], i64 0
; NO-VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x float> [[TMP1]], <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x float> @llvm.vp.maxnum.nxv1f32(<vscale x 1 x float> [[TMP2]], <vscale x 1 x float> splat (float 4.200000e+01), <vscale x 1 x i1> [[MASK]], i32 [[EVL:%.*]])
; NO-VEC-COMBINE-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> [[X:%.*]], <vscale x 1 x float> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; NO-VEC-COMBINE-NEXT:    ret <vscale x 1 x float> [[TMP4]]
;
  %splat = insertelement <vscale x 1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 1 x i1> %splat, <vscale x 1 x i1> poison, <vscale x 1 x i32> zeroinitializer
  %1 = insertelement <vscale x 1 x float> poison, float %y, i64 0
  %2 = shufflevector <vscale x 1 x float > %1, <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x float> @llvm.vp.maxnum.nxv1f32(<vscale x 1 x float> %2, <vscale x 1 x float> splat (float 42.0), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> %x, <vscale x 1 x float> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x float> %4
}

define <vscale x 1 x float> @maxnum_nxv1f32_anymask(<vscale x 1 x float> %x, float %y, <vscale x 1 x i1> %mask, i32 zeroext %evl) {
; ALL-LABEL: @maxnum_nxv1f32_anymask(
; ALL-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 1 x float> poison, float [[Y:%.*]], i64 0
; ALL-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 1 x float> [[TMP1]], <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
; ALL-NEXT:    [[TMP3:%.*]] = call <vscale x 1 x float> @llvm.vp.maxnum.nxv1f32(<vscale x 1 x float> [[TMP2]], <vscale x 1 x float> splat (float 4.200000e+01), <vscale x 1 x i1> [[MASK:%.*]], i32 [[EVL:%.*]])
; ALL-NEXT:    [[TMP4:%.*]] = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> [[X:%.*]], <vscale x 1 x float> [[TMP3]], <vscale x 1 x i1> [[MASK]], i32 [[EVL]])
; ALL-NEXT:    ret <vscale x 1 x float> [[TMP4]]
;
  %1 = insertelement <vscale x 1 x float> poison, float %y, i64 0
  %2 = shufflevector <vscale x 1 x float> %1, <vscale x 1 x float> poison, <vscale x 1 x i32> zeroinitializer
  %3 = call <vscale x 1 x float> @llvm.vp.maxnum.nxv1f32(<vscale x 1 x float> %2, <vscale x 1 x float> splat (float 42.0), <vscale x 1 x i1> %mask, i32 %evl)
  %4 = call <vscale x 1 x float> @llvm.vp.fadd.nxv1f32(<vscale x 1 x float> %x, <vscale x 1 x float> %3, <vscale x 1 x i1> %mask, i32 %evl)
  ret <vscale x 1 x float> %4
}

define <vscale x 8 x i8> @add_nxv8i8_allonesmask(<vscale x 8 x i8> %x, i8 %y, i32 zeroext %evl) {
; VEC-COMBINE-LABEL: @add_nxv8i8_allonesmask(
; VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 8 x i1> poison, i1 true, i32 0
; VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 8 x i1> [[SPLAT]], <vscale x 8 x i1> poison, <vscale x 8 x i32> zeroinitializer
; VEC-COMBINE-NEXT:    [[TMP1:%.*]] = add i8 [[Y:%.*]], 42
; VEC-COMBINE-NEXT:    [[DOTSPLATINSERT:%.*]] = insertelement <vscale x 8 x i8> poison, i8 [[TMP1]], i64 0
; VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 8 x i8> [[DOTSPLATINSERT]], <vscale x 8 x i8> poison, <vscale x 8 x i32> zeroinitializer
; VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 8 x i8> @llvm.vp.mul.nxv8i8(<vscale x 8 x i8> [[X:%.*]], <vscale x 8 x i8> [[TMP2]], <vscale x 8 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-NEXT:    ret <vscale x 8 x i8> [[TMP3]]
;
; NO-VEC-COMBINE-LABEL: @add_nxv8i8_allonesmask(
; NO-VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 8 x i1> poison, i1 true, i32 0
; NO-VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 8 x i1> [[SPLAT]], <vscale x 8 x i1> poison, <vscale x 8 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 8 x i8> poison, i8 [[Y:%.*]], i64 0
; NO-VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 8 x i8> [[TMP1]], <vscale x 8 x i8> poison, <vscale x 8 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 8 x i8> @llvm.vp.add.nxv8i8(<vscale x 8 x i8> [[TMP2]], <vscale x 8 x i8> splat (i8 42), <vscale x 8 x i1> [[MASK]], i32 [[EVL:%.*]])
; NO-VEC-COMBINE-NEXT:    [[TMP4:%.*]] = call <vscale x 8 x i8> @llvm.vp.mul.nxv8i8(<vscale x 8 x i8> [[X:%.*]], <vscale x 8 x i8> [[TMP3]], <vscale x 8 x i1> [[MASK]], i32 [[EVL]])
; NO-VEC-COMBINE-NEXT:    ret <vscale x 8 x i8> [[TMP4]]
;
  %splat = insertelement <vscale x 8 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 8 x i1> %splat, <vscale x 8 x i1> poison, <vscale x 8 x i32> zeroinitializer
  %1 = insertelement <vscale x 8 x i8> poison, i8 %y, i64 0
  %2 = shufflevector <vscale x 8 x i8> %1, <vscale x 8 x i8> poison, <vscale x 8 x i32> zeroinitializer
  %3 = call <vscale x 8 x i8>  @llvm.vp.add.nxv8i8(<vscale x 8 x i8> %2, <vscale x 8 x i8> splat (i8 42), <vscale x 8 x i1> %mask, i32 %evl)
  %4 = call <vscale x 8 x i8> @llvm.vp.mul.nxv8i8(<vscale x 8 x i8> %x, <vscale x 8 x i8> %3,  <vscale x 8 x i1> %mask, i32 %evl)
  ret <vscale x 8 x i8> %4
}

define <vscale x 8 x i8> @add_nxv8i8_anymask(<vscale x 8 x i8> %x, i8 %y, <vscale x 8 x i1> %mask, i32 zeroext %evl) {
; ALL-LABEL: @add_nxv8i8_anymask(
; ALL-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 8 x i8> poison, i8 [[Y:%.*]], i64 0
; ALL-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 8 x i8> [[TMP1]], <vscale x 8 x i8> poison, <vscale x 8 x i32> zeroinitializer
; ALL-NEXT:    [[TMP3:%.*]] = call <vscale x 8 x i8> @llvm.vp.add.nxv8i8(<vscale x 8 x i8> [[TMP2]], <vscale x 8 x i8> splat (i8 42), <vscale x 8 x i1> [[MASK:%.*]], i32 [[EVL:%.*]])
; ALL-NEXT:    [[TMP4:%.*]] = call <vscale x 8 x i8> @llvm.vp.mul.nxv8i8(<vscale x 8 x i8> [[X:%.*]], <vscale x 8 x i8> [[TMP3]], <vscale x 8 x i1> [[MASK]], i32 [[EVL]])
; ALL-NEXT:    ret <vscale x 8 x i8> [[TMP4]]
;
  %1 = insertelement <vscale x 8 x i8> poison, i8 %y, i64 0
  %2 = shufflevector <vscale x 8 x i8> %1, <vscale x 8 x i8> poison, <vscale x 8 x i32> zeroinitializer
  %3 = call <vscale x 8 x i8>  @llvm.vp.add.nxv8i8(<vscale x 8 x i8> %2, <vscale x 8 x i8> splat (i8 42), <vscale x 8 x i1> %mask, i32 %evl)
  %4 = call <vscale x 8 x i8> @llvm.vp.mul.nxv8i8(<vscale x 8 x i8> %x, <vscale x 8 x i8> %3,  <vscale x 8 x i1> %mask, i32 %evl)
  ret <vscale x 8 x i8> %4
}

define <vscale x 8 x half> @fadd_nxv1f16_allonesmask(<vscale x 8 x half> %x, half %y, i32 zeroext %evl) {
; VEC-COMBINE-LABEL: @fadd_nxv1f16_allonesmask(
; VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 8 x i1> poison, i1 true, i32 0
; VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 8 x i1> [[SPLAT]], <vscale x 8 x i1> poison, <vscale x 8 x i32> zeroinitializer
; VEC-COMBINE-NEXT:    [[TMP1:%.*]] = fadd half [[Y:%.*]], 0xH5140
; VEC-COMBINE-NEXT:    [[DOTSPLATINSERT:%.*]] = insertelement <vscale x 8 x half> poison, half [[TMP1]], i64 0
; VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 8 x half> [[DOTSPLATINSERT]], <vscale x 8 x half> poison, <vscale x 8 x i32> zeroinitializer
; VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 8 x half> @llvm.vp.fadd.nxv8f16(<vscale x 8 x half> [[X:%.*]], <vscale x 8 x half> [[TMP2]], <vscale x 8 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-NEXT:    ret <vscale x 8 x half> [[TMP3]]
;
; NO-VEC-COMBINE-LABEL: @fadd_nxv1f16_allonesmask(
; NO-VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <vscale x 8 x i1> poison, i1 true, i32 0
; NO-VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <vscale x 8 x i1> [[SPLAT]], <vscale x 8 x i1> poison, <vscale x 8 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 8 x half> poison, half [[Y:%.*]], i64 0
; NO-VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 8 x half> [[TMP1]], <vscale x 8 x half> poison, <vscale x 8 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <vscale x 8 x half> @llvm.vp.fadd.nxv8f16(<vscale x 8 x half> [[TMP2]], <vscale x 8 x half> splat (half 0xH5140), <vscale x 8 x i1> [[MASK]], i32 [[EVL:%.*]])
; NO-VEC-COMBINE-NEXT:    [[TMP4:%.*]] = call <vscale x 8 x half> @llvm.vp.fadd.nxv8f16(<vscale x 8 x half> [[X:%.*]], <vscale x 8 x half> [[TMP3]], <vscale x 8 x i1> [[MASK]], i32 [[EVL]])
; NO-VEC-COMBINE-NEXT:    ret <vscale x 8 x half> [[TMP4]]
;
  %splat = insertelement <vscale x 8 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <vscale x 8 x i1> %splat, <vscale x 8 x i1> poison, <vscale x 8 x i32> zeroinitializer
  %1 = insertelement <vscale x 8 x half> poison, half %y, i64 0
  %2 = shufflevector <vscale x 8 x half> %1, <vscale x 8 x half> poison, <vscale x 8 x i32> zeroinitializer
  %3 = call <vscale x 8 x half> @llvm.vp.fadd.nxv8f16(<vscale x 8 x half> %2, <vscale x 8 x half> splat (half 42.0), <vscale x 8 x i1> %mask, i32 %evl)
  %4 = call <vscale x 8 x half> @llvm.vp.fadd.nxv8f16(<vscale x 8 x half> %x, <vscale x 8 x half> %3, <vscale x 8 x i1> %mask, i32 %evl)
  ret <vscale x 8 x half> %4
}

define <vscale x 8 x half> @fadd_nxv8f16_anymask(<vscale x 8 x half> %x, half %y, <vscale x 8 x i1> %mask, i32 zeroext %evl) {
; ALL-LABEL: @fadd_nxv8f16_anymask(
; ALL-NEXT:    [[TMP1:%.*]] = insertelement <vscale x 8 x half> poison, half [[Y:%.*]], i64 0
; ALL-NEXT:    [[TMP2:%.*]] = shufflevector <vscale x 8 x half> [[TMP1]], <vscale x 8 x half> poison, <vscale x 8 x i32> zeroinitializer
; ALL-NEXT:    [[TMP3:%.*]] = call <vscale x 8 x half> @llvm.vp.fadd.nxv8f16(<vscale x 8 x half> [[TMP2]], <vscale x 8 x half> splat (half 0xH5140), <vscale x 8 x i1> [[MASK:%.*]], i32 [[EVL:%.*]])
; ALL-NEXT:    [[TMP4:%.*]] = call <vscale x 8 x half> @llvm.vp.fadd.nxv8f16(<vscale x 8 x half> [[X:%.*]], <vscale x 8 x half> [[TMP3]], <vscale x 8 x i1> [[MASK]], i32 [[EVL]])
; ALL-NEXT:    ret <vscale x 8 x half> [[TMP4]]
;
  %1 = insertelement <vscale x 8 x half> poison, half %y, i64 0
  %2 = shufflevector <vscale x 8 x half> %1, <vscale x 8 x half> poison, <vscale x 8 x i32> zeroinitializer
  %3 = call <vscale x 8 x half> @llvm.vp.fadd.nxv8f16(<vscale x 8 x half> %2, <vscale x 8 x half> splat (half 42.0), <vscale x 8 x i1> %mask, i32 %evl)
  %4 = call <vscale x 8 x half> @llvm.vp.fadd.nxv8f16(<vscale x 8 x half> %x, <vscale x 8 x half> %3, <vscale x 8 x i1> %mask, i32 %evl)
  ret <vscale x 8 x half> %4
}

define <1 x i64> @add_v1i64_allonesmask(<1 x i64> %x, i64 %y, i32 zeroext %evl) {
; VEC-COMBINE-64-LABEL: @add_v1i64_allonesmask(
; VEC-COMBINE-64-NEXT:    [[SPLAT:%.*]] = insertelement <1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-64-NEXT:    [[MASK:%.*]] = shufflevector <1 x i1> [[SPLAT]], <1 x i1> poison, <1 x i32> zeroinitializer
; VEC-COMBINE-64-NEXT:    [[TMP1:%.*]] = add i64 [[Y:%.*]], 42
; VEC-COMBINE-64-NEXT:    [[DOTSPLATINSERT:%.*]] = insertelement <1 x i64> poison, i64 [[TMP1]], i64 0
; VEC-COMBINE-64-NEXT:    [[TMP2:%.*]] = shufflevector <1 x i64> [[DOTSPLATINSERT]], <1 x i64> poison, <1 x i32> zeroinitializer
; VEC-COMBINE-64-NEXT:    [[TMP3:%.*]] = call <1 x i64> @llvm.vp.mul.v1i64(<1 x i64> [[X:%.*]], <1 x i64> [[TMP2]], <1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-64-NEXT:    ret <1 x i64> [[TMP3]]
;
; NO-VEC-COMBINE-LABEL: @add_v1i64_allonesmask(
; NO-VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <1 x i1> poison, i1 true, i32 0
; NO-VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <1 x i1> [[SPLAT]], <1 x i1> poison, <1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP1:%.*]] = insertelement <1 x i64> poison, i64 [[Y:%.*]], i64 0
; NO-VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <1 x i64> [[TMP1]], <1 x i64> poison, <1 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <1 x i64> @llvm.vp.add.v1i64(<1 x i64> [[TMP2]], <1 x i64> splat (i64 42), <1 x i1> [[MASK]], i32 [[EVL:%.*]])
; NO-VEC-COMBINE-NEXT:    [[TMP4:%.*]] = call <1 x i64> @llvm.vp.mul.v1i64(<1 x i64> [[X:%.*]], <1 x i64> [[TMP3]], <1 x i1> [[MASK]], i32 [[EVL]])
; NO-VEC-COMBINE-NEXT:    ret <1 x i64> [[TMP4]]
;
; VEC-COMBINE-32-LABEL: @add_v1i64_allonesmask(
; VEC-COMBINE-32-NEXT:    [[SPLAT:%.*]] = insertelement <1 x i1> poison, i1 true, i32 0
; VEC-COMBINE-32-NEXT:    [[MASK:%.*]] = shufflevector <1 x i1> [[SPLAT]], <1 x i1> poison, <1 x i32> zeroinitializer
; VEC-COMBINE-32-NEXT:    [[TMP1:%.*]] = insertelement <1 x i64> poison, i64 [[Y:%.*]], i64 0
; VEC-COMBINE-32-NEXT:    [[TMP2:%.*]] = shufflevector <1 x i64> [[TMP1]], <1 x i64> poison, <1 x i32> zeroinitializer
; VEC-COMBINE-32-NEXT:    [[TMP3:%.*]] = call <1 x i64> @llvm.vp.add.v1i64(<1 x i64> [[TMP2]], <1 x i64> splat (i64 42), <1 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-32-NEXT:    [[TMP4:%.*]] = call <1 x i64> @llvm.vp.mul.v1i64(<1 x i64> [[X:%.*]], <1 x i64> [[TMP3]], <1 x i1> [[MASK]], i32 [[EVL]])
; VEC-COMBINE-32-NEXT:    ret <1 x i64> [[TMP4]]
;
  %splat = insertelement <1 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <1 x i1> %splat, <1 x i1> poison, <1 x i32> zeroinitializer
  %1 = insertelement <1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <1 x i64> %1, <1 x i64> poison, <1 x i32> zeroinitializer
  %3 = call <1 x i64> @llvm.vp.add.v1i64(<1 x i64> %2, <1 x i64> shufflevector(<1 x i64> insertelement(<1 x i64> poison, i64 42, i32 0), <1 x i64> poison, <1 x i32> zeroinitializer), <1 x i1> %mask, i32 %evl)
  %4 = call <1 x i64> @llvm.vp.mul.v1i64(<1 x i64> %x, <1 x i64> %3, <1 x i1> %mask, i32 %evl)
  ret <1 x i64> %4
}

define <1 x i64> @add_v1i64_anymask(<1 x i64> %x, i64 %y, <1 x i1> %mask, i32 zeroext %evl) {
; ALL-LABEL: @add_v1i64_anymask(
; ALL-NEXT:    [[TMP1:%.*]] = insertelement <1 x i64> poison, i64 [[Y:%.*]], i64 0
; ALL-NEXT:    [[TMP2:%.*]] = shufflevector <1 x i64> [[TMP1]], <1 x i64> poison, <1 x i32> zeroinitializer
; ALL-NEXT:    [[TMP3:%.*]] = call <1 x i64> @llvm.vp.add.v1i64(<1 x i64> [[TMP2]], <1 x i64> splat (i64 42), <1 x i1> [[MASK:%.*]], i32 [[EVL:%.*]])
; ALL-NEXT:    [[TMP4:%.*]] = call <1 x i64> @llvm.vp.mul.v1i64(<1 x i64> [[X:%.*]], <1 x i64> [[TMP3]], <1 x i1> [[MASK]], i32 [[EVL]])
; ALL-NEXT:    ret <1 x i64> [[TMP4]]
;
  %1 = insertelement <1 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <1 x i64> %1, <1 x i64> poison, <1 x i32> zeroinitializer
  %3 = call <1 x i64> @llvm.vp.add.v1i64(<1 x i64> %2, <1 x i64> shufflevector(<1 x i64> insertelement(<1 x i64> poison, i64 42, i32 0), <1 x i64> poison, <1 x i32> zeroinitializer), <1 x i1> %mask, i32 %evl)
  %4 = call <1 x i64> @llvm.vp.mul.v1i64(<1 x i64> %x, <1 x i64> %3, <1 x i1> %mask, i32 %evl)
  ret <1 x i64> %4
}

define <4 x i64> @add_v4i64_allonesmask(<4 x i64> %x, i64 %y, i32 zeroext %evl) {
; VEC-COMBINE-LABEL: @add_v4i64_allonesmask(
; VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <4 x i1> poison, i1 true, i32 0
; VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <4 x i1> [[SPLAT]], <4 x i1> poison, <4 x i32> zeroinitializer
; VEC-COMBINE-NEXT:    [[TMP1:%.*]] = add i64 [[Y:%.*]], 42
; VEC-COMBINE-NEXT:    [[DOTSPLATINSERT:%.*]] = insertelement <4 x i64> poison, i64 [[TMP1]], i64 0
; VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <4 x i64> [[DOTSPLATINSERT]], <4 x i64> poison, <4 x i32> zeroinitializer
; VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <4 x i64> @llvm.vp.mul.v4i64(<4 x i64> [[X:%.*]], <4 x i64> [[TMP2]], <4 x i1> [[MASK]], i32 [[EVL:%.*]])
; VEC-COMBINE-NEXT:    ret <4 x i64> [[TMP3]]
;
; NO-VEC-COMBINE-LABEL: @add_v4i64_allonesmask(
; NO-VEC-COMBINE-NEXT:    [[SPLAT:%.*]] = insertelement <4 x i1> poison, i1 true, i32 0
; NO-VEC-COMBINE-NEXT:    [[MASK:%.*]] = shufflevector <4 x i1> [[SPLAT]], <4 x i1> poison, <4 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP1:%.*]] = insertelement <4 x i64> poison, i64 [[Y:%.*]], i64 0
; NO-VEC-COMBINE-NEXT:    [[TMP2:%.*]] = shufflevector <4 x i64> [[TMP1]], <4 x i64> poison, <4 x i32> zeroinitializer
; NO-VEC-COMBINE-NEXT:    [[TMP3:%.*]] = call <4 x i64> @llvm.vp.add.v4i64(<4 x i64> [[TMP2]], <4 x i64> splat (i64 42), <4 x i1> [[MASK]], i32 [[EVL:%.*]])
; NO-VEC-COMBINE-NEXT:    [[TMP4:%.*]] = call <4 x i64> @llvm.vp.mul.v4i64(<4 x i64> [[X:%.*]], <4 x i64> [[TMP3]], <4 x i1> [[MASK]], i32 [[EVL]])
; NO-VEC-COMBINE-NEXT:    ret <4 x i64> [[TMP4]]
;
  %splat = insertelement <4 x i1> poison, i1 -1, i32 0
  %mask = shufflevector <4 x i1> %splat, <4 x i1> poison, <4 x i32> zeroinitializer
  %1 = insertelement <4 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <4 x i64> %1, <4 x i64> poison, <4 x i32> zeroinitializer
  %3 = call <4 x i64> @llvm.vp.add.v4i64(<4 x i64> %2, <4 x i64> shufflevector(<4 x i64> insertelement(<4 x i64> poison, i64 42, i32 0), <4 x i64> poison, <4 x i32> zeroinitializer), <4 x i1> %mask, i32 %evl)
  %4 = call <4 x i64> @llvm.vp.mul.v4i64(<4 x i64> %x, <4 x i64> %3, <4 x i1> %mask, i32 %evl)
  ret <4 x i64> %4
}

define <4 x i64> @add_v4i64_anymask(<4 x i64> %x, i64 %y, <4 x i1> %mask, i32 zeroext %evl) {
; ALL-LABEL: @add_v4i64_anymask(
; ALL-NEXT:    [[TMP1:%.*]] = insertelement <4 x i64> poison, i64 [[Y:%.*]], i64 0
; ALL-NEXT:    [[TMP2:%.*]] = shufflevector <4 x i64> [[TMP1]], <4 x i64> poison, <4 x i32> zeroinitializer
; ALL-NEXT:    [[TMP3:%.*]] = call <4 x i64> @llvm.vp.add.v4i64(<4 x i64> [[TMP2]], <4 x i64> splat (i64 42), <4 x i1> [[MASK:%.*]], i32 [[EVL:%.*]])
; ALL-NEXT:    [[TMP4:%.*]] = call <4 x i64> @llvm.vp.mul.v4i64(<4 x i64> [[X:%.*]], <4 x i64> [[TMP3]], <4 x i1> [[MASK]], i32 [[EVL]])
; ALL-NEXT:    ret <4 x i64> [[TMP4]]
;
  %1 = insertelement <4 x i64> poison, i64 %y, i64 0
  %2 = shufflevector <4 x i64> %1, <4 x i64> poison, <4 x i32> zeroinitializer
  %3 = call <4 x i64> @llvm.vp.add.v4i64(<4 x i64> %2, <4 x i64> shufflevector(<4 x i64> insertelement(<4 x i64> poison, i64 42, i32 0), <4 x i64> poison, <4 x i32> zeroinitializer), <4 x i1> %mask, i32 %evl)
  %4 = call <4 x i64> @llvm.vp.mul.v4i64(<4 x i64> %x, <4 x i64> %3, <4 x i1> %mask, i32 %evl)
  ret <4 x i64> %4
}
