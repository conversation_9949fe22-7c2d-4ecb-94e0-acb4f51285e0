; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -passes=vector-combine -S %s | FileCheck %s

target triple = "aarch64"

define <8 x i8> @concat_small(<4 x i8> %a, <4 x i8> %b) {
; CHECK-LABEL: @concat_small(
; CHECK-NEXT:    [[R:%.*]] = shufflevector <4 x i8> [[A:%.*]], <4 x i8> [[B:%.*]], <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
; CHECK-NEXT:    ret <8 x i8> [[R]]
;
  %r = shufflevector <4 x i8> %a, <4 x i8> %b, <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
  ret <8 x i8> %r
}

define <8 x i32> @concat_big(<4 x i32> %a, <4 x i32> %b) {
; CHECK-LABEL: @concat_big(
; CHECK-NEXT:    [[R:%.*]] = shufflevector <4 x i32> [[A:%.*]], <4 x i32> [[B:%.*]], <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
; CHECK-NEXT:    ret <8 x i32> [[R]]
;
  %r = shufflevector <4 x i32> %a, <4 x i32> %b, <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
  ret <8 x i32> %r
}

define <8 x i8> @concatinit_addmul_small(<8 x i8> %a, <8 x i8> %b, <8 x i8> %c) {
; CHECK-LABEL: @concatinit_addmul_small(
; CHECK-NEXT:    [[TMP1:%.*]] = mul <8 x i8> [[A:%.*]], [[B:%.*]]
; CHECK-NEXT:    [[R:%.*]] = add <8 x i8> [[TMP1]], [[C:%.*]]
; CHECK-NEXT:    ret <8 x i8> [[R]]
;
  %ab = shufflevector <8 x i8> %a, <8 x i8> poison, <4 x i32> <i32 0, i32 1, i32 2, i32 3>
  %at = shufflevector <8 x i8> %a, <8 x i8> poison, <4 x i32> <i32 4, i32 5, i32 6, i32 7>
  %bb = shufflevector <8 x i8> %b, <8 x i8> poison, <4 x i32> <i32 0, i32 1, i32 2, i32 3>
  %bt = shufflevector <8 x i8> %b, <8 x i8> poison, <4 x i32> <i32 4, i32 5, i32 6, i32 7>
  %cb = shufflevector <8 x i8> %c, <8 x i8> poison, <4 x i32> <i32 0, i32 1, i32 2, i32 3>
  %ct = shufflevector <8 x i8> %c, <8 x i8> poison, <4 x i32> <i32 4, i32 5, i32 6, i32 7>
  %xb = mul <4 x i8> %ab, %bb
  %xt = mul <4 x i8> %at, %bt
  %yb = add <4 x i8> %xb, %cb
  %yt = add <4 x i8> %xt, %ct
  %r = shufflevector <4 x i8> %yb, <4 x i8> %yt, <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
  ret <8 x i8> %r
}

define <8 x i32> @concatinit_addmul_big(<8 x i32> %a, <8 x i32> %b, <8 x i32> %c) {
; CHECK-LABEL: @concatinit_addmul_big(
; CHECK-NEXT:    [[TMP1:%.*]] = mul <8 x i32> [[A:%.*]], [[B:%.*]]
; CHECK-NEXT:    [[R:%.*]] = add <8 x i32> [[TMP1]], [[C:%.*]]
; CHECK-NEXT:    ret <8 x i32> [[R]]
;
  %ab = shufflevector <8 x i32> %a, <8 x i32> poison, <4 x i32> <i32 0, i32 1, i32 2, i32 3>
  %at = shufflevector <8 x i32> %a, <8 x i32> poison, <4 x i32> <i32 4, i32 5, i32 6, i32 7>
  %bb = shufflevector <8 x i32> %b, <8 x i32> poison, <4 x i32> <i32 0, i32 1, i32 2, i32 3>
  %bt = shufflevector <8 x i32> %b, <8 x i32> poison, <4 x i32> <i32 4, i32 5, i32 6, i32 7>
  %cb = shufflevector <8 x i32> %c, <8 x i32> poison, <4 x i32> <i32 0, i32 1, i32 2, i32 3>
  %ct = shufflevector <8 x i32> %c, <8 x i32> poison, <4 x i32> <i32 4, i32 5, i32 6, i32 7>
  %xb = mul <4 x i32> %ab, %bb
  %xt = mul <4 x i32> %at, %bt
  %yb = add <4 x i32> %xb, %cb
  %yt = add <4 x i32> %xt, %ct
  %r = shufflevector <4 x i32> %yb, <4 x i32> %yt, <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
  ret <8 x i32> %r
}

define <8 x i8> @concata_addmul_small(<4 x i8> %a1, <4 x i8> %a2, <8 x i8> %b, <8 x i8> %c) {
; CHECK-LABEL: @concata_addmul_small(
; CHECK-NEXT:    [[TMP1:%.*]] = shufflevector <4 x i8> [[A1:%.*]], <4 x i8> [[A2:%.*]], <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
; CHECK-NEXT:    [[TMP2:%.*]] = mul <8 x i8> [[TMP1]], [[B:%.*]]
; CHECK-NEXT:    [[R:%.*]] = add <8 x i8> [[TMP2]], [[C:%.*]]
; CHECK-NEXT:    ret <8 x i8> [[R]]
;
  %bb = shufflevector <8 x i8> %b, <8 x i8> poison, <4 x i32> <i32 0, i32 1, i32 2, i32 3>
  %bt = shufflevector <8 x i8> %b, <8 x i8> poison, <4 x i32> <i32 4, i32 5, i32 6, i32 7>
  %cb = shufflevector <8 x i8> %c, <8 x i8> poison, <4 x i32> <i32 0, i32 1, i32 2, i32 3>
  %ct = shufflevector <8 x i8> %c, <8 x i8> poison, <4 x i32> <i32 4, i32 5, i32 6, i32 7>
  %xb = mul <4 x i8> %a1, %bb
  %xt = mul <4 x i8> %a2, %bt
  %yb = add <4 x i8> %xb, %cb
  %yt = add <4 x i8> %xt, %ct
  %r = shufflevector <4 x i8> %yb, <4 x i8> %yt, <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
  ret <8 x i8> %r
}

define <8 x i32> @concata_addmul_big(<4 x i32> %a1, <4 x i32> %a2, <8 x i32> %b, <8 x i32> %c) {
; CHECK-LABEL: @concata_addmul_big(
; CHECK-NEXT:    [[TMP1:%.*]] = shufflevector <4 x i32> [[A1:%.*]], <4 x i32> [[A2:%.*]], <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
; CHECK-NEXT:    [[TMP2:%.*]] = mul <8 x i32> [[TMP1]], [[B:%.*]]
; CHECK-NEXT:    [[R:%.*]] = add <8 x i32> [[TMP2]], [[C:%.*]]
; CHECK-NEXT:    ret <8 x i32> [[R]]
;
  %bb = shufflevector <8 x i32> %b, <8 x i32> poison, <4 x i32> <i32 0, i32 1, i32 2, i32 3>
  %bt = shufflevector <8 x i32> %b, <8 x i32> poison, <4 x i32> <i32 4, i32 5, i32 6, i32 7>
  %cb = shufflevector <8 x i32> %c, <8 x i32> poison, <4 x i32> <i32 0, i32 1, i32 2, i32 3>
  %ct = shufflevector <8 x i32> %c, <8 x i32> poison, <4 x i32> <i32 4, i32 5, i32 6, i32 7>
  %xb = mul <4 x i32> %a1, %bb
  %xt = mul <4 x i32> %a2, %bt
  %yb = add <4 x i32> %xb, %cb
  %yt = add <4 x i32> %xt, %ct
  %r = shufflevector <4 x i32> %yb, <4 x i32> %yt, <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
  ret <8 x i32> %r
}

define <16 x i32> @concata_addmul_bigger(<4 x i32> %a1a, <4 x i32> %a2a, <4 x i32> %a3a, <4 x i32> %a4a, <16 x i32> %b, <16 x i32> %c) {
; CHECK-LABEL: @concata_addmul_bigger(
; CHECK-NEXT:    [[TMP1:%.*]] = shufflevector <4 x i32> [[A4A:%.*]], <4 x i32> [[A3A:%.*]], <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
; CHECK-NEXT:    [[TMP2:%.*]] = shufflevector <4 x i32> [[A2A:%.*]], <4 x i32> [[A1A:%.*]], <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
; CHECK-NEXT:    [[TMP3:%.*]] = shufflevector <8 x i32> [[TMP1]], <8 x i32> [[TMP2]], <16 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7, i32 8, i32 9, i32 10, i32 11, i32 12, i32 13, i32 14, i32 15>
; CHECK-NEXT:    [[TMP4:%.*]] = mul <16 x i32> [[TMP3]], [[B:%.*]]
; CHECK-NEXT:    [[R:%.*]] = add <16 x i32> [[TMP4]], [[C:%.*]]
; CHECK-NEXT:    ret <16 x i32> [[R]]
;
  %a1 = shufflevector <4 x i32> %a1a, <4 x i32> poison, <4 x i32> <i32 3, i32 2, i32 1, i32 0>
  %a2 = shufflevector <4 x i32> %a2a, <4 x i32> poison, <4 x i32> <i32 3, i32 2, i32 1, i32 0>
  %a3 = shufflevector <4 x i32> %a3a, <4 x i32> poison, <4 x i32> <i32 3, i32 2, i32 1, i32 0>
  %a4 = shufflevector <4 x i32> %a4a, <4 x i32> poison, <4 x i32> <i32 3, i32 2, i32 1, i32 0>
  %b1 = shufflevector <16 x i32> %b, <16 x i32> poison, <4 x i32> <i32 15, i32 14, i32 13, i32 12>
  %b2 = shufflevector <16 x i32> %b, <16 x i32> poison, <4 x i32> <i32 11, i32 10, i32 9, i32 8>
  %b3 = shufflevector <16 x i32> %b, <16 x i32> poison, <4 x i32> <i32 7, i32 6, i32 5, i32 4>
  %b4 = shufflevector <16 x i32> %b, <16 x i32> poison, <4 x i32> <i32 3, i32 2, i32 1, i32 0>
  %c1 = shufflevector <16 x i32> %c, <16 x i32> poison, <4 x i32> <i32 15, i32 14, i32 13, i32 12>
  %c2 = shufflevector <16 x i32> %c, <16 x i32> poison, <4 x i32> <i32 11, i32 10, i32 9, i32 8>
  %c3 = shufflevector <16 x i32> %c, <16 x i32> poison, <4 x i32> <i32 7, i32 6, i32 5, i32 4>
  %c4 = shufflevector <16 x i32> %c, <16 x i32> poison, <4 x i32> <i32 3, i32 2, i32 1, i32 0>
  %x1 = mul <4 x i32> %a1, %b1
  %x2 = mul <4 x i32> %a2, %b2
  %x3 = mul <4 x i32> %a3, %b3
  %x4 = mul <4 x i32> %a4, %b4
  %y1 = add <4 x i32> %x1, %c1
  %y2 = add <4 x i32> %x2, %c2
  %y3 = add <4 x i32> %x3, %c3
  %y4 = add <4 x i32> %x4, %c4
  %cc1 = shufflevector <4 x i32> %y1, <4 x i32> %y2, <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
  %cc2 = shufflevector <4 x i32> %y3, <4 x i32> %y4, <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
  %r = shufflevector <8 x i32> %cc1, <8 x i32> %cc2, <16 x i32> <i32 15, i32 14, i32 13, i32 12, i32 11, i32 10, i32 9, i32 8, i32 7, i32 6, i32 5, i32 4, i32 3, i32 2, i32 1, i32 0>
  ret <16 x i32> %r
}

define <16 x i32> @concata_addmul_bigger_undef(<4 x i32> %a1a, <4 x i32> %a2a, <4 x i32> %a3a, <4 x i32> %a4a, <16 x i32> %b, <16 x i32> %c) {
; CHECK-LABEL: @concata_addmul_bigger_undef(
; CHECK-NEXT:    [[A1:%.*]] = shufflevector <4 x i32> [[A1A:%.*]], <4 x i32> poison, <4 x i32> <i32 3, i32 2, i32 1, i32 0>
; CHECK-NEXT:    [[A2:%.*]] = shufflevector <4 x i32> [[A2A:%.*]], <4 x i32> poison, <4 x i32> <i32 3, i32 2, i32 1, i32 0>
; CHECK-NEXT:    [[A3:%.*]] = shufflevector <4 x i32> [[A3A:%.*]], <4 x i32> poison, <4 x i32> <i32 3, i32 2, i32 1, i32 0>
; CHECK-NEXT:    [[A4:%.*]] = shufflevector <4 x i32> [[A4A:%.*]], <4 x i32> poison, <4 x i32> <i32 3, i32 2, i32 1, i32 0>
; CHECK-NEXT:    [[B1:%.*]] = shufflevector <16 x i32> [[B:%.*]], <16 x i32> poison, <4 x i32> <i32 15, i32 14, i32 13, i32 12>
; CHECK-NEXT:    [[B2:%.*]] = shufflevector <16 x i32> [[B]], <16 x i32> poison, <4 x i32> <i32 11, i32 10, i32 9, i32 8>
; CHECK-NEXT:    [[B3:%.*]] = shufflevector <16 x i32> [[B]], <16 x i32> poison, <4 x i32> <i32 7, i32 6, i32 5, i32 4>
; CHECK-NEXT:    [[B4:%.*]] = shufflevector <16 x i32> [[B]], <16 x i32> poison, <4 x i32> <i32 3, i32 2, i32 1, i32 0>
; CHECK-NEXT:    [[C1:%.*]] = shufflevector <16 x i32> [[C:%.*]], <16 x i32> poison, <4 x i32> <i32 15, i32 14, i32 13, i32 12>
; CHECK-NEXT:    [[C2:%.*]] = shufflevector <16 x i32> [[C]], <16 x i32> poison, <4 x i32> <i32 11, i32 10, i32 9, i32 8>
; CHECK-NEXT:    [[C3:%.*]] = shufflevector <16 x i32> [[C]], <16 x i32> poison, <4 x i32> <i32 7, i32 6, i32 5, i32 4>
; CHECK-NEXT:    [[C4:%.*]] = shufflevector <16 x i32> [[C]], <16 x i32> poison, <4 x i32> <i32 3, i32 2, i32 1, i32 0>
; CHECK-NEXT:    [[X1:%.*]] = mul <4 x i32> [[A1]], [[B1]]
; CHECK-NEXT:    [[X2:%.*]] = mul <4 x i32> [[A2]], [[B2]]
; CHECK-NEXT:    [[X3:%.*]] = mul <4 x i32> [[A3]], [[B3]]
; CHECK-NEXT:    [[X4:%.*]] = mul <4 x i32> [[A4]], [[B4]]
; CHECK-NEXT:    [[Y1:%.*]] = add <4 x i32> [[X1]], [[C1]]
; CHECK-NEXT:    [[Y2:%.*]] = add <4 x i32> [[X2]], [[C2]]
; CHECK-NEXT:    [[Y3:%.*]] = add <4 x i32> [[X3]], [[C3]]
; CHECK-NEXT:    [[Y4:%.*]] = add <4 x i32> [[X4]], [[C4]]
; CHECK-NEXT:    [[CC1:%.*]] = shufflevector <4 x i32> [[Y1]], <4 x i32> [[Y2]], <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 poison, i32 poison, i32 poison, i32 7>
; CHECK-NEXT:    [[CC2:%.*]] = shufflevector <4 x i32> [[Y3]], <4 x i32> [[Y4]], <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
; CHECK-NEXT:    [[R:%.*]] = shufflevector <8 x i32> [[CC1]], <8 x i32> [[CC2]], <16 x i32> <i32 15, i32 14, i32 13, i32 12, i32 11, i32 10, i32 9, i32 8, i32 7, i32 6, i32 5, i32 4, i32 3, i32 2, i32 1, i32 0>
; CHECK-NEXT:    ret <16 x i32> [[R]]
;
  %a1 = shufflevector <4 x i32> %a1a, <4 x i32> poison, <4 x i32> <i32 3, i32 2, i32 1, i32 0>
  %a2 = shufflevector <4 x i32> %a2a, <4 x i32> poison, <4 x i32> <i32 3, i32 2, i32 1, i32 0>
  %a3 = shufflevector <4 x i32> %a3a, <4 x i32> poison, <4 x i32> <i32 3, i32 2, i32 1, i32 0>
  %a4 = shufflevector <4 x i32> %a4a, <4 x i32> poison, <4 x i32> <i32 3, i32 2, i32 1, i32 0>
  %b1 = shufflevector <16 x i32> %b, <16 x i32> poison, <4 x i32> <i32 15, i32 14, i32 13, i32 12>
  %b2 = shufflevector <16 x i32> %b, <16 x i32> poison, <4 x i32> <i32 11, i32 10, i32 9, i32 8>
  %b3 = shufflevector <16 x i32> %b, <16 x i32> poison, <4 x i32> <i32 7, i32 6, i32 5, i32 4>
  %b4 = shufflevector <16 x i32> %b, <16 x i32> poison, <4 x i32> <i32 3, i32 2, i32 1, i32 0>
  %c1 = shufflevector <16 x i32> %c, <16 x i32> poison, <4 x i32> <i32 15, i32 14, i32 13, i32 12>
  %c2 = shufflevector <16 x i32> %c, <16 x i32> poison, <4 x i32> <i32 11, i32 10, i32 9, i32 8>
  %c3 = shufflevector <16 x i32> %c, <16 x i32> poison, <4 x i32> <i32 7, i32 6, i32 5, i32 4>
  %c4 = shufflevector <16 x i32> %c, <16 x i32> poison, <4 x i32> <i32 3, i32 2, i32 1, i32 0>
  %x1 = mul <4 x i32> %a1, %b1
  %x2 = mul <4 x i32> %a2, %b2
  %x3 = mul <4 x i32> %a3, %b3
  %x4 = mul <4 x i32> %a4, %b4
  %y1 = add <4 x i32> %x1, %c1
  %y2 = add <4 x i32> %x2, %c2
  %y3 = add <4 x i32> %x3, %c3
  %y4 = add <4 x i32> %x4, %c4
  %cc1 = shufflevector <4 x i32> %y1, <4 x i32> %y2, <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 poison, i32 poison, i32 poison, i32 7>
  %cc2 = shufflevector <4 x i32> %y3, <4 x i32> %y4, <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
  %r = shufflevector <8 x i32> %cc1, <8 x i32> %cc2, <16 x i32> <i32 15, i32 14, i32 13, i32 12, i32 11, i32 10, i32 9, i32 8, i32 7, i32 6, i32 5, i32 4, i32 3, i32 2, i32 1, i32 0>
  ret <16 x i32> %r
}

define <16 x i32> @splat_concat(<4 x i32> %a1a, <4 x i32> %a2a, <4 x i32> %a3a, <4 x i32> %a4a, <16 x i32> %b, <16 x i32> %c) {
; CHECK-LABEL: @splat_concat(
; CHECK-NEXT:    [[TMP1:%.*]] = shufflevector <4 x i32> [[A4A:%.*]], <4 x i32> [[A3A:%.*]], <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
; CHECK-NEXT:    [[TMP2:%.*]] = shufflevector <4 x i32> [[A2A:%.*]], <4 x i32> [[A1A:%.*]], <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
; CHECK-NEXT:    [[TMP3:%.*]] = shufflevector <8 x i32> [[TMP1]], <8 x i32> [[TMP2]], <16 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7, i32 8, i32 9, i32 10, i32 11, i32 12, i32 13, i32 14, i32 15>
; CHECK-NEXT:    [[TMP4:%.*]] = mul <16 x i32> [[TMP3]], [[B:%.*]]
; CHECK-NEXT:    [[TMP5:%.*]] = add <16 x i32> [[TMP4]], [[C:%.*]]
; CHECK-NEXT:    [[TMP6:%.*]] = shufflevector <4 x i32> [[A4A]], <4 x i32> poison, <16 x i32> zeroinitializer
; CHECK-NEXT:    [[R:%.*]] = xor <16 x i32> [[TMP5]], [[TMP6]]
; CHECK-NEXT:    ret <16 x i32> [[R]]
;
  %a1 = shufflevector <4 x i32> %a1a, <4 x i32> poison, <4 x i32> <i32 3, i32 2, i32 1, i32 0>
  %a2 = shufflevector <4 x i32> %a2a, <4 x i32> poison, <4 x i32> <i32 3, i32 2, i32 1, i32 0>
  %a3 = shufflevector <4 x i32> %a3a, <4 x i32> poison, <4 x i32> <i32 3, i32 2, i32 1, i32 0>
  %a4 = shufflevector <4 x i32> %a4a, <4 x i32> poison, <4 x i32> <i32 3, i32 2, i32 1, i32 0>
  %b1 = shufflevector <16 x i32> %b, <16 x i32> poison, <4 x i32> <i32 15, i32 14, i32 13, i32 12>
  %b2 = shufflevector <16 x i32> %b, <16 x i32> poison, <4 x i32> <i32 11, i32 10, i32 9, i32 8>
  %b3 = shufflevector <16 x i32> %b, <16 x i32> poison, <4 x i32> <i32 7, i32 6, i32 5, i32 4>
  %b4 = shufflevector <16 x i32> %b, <16 x i32> poison, <4 x i32> <i32 3, i32 2, i32 1, i32 0>
  %c1 = shufflevector <16 x i32> %c, <16 x i32> poison, <4 x i32> <i32 15, i32 14, i32 13, i32 12>
  %c2 = shufflevector <16 x i32> %c, <16 x i32> poison, <4 x i32> <i32 11, i32 10, i32 9, i32 8>
  %c3 = shufflevector <16 x i32> %c, <16 x i32> poison, <4 x i32> <i32 7, i32 6, i32 5, i32 4>
  %c4 = shufflevector <16 x i32> %c, <16 x i32> poison, <4 x i32> <i32 3, i32 2, i32 1, i32 0>
  %splata = shufflevector <4 x i32> %a4a, <4 x i32> poison, <4 x i32> zeroinitializer
  %x1 = mul <4 x i32> %a1, %b1
  %x2 = mul <4 x i32> %a2, %b2
  %x3 = mul <4 x i32> %a3, %b3
  %x4 = mul <4 x i32> %a4, %b4
  %y1 = add <4 x i32> %x1, %c1
  %y2 = add <4 x i32> %x2, %c2
  %y3 = add <4 x i32> %x3, %c3
  %y4 = add <4 x i32> %x4, %c4
  %z1 = xor <4 x i32> %y1, %splata
  %z2 = xor <4 x i32> %y2, %splata
  %z3 = xor <4 x i32> %y3, %splata
  %z4 = xor <4 x i32> %y4, %splata
  %cc1 = shufflevector <4 x i32> %z1, <4 x i32> %z2, <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
  %cc2 = shufflevector <4 x i32> %z3, <4 x i32> %z4, <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
  %r = shufflevector <8 x i32> %cc1, <8 x i32> %cc2, <16 x i32> <i32 15, i32 14, i32 13, i32 12, i32 11, i32 10, i32 9, i32 8, i32 7, i32 6, i32 5, i32 4, i32 3, i32 2, i32 1, i32 0>
  ret <16 x i32> %r
}

define <16 x i32> @two_concats(<4 x i32> %a1a, <4 x i32> %a2a, <4 x i32> %a3a, <4 x i32> %a4a, <16 x i32> %b, <16 x i32> %c) {
; CHECK-LABEL: @two_concats(
; CHECK-NEXT:    [[TMP1:%.*]] = shufflevector <4 x i32> [[A4A:%.*]], <4 x i32> [[A3A:%.*]], <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
; CHECK-NEXT:    [[TMP2:%.*]] = shufflevector <4 x i32> [[A2A:%.*]], <4 x i32> [[A1A:%.*]], <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
; CHECK-NEXT:    [[TMP3:%.*]] = shufflevector <8 x i32> [[TMP1]], <8 x i32> [[TMP2]], <16 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7, i32 8, i32 9, i32 10, i32 11, i32 12, i32 13, i32 14, i32 15>
; CHECK-NEXT:    [[TMP4:%.*]] = mul <16 x i32> [[TMP3]], [[B:%.*]]
; CHECK-NEXT:    [[TMP5:%.*]] = add <16 x i32> [[TMP4]], [[C:%.*]]
; CHECK-NEXT:    [[TMP6:%.*]] = shufflevector <4 x i32> [[A1A]], <4 x i32> [[A1A]], <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
; CHECK-NEXT:    [[TMP7:%.*]] = shufflevector <4 x i32> [[A1A]], <4 x i32> [[A1A]], <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
; CHECK-NEXT:    [[TMP8:%.*]] = shufflevector <8 x i32> [[TMP6]], <8 x i32> [[TMP7]], <16 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7, i32 8, i32 9, i32 10, i32 11, i32 12, i32 13, i32 14, i32 15>
; CHECK-NEXT:    [[R:%.*]] = xor <16 x i32> [[TMP5]], [[TMP8]]
; CHECK-NEXT:    ret <16 x i32> [[R]]
;
  %a1 = shufflevector <4 x i32> %a1a, <4 x i32> poison, <4 x i32> <i32 3, i32 2, i32 1, i32 0>
  %a2 = shufflevector <4 x i32> %a2a, <4 x i32> poison, <4 x i32> <i32 3, i32 2, i32 1, i32 0>
  %a3 = shufflevector <4 x i32> %a3a, <4 x i32> poison, <4 x i32> <i32 3, i32 2, i32 1, i32 0>
  %a4 = shufflevector <4 x i32> %a4a, <4 x i32> poison, <4 x i32> <i32 3, i32 2, i32 1, i32 0>
  %b1 = shufflevector <16 x i32> %b, <16 x i32> poison, <4 x i32> <i32 15, i32 14, i32 13, i32 12>
  %b2 = shufflevector <16 x i32> %b, <16 x i32> poison, <4 x i32> <i32 11, i32 10, i32 9, i32 8>
  %b3 = shufflevector <16 x i32> %b, <16 x i32> poison, <4 x i32> <i32 7, i32 6, i32 5, i32 4>
  %b4 = shufflevector <16 x i32> %b, <16 x i32> poison, <4 x i32> <i32 3, i32 2, i32 1, i32 0>
  %c1 = shufflevector <16 x i32> %c, <16 x i32> poison, <4 x i32> <i32 15, i32 14, i32 13, i32 12>
  %c2 = shufflevector <16 x i32> %c, <16 x i32> poison, <4 x i32> <i32 11, i32 10, i32 9, i32 8>
  %c3 = shufflevector <16 x i32> %c, <16 x i32> poison, <4 x i32> <i32 7, i32 6, i32 5, i32 4>
  %c4 = shufflevector <16 x i32> %c, <16 x i32> poison, <4 x i32> <i32 3, i32 2, i32 1, i32 0>
  %x1 = mul <4 x i32> %a1, %b1
  %x2 = mul <4 x i32> %a2, %b2
  %x3 = mul <4 x i32> %a3, %b3
  %x4 = mul <4 x i32> %a4, %b4
  %y1 = add <4 x i32> %x1, %c1
  %y2 = add <4 x i32> %x2, %c2
  %y3 = add <4 x i32> %x3, %c3
  %y4 = add <4 x i32> %x4, %c4
  %z1 = xor <4 x i32> %y1, %a1
  %z2 = xor <4 x i32> %y2, %a1
  %z3 = xor <4 x i32> %y3, %a1
  %z4 = xor <4 x i32> %y4, %a1
  %cc1 = shufflevector <4 x i32> %z1, <4 x i32> %z2, <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
  %cc2 = shufflevector <4 x i32> %z3, <4 x i32> %z4, <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
  %r = shufflevector <8 x i32> %cc1, <8 x i32> %cc2, <16 x i32> <i32 15, i32 14, i32 13, i32 12, i32 11, i32 10, i32 9, i32 8, i32 7, i32 6, i32 5, i32 4, i32 3, i32 2, i32 1, i32 0>
  ret <16 x i32> %r
}


define <16 x double> @konkat(<16 x double> %wide.vec, <16 x double> %wide.vec115, <2 x double> %l27, <2 x double> %l28, <2 x double> %l29, <2 x double> %l30) {
; CHECK-LABEL: @konkat(
; CHECK-NEXT:    [[TMP1:%.*]] = shufflevector <2 x double> [[L27:%.*]], <2 x double> [[L28:%.*]], <4 x i32> <i32 0, i32 1, i32 2, i32 3>
; CHECK-NEXT:    [[TMP2:%.*]] = shufflevector <2 x double> [[L29:%.*]], <2 x double> [[L30:%.*]], <4 x i32> <i32 0, i32 1, i32 2, i32 3>
; CHECK-NEXT:    [[TMP3:%.*]] = shufflevector <2 x double> [[L27]], <2 x double> [[L28]], <4 x i32> <i32 0, i32 1, i32 2, i32 3>
; CHECK-NEXT:    [[TMP4:%.*]] = shufflevector <2 x double> [[L29]], <2 x double> [[L30]], <4 x i32> <i32 0, i32 1, i32 2, i32 3>
; CHECK-NEXT:    [[TMP5:%.*]] = shufflevector <4 x double> [[TMP1]], <4 x double> [[TMP2]], <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
; CHECK-NEXT:    [[TMP6:%.*]] = shufflevector <4 x double> [[TMP3]], <4 x double> [[TMP4]], <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
; CHECK-NEXT:    [[TMP7:%.*]] = shufflevector <8 x double> [[TMP5]], <8 x double> [[TMP6]], <16 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7, i32 8, i32 9, i32 10, i32 11, i32 12, i32 13, i32 14, i32 15>
; CHECK-NEXT:    [[TMP8:%.*]] = fmul reassoc nsz contract <16 x double> [[WIDE_VEC:%.*]], [[TMP7]]
; CHECK-NEXT:    [[INTERLEAVED_VEC:%.*]] = fadd reassoc nsz contract <16 x double> [[WIDE_VEC115:%.*]], [[TMP8]]
; CHECK-NEXT:    ret <16 x double> [[INTERLEAVED_VEC]]
;
  %broadcast.splat = shufflevector <2 x double> %l27, <2 x double> poison, <2 x i32> zeroinitializer
  %l32 = shufflevector <2 x double> %l27, <2 x double> poison, <2 x i32> <i32 1, i32 poison>
  %broadcast.splat102 = shufflevector <2 x double> %l32, <2 x double> poison, <2 x i32> zeroinitializer
  %broadcast.splat104 = shufflevector <2 x double> %l28, <2 x double> poison, <2 x i32> zeroinitializer
  %l33 = shufflevector <2 x double> %l28, <2 x double> poison, <2 x i32> <i32 1, i32 poison>
  %broadcast.splat106 = shufflevector <2 x double> %l33, <2 x double> poison, <2 x i32> zeroinitializer
  %broadcast.splat108 = shufflevector <2 x double> %l29, <2 x double> poison, <2 x i32> zeroinitializer
  %l34 = shufflevector <2 x double> %l29, <2 x double> poison, <2 x i32> <i32 1, i32 poison>
  %broadcast.splat110 = shufflevector <2 x double> %l34, <2 x double> poison, <2 x i32> zeroinitializer
  %broadcast.splat112 = shufflevector <2 x double> %l30, <2 x double> poison, <2 x i32> zeroinitializer
  %l35 = shufflevector <2 x double> %l30, <2 x double> poison, <2 x i32> <i32 1, i32 poison>
  %broadcast.splat114 = shufflevector <2 x double> %l35, <2 x double> poison, <2 x i32> zeroinitializer
  %strided.vec = shufflevector <16 x double> %wide.vec, <16 x double> poison, <2 x i32> <i32 0, i32 8>
  %strided.vec94 = shufflevector <16 x double> %wide.vec, <16 x double> poison, <2 x i32> <i32 1, i32 9>
  %strided.vec95 = shufflevector <16 x double> %wide.vec, <16 x double> poison, <2 x i32> <i32 2, i32 10>
  %strided.vec96 = shufflevector <16 x double> %wide.vec, <16 x double> poison, <2 x i32> <i32 3, i32 11>
  %strided.vec97 = shufflevector <16 x double> %wide.vec, <16 x double> poison, <2 x i32> <i32 4, i32 12>
  %strided.vec98 = shufflevector <16 x double> %wide.vec, <16 x double> poison, <2 x i32> <i32 5, i32 13>
  %strided.vec99 = shufflevector <16 x double> %wide.vec, <16 x double> poison, <2 x i32> <i32 6, i32 14>
  %strided.vec100 = shufflevector <16 x double> %wide.vec, <16 x double> poison, <2 x i32> <i32 7, i32 15>
  %37 = fmul reassoc nsz contract <2 x double> %strided.vec, %broadcast.splat
  %38 = fmul reassoc nsz contract <2 x double> %strided.vec94, %broadcast.splat102
  %39 = fmul reassoc nsz contract <2 x double> %strided.vec95, %broadcast.splat104
  %40 = fmul reassoc nsz contract <2 x double> %strided.vec96, %broadcast.splat106
  %41 = fmul reassoc nsz contract <2 x double> %strided.vec97, %broadcast.splat108
  %42 = fmul reassoc nsz contract <2 x double> %strided.vec98, %broadcast.splat110
  %43 = fmul reassoc nsz contract <2 x double> %strided.vec99, %broadcast.splat112
  %44 = fmul reassoc nsz contract <2 x double> %strided.vec100, %broadcast.splat114
  %strided.vec116 = shufflevector <16 x double> %wide.vec115, <16 x double> poison, <2 x i32> <i32 0, i32 8>
  %strided.vec117 = shufflevector <16 x double> %wide.vec115, <16 x double> poison, <2 x i32> <i32 1, i32 9>
  %strided.vec118 = shufflevector <16 x double> %wide.vec115, <16 x double> poison, <2 x i32> <i32 2, i32 10>
  %strided.vec119 = shufflevector <16 x double> %wide.vec115, <16 x double> poison, <2 x i32> <i32 3, i32 11>
  %strided.vec120 = shufflevector <16 x double> %wide.vec115, <16 x double> poison, <2 x i32> <i32 4, i32 12>
  %strided.vec121 = shufflevector <16 x double> %wide.vec115, <16 x double> poison, <2 x i32> <i32 5, i32 13>
  %strided.vec122 = shufflevector <16 x double> %wide.vec115, <16 x double> poison, <2 x i32> <i32 6, i32 14>
  %strided.vec123 = shufflevector <16 x double> %wide.vec115, <16 x double> poison, <2 x i32> <i32 7, i32 15>
  %46 = fadd reassoc nsz contract <2 x double> %strided.vec116, %37
  %47 = fadd reassoc nsz contract <2 x double> %strided.vec117, %38
  %48 = fadd reassoc nsz contract <2 x double> %strided.vec118, %39
  %49 = fadd reassoc nsz contract <2 x double> %strided.vec119, %40
  %50 = fadd reassoc nsz contract <2 x double> %strided.vec120, %41
  %51 = fadd reassoc nsz contract <2 x double> %strided.vec121, %42
  %52 = fadd reassoc nsz contract <2 x double> %strided.vec122, %43
  %53 = fadd reassoc nsz contract <2 x double> %strided.vec123, %44
  %54 = shufflevector <2 x double> %46, <2 x double> %47, <4 x i32> <i32 0, i32 1, i32 2, i32 3>
  %55 = shufflevector <2 x double> %48, <2 x double> %49, <4 x i32> <i32 0, i32 1, i32 2, i32 3>
  %56 = shufflevector <2 x double> %50, <2 x double> %51, <4 x i32> <i32 0, i32 1, i32 2, i32 3>
  %57 = shufflevector <2 x double> %52, <2 x double> %53, <4 x i32> <i32 0, i32 1, i32 2, i32 3>
  %58 = shufflevector <4 x double> %54, <4 x double> %55, <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
  %59 = shufflevector <4 x double> %56, <4 x double> %57, <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
  %interleaved.vec = shufflevector <8 x double> %58, <8 x double> %59, <16 x i32> <i32 0, i32 2, i32 4, i32 6, i32 8, i32 10, i32 12, i32 14, i32 1, i32 3, i32 5, i32 7, i32 9, i32 11, i32 13, i32 15>
  ret <16 x double> %interleaved.vec
}
