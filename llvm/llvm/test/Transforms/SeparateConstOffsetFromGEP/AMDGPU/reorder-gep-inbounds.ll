; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 3
; RUN: opt -mtriple=amdgcn-amd-amdhsa -mcpu=gfx90a -S -passes=separate-const-offset-from-gep < %s | FileCheck %s

define void @inboundsPossiblyNegative(ptr %in.ptr, i64 %in.idx1) {
; CHECK-LABEL: define void @inboundsPossiblyNegative(
; CHECK-SAME: ptr [[IN_PTR:%.*]], i64 [[IN_IDX1:%.*]]) #[[ATTR0:[0-9]+]] {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP0:%.*]] = getelementptr <2 x i8>, ptr [[IN_PTR]], i64 [[IN_IDX1]]
; CHECK-NEXT:    [[TMP1:%.*]] = getelementptr <2 x i8>, ptr [[TMP0]], i64 1
; CHECK-NEXT:    ret void
;
entry:
  %const1 = getelementptr inbounds <2 x i8>, ptr %in.ptr, i64 1
  %idx1 = getelementptr inbounds <2 x i8>, ptr %const1, i64 %in.idx1
  ret void
}

define void @inboundsNonNegative_nonCanonical(ptr %in.ptr, i32 %in.idx1) {
; CHECK-LABEL: define void @inboundsNonNegative_nonCanonical(
; CHECK-SAME: ptr [[IN_PTR:%.*]], i32 [[IN_IDX1:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[IN_IDX1_NNEG1:%.*]] = and i32 [[IN_IDX1]], 2147483647
; CHECK-NEXT:    [[IN_IDX1_NNEG:%.*]] = sext i32 [[IN_IDX1_NNEG1]] to i64
; CHECK-NEXT:    [[TMP0:%.*]] = getelementptr inbounds <2 x i8>, ptr [[IN_PTR]], i64 [[IN_IDX1_NNEG]]
; CHECK-NEXT:    [[TMP1:%.*]] = getelementptr inbounds <2 x i8>, ptr [[TMP0]], i32 1
; CHECK-NEXT:    ret void
;
entry:
  %in.idx1.nneg = and i32 %in.idx1, 2147483647
  %const1 = getelementptr inbounds <2 x i8>, ptr %in.ptr, i32 1
  %idx1 = getelementptr inbounds <2 x i8>, ptr %const1, i32 %in.idx1.nneg
  ret void
}

define void @inboundsNonNegative(ptr %in.ptr, i64 %in.idx1) {
; CHECK-LABEL: define void @inboundsNonNegative(
; CHECK-SAME: ptr [[IN_PTR:%.*]], i64 [[IN_IDX1:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[IDXPROM:%.*]] = and i64 [[IN_IDX1]], 9223372036854775807
; CHECK-NEXT:    [[TMP0:%.*]] = getelementptr inbounds <2 x i8>, ptr [[IN_PTR]], i64 [[IDXPROM]]
; CHECK-NEXT:    [[TMP1:%.*]] = getelementptr inbounds <2 x i8>, ptr [[TMP0]], i64 1
; CHECK-NEXT:    ret void
;
entry:
  %in.idx1.nneg = and i64 %in.idx1, 9223372036854775807
  %const1 = getelementptr inbounds <2 x i8>, ptr %in.ptr, i64 1
  %idx1 = getelementptr inbounds <2 x i8>, ptr %const1, i64 %in.idx1.nneg
  ret void
}

define void @inboundsNonchained(ptr %in.ptr, i64 %in.idx1) {
; CHECK-LABEL: define void @inboundsNonchained(
; CHECK-SAME: ptr [[IN_PTR:%.*]], i64 [[IN_IDX1:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[IDXPROM:%.*]] = and i64 [[IN_IDX1]], 9223372036854775807
; CHECK-NEXT:    [[TMP0:%.*]] = getelementptr <2 x i8>, ptr [[IN_PTR]], i64 [[IDXPROM]]
; CHECK-NEXT:    [[TMP1:%.*]] = getelementptr <2 x i8>, ptr [[TMP0]], i64 1
; CHECK-NEXT:    ret void
;
entry:
  %in.idx1.nneg = and i64 %in.idx1, 9223372036854775807
  %const1 = getelementptr inbounds <2 x i8>, ptr %in.ptr, i64 1
  %idx1 = getelementptr <2 x i8>, ptr %const1, i64 %in.idx1.nneg
  ret void
}

define void @inboundsNonNegativeType_i16i8(ptr %in.ptr, i64 %in.idx1) {
; CHECK-LABEL: define void @inboundsNonNegativeType_i16i8(
; CHECK-SAME: ptr [[IN_PTR:%.*]], i64 [[IN_IDX1:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[IDXPROM:%.*]] = and i64 [[IN_IDX1]], 9223372036854775807
; CHECK-NEXT:    [[TMP0:%.*]] = getelementptr inbounds i8, ptr [[IN_PTR]], i64 [[IDXPROM]]
; CHECK-NEXT:    [[TMP1:%.*]] = getelementptr inbounds i16, ptr [[TMP0]], i64 1024
; CHECK-NEXT:    ret void
;
entry:
  %in.idx1.nneg = and i64 %in.idx1, 9223372036854775807
  %const1 = getelementptr inbounds i16, ptr %in.ptr, i64 1024
  %idx1 = getelementptr inbounds i8, ptr %const1, i64 %in.idx1.nneg
  ret void
}

define void @inboundsNonNegative_i8i16(ptr %in.ptr, i64 %in.idx1) {
; CHECK-LABEL: define void @inboundsNonNegative_i8i16(
; CHECK-SAME: ptr [[IN_PTR:%.*]], i64 [[IN_IDX1:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[IDXPROM:%.*]] = and i64 [[IN_IDX1]], 9223372036854775807
; CHECK-NEXT:    [[TMP0:%.*]] = getelementptr inbounds i16, ptr [[IN_PTR]], i64 [[IDXPROM]]
; CHECK-NEXT:    [[TMP1:%.*]] = getelementptr inbounds i8, ptr [[TMP0]], i64 1024
; CHECK-NEXT:    ret void
;
entry:
  %in.idx1.nneg = and i64 %in.idx1, 9223372036854775807
  %const1 = getelementptr inbounds i8, ptr %in.ptr, i64 1024
  %idx1 = getelementptr inbounds i16, ptr %const1, i64 %in.idx1.nneg
  ret void
}

define void @inboundsNonchained_first(ptr %in.ptr, i64 %in.idx1) {
; CHECK-LABEL: define void @inboundsNonchained_first(
; CHECK-SAME: ptr [[IN_PTR:%.*]], i64 [[IN_IDX1:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[IN_IDX1_NNEG:%.*]] = and i64 [[IN_IDX1]], 9223372036854775807
; CHECK-NEXT:    [[TMP0:%.*]] = getelementptr i32, ptr [[IN_PTR]], i64 [[IN_IDX1_NNEG]]
; CHECK-NEXT:    [[TMP1:%.*]] = getelementptr i8, ptr [[TMP0]], i64 1024
; CHECK-NEXT:    ret void
;
entry:
  %in.idx1.nneg = and i64 %in.idx1, 9223372036854775807
  %const1 = getelementptr inbounds i8, ptr %in.ptr, i64 1024
  %idx1 = getelementptr i32, ptr %const1, i64 %in.idx1.nneg
  ret void
}

define void @inboundsNonchained_second(ptr %in.ptr, i64 %in.idx1) {
; CHECK-LABEL: define void @inboundsNonchained_second(
; CHECK-SAME: ptr [[IN_PTR:%.*]], i64 [[IN_IDX1:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[IN_IDX1_NNEG:%.*]] = and i64 [[IN_IDX1]], 9223372036854775807
; CHECK-NEXT:    [[TMP0:%.*]] = getelementptr i64, ptr [[IN_PTR]], i64 [[IN_IDX1_NNEG]]
; CHECK-NEXT:    [[TMP1:%.*]] = getelementptr i8, ptr [[TMP0]], i64 1024
; CHECK-NEXT:    ret void
;
entry:
  %in.idx1.nneg = and i64 %in.idx1, 9223372036854775807
  %const1 = getelementptr i8, ptr %in.ptr, i64 1024
  %idx1 = getelementptr inbounds i64, ptr %const1, i64 %in.idx1.nneg
  ret void
}

define void @notInbounds(ptr %in.ptr, i64 %in.idx1) {
; CHECK-LABEL: define void @notInbounds(
; CHECK-SAME: ptr [[IN_PTR:%.*]], i64 [[IN_IDX1:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[IN_IDX1_NNEG:%.*]] = and i64 [[IN_IDX1]], 9223372036854775807
; CHECK-NEXT:    [[TMP0:%.*]] = getelementptr i128, ptr [[IN_PTR]], i64 [[IN_IDX1_NNEG]]
; CHECK-NEXT:    [[TMP1:%.*]] = getelementptr i8, ptr [[TMP0]], i64 1024
; CHECK-NEXT:    ret void
;
entry:
  %in.idx1.nneg = and i64 %in.idx1, 9223372036854775807
  %const1 = getelementptr i8, ptr %in.ptr, i64 1024
  %idx1 = getelementptr i128, ptr %const1, i64 %in.idx1.nneg
  ret void
}

define void @vectorType1(ptr %in.ptr, i64 %in.idx1) {
; CHECK-LABEL: define void @vectorType1(
; CHECK-SAME: ptr [[IN_PTR:%.*]], i64 [[IN_IDX1:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[IN_IDX1_NNEG:%.*]] = and i64 [[IN_IDX1]], 2147483647
; CHECK-NEXT:    [[TMP0:%.*]] = getelementptr inbounds <2 x i8>, ptr [[IN_PTR]], i64 [[IN_IDX1_NNEG]]
; CHECK-NEXT:    [[TMP1:%.*]] = getelementptr inbounds <4 x i8>, ptr [[TMP0]], i32 3
; CHECK-NEXT:    ret void
;
entry:
  %in.idx1.nneg = and i64 %in.idx1, 2147483647
  %const1 = getelementptr inbounds <4 x i8>, ptr %in.ptr, i32 3
  %idx1 = getelementptr inbounds <2 x i8>, ptr %const1, i64 %in.idx1.nneg
  ret void
}

define void @vectorType2(ptr %in.ptr, i64 %in.idx1) {
; CHECK-LABEL: define void @vectorType2(
; CHECK-SAME: ptr [[IN_PTR:%.*]], i64 [[IN_IDX1:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[IN_IDX1_NNEG:%.*]] = and i64 [[IN_IDX1]], 2147483647
; CHECK-NEXT:    [[TMP0:%.*]] = getelementptr inbounds <4 x half>, ptr [[IN_PTR]], i64 [[IN_IDX1_NNEG]]
; CHECK-NEXT:    [[TMP1:%.*]] = getelementptr inbounds <4 x i8>, ptr [[TMP0]], i32 1
; CHECK-NEXT:    ret void
;
entry:
  %in.idx1.nneg = and i64 %in.idx1, 2147483647
  %const1 = getelementptr inbounds <4 x i8>, ptr %in.ptr, i32 1
  %idx1 = getelementptr inbounds <4 x half>, ptr %const1, i64 %in.idx1.nneg
  ret void
}

define void @vectorType3(ptr %in.ptr, i64 %in.idx1) {
; CHECK-LABEL: define void @vectorType3(
; CHECK-SAME: ptr [[IN_PTR:%.*]], i64 [[IN_IDX1:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[IN_IDX1_NNEG:%.*]] = and i64 [[IN_IDX1]], 2147483647
; CHECK-NEXT:    [[TMP0:%.*]] = getelementptr inbounds ptr, ptr [[IN_PTR]], i64 [[IN_IDX1_NNEG]]
; CHECK-NEXT:    [[TMP1:%.*]] = getelementptr inbounds <4 x ptr>, ptr [[TMP0]], i32 1
; CHECK-NEXT:    ret void
;
entry:
  %in.idx1.nneg = and i64 %in.idx1, 2147483647
  %const1 = getelementptr inbounds <4 x ptr>, ptr %in.ptr, i32 1
  %idx1 = getelementptr inbounds ptr, ptr %const1, i64 %in.idx1.nneg
  ret void
}

define void @vectorType4(ptr %in.ptr, i64 %in.idx1) {
; CHECK-LABEL: define void @vectorType4(
; CHECK-SAME: ptr [[IN_PTR:%.*]], i64 [[IN_IDX1:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[IN_IDX1_NNEG:%.*]] = and i64 [[IN_IDX1]], 2147483647
; CHECK-NEXT:    [[TMP0:%.*]] = getelementptr inbounds <8 x ptr addrspace(1)>, ptr [[IN_PTR]], i64 [[IN_IDX1_NNEG]]
; CHECK-NEXT:    [[TMP1:%.*]] = getelementptr inbounds <4 x ptr>, ptr [[TMP0]], i32 3
; CHECK-NEXT:    ret void
;
entry:
  %in.idx1.nneg = and i64 %in.idx1, 2147483647
  %const1 = getelementptr inbounds <4 x ptr>, ptr %in.ptr, i32 3
  %idx1 = getelementptr inbounds <8 x ptr addrspace(1)>, ptr %const1, i64 %in.idx1.nneg
  ret void
}


define void @ptrType(ptr %in.ptr, i64 %in.idx1) {
; CHECK-LABEL: define void @ptrType(
; CHECK-SAME: ptr [[IN_PTR:%.*]], i64 [[IN_IDX1:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[IN_IDX1_NNEG:%.*]] = and i64 [[IN_IDX1]], 2147483647
; CHECK-NEXT:    [[TMP0:%.*]] = getelementptr inbounds ptr, ptr [[IN_PTR]], i64 [[IN_IDX1_NNEG]]
; CHECK-NEXT:    [[TMP1:%.*]] = getelementptr inbounds ptr addrspace(2), ptr [[TMP0]], i32 1
; CHECK-NEXT:    ret void
;
entry:
  %in.idx1.nneg = and i64 %in.idx1, 2147483647
  %const1 = getelementptr inbounds ptr addrspace(2), ptr %in.ptr, i32 1
  %idx1 = getelementptr inbounds ptr, ptr %const1, i64 %in.idx1.nneg
  ret void
}

define void @ptrType2(ptr %in.ptr, i64 %in.idx1) {
; CHECK-LABEL: define void @ptrType2(
; CHECK-SAME: ptr [[IN_PTR:%.*]], i64 [[IN_IDX1:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[IN_IDX1_NNEG:%.*]] = and i64 [[IN_IDX1]], 2147483647
; CHECK-NEXT:    [[TMP0:%.*]] = getelementptr inbounds i64, ptr [[IN_PTR]], i64 [[IN_IDX1_NNEG]]
; CHECK-NEXT:    [[TMP1:%.*]] = getelementptr inbounds ptr addrspace(3), ptr [[TMP0]], i32 3
; CHECK-NEXT:    ret void
;
entry:
  %in.idx1.nneg = and i64 %in.idx1, 2147483647
  %const1 = getelementptr inbounds ptr addrspace(3), ptr %in.ptr, i32 3
  %idx1 = getelementptr inbounds i64, ptr %const1, i64 %in.idx1.nneg
  ret void
}

define void @ptrType3(ptr %in.ptr, i64 %in.idx1) {
; CHECK-LABEL: define void @ptrType3(
; CHECK-SAME: ptr [[IN_PTR:%.*]], i64 [[IN_IDX1:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[IN_IDX1_NNEG:%.*]] = and i64 [[IN_IDX1]], 2147483647
; CHECK-NEXT:    [[TMP0:%.*]] = getelementptr inbounds i16, ptr [[IN_PTR]], i64 [[IN_IDX1_NNEG]]
; CHECK-NEXT:    [[TMP1:%.*]] = getelementptr inbounds ptr addrspace(7), ptr [[TMP0]], i32 3
; CHECK-NEXT:    ret void
;
entry:
  %in.idx1.nneg = and i64 %in.idx1, 2147483647
  %const1 = getelementptr inbounds ptr addrspace(7), ptr %in.ptr, i32 3
  %idx1 = getelementptr inbounds i16, ptr %const1, i64 %in.idx1.nneg
  ret void
}

define void @addrspace1(ptr addrspace(1) %in.ptr, i64 %in.idx1) {
; CHECK-LABEL: define void @addrspace1(
; CHECK-SAME: ptr addrspace(1) [[IN_PTR:%.*]], i64 [[IN_IDX1:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[IN_IDX1_NNEG:%.*]] = and i64 [[IN_IDX1]], 9223372036854775807
; CHECK-NEXT:    [[TMP0:%.*]] = getelementptr inbounds i128, ptr addrspace(1) [[IN_PTR]], i64 [[IN_IDX1_NNEG]]
; CHECK-NEXT:    [[TMP1:%.*]] = getelementptr inbounds i8, ptr addrspace(1) [[TMP0]], i64 1024
; CHECK-NEXT:    ret void
;
entry:
  %in.idx1.nneg = and i64 %in.idx1, 9223372036854775807
  %const1 = getelementptr inbounds i8, ptr addrspace(1) %in.ptr, i64 1024
  %idx1 = getelementptr inbounds i128, ptr addrspace(1) %const1, i64 %in.idx1.nneg
  ret void
}

define void @addrspace3(ptr addrspace(3) %in.ptr, i64 %in.idx1) {
; CHECK-LABEL: define void @addrspace3(
; CHECK-SAME: ptr addrspace(3) [[IN_PTR:%.*]], i64 [[IN_IDX1:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[IN_IDX1_NNEG:%.*]] = and i64 [[IN_IDX1]], 9223372036854775807
; CHECK-NEXT:    [[IDXPROM:%.*]] = trunc i64 [[IN_IDX1_NNEG]] to i32
; CHECK-NEXT:    [[TMP0:%.*]] = getelementptr i128, ptr addrspace(3) [[IN_PTR]], i32 [[IDXPROM]]
; CHECK-NEXT:    [[TMP1:%.*]] = getelementptr i8, ptr addrspace(3) [[TMP0]], i64 1024
; CHECK-NEXT:    ret void
;
entry:
  %in.idx1.nneg = and i64 %in.idx1, 9223372036854775807
  %const1 = getelementptr inbounds i8, ptr addrspace(3) %in.ptr, i64 1024
  %idx1 = getelementptr inbounds i128, ptr addrspace(3) %const1, i64 %in.idx1.nneg
  ret void
}

define void @addrspace7(ptr addrspace(7) %in.ptr, i64 %in.idx1) {
; CHECK-LABEL: define void @addrspace7(
; CHECK-SAME: ptr addrspace(7) [[IN_PTR:%.*]], i64 [[IN_IDX1:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[IN_IDX1_NNEG:%.*]] = and i64 [[IN_IDX1]], 9223372036854775807
; CHECK-NEXT:    [[IDXPROM:%.*]] = trunc i64 [[IN_IDX1_NNEG]] to i32
; CHECK-NEXT:    [[TMP0:%.*]] = getelementptr i128, ptr addrspace(7) [[IN_PTR]], i32 [[IDXPROM]]
; CHECK-NEXT:    [[TMP1:%.*]] = getelementptr i8, ptr addrspace(7) [[TMP0]], i64 1024
; CHECK-NEXT:    ret void
;
entry:
  %in.idx1.nneg = and i64 %in.idx1, 9223372036854775807
  %const1 = getelementptr inbounds i8, ptr addrspace(7) %in.ptr, i64 1024
  %idx1 = getelementptr inbounds i128, ptr addrspace(7) %const1, i64 %in.idx1.nneg
  ret void
}
