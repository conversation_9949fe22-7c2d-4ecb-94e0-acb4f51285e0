; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 3
; RUN: opt -mtriple=nvptx64-nvidia-cuda -S -passes=separate-const-offset-from-gep  < %s | FileCheck %s

define protected amdgpu_kernel void @sink_addr(ptr %in.ptr, i64 %in.idx0, i64 %in.idx1) {
; CHECK-LABEL: define protected amdgpu_kernel void @sink_addr(
; CHECK-SAME: ptr [[IN_PTR:%.*]], i64 [[IN_IDX0:%.*]], i64 [[IN_IDX1:%.*]]) {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[IDX0:%.*]] = getelementptr [8192 x i64], ptr [[IN_PTR]], i64 [[IN_IDX0]], i64 [[IN_IDX1]]
; CHECK-NEXT:    [[TMP0:%.*]] = getelementptr [8192 x i64], ptr [[IN_PTR]], i64 [[IN_IDX0]], i64 0
; CHECK-NEXT:    [[TMP3:%.*]] = getelementptr i64, ptr [[TMP0]], i64 [[IN_IDX1]]
; CHECK-NEXT:    [[IDX1:%.*]] = getelementptr i8, ptr [[TMP3]], i64 2048
; CHECK-NEXT:    [[TMP1:%.*]] = getelementptr [8192 x i64], ptr [[IN_PTR]], i64 [[IN_IDX0]], i64 0
; CHECK-NEXT:    [[TMP4:%.*]] = getelementptr i64, ptr [[TMP1]], i64 [[IN_IDX1]]
; CHECK-NEXT:    [[IDX2:%.*]] = getelementptr i8, ptr [[TMP4]], i64 4096
; CHECK-NEXT:    [[TMP2:%.*]] = getelementptr [8192 x i64], ptr [[IN_PTR]], i64 [[IN_IDX0]], i64 0
; CHECK-NEXT:    [[TMP7:%.*]] = getelementptr i64, ptr [[TMP2]], i64 [[IN_IDX1]]
; CHECK-NEXT:    [[IDX3:%.*]] = getelementptr i8, ptr [[TMP7]], i64 6144
; CHECK-NEXT:    [[CMP0:%.*]] = icmp eq i64 [[IN_IDX0]], 0
; CHECK-NEXT:    br i1 [[CMP0]], label [[BB_1:%.*]], label [[END:%.*]]
; CHECK:       bb.1:
; CHECK-NEXT:    [[VAL0:%.*]] = load <8 x i64>, ptr [[IDX0]], align 16
; CHECK-NEXT:    [[VAL1:%.*]] = load <8 x i64>, ptr [[IDX1]], align 16
; CHECK-NEXT:    [[VAL2:%.*]] = load <8 x i64>, ptr [[IDX2]], align 16
; CHECK-NEXT:    [[VAL3:%.*]] = load <8 x i64>, ptr [[IDX3]], align 16
; CHECK-NEXT:    call void asm sideeffect "
; CHECK-NEXT:    call void asm sideeffect "
; CHECK-NEXT:    call void asm sideeffect "
; CHECK-NEXT:    call void asm sideeffect "
; CHECK-NEXT:    br label [[END]]
; CHECK:       end:
; CHECK-NEXT:    call void asm sideeffect "
; CHECK-NEXT:    call void asm sideeffect "
; CHECK-NEXT:    call void asm sideeffect "
; CHECK-NEXT:    call void asm sideeffect "
; CHECK-NEXT:    ret void
;
entry:
  %idx0 = getelementptr [8192 x i64], ptr %in.ptr, i64 %in.idx0, i64 %in.idx1
  %const1 = getelementptr [8192 x i64], ptr %in.ptr, i64 %in.idx0, i64 256
  %idx1 = getelementptr i64, ptr  %const1, i64 %in.idx1
  %const2 = getelementptr [8192 x i64], ptr %in.ptr, i64 %in.idx0, i64 512
  %idx2 = getelementptr i64, ptr  %const2, i64 %in.idx1
  %const3 = getelementptr [8192 x i64], ptr %in.ptr, i64 %in.idx0, i64 768
  %idx3 = getelementptr i64, ptr  %const3, i64 %in.idx1
  %cmp0 = icmp eq i64 %in.idx0, 0
  br i1 %cmp0, label %bb.1, label %end

bb.1:
  %val0 = load <8 x i64>, ptr  %idx0, align 16
  %val1 = load <8 x i64>, ptr  %idx1, align 16
  %val2 = load <8 x i64>, ptr  %idx2, align 16
  %val3 = load <8 x i64>, ptr  %idx3, align 16
  call void asm sideeffect "; use $0", "v"(<8 x i64> %val0)
  call void asm sideeffect "; use $0", "v"(<8 x i64> %val1)
  call void asm sideeffect "; use $0", "v"(<8 x i64> %val2)
  call void asm sideeffect "; use $0", "v"(<8 x i64> %val3)
  br label %end

end:
  call void asm sideeffect "; use $0", "v"(ptr  %idx0)
  call void asm sideeffect "; use $0", "v"(ptr  %idx1)
  call void asm sideeffect "; use $0", "v"(ptr  %idx2)
  call void asm sideeffect "; use $0", "v"(ptr  %idx3)
  ret void
}

define void @inboundsPossiblyNegative1(ptr %in.ptr, i64 %in.idx1) {
; CHECK-LABEL: define void @inboundsPossiblyNegative1(
; CHECK-SAME: ptr [[IN_PTR:%.*]], i64 [[IN_IDX1:%.*]]) {
; CHECK-NEXT:    [[TMP0:%.*]] = getelementptr <2 x i8>, ptr [[IN_PTR]], i64 [[IN_IDX1]]
; CHECK-NEXT:    [[TMP1:%.*]] = getelementptr <2 x i8>, ptr [[TMP0]], i64 1
; CHECK-NEXT:    ret void
;
  %const1 = getelementptr inbounds <2 x i8>, ptr %in.ptr, i64 1
  %idx1 = getelementptr inbounds <2 x i8>, ptr %const1, i64 %in.idx1
  ret void
}

define void @inboundsPossiblyNegative2(ptr %in.ptr, i64 %in.idx1) {
; CHECK-LABEL: define void @inboundsPossiblyNegative2(
; CHECK-SAME: ptr [[IN_PTR:%.*]], i64 [[IN_IDX1:%.*]]) {
; CHECK-NEXT:    [[IN_IDX1_NNEG:%.*]] = and i64 [[IN_IDX1]], 9223372036854775807
; CHECK-NEXT:    [[TMP1:%.*]] = getelementptr <2 x i8>, ptr [[IN_PTR]], i64 [[IN_IDX1_NNEG]]
; CHECK-NEXT:    [[TMP2:%.*]] = getelementptr <2 x i8>, ptr [[TMP1]], i64 -1
; CHECK-NEXT:    ret void
;
  %in.idx1.nneg = and i64 %in.idx1, 9223372036854775807
  %const1 = getelementptr inbounds <2 x i8>, ptr %in.ptr, i64 -1
  %idx1 = getelementptr inbounds <2 x i8>, ptr %const1, i64 %in.idx1.nneg
  ret void
}

define void @inboundsNonNegative(ptr %in.ptr, i64 %in.idx1) {
; CHECK-LABEL: define void @inboundsNonNegative(
; CHECK-SAME: ptr [[IN_PTR:%.*]], i64 [[IN_IDX1:%.*]]) {
; CHECK-NEXT:    [[IDXPROM:%.*]] = and i64 [[IN_IDX1]], 9223372036854775807
; CHECK-NEXT:    [[TMP0:%.*]] = getelementptr inbounds <2 x i8>, ptr [[IN_PTR]], i64 [[IDXPROM]]
; CHECK-NEXT:    [[TMP1:%.*]] = getelementptr inbounds <2 x i8>, ptr [[TMP0]], i64 1
; CHECK-NEXT:    ret void
;
  %in.idx1.nneg = and i64 %in.idx1, 9223372036854775807
  %const1 = getelementptr inbounds <2 x i8>, ptr %in.ptr, i64 1
  %idx1 = getelementptr inbounds <2 x i8>, ptr %const1, i64 %in.idx1.nneg
  ret void
}

