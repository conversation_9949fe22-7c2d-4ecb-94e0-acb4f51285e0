; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt %s -passes='function(scalarizer)' -S | FileCheck %s

; Test to make sure that struct return intrinsics that are not `isTriviallyScalarizable` do not get scalarized.

define <3 x i32> @test_(<3 x i32> %a, <3 x i32> %b) {
; CHECK-LABEL: define <3 x i32> @test_(
; CHECK-SAME: <3 x i32> [[A:%.*]], <3 x i32> [[B:%.*]]) {
; CHECK-NEXT:    [[R:%.*]] = call { <3 x i32>, <3 x i1> } @llvm.uadd.with.overflow.v3i32(<3 x i32> [[B]], <3 x i32> [[B]])
; CHECK-NEXT:    [[EL:%.*]] = extractvalue { <3 x i32>, <3 x i1> } [[R]], 0
; CHECK-NEXT:    ret <3 x i32> [[EL]]
;
  %r = call { <3 x i32>, <3 x i1> } @llvm.uadd.with.overflow.v3i32(<3 x i32> %b, <3 x i32> %b)
  %el = extractvalue { <3 x i32>, <3 x i1> } %r, 0
  ret <3 x i32> %el
}
