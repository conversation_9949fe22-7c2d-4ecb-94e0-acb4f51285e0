; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt %s -passes='function(scalarizer<load-store>,dce)' -S | FileCheck --check-prefixes=ALL %s

target datalayout = "e-p:64:64:64-i1:8:8-i8:8:8-i16:16:16-i32:32:32-i64:64:64-f32:32:32-f64:64:64-v64:64:64-v128:128:128-a0:0:64-s0:64:64-f80:128:128-n8:16:32:64-S128"

; Test that constant inserts are nicely scalarized
define <4 x i32> @f1(ptr %src, i32 %repl, i32 %index) {
; ALL-LABEL: @f1(
; ALL-NEXT:    [[VAL0_I0:%.*]] = load i32, ptr [[SRC:%.*]], align 16
; ALL-NEXT:    [[SRC_I1:%.*]] = getelementptr i32, ptr [[SRC]], i32 1
; ALL-NEXT:    [[VAL0_I1:%.*]] = load i32, ptr [[SRC_I1]], align 4
; ALL-NEXT:    [[SRC_I2:%.*]] = getelementptr i32, ptr [[SRC]], i32 2
; ALL-NEXT:    [[VAL0_I2:%.*]] = load i32, ptr [[SRC_I2]], align 8
; ALL-NEXT:    [[VAL2_I0:%.*]] = shl i32 1, [[VAL0_I0]]
; ALL-NEXT:    [[VAL2_I1:%.*]] = shl i32 2, [[VAL0_I1]]
; ALL-NEXT:    [[VAL2_I2:%.*]] = shl i32 3, [[VAL0_I2]]
; ALL-NEXT:    [[VAL2_I3:%.*]] = shl i32 4, [[REPL:%.*]]
; ALL-NEXT:    [[VAL2_UPTO0:%.*]] = insertelement <4 x i32> poison, i32 [[VAL2_I0]], i64 0
; ALL-NEXT:    [[VAL2_UPTO1:%.*]] = insertelement <4 x i32> [[VAL2_UPTO0]], i32 [[VAL2_I1]], i64 1
; ALL-NEXT:    [[VAL2_UPTO2:%.*]] = insertelement <4 x i32> [[VAL2_UPTO1]], i32 [[VAL2_I2]], i64 2
; ALL-NEXT:    [[VAL2:%.*]] = insertelement <4 x i32> [[VAL2_UPTO2]], i32 [[VAL2_I3]], i64 3
; ALL-NEXT:    ret <4 x i32> [[VAL2]]
;
  %val0 = load <4 x i32> , ptr %src
  %val1 = insertelement <4 x i32> %val0, i32 %repl, i32 3
  %val2 = shl <4 x i32> <i32 1, i32 2, i32 3, i32 4>, %val1
  ret <4 x i32> %val2
}
