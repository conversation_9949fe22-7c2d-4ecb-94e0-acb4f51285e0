; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt %s -passes='function(scalarizer<load-store>,dce)' -S | FileCheck %s
target datalayout = "e-p:64:64:64-i1:8:8-i8:8:8-i16:16:16-i32:32:32-i64:64:64-f32:32:32-f64:64:64-v64:64:64-v128:128:128-a0:0:64-s0:64:64-f80:128:128-n8:16:32:64-S128"

define <1 x i32> @f1(<1 x ptr> %src, i32 %index) {
; CHECK-LABEL: @f1(
; CHECK-NEXT:    [[INDEX_IS_0:%.*]] = icmp eq i32 [[INDEX:%.*]], 0
; CHECK-NEXT:    [[SRC_I0:%.*]] = extractelement <1 x ptr> [[SRC:%.*]], i64 0
; CHECK-NEXT:    [[DOTUPTO0:%.*]] = select i1 [[INDEX_IS_0]], ptr [[SRC_I0]], ptr poison
; CHECK-NEXT:    [[DOTI0:%.*]] = load i32, ptr [[DOTUPTO0]], align 4
; CHECK-NEXT:    [[TMP1:%.*]] = insertelement <1 x i32> poison, i32 [[DOTI0]], i64 0
; CHECK-NEXT:    ret <1 x i32> [[TMP1]]
;
  %1 = extractelement <1 x ptr> %src, i32 %index
  %2 = load <1 x i32>, ptr %1, align 4
  ret <1 x i32> %2
}

define <1 x i32> @f1b(<1 x ptr> %src) {
; CHECK-LABEL: @f1b(
; CHECK-NEXT:    [[SRC_I0:%.*]] = extractelement <1 x ptr> [[SRC:%.*]], i64 0
; CHECK-NEXT:    [[DOTI0:%.*]] = load i32, ptr [[SRC_I0]], align 4
; CHECK-NEXT:    [[TMP1:%.*]] = insertelement <1 x i32> poison, i32 [[DOTI0]], i64 0
; CHECK-NEXT:    ret <1 x i32> [[TMP1]]
;
  %1 = extractelement <1 x ptr> %src, i32 0
  %2 = load <1 x i32>, ptr %1, align 4
  ret <1 x i32> %2
}

define <2 x i32> @f2(<1 x ptr> %src, i32 %index) {
; CHECK-LABEL: @f2(
; CHECK-NEXT:    [[INDEX_IS_0:%.*]] = icmp eq i32 [[INDEX:%.*]], 0
; CHECK-NEXT:    [[SRC_I0:%.*]] = extractelement <1 x ptr> [[SRC:%.*]], i64 0
; CHECK-NEXT:    [[DOTUPTO0:%.*]] = select i1 [[INDEX_IS_0]], ptr [[SRC_I0]], ptr poison
; CHECK-NEXT:    [[DOTUPTO0_I1:%.*]] = getelementptr i32, ptr [[DOTUPTO0]], i32 1
; CHECK-NEXT:    [[DOTI0:%.*]] = load i32, ptr [[DOTUPTO0]], align 4
; CHECK-NEXT:    [[DOTI1:%.*]] = load i32, ptr [[DOTUPTO0_I1]], align 4
; CHECK-NEXT:    [[DOTUPTO01:%.*]] = insertelement <2 x i32> poison, i32 [[DOTI0]], i64 0
; CHECK-NEXT:    [[TMP1:%.*]] = insertelement <2 x i32> [[DOTUPTO01]], i32 [[DOTI1]], i64 1
; CHECK-NEXT:    ret <2 x i32> [[TMP1]]
;
  %1 = extractelement <1 x ptr> %src, i32 %index
  %2 = load <2 x i32>, ptr %1, align 4
  ret <2 x i32> %2
}

define <2 x i32> @f2b(<1 x ptr> %src) {
; CHECK-LABEL: @f2b(
; CHECK-NEXT:    [[SRC_I0:%.*]] = extractelement <1 x ptr> [[SRC:%.*]], i64 0
; CHECK-NEXT:    [[SRC_I0_I1:%.*]] = getelementptr i32, ptr [[SRC_I0]], i32 1
; CHECK-NEXT:    [[DOTI0:%.*]] = load i32, ptr [[SRC_I0]], align 4
; CHECK-NEXT:    [[DOTI1:%.*]] = load i32, ptr [[SRC_I0_I1]], align 4
; CHECK-NEXT:    [[DOTUPTO0:%.*]] = insertelement <2 x i32> poison, i32 [[DOTI0]], i64 0
; CHECK-NEXT:    [[TMP1:%.*]] = insertelement <2 x i32> [[DOTUPTO0]], i32 [[DOTI1]], i64 1
; CHECK-NEXT:    ret <2 x i32> [[TMP1]]
;
  %1 = extractelement <1 x ptr> %src, i32 0
  %2 = load <2 x i32>, ptr %1, align 4
  ret <2 x i32> %2
}

define void @f3(<1 x ptr> %src, i32 %index, <2 x i32> %val) {
; CHECK-LABEL: @f3(
; CHECK-NEXT:    [[VAL_I0:%.*]] = extractelement <2 x i32> [[VAL:%.*]], i64 0
; CHECK-NEXT:    [[VAL_I1:%.*]] = extractelement <2 x i32> [[VAL]], i64 1
; CHECK-NEXT:    [[INDEX_IS_0:%.*]] = icmp eq i32 [[INDEX:%.*]], 0
; CHECK-NEXT:    [[SRC_I0:%.*]] = extractelement <1 x ptr> [[SRC:%.*]], i64 0
; CHECK-NEXT:    [[DOTUPTO0:%.*]] = select i1 [[INDEX_IS_0]], ptr [[SRC_I0]], ptr poison
; CHECK-NEXT:    [[DOTUPTO0_I1:%.*]] = getelementptr i32, ptr [[DOTUPTO0]], i32 1
; CHECK-NEXT:    store i32 [[VAL_I0]], ptr [[DOTUPTO0]], align 4
; CHECK-NEXT:    store i32 [[VAL_I1]], ptr [[DOTUPTO0_I1]], align 4
; CHECK-NEXT:    ret void
;
  %1 = extractelement <1 x ptr> %src, i32 %index
  store <2 x i32> %val, ptr %1, align 4
  ret void
}

define void @f3b(<1 x ptr> %src, <2 x i32> %val) {
; CHECK-LABEL: @f3b(
; CHECK-NEXT:    [[VAL_I0:%.*]] = extractelement <2 x i32> [[VAL:%.*]], i64 0
; CHECK-NEXT:    [[VAL_I1:%.*]] = extractelement <2 x i32> [[VAL]], i64 1
; CHECK-NEXT:    [[SRC_I0:%.*]] = extractelement <1 x ptr> [[SRC:%.*]], i64 0
; CHECK-NEXT:    [[SRC_I0_I1:%.*]] = getelementptr i32, ptr [[SRC_I0]], i32 1
; CHECK-NEXT:    store i32 [[VAL_I0]], ptr [[SRC_I0]], align 4
; CHECK-NEXT:    store i32 [[VAL_I1]], ptr [[SRC_I0_I1]], align 4
; CHECK-NEXT:    ret void
;
  %1 = extractelement <1 x ptr> %src, i32 0
  store <2 x i32> %val, ptr %1, align 4
  ret void
}
