; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 2
; RUN: opt %s -passes='function(scalarizer)' -S -o - | FileCheck %s

; Don't crash

define void @foo(i1 %arg) {
; CHECK-LABEL: define void @foo(i1 %arg) {
; CHECK-NEXT:    br label [[BB1:%.*]]
; CHECK:       bb2:
; CHECK-NEXT:    br label [[BB1]]
; CHECK:       bb1:
; CHECK-NEXT:    [[BB2_VEC_I1:%.*]] = phi i16 [ 200, [[TMP0:%.*]] ], [ [[BB2_VEC_I1]], [[BB2:%.*]] ]
; CHECK-NEXT:    br i1 %arg, label [[BB3:%.*]], label [[BB2]]
; CHECK:       bb3:
; CHECK-NEXT:    ret void
;
  br label %bb1

bb2:                                        ; preds = %bb1
  %bb2_vec = shufflevector <2 x i16> <i16 0, i16 10000>, <2 x i16> %bb1_vec, <2 x i32> <i32 0, i32 3>
  br label %bb1

bb1:                                        ; preds = %bb2, %0
  %bb1_vec = phi <2 x i16> [ <i16 100, i16 200>, %0 ], [ %bb2_vec, %bb2 ]
  br i1 %arg, label %bb3, label %bb2

bb3:
  ret void
}

; See https://reviews.llvm.org/D83101#2135945
define void @f1_crash(<2 x i16> %base, i1 %c, ptr %ptr) {
; CHECK-LABEL: define void @f1_crash
; CHECK-SAME: (<2 x i16> [[BASE:%.*]], i1 [[C:%.*]], ptr [[PTR:%.*]]) {
; CHECK-NEXT:  vector.ph:
; CHECK-NEXT:    [[BASE_I0:%.*]] = extractelement <2 x i16> [[BASE]], i64 0
; CHECK-NEXT:    [[BASE_I1:%.*]] = extractelement <2 x i16> [[BASE]], i64 1
; CHECK-NEXT:    br label [[VECTOR_BODY115:%.*]]
; CHECK:       vector.body115:
; CHECK-NEXT:    [[VECTOR_RECUR_I0:%.*]] = phi i16 [ [[BASE_I0]], [[VECTOR_PH:%.*]] ], [ [[WIDE_LOAD125_I0:%.*]], [[VECTOR_BODY115]] ]
; CHECK-NEXT:    [[VECTOR_RECUR_I1:%.*]] = phi i16 [ [[BASE_I1]], [[VECTOR_PH]] ], [ [[WIDE_LOAD125_I1:%.*]], [[VECTOR_BODY115]] ]
; CHECK-NEXT:    [[WIDE_LOAD125:%.*]] = load <2 x i16>, ptr [[PTR]], align 1
; CHECK-NEXT:    [[WIDE_LOAD125_I0]] = extractelement <2 x i16> [[WIDE_LOAD125]], i64 0
; CHECK-NEXT:    [[WIDE_LOAD125_I1]] = extractelement <2 x i16> [[WIDE_LOAD125]], i64 1
; CHECK-NEXT:    br i1 [[C]], label [[MIDDLE_BLOCK113:%.*]], label [[VECTOR_BODY115]]
; CHECK:       middle.block113:
; CHECK-NEXT:    ret void
;

vector.ph:
  br label %vector.body115

vector.body115:
  %vector.recur = phi <2 x i16> [ %base, %vector.ph ], [ %wide.load125, %vector.body115 ]
  %wide.load125 = load <2 x i16>, ptr %ptr, align 1
  br i1 %c, label %middle.block113, label %vector.body115

middle.block113:
  ret void
}
