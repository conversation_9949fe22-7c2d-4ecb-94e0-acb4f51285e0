; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt %s -passes='function(scalarizer<load-store>)' -S | FileCheck %s

; This verifies that the order of extract element instructions is
; deterministic. In the past we could end up with different results depending
; on the compiler used (due to argument evaluation order being undefined in
; C++). The order of the extracts is not really important for correctness of
; the result, but when debugging and creating test cases it is helpful if we
; get the same out put regardless of which compiler we use when building the
; compiler.

define <2 x i32> @test1(i1 %b, <2 x i32> %i, <2 x i32> %j) {
; CHECK-LABEL: @test1(
; CHECK-NEXT:    [[I_I0:%.*]] = extractelement <2 x i32> [[I:%.*]], i64 0
; CHECK-NEXT:    [[J_I0:%.*]] = extractelement <2 x i32> [[J:%.*]], i64 0
; CHECK-NEXT:    [[RES_I0:%.*]] = select i1 [[B:%.*]], i32 [[I_I0]], i32 [[J_I0]]
; CHECK-NEXT:    [[I_I1:%.*]] = extractelement <2 x i32> [[I]], i64 1
; CHECK-NEXT:    [[J_I1:%.*]] = extractelement <2 x i32> [[J]], i64 1
; CHECK-NEXT:    [[RES_I1:%.*]] = select i1 [[B]], i32 [[I_I1]], i32 [[J_I1]]
; CHECK-NEXT:    [[RES_UPTO0:%.*]] = insertelement <2 x i32> poison, i32 [[RES_I0]], i64 0
; CHECK-NEXT:    [[RES:%.*]] = insertelement <2 x i32> [[RES_UPTO0]], i32 [[RES_I1]], i64 1
; CHECK-NEXT:    ret <2 x i32> [[RES]]
;
  %res = select i1 %b, <2 x i32> %i, <2 x i32> %j
  ret <2 x i32> %res
}

define <2 x i32> @test2(<2 x i1> %b, <2 x i32> %i, <2 x i32> %j) {
; CHECK-LABEL: @test2(
; CHECK-NEXT:    [[B_I0:%.*]] = extractelement <2 x i1> [[B:%.*]], i64 0
; CHECK-NEXT:    [[I_I0:%.*]] = extractelement <2 x i32> [[I:%.*]], i64 0
; CHECK-NEXT:    [[J_I0:%.*]] = extractelement <2 x i32> [[J:%.*]], i64 0
; CHECK-NEXT:    [[RES_I0:%.*]] = select i1 [[B_I0]], i32 [[I_I0]], i32 [[J_I0]]
; CHECK-NEXT:    [[B_I1:%.*]] = extractelement <2 x i1> [[B]], i64 1
; CHECK-NEXT:    [[I_I1:%.*]] = extractelement <2 x i32> [[I]], i64 1
; CHECK-NEXT:    [[J_I1:%.*]] = extractelement <2 x i32> [[J]], i64 1
; CHECK-NEXT:    [[RES_I1:%.*]] = select i1 [[B_I1]], i32 [[I_I1]], i32 [[J_I1]]
; CHECK-NEXT:    [[RES_UPTO0:%.*]] = insertelement <2 x i32> poison, i32 [[RES_I0]], i64 0
; CHECK-NEXT:    [[RES:%.*]] = insertelement <2 x i32> [[RES_UPTO0]], i32 [[RES_I1]], i64 1
; CHECK-NEXT:    ret <2 x i32> [[RES]]
;
  %res = select <2 x i1> %b, <2 x i32> %i, <2 x i32> %j
  ret <2 x i32> %res
}

define <2 x i32> @test3(<2 x i32> %i, <2 x i32> %j) {
; CHECK-LABEL: @test3(
; CHECK-NEXT:    [[I_I0:%.*]] = extractelement <2 x i32> [[I:%.*]], i64 0
; CHECK-NEXT:    [[J_I0:%.*]] = extractelement <2 x i32> [[J:%.*]], i64 0
; CHECK-NEXT:    [[RES_I0:%.*]] = add nuw nsw i32 [[I_I0]], [[J_I0]]
; CHECK-NEXT:    [[I_I1:%.*]] = extractelement <2 x i32> [[I]], i64 1
; CHECK-NEXT:    [[J_I1:%.*]] = extractelement <2 x i32> [[J]], i64 1
; CHECK-NEXT:    [[RES_I1:%.*]] = add nuw nsw i32 [[I_I1]], [[J_I1]]
; CHECK-NEXT:    [[RES_UPTO0:%.*]] = insertelement <2 x i32> poison, i32 [[RES_I0]], i64 0
; CHECK-NEXT:    [[RES:%.*]] = insertelement <2 x i32> [[RES_UPTO0]], i32 [[RES_I1]], i64 1
; CHECK-NEXT:    ret <2 x i32> [[RES]]
;
  %res = add nuw nsw <2 x i32> %i, %j
  ret <2 x i32> %res
}

define void @test4(ptr %ptr, <2 x i32> %val) {
; CHECK-LABEL: @test4(
; CHECK-NEXT:    [[VAL_I0:%.*]] = extractelement <2 x i32> [[VAL:%.*]], i64 0
; CHECK-NEXT:    store i32 [[VAL_I0]], ptr [[PTR:%.*]], align 8
; CHECK-NEXT:    [[VAL_I1:%.*]] = extractelement <2 x i32> [[VAL]], i64 1
; CHECK-NEXT:    [[PTR_I1:%.*]] = getelementptr i32, ptr [[PTR]], i32 1
; CHECK-NEXT:    store i32 [[VAL_I1]], ptr [[PTR_I1]], align 4
; CHECK-NEXT:    ret void
;
  store <2 x i32> %val, ptr %ptr
  ret void
}

