; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 4
; RUN: opt < %s -S -passes='loop-mssa(simple-loop-unswitch),instcombine' -verify-memoryssa | FileCheck %s

@.str9 = external constant [1 x i8]

declare i32 @strcmp(ptr, ptr)

define i32 @_ZN9Generator6strregEPKc(ptr %this, ptr %s) {
; CHECK-LABEL: define i32 @_ZN9Generator6strregEPKc(
; CHECK-SAME: ptr [[THIS:%.*]], ptr [[S:%.*]]) {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    br label [[BB184:%.*]]
; CHECK:       bb55:
; CHECK-NEXT:    ret i32 0
; CHECK:       bb88:
; CHECK-NEXT:    br i1 poison, label [[BB154:%.*]], label [[BB128:%.*]]
; CHECK:       bb128:
; CHECK-NEXT:    br i1 poison, label [[BB250:%.*]], label [[BB166:%.*]]
; CHECK:       bb154:
; CHECK-NEXT:    br i1 false, label [[BB250]], label [[BB166]]
; CHECK:       bb166:
; CHECK-NEXT:    br label [[BB184]]
; CHECK:       bb184:
; CHECK-NEXT:    br i1 false, label [[BB88:%.*]], label [[BB55:%.*]]
; CHECK:       bb250:
; CHECK-NEXT:    ret i32 poison
;
entry:
  %s_addr.0 = select i1 false, ptr @.str9, ptr %s
  %tmp122 = icmp eq ptr %s_addr.0, null
  br label %bb184

bb55:
  ret i32 0

bb88:
  br i1 %tmp122, label %bb154, label %bb128

bb128:
  %tmp138 = call i32 @strcmp( ptr null, ptr %s_addr.0 )
  %iftmp.37.0.in4 = icmp eq i32 %tmp138, 0
  br i1 %iftmp.37.0.in4, label %bb250, label %bb166

bb154:
  br i1 false, label %bb250, label %bb166

bb166:
  %tmp175 = add i32 %idx.0, 1
  %tmp177 = add i32 %tmp175, 0
  %tmp181 = add i32 %tmp177, 0
  %tmp183 = add i32 %i33.0, 1
  br label %bb184

bb184:
  %i33.0 = phi i32 [ 0, %entry ], [ %tmp183, %bb166 ]
  %idx.0 = phi i32 [ 0, %entry ], [ %tmp181, %bb166 ]
  %tmp49 = icmp slt i32 %i33.0, 0
  br i1 %tmp49, label %bb88, label %bb55

bb250:
  ret i32 %idx.0
}
