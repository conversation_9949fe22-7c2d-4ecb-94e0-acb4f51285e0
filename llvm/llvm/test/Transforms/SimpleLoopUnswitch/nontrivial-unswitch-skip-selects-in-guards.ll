; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -passes='loop(simple-loop-unswitch<nontrivial>),verify<loops>' -S < %s | FileCheck %s
; RUN: opt -passes='loop-mssa(simple-loop-unswitch<nontrivial>),verify<loops>' -S -verify-memoryssa < %s | FileCheck %s

declare ptr @pluto()
declare void @llvm.experimental.guard(i1, ...)
declare void @widget()

define void @foo(ptr addrspace(1) %arg, i64 %arg1) personality ptr @pluto {
; CHECK-LABEL: @foo(
; CHECK-NEXT:  bb:
; CHECK-NEXT:    [[TMP:%.*]] = icmp slt i32 poison, 570
; CHECK-NEXT:    [[TMP2:%.*]] = select i1 [[TMP]], i1 true, i1 false
; CHECK-NEXT:    br i1 [[TMP]], label [[BB_SPLIT_US:%.*]], label [[BB_SPLIT:%.*]]
; CHECK:       bb.split.us:
; CHECK-NEXT:    br label [[BB3_US:%.*]]
; CHECK:       bb3.us:
; CHECK-NEXT:    br label [[GUARDED_US:%.*]]
; CHECK:       bb4.us:
; CHECK-NEXT:    invoke void @widget()
; CHECK-NEXT:    to label [[BB6_US:%.*]] unwind label [[BB7_SPLIT_US:%.*]]
; CHECK:       bb6.us:
; CHECK-NEXT:    invoke void @widget()
; CHECK-NEXT:    to label [[BB3_US]] unwind label [[BB7_SPLIT_US]]
; CHECK:       guarded.us:
; CHECK-NEXT:    invoke void @widget()
; CHECK-NEXT:    to label [[BB4_US:%.*]] unwind label [[BB7_SPLIT_US]]
; CHECK:       bb7.split.us:
; CHECK-NEXT:    [[TMP8_US:%.*]] = landingpad { ptr, i32 }
; CHECK-NEXT:    cleanup
; CHECK-NEXT:    br label [[BB7:%.*]]
; CHECK:       bb.split:
; CHECK-NEXT:    br label [[BB3:%.*]]
; CHECK:       bb3:
; CHECK-NEXT:    br label [[DEOPT:%.*]]
; CHECK:       deopt:
; CHECK-NEXT:    call void (i1, ...) @llvm.experimental.guard(i1 false, i32 7) [ "deopt"() ]
; CHECK-NEXT:    unreachable
; CHECK:       bb7:
; CHECK-NEXT:    ret void
;
bb:
  %tmp = icmp slt i32 poison, 570
  %tmp2 = select i1 %tmp, i1 true, i1 false
  br label %bb3

bb3:                                              ; preds = %bb6, %bb
  call void (i1, ...) @llvm.experimental.guard(i1 %tmp2, i32 7) [ "deopt"() ]
  invoke void @widget()
  to label %bb4 unwind label %bb7

bb4:                                              ; preds = %bb3
  invoke void @widget()
  to label %bb6 unwind label %bb7

bb6:                                              ; preds = %bb4
  invoke void @widget()
  to label %bb3 unwind label %bb7

bb7:                                              ; preds = %bb6, %bb4, %bb3
  %tmp8 = landingpad { ptr, i32 }
  cleanup
  ret void
}

