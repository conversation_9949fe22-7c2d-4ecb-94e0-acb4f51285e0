; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S -passes=safe-stack -mtriple=x86_64-pc-linux-gnu < %s -o - | FileCheck %s --check-prefix=TLS

target datalayout = "A5"

define void @correct_alloca_addrspace() nounwind uwtable safestack {
; TLS-LABEL: define void @correct_alloca_addrspace(
; TLS-SAME: ) #[[ATTR0:[0-9]+]] !annotation [[META0:![0-9]+]] {
; TLS-NEXT:  [[ENTRY:.*:]]
; TLS-NEXT:    [[UNSAFE_STACK_PTR:%.*]] = load ptr addrspace(5), ptr @__safestack_unsafe_stack_ptr, align 8
; TLS-NEXT:    [[UNSAFE_STACK_STATIC_TOP:%.*]] = getelementptr i8, ptr addrspace(5) [[UNSAFE_STACK_PTR]], i32 -16
; TLS-NEXT:    store ptr addrspace(5) [[UNSAFE_STACK_STATIC_TOP]], ptr @__safestack_unsafe_stack_ptr, align 8
; TLS-NEXT:    [[TMP0:%.*]] = getelementptr i8, ptr addrspace(5) [[UNSAFE_STACK_PTR]], i32 -8
; TLS-NEXT:    call void @Capture_as5(ptr addrspace(5) [[TMP0]])
; TLS-NEXT:    store ptr addrspace(5) [[UNSAFE_STACK_PTR]], ptr @__safestack_unsafe_stack_ptr, align 8
; TLS-NEXT:    ret void
;
entry:
  %a = alloca i8, align 8, addrspace(5)
  call void @Capture_as5(ptr addrspace(5) %a)
  ret void
}

declare void @Capture_as5(ptr addrspace(5))
;.
; TLS: [[META0]] = !{!"unsafe-stack-size", i32 16}
;.
