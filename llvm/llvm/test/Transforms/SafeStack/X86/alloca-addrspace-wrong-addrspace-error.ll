; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: not --crash opt -passes=safe-stack -mtriple=x86_64-pc-linux-gnu -disable-output %s 2>&1 | FileCheck %s

target datalayout = "A5"

; Declaration of __safestack_unsafe_stack_ptr already exists with wrong address space
@__safestack_unsafe_stack_ptr = external thread_local(initialexec) global ptr

; CHECK: LLVM ERROR: __safestack_unsafe_stack_ptr must have void* type

define void @alloca_addrspace() nounwind uwtable safestack {
  %a = alloca i8, align 8, addrspace(5)
  call void @Capture_as5(ptr addrspace(5) %a)
  ret void
}

declare void @Capture_as5(ptr addrspace(5))
