; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt < %s -passes='unify-loop-exits' -S | FileCheck %s

define fastcc void @undef_phi(i64 %i5247, i1 %i4530, i1 %i4936.not) {
; CHECK-LABEL: define fastcc void @undef_phi(
; CHECK-SAME: i64 [[I5247:%.*]], i1 [[I4530:%.*]], i1 [[I4936_NOT:%.*]]) {
; CHECK-NEXT:  [[BB:.*:]]
; CHECK-NEXT:    br label %[[MBB3932:.*]]
; CHECK:       [[MBB3932]]:
; CHECK-NEXT:    br label %[[MBB4454:.*]]
; CHECK:       [[MBB4321:.*]]:
; CHECK-NEXT:    [[TMP0:%.*]] = trunc i64 [[I5247]] to i32
; CHECK-NEXT:    [[I5290:%.*]] = icmp eq i32 [[TMP0]], 0
; CHECK-NEXT:    br i1 [[I5290]], label %[[MBB3932]], label %[[LOOP_EXIT_GUARD:.*]]
; CHECK:       [[MBB4454]]:
; CHECK-NEXT:    br i1 [[I4530]], label %[[MBB4535:.*]], label %[[LOOP_EXIT_GUARD1:.*]]
; CHECK:       [[MBB4531:.*]]:
; CHECK-NEXT:    ret void
; CHECK:       [[MBB4535]]:
; CHECK-NEXT:    br i1 [[I4936_NOT]], label %[[LOOP_EXIT_GUARD1]], label %[[MBB4454]]
; CHECK:       [[MBB5291:.*]]:
; CHECK-NEXT:    [[I5293:%.*]] = insertvalue [2 x i32] zeroinitializer, i32 [[DOTMOVED:%.*]], 1
; CHECK-NEXT:    store volatile [2 x i32] [[I5293]], ptr addrspace(5) null, align 4
; CHECK-NEXT:    ret void
; CHECK:       [[LOOP_EXIT_GUARD]]:
; CHECK-NEXT:    [[DOTMOVED]] = phi i32 [ [[TMP0]], %[[MBB4321]] ], [ poison, %[[LOOP_EXIT_GUARD1]] ]
; CHECK-NEXT:    [[GUARD_MBB4531:%.*]] = phi i1 [ false, %[[MBB4321]] ], [ [[GUARD_MBB4531_MOVED:%.*]], %[[LOOP_EXIT_GUARD1]] ]
; CHECK-NEXT:    br i1 [[GUARD_MBB4531]], label %[[MBB4531]], label %[[MBB5291]]
; CHECK:       [[LOOP_EXIT_GUARD1]]:
; CHECK-NEXT:    [[GUARD_MBB4531_MOVED]] = phi i1 [ true, %[[MBB4454]] ], [ poison, %[[MBB4535]] ]
; CHECK-NEXT:    [[GUARD_LOOP_EXIT_GUARD:%.*]] = phi i1 [ true, %[[MBB4454]] ], [ false, %[[MBB4535]] ]
; CHECK-NEXT:    br i1 [[GUARD_LOOP_EXIT_GUARD]], label %[[LOOP_EXIT_GUARD]], label %[[MBB4321]]
;
mbb:
  br label %mbb3932

mbb3932:                                           ; preds = %mbb4321, %mbb
  br label %mbb4454

mbb4321:                                           ; preds = %mbb4535
  %0 = trunc i64 %i5247 to i32
  %i5290 = icmp eq i32 %0, 0
  br i1 %i5290, label %mbb3932, label %mbb5291

mbb4454:                                           ; preds = %mbb4535, %mbb3932
  br i1 %i4530, label %mbb4535, label %mbb4531

mbb4531:                                           ; preds = %mbb4454
  ret void

mbb4535:                                           ; preds = %mbb4454
  br i1 %i4936.not, label %mbb4321, label %mbb4454

mbb5291:                                           ; preds = %mbb4321
  %i5293 = insertvalue [2 x i32] zeroinitializer, i32 %0, 1
  store volatile [2 x i32] %i5293, ptr addrspace(5) null, align 4
  ret void
}
