; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 4
; RUN: %if x86-registered-target %{ opt -passes=slp-vectorizer -S -mtriple=x86_64 < %s | FileCheck %s %}
; RUN: %if aarch64-registered-target %{ opt -passes=slp-vectorizer -S -mtriple=aarch64 < %s | FileCheck %s %}
; Vectorization tree roots at vector build sequence (insertelement),
; SLP crashed on generating vector code for pair {%i4, 0.0} trying to produce
; a shuffle with %ins1 as a source because it was marked deleted
; due to vectorization.

define void @test() {
; CHECK-LABEL: define void @test() {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    br label [[LOOP:%.*]]
; CHECK:       loop:
; CHECK-NEXT:    [[TMP0:%.*]] = phi <2 x float> [ zeroinitializer, [[ENTRY:%.*]] ], [ [[TMP3:%.*]], [[LOOP]] ]
; CHECK-NEXT:    [[TMP1:%.*]] = fadd <2 x float> zeroinitializer, [[TMP0]]
; CHECK-NEXT:    [[TMP2:%.*]] = select <2 x i1> zeroinitializer, <2 x float> [[TMP1]], <2 x float> zeroinitializer
; CHECK-NEXT:    [[TMP3]] = shufflevector <2 x float> [[TMP2]], <2 x float> <float poison, float 0.000000e+00>, <2 x i32> <i32 0, i32 3>
; CHECK-NEXT:    br label [[LOOP]]
;
entry:
  br label %loop

loop:
  %ph0 = phi float [ 0.000000e+00, %entry ], [ %i4, %loop ]
  %ph1 = phi float [ 0.000000e+00, %entry ], [ 0.000000e+00, %loop ]
  %i = fadd float 0.000000e+00, %ph0
  %i1 = fadd float 0.000000e+00, %ph1
  %i2 = select i1 false, float %i, float 0.000000e+00
  %i3 = select i1 false, float %i1, float 0.000000e+00
  %ins0 = insertelement <2 x float> zeroinitializer, float %i2, i64 0
  %ins1 = insertelement <2 x float> %ins0, float %i3, i64 1
  %i4 = extractelement <2 x float> %ins1, i64 0
  br label %loop
}

define void @test1() {
; CHECK-LABEL: define void @test1() {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    br label [[LOOP:%.*]]
; CHECK:       loop:
; CHECK-NEXT:    [[TMP0:%.*]] = phi <2 x float> [ zeroinitializer, [[ENTRY:%.*]] ], [ [[TMP2:%.*]], [[LOOP]] ]
; CHECK-NEXT:    [[TMP1:%.*]] = fadd <2 x float> zeroinitializer, [[TMP0]]
; CHECK-NEXT:    [[TMP2]] = select <2 x i1> zeroinitializer, <2 x float> [[TMP1]], <2 x float> zeroinitializer
; CHECK-NEXT:    br label [[LOOP]]
;
entry:
  br label %loop

loop:
  %ph0 = phi float [ 0.000000e+00, %entry ], [ %i4, %loop ]
  %ph1 = phi float [ 0.000000e+00, %entry ], [ %i5, %loop ]
  %i = fadd float 0.000000e+00, %ph0
  %i1 = fadd float 0.000000e+00, %ph1
  %i2 = select i1 false, float %i, float 0.000000e+00
  %i3 = select i1 false, float %i1, float 0.000000e+00
  %ins0 = insertelement <2 x float> zeroinitializer, float %i2, i64 0
  %ins1 = insertelement <2 x float> %ins0, float %i3, i64 1
  %i4 = extractelement <2 x float> %ins1, i64 0
  %i5 = extractelement <2 x float> %ins1, i64 1
  br label %loop
}

define void @test2() {
; CHECK-LABEL: define void @test2() {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    br label [[BB3:%.*]]
; CHECK:       bb1:
; CHECK-NEXT:    [[PH:%.*]] = phi float [ poison, [[BB2:%.*]] ], [ [[TMP3:%.*]], [[LOOP:%.*]] ]
; CHECK-NEXT:    unreachable
; CHECK:       bb2:
; CHECK-NEXT:    br i1 poison, label [[BB3]], label [[BB1:%.*]]
; CHECK:       bb3:
; CHECK-NEXT:    br label [[LOOP]]
; CHECK:       loop:
; CHECK-NEXT:    [[TMP0:%.*]] = phi <2 x float> [ zeroinitializer, [[BB3]] ], [ [[TMP2:%.*]], [[LOOP]] ]
; CHECK-NEXT:    [[TMP1:%.*]] = fadd <2 x float> zeroinitializer, [[TMP0]]
; CHECK-NEXT:    [[TMP2]] = select <2 x i1> zeroinitializer, <2 x float> [[TMP1]], <2 x float> zeroinitializer
; CHECK-NEXT:    [[TMP3]] = extractelement <2 x float> [[TMP2]], i64 1
; CHECK-NEXT:    br i1 poison, label [[BB1]], label [[LOOP]]
;
entry:
  br label %bb3

bb1:
  %ph = phi float [ poison, %bb2 ], [ %i5, %loop ]
  unreachable

bb2:
  br i1 poison, label %bb3, label %bb1

bb3:
  br label %loop

loop:
  %ph0 = phi float [ 0.000000e+00, %bb3 ], [ %i4, %loop ]
  %ph1 = phi float [ 0.000000e+00, %bb3 ], [ %i5, %loop ]
  %i = fadd float 0.000000e+00, %ph0
  %i1 = fadd float 0.000000e+00, %ph1
  %i2 = select i1 false, float %i, float 0.000000e+00
  %i3 = select i1 false, float %i1, float 0.000000e+00
  %ins0 = insertelement <2 x float> zeroinitializer, float %i2, i64 0
  %ins1 = insertelement <2 x float> %ins0, float %i3, i64 1
  %i4 = extractelement <2 x float> %ins1, i64 0
  %i5 = extractelement <2 x float> %ins1, i64 1
  br i1 poison, label %bb1, label %loop
}
