; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 2
; RUN: %if x86-registered-target %{ opt -S -passes=slp-vectorizer -mtriple=x86_64-unknown-linux-gnu < %s | FileCheck %s %}
; RUN: %if aarch64-registered-target %{ opt -S -passes=slp-vectorizer -mtriple=aarch64-unknown-linux-gnu < %s | FileCheck %s %}

define void @blam(ptr %arg, double %load2, i1 %fcmp3) {
; CHECK-LABEL: define void @blam
; CHECK-SAME: (ptr [[ARG:%.*]], double [[LOAD2:%.*]], i1 [[FCMP3:%.*]]) {
; CHECK-NEXT:  bb:
; CHECK-NEXT:    [[GETELEMENTPTR13:%.*]] = getelementptr double, ptr [[ARG]], i64 3
; CHECK-NEXT:    [[TMP0:%.*]] = load <2 x double>, ptr [[ARG]], align 8
; CHECK-NEXT:    [[TMP1:%.*]] = insertelement <2 x i1> poison, i1 [[FCMP3]], i32 0
; CHECK-NEXT:    [[TMP2:%.*]] = shufflevector <2 x i1> [[TMP1]], <2 x i1> poison, <2 x i32> zeroinitializer
; CHECK-NEXT:    [[TMP3:%.*]] = select <2 x i1> [[TMP2]], <2 x double> zeroinitializer, <2 x double> [[TMP0]]
; CHECK-NEXT:    [[TMP4:%.*]] = insertelement <2 x double> [[TMP0]], double [[LOAD2]], i32 0
; CHECK-NEXT:    [[TMP5:%.*]] = fcmp olt <2 x double> [[TMP4]], zeroinitializer
; CHECK-NEXT:    [[TMP6:%.*]] = select <2 x i1> [[TMP5]], <2 x double> zeroinitializer, <2 x double> [[TMP0]]
; CHECK-NEXT:    [[TMP7:%.*]] = fcmp olt <2 x double> [[TMP3]], zeroinitializer
; CHECK-NEXT:    [[TMP8:%.*]] = select <2 x i1> [[TMP7]], <2 x double> <double 0.000000e+00, double 1.000000e+00>, <2 x double> <double 1.000000e+00, double 0.000000e+00>
; CHECK-NEXT:    [[TMP9:%.*]] = shufflevector <2 x double> [[TMP8]], <2 x double> poison, <2 x i32> <i32 1, i32 0>
; CHECK-NEXT:    [[TMP10:%.*]] = fcmp olt <2 x double> [[TMP9]], [[TMP6]]
; CHECK-NEXT:    [[TMP11:%.*]] = shufflevector <2 x double> [[TMP4]], <2 x double> <double poison, double 0.000000e+00>, <2 x i32> <i32 0, i32 3>
; CHECK-NEXT:    [[TMP12:%.*]] = shufflevector <2 x double> [[TMP4]], <2 x double> <double 0.000000e+00, double poison>, <2 x i32> <i32 2, i32 0>
; CHECK-NEXT:    [[TMP13:%.*]] = select <2 x i1> [[TMP10]], <2 x double> [[TMP11]], <2 x double> [[TMP12]]
; CHECK-NEXT:    [[TMP14:%.*]] = fcmp olt <2 x double> [[TMP13]], zeroinitializer
; CHECK-NEXT:    [[TMP15:%.*]] = select <2 x i1> [[TMP14]], <2 x double> zeroinitializer, <2 x double> splat (double 1.000000e+00)
; CHECK-NEXT:    [[TMP16:%.*]] = fcmp ogt <2 x double> [[TMP15]], zeroinitializer
; CHECK-NEXT:    [[TMP17:%.*]] = shufflevector <2 x double> [[TMP4]], <2 x double> poison, <2 x i32> zeroinitializer
; CHECK-NEXT:    [[TMP18:%.*]] = select <2 x i1> [[TMP16]], <2 x double> zeroinitializer, <2 x double> [[TMP17]]
; CHECK-NEXT:    [[TMP19:%.*]] = fcmp olt <2 x double> [[TMP18]], zeroinitializer
; CHECK-NEXT:    [[TMP20:%.*]] = select <2 x i1> [[TMP19]], <2 x double> splat (double 1.000000e+00), <2 x double> zeroinitializer
; CHECK-NEXT:    store <2 x double> [[TMP20]], ptr [[GETELEMENTPTR13]], align 8
; CHECK-NEXT:    ret void
;
bb:
  %getelementptr = getelementptr double, ptr %arg, i64 1
  %load = load double, ptr %getelementptr, align 8
  %fcmp = fcmp olt double %load, 0.000000e+00
  %select3 = select i1 %fcmp, double 0.000000e+00, double %load
  %select4 = select i1 %fcmp3, double 0.000000e+00, double %load
  %load7 = load double, ptr %arg, align 8
  %select10 = select i1 %fcmp3, double 0.000000e+00, double %load7
  %fcmp11 = fcmp olt double %load2, 0.000000e+00
  %select128 = select i1 %fcmp11, double 0.000000e+00, double %load7
  %getelementptr13 = getelementptr double, ptr %arg, i64 3
  %getelementptr21 = getelementptr double, ptr %arg, i64 4
  %fcmp23 = fcmp olt double %select10, 0.000000e+00
  %select24 = select i1 %fcmp23, double 0.000000e+00, double 1.000000e+00
  %fcmp29 = fcmp olt double %select4, 0.000000e+00
  %select30 = select i1 %fcmp29, double 1.000000e+00, double 0.000000e+00
  %fcmp33 = fcmp olt double %select24, %select3
  %select34 = select i1 %fcmp33, double 0.000000e+00, double %load2
  %fcmp39 = fcmp olt double %select30, %select128
  %select40 = select i1 %fcmp39, double %load2, double 0.000000e+00
  %fcmp62 = fcmp olt double %select34, 0.000000e+00
  %select639 = select i1 %fcmp62, double 0.000000e+00, double 1.000000e+00
  %fcmp76 = fcmp olt double %select40, 0.000000e+00
  %select77 = select i1 %fcmp76, double 0.000000e+00, double 1.000000e+00
  %fcmp90 = fcmp ogt double %select639, 0.000000e+00
  %select91 = select i1 %fcmp90, double 0.000000e+00, double %load2
  %fcmp92 = fcmp ogt double %select77, 0.000000e+00
  %select93 = select i1 %fcmp92, double 0.000000e+00, double %load2
  %fcmp108 = fcmp olt double %select93, 0.000000e+00
  %select109 = select i1 %fcmp108, double 1.000000e+00, double 0.000000e+00
  %fcmp110 = fcmp olt double %select91, 0.000000e+00
  %select111 = select i1 %fcmp110, double 1.000000e+00, double 0.000000e+00
  store double %select111, ptr %getelementptr21, align 8
  store double %select109, ptr %getelementptr13, align 8
  ret void
}
