; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 3
; RUN: %if x86-registered-target %{ opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown-linux-gnu < %s | FileCheck %s %}
; RUN: %if aarch64-registered-target %{ opt -S --passes=slp-vectorizer -mtriple=aarch64-unknown-linux-gnu < %s | FileCheck %s %}

define void @test() {
; CHECK-LABEL: define void @test() {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP0:%.*]] = extractelement <1 x i64> zeroinitializer, i64 0
; CHECK-NEXT:    [[BF_VALUE:%.*]] = and i64 [[TMP0]], 0
; CHECK-NEXT:    [[BF_CLEAR:%.*]] = and i64 0, 1
; CHECK-NEXT:    [[BF_SET:%.*]] = or i64 [[BF_CLEAR]], [[BF_VALUE]]
; CHECK-NEXT:    store i64 [[BF_SET]], ptr null, align 8
; CHECK-NEXT:    ret void
;
entry:
  %0 = extractelement <1 x i64> zeroinitializer, i64 0
  %bf.value = and i64 %0, 0
  %bf.clear = and i64 0, 1
  %bf.set = or i64 %bf.clear, %bf.value
  store i64 %bf.set, ptr null, align 8
  ret void
}
