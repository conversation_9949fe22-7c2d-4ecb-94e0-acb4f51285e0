; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S --passes=slp-vectorizer < %s | FileCheck %s

define i32 @test(i1 %.b, i8 %conv18, i32 %k.promoted61) {
; CHECK-LABEL: define i32 @test(
; CHECK-SAME: i1 [[DOTB:%.*]], i8 [[CONV18:%.*]], i32 [[K_PROMOTED61:%.*]]) {
; CHECK-NEXT:    [[TMP1:%.*]] = insertelement <4 x i1> poison, i1 [[DOTB]], i32 0
; CHECK-NEXT:    [[TMP2:%.*]] = shufflevector <4 x i1> [[TMP1]], <4 x i1> poison, <4 x i32> zeroinitializer
; CHECK-NEXT:    [[TMP3:%.*]] = xor <4 x i1> [[TMP2]], splat (i1 true)
; CHECK-NEXT:    [[TMP4:%.*]] = zext <4 x i1> [[TMP3]] to <4 x i8>
; CHECK-NEXT:    [[TMP5:%.*]] = icmp eq <4 x i8> [[TMP4]], zeroinitializer
; CHECK-NEXT:    [[TMP6:%.*]] = zext <4 x i1> [[TMP3]] to <4 x i8>
; CHECK-NEXT:    [[TMP7:%.*]] = freeze <4 x i8> [[TMP6]]
; CHECK-NEXT:    [[TMP8:%.*]] = insertelement <4 x i8> poison, i8 [[CONV18]], i32 0
; CHECK-NEXT:    [[TMP9:%.*]] = shufflevector <4 x i8> [[TMP8]], <4 x i8> poison, <4 x i32> zeroinitializer
; CHECK-NEXT:    [[TMP10:%.*]] = icmp ugt <4 x i8> [[TMP7]], [[TMP9]]
; CHECK-NEXT:    [[TMP11:%.*]] = select <4 x i1> [[TMP10]], <4 x i8> zeroinitializer, <4 x i8> [[TMP7]]
; CHECK-NEXT:    [[TMP12:%.*]] = sub nuw <4 x i8> [[TMP9]], [[TMP11]]
; CHECK-NEXT:    [[TMP13:%.*]] = select <4 x i1> [[TMP5]], <4 x i8> [[TMP9]], <4 x i8> [[TMP12]]
; CHECK-NEXT:    [[TMP14:%.*]] = zext <4 x i8> [[TMP13]] to <4 x i32>
; CHECK-NEXT:    [[TMP15:%.*]] = call i32 @llvm.vector.reduce.or.v4i32(<4 x i32> [[TMP14]])
; CHECK-NEXT:    [[OP_RDX:%.*]] = or i32 [[TMP15]], [[K_PROMOTED61]]
; CHECK-NEXT:    ret i32 [[OP_RDX]]
;
  %not..b79 = xor i1 %.b, true
  %3 = zext i1 %not..b79 to i8
  %cmp.i51 = icmp eq i8 %3, 0
  %cond.i55 = freeze i8 %3
  %.cmp = icmp ugt i8 %cond.i55, %conv18
  %.urem = select i1 %.cmp, i8 0, i8 %cond.i55
  %4 = sub nuw i8 %conv18, %.urem
  %cond.in.i = select i1 %cmp.i51, i8 %conv18, i8 %4
  %not..b80 = xor i1 %.b, true
  %5 = zext i1 %not..b80 to i8
  %cmp.i51.1 = icmp eq i8 %5, 0
  %cond.i55.1 = freeze i8 %5
  %.cmp.1 = icmp ugt i8 %cond.i55.1, %conv18
  %.urem.1 = select i1 %.cmp.1, i8 0, i8 %cond.i55.1
  %6 = sub nuw i8 %conv18, %.urem.1
  %cond.in.i.1 = select i1 %cmp.i51.1, i8 %conv18, i8 %6
  %not..b81 = xor i1 %.b, true
  %7 = zext i1 %not..b81 to i8
  %cmp.i51.2 = icmp eq i8 %7, 0
  %cond.i55.2 = freeze i8 %7
  %.cmp.2 = icmp ugt i8 %cond.i55.2, %conv18
  %.urem.2 = select i1 %.cmp.2, i8 0, i8 %cond.i55.2
  %8 = sub nuw i8 %conv18, %.urem.2
  %cond.in.i.2 = select i1 %cmp.i51.2, i8 %conv18, i8 %8
  %not..b = xor i1 %.b, true
  %9 = zext i1 %not..b to i8
  %cmp.i51.3 = icmp eq i8 %9, 0
  %cond.i55.3 = freeze i8 %9
  %.cmp.3 = icmp ugt i8 %cond.i55.3, %conv18
  %.urem.3 = select i1 %.cmp.3, i8 0, i8 %cond.i55.3
  %10 = sub nuw i8 %conv18, %.urem.3
  %cond.in.i.3 = select i1 %cmp.i51.3, i8 %conv18, i8 %10
  %conv26 = zext nneg i8 %cond.in.i to i32
  %or = or i32 %k.promoted61, %conv26
  %conv26.1 = zext nneg i8 %cond.in.i.1 to i32
  %or.1 = or i32 %or, %conv26.1
  %conv26.2 = zext nneg i8 %cond.in.i.2 to i32
  %or.2 = or i32 %or.1, %conv26.2
  %conv26.3 = zext nneg i8 %cond.in.i.3 to i32
  %or.3 = or i32 %or.2, %conv26.3
  ret i32 %or.3
}
