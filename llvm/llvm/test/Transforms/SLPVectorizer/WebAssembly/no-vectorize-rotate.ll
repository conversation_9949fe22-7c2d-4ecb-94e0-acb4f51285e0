; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt < %s -passes=slp-vectorizer,instcombine -S | FileCheck %s

; Regression test for a bug in the SLP vectorizer that was causing
; these rotates to be incorrectly combined into a vector rotate.

target datalayout = "e-m:e-p:32:32-i64:64-n32:64-S128"
target triple = "wasm32-unknown-unknown"

define void @foo(<2 x i64> %x, <4 x i32> %y, ptr %out) #0 {
; CHECK-LABEL: @foo(
; CHECK-NEXT:    [[A:%.*]] = extractelement <2 x i64> [[X:%.*]], i64 0
; CHECK-NEXT:    [[B:%.*]] = extractelement <4 x i32> [[Y:%.*]], i64 2
; CHECK-NEXT:    [[CONV6:%.*]] = zext i32 [[B]] to i64
; CHECK-NEXT:    [[C:%.*]] = tail call i64 @llvm.fshl.i64(i64 [[A]], i64 [[A]], i64 [[CONV6]])
; CHECK-NEXT:    store i64 [[C]], ptr [[OUT:%.*]], align 8
; CHECK-NEXT:    [[D:%.*]] = extractelement <2 x i64> [[X]], i64 1
; CHECK-NEXT:    [[E:%.*]] = extractelement <4 x i32> [[Y]], i64 3
; CHECK-NEXT:    [[CONV17:%.*]] = zext i32 [[E]] to i64
; CHECK-NEXT:    [[F:%.*]] = tail call i64 @llvm.fshl.i64(i64 [[D]], i64 [[D]], i64 [[CONV17]])
; CHECK-NEXT:    [[ARRAYIDX2:%.*]] = getelementptr inbounds nuw i8, ptr [[OUT]], i32 8
; CHECK-NEXT:    store i64 [[F]], ptr [[ARRAYIDX2]], align 8
; CHECK-NEXT:    ret void
;
  %a = extractelement <2 x i64> %x, i32 0
  %b = extractelement <4 x i32> %y, i32 2
  %conv6 = zext i32 %b to i64
  %c = tail call i64 @llvm.fshl.i64(i64 %a, i64 %a, i64 %conv6)
  store i64 %c, ptr %out
  %d = extractelement <2 x i64> %x, i32 1
  %e = extractelement <4 x i32> %y, i32 3
  %conv17 = zext i32 %e to i64
  %f = tail call i64 @llvm.fshl.i64(i64 %d, i64 %d, i64 %conv17)
  %arrayidx2 = getelementptr inbounds i64, ptr %out, i32 1
  store i64 %f, ptr %arrayidx2
  ret void
}

declare i64 @llvm.fshl.i64(i64, i64, i64)

attributes #0 = {"target-cpu"="generic" "target-features"="+simd128"}
