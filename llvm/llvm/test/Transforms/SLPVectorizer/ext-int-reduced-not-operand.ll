; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 4
; RUN: %if x86-registered-target %{ opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown-linux-gnu -slp-threshold=-99999 < %s | FileCheck %s %}
; RUN: %if x86-registered-target %{ opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown-linux-gnu -slp-threshold=-99999\
; RUN: -slp-skip-early-profitability-check < %s | FileCheck %s --check-prefixes=FORCED %}
; RUN: %if aarch64-registered-target %{ opt -S --passes=slp-vectorizer -mtriple=aarch64-unknown-linux-gnu -slp-threshold=-99999 < %s | FileCheck %s %}
; RUN: %if aarch64-registered-target %{ opt -S --passes=slp-vectorizer -mtriple=aarch64-unknown-linux-gnu -slp-threshold=-99999\
; RUN: -slp-skip-early-profitability-check < %s | FileCheck %s --check-prefixes=FORCED %}

define i64 @wombat() {
; FORCED-LABEL: define i64 @wombat() {
; FORCED-NEXT:  bb:
; FORCED-NEXT:    br label [[BB2:%.*]]
; FORCED:       bb1:
; FORCED-NEXT:    br label [[BB2]]
; FORCED:       bb2:
; FORCED-NEXT:    [[PHI:%.*]] = phi i32 [ 0, [[BB:%.*]] ], [ 0, [[BB1:%.*]] ]
; FORCED-NEXT:    [[TMP0:%.*]] = insertelement <2 x i32> poison, i32 [[PHI]], i32 0
; FORCED-NEXT:    [[TMP1:%.*]] = shufflevector <2 x i32> [[TMP0]], <2 x i32> poison, <2 x i32> zeroinitializer
; FORCED-NEXT:    [[TMP2:%.*]] = trunc <2 x i32> [[TMP1]] to <2 x i1>
; FORCED-NEXT:    [[TMP3:%.*]] = extractelement <2 x i1> [[TMP2]], i32 0
; FORCED-NEXT:    [[TMP4:%.*]] = zext i1 [[TMP3]] to i64
; FORCED-NEXT:    [[TMP5:%.*]] = extractelement <2 x i1> [[TMP2]], i32 1
; FORCED-NEXT:    [[TMP6:%.*]] = zext i1 [[TMP5]] to i64
; FORCED-NEXT:    [[OR:%.*]] = or i64 [[TMP4]], [[TMP6]]
; FORCED-NEXT:    ret i64 [[OR]]
;
; CHECK-LABEL: define i64 @wombat() {
; CHECK-NEXT:  bb:
; CHECK-NEXT:    br label [[BB2:%.*]]
; CHECK:       bb1:
; CHECK-NEXT:    br label [[BB2]]
; CHECK:       bb2:
; CHECK-NEXT:    [[PHI:%.*]] = phi i32 [ 0, [[BB:%.*]] ], [ 0, [[BB1:%.*]] ]
; CHECK-NEXT:    [[TMP4:%.*]] = zext i32 [[PHI]] to i64
; CHECK-NEXT:    [[TMP6:%.*]] = sext i32 [[PHI]] to i64
; CHECK-NEXT:    [[OR:%.*]] = or i64 [[TMP4]], [[TMP6]]
; CHECK-NEXT:    ret i64 [[OR]]
;
bb:
  br label %bb2

bb1:
  br label %bb2

bb2:
  %phi = phi i32 [ 0, %bb ], [ 0, %bb1 ]
  %zext = zext i32 %phi to i64
  %sext = sext i32 %phi to i64
  %or = or i64 %zext, %sext
  ret i64 %or
}
