; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -passes=slp-vectorizer -S -slp-max-reg-size=1024 %s | FileCheck %s

define ptr @test() {
; CHECK-LABEL: @test(
; CHECK-NEXT:    store <4 x double> <double poison, double 0.000000e+00, double 0.000000e+00, double 0.000000e+00>, ptr null, align 8
; CHECK-NEXT:    ret ptr null
;
  store double poison, ptr null, align 8
  %1 = getelementptr i8, ptr null, i64 8
  %2 = fmul double 0.000000e+00, 0.000000e+00
  store double %2, ptr %1, align 8
  %3 = getelementptr i8, ptr null, i64 16
  %4 = fmul double 0.000000e+00, 0.000000e+00
  store double %4, ptr %3, align 8
  %5 = getelementptr i8, ptr null, i64 24
  store double %2, ptr %5, align 8
  ret ptr null
}
