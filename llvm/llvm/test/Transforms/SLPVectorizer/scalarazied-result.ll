; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: %if x86-registered-target %{ opt -passes=slp-vectorizer -mtriple=x86_64-unknown-linux-gnu -S < %s | FileCheck %s %}
; RUN: %if aarch64-registered-target %{ opt -passes=slp-vectorizer -mtriple=aarch64-unknown-linux-gnu -S < %s | FileCheck %s %}

define void @test() {
; CHECK-LABEL: @test(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    ret void
;
entry:
  %0 = extractelement <8 x half> zeroinitializer, i64 1
  %tobool = fcmp une half %0, 0xH0000
  %1 = extractelement <8 x half> zeroinitializer, i64 1
  %tobool3 = fcmp une half %1, 0xH0000
  ret void
}
