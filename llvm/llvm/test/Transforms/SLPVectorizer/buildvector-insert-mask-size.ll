; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: %if x86-registered-target %{ opt -S -passes=slp-vectorizer -mtriple=x86_64-unknown-linux < %s -slp-threshold=-1 | FileCheck %s %}
; RUN: %if aarch64-registered-target %{ opt -S -passes=slp-vectorizer -mtriple=aarch64-unknown-linux < %s -slp-threshold=-1 | FileCheck %s %}

define void @test() {
; CHECK-LABEL: @test(
; CHECK-NEXT:    [[TMP1:%.*]] = getelementptr inbounds float, ptr undef, i32 2
; CHECK-NEXT:    [[TMP2:%.*]] = load <2 x float>, ptr [[TMP1]], align 4
; CHECK-NEXT:    [[TMP3:%.*]] = shufflevector <2 x float> [[TMP2]], <2 x float> poison, <3 x i32> <i32 0, i32 poison, i32 1>
; CHECK-NEXT:    store <3 x float> [[TMP3]], ptr null, align 4
; CHECK-NEXT:    ret void
;
  %1 = getelementptr inbounds float, ptr undef, i32 2
  %2 = load float, ptr %1, align 4
  %3 = getelementptr inbounds float, ptr undef, i32 3
  %4 = load float, ptr %3, align 4
  %5 = insertelement <3 x float> poison, float %2, i64 0
  %6 = insertelement <3 x float> %5, float %4, i64 2
  store <3 x float> %6, ptr null, align 4
  ret void
}
