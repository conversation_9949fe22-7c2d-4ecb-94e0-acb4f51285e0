; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: %if x86_64-registered-target %{ opt -S -passes=slp-vectorizer -mtriple=x86_64-grtev4-linux-gnu -o - < %s | FileCheck %s %}
; RUN: %if aarch64-registered-target %{ opt -S -passes=slp-vectorizer -mtriple=aarch64-unknown-linux-gnu -o - < %s | FileCheck %s %}

define i32 @crash() {
; CHECK-LABEL: @crash(
; CHECK-NEXT:  label:
; CHECK-NEXT:    [[ADD:%.*]] = fadd <2 x double> zeroinitializer, zeroinitializer
; CHECK-NEXT:    [[SHUFFLE:%.*]] = shufflevector <2 x double> [[ADD]], <2 x double> poison, <2 x i32> <i32 1, i32 0>
; CHECK-NEXT:    [[TMP0:%.*]] = shufflevector <2 x double> zeroinitializer, <2 x double> [[SHUFFLE]], <2 x i32> <i32 2, i32 1>
; CHECK-NEXT:    [[TMP1:%.*]] = fmul <2 x double> [[SHUFFLE]], zeroinitializer
; CHECK-NEXT:    [[TMP2:%.*]] = extractelement <2 x double> [[TMP1]], i32 0
; CHECK-NEXT:    [[TMP3:%.*]] = extractelement <2 x double> [[TMP1]], i32 1
; CHECK-NEXT:    [[ADD1:%.*]] = fadd double [[TMP2]], [[TMP3]]
; CHECK-NEXT:    [[MUL1:%.*]] = fmul double [[ADD1]], 0.000000e+00
; CHECK-NEXT:    store double [[MUL1]], ptr null, align 16
; CHECK-NEXT:    ret i32 0
;
label:
  %0 = extractelement <2 x double> zeroinitializer, i64 1
  %1 = extractelement <2 x double> zeroinitializer, i64 0
  %add = fadd <2 x double> zeroinitializer, zeroinitializer
  %extract1 = extractelement <2 x double> %add, i64 1
  %2 = fmul double %extract1, %1
  %insert = insertelement <2 x double> zeroinitializer, double %extract1, i64 0
  %extract0 = extractelement <2 x double> %add, i64 0
  %mul = fmul double %extract0, %0
  %add1 = fadd double %2, %mul
  %mul1 = fmul double %add1, 0.000000e+00
  store double %mul1, ptr null, align 16
  ret i32 0
}
