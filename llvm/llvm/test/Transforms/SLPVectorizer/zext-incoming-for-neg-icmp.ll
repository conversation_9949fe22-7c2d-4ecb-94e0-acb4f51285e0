; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 4
; RUN: %if x86-registered-target %{ opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown-linux-gnu < %s | FileCheck %s %}
; RUN: %if aarch64-registered-target %{ opt -S --passes=slp-vectorizer -mtriple=aarch64-unknown-linux-gnu < %s | FileCheck %s %}

define i32 @test(i32 %a, i8 %b, i8 %c) {
; CHECK-LABEL: define i32 @test(
; CHECK-SAME: i32 [[A:%.*]], i8 [[B:%.*]], i8 [[C:%.*]]) {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP0:%.*]] = insertelement <4 x i8> poison, i8 [[C]], i32 0
; CHECK-NEXT:    [[TMP1:%.*]] = shufflevector <4 x i8> [[TMP0]], <4 x i8> poison, <4 x i32> zeroinitializer
; CHECK-NEXT:    [[TMP2:%.*]] = add <4 x i8> [[TMP1]], <i8 -1, i8 -2, i8 -3, i8 -4>
; CHECK-NEXT:    [[TMP3:%.*]] = insertelement <4 x i8> poison, i8 [[B]], i32 0
; CHECK-NEXT:    [[TMP4:%.*]] = shufflevector <4 x i8> [[TMP3]], <4 x i8> poison, <4 x i32> zeroinitializer
; CHECK-NEXT:    [[TMP8:%.*]] = zext <4 x i8> [[TMP2]] to <4 x i16>
; CHECK-NEXT:    [[TMP9:%.*]] = sext <4 x i8> [[TMP4]] to <4 x i16>
; CHECK-NEXT:    [[TMP5:%.*]] = icmp sle <4 x i16> [[TMP8]], [[TMP9]]
; CHECK-NEXT:    [[TMP10:%.*]] = bitcast <4 x i1> [[TMP5]] to i4
; CHECK-NEXT:    [[TMP11:%.*]] = call i4 @llvm.ctpop.i4(i4 [[TMP10]])
; CHECK-NEXT:    [[TMP7:%.*]] = zext i4 [[TMP11]] to i32
; CHECK-NEXT:    [[OP_RDX:%.*]] = add i32 [[TMP7]], [[A]]
; CHECK-NEXT:    ret i32 [[OP_RDX]]
;
entry:
  %0 = add i8 %c, -3
  %dec19 = add i8 %c, -1
  %conv20 = zext i8 %dec19 to i32
  %conv16.1 = sext i8 %b to i32
  %cmp17.1 = icmp sle i32 %conv20, %conv16.1
  %conv18.1 = zext i1 %cmp17.1 to i32
  %a.1 = add nsw i32 %conv18.1, %a
  %dec19.1 = add i8 %c, -2
  %conv20.1 = zext i8 %dec19.1 to i32
  %conv16.2 = sext i8 %b to i32
  %cmp17.2 = icmp sle i32 %conv20.1, %conv16.2
  %conv18.2 = zext i1 %cmp17.2 to i32
  %a.2 = add nsw i32 %a.1, %conv18.2
  %1 = zext i8 %0 to i32
  %conv16.158 = sext i8 %b to i32
  %cmp17.159 = icmp sle i32 %1, %conv16.158
  %conv18.160 = zext i1 %cmp17.159 to i32
  %a.161 = add nsw i32 %a.2, %conv18.160
  %dec19.162 = add i8 %c, -4
  %conv20.163 = zext i8 %dec19.162 to i32
  %conv16.1.1 = sext i8 %b to i32
  %cmp17.1.1 = icmp sle i32 %conv20.163, %conv16.1.1
  %conv18.1.1 = zext i1 %cmp17.1.1 to i32
  %a.1.1 = add nsw i32 %a.161, %conv18.1.1
  ret i32 %a.1.1
}

