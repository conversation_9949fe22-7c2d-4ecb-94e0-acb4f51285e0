; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 4
; RUN: opt -S --passes=slp-vectorizer < %s | FileCheck %s

define i32 @test(i4 %0) {
; CHECK-LABEL: define i32 @test(
; CHECK-SAME: i4 [[TMP0:%.*]]) {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP1:%.*]] = trunc i8 0 to i4
; CHECK-NEXT:    [[TMP2:%.*]] = trunc i8 0 to i4
; CHECK-NEXT:    [[ADD_R:%.*]] = or i4 [[TMP1]], [[TMP0]]
; CHECK-NEXT:    [[ADD_R14:%.*]] = or i4 0, [[TMP2]]
; CHECK-NEXT:    [[CMP_NOT:%.*]] = icmp eq i4 [[ADD_R]], [[ADD_R14]]
; CHECK-NEXT:    ret i32 0
;
entry:
  %1 = trunc i8 0 to i4
  %2 = trunc i8 0 to i4
  %add.r = or i4 %1, %0
  %add.r14 = or i4 0, %2
  %cmp.not = icmp eq i4 %add.r, %add.r14
  ret i32 0
}
