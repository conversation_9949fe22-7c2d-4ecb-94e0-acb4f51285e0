; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: %if x86-registered-target %{ opt < %s -S -mtriple=x86_64-unknown -passes=slp-vectorizer -slp-min-reg-size=64 -slp-threshold=-1000 | FileCheck %s %}
; RUN: %if aarch64-registered-target %{ opt < %s -S -mtriple=aarch64-unknown -passes=slp-vectorizer -slp-min-reg-size=64 -slp-threshold=-1000 | FileCheck %s %}

define i32 @non-ordered-stores(ptr noalias nocapture %in, ptr noalias nocapture %inn, ptr noalias nocapture %out) {
; CHECK-LABEL: @non-ordered-stores(
; CHECK-NEXT:    [[LOAD_1:%.*]] = load i32, ptr [[IN:%.*]], align 4
; CHECK-NEXT:    [[GEP_1:%.*]] = getelementptr inbounds i32, ptr [[IN]], i64 1
; CHECK-NEXT:    [[LOAD_2:%.*]] = load i32, ptr [[GEP_1]], align 4
; CHECK-NEXT:    [[GEP_2:%.*]] = getelementptr inbounds i32, ptr [[IN]], i64 2
; CHECK-NEXT:    [[LOAD_3:%.*]] = load i32, ptr [[GEP_2]], align 4
; CHECK-NEXT:    [[GEP_3:%.*]] = getelementptr inbounds i32, ptr [[IN]], i64 3
; CHECK-NEXT:    [[LOAD_4:%.*]] = load i32, ptr [[GEP_3]], align 4
; CHECK-NEXT:    [[LOAD_5:%.*]] = load i32, ptr [[INN:%.*]], align 4
; CHECK-NEXT:    [[GEP_4:%.*]] = getelementptr inbounds i32, ptr [[INN]], i64 1
; CHECK-NEXT:    [[LOAD_6:%.*]] = load i32, ptr [[GEP_4]], align 4
; CHECK-NEXT:    [[GEP_5:%.*]] = getelementptr inbounds i32, ptr [[INN]], i64 2
; CHECK-NEXT:    [[LOAD_7:%.*]] = load i32, ptr [[GEP_5]], align 4
; CHECK-NEXT:    [[GEP_6:%.*]] = getelementptr inbounds i32, ptr [[INN]], i64 3
; CHECK-NEXT:    [[LOAD_8:%.*]] = load i32, ptr [[GEP_6]], align 4
; CHECK-NEXT:    [[TMP1:%.*]] = insertelement <2 x i32> poison, i32 [[LOAD_1]], i32 0
; CHECK-NEXT:    [[TMP2:%.*]] = insertelement <2 x i32> [[TMP1]], i32 [[LOAD_3]], i32 1
; CHECK-NEXT:    [[TMP3:%.*]] = insertelement <2 x i32> poison, i32 [[LOAD_5]], i32 0
; CHECK-NEXT:    [[TMP4:%.*]] = insertelement <2 x i32> [[TMP3]], i32 [[LOAD_7]], i32 1
; CHECK-NEXT:    [[TMP5:%.*]] = mul <2 x i32> [[TMP2]], [[TMP4]]
; CHECK-NEXT:    [[TMP6:%.*]] = insertelement <2 x i32> poison, i32 [[LOAD_2]], i32 0
; CHECK-NEXT:    [[TMP7:%.*]] = insertelement <2 x i32> [[TMP6]], i32 [[LOAD_4]], i32 1
; CHECK-NEXT:    [[TMP8:%.*]] = insertelement <2 x i32> poison, i32 [[LOAD_6]], i32 0
; CHECK-NEXT:    [[TMP9:%.*]] = insertelement <2 x i32> [[TMP8]], i32 [[LOAD_8]], i32 1
; CHECK-NEXT:    [[TMP10:%.*]] = mul <2 x i32> [[TMP7]], [[TMP9]]
; CHECK-NEXT:    br label [[BLOCK1:%.*]]
; CHECK:       block1:
; CHECK-NEXT:    [[GEP_X:%.*]] = getelementptr inbounds i32, ptr [[INN]], i64 5
; CHECK-NEXT:    [[LOAD_9:%.*]] = load i32, ptr [[GEP_X]], align 4
; CHECK-NEXT:    br label [[BLOCK2:%.*]]
; CHECK:       block2:
; CHECK-NEXT:    [[GEP_9:%.*]] = getelementptr inbounds i32, ptr [[OUT:%.*]], i64 2
; CHECK-NEXT:    [[GEP_10:%.*]] = getelementptr inbounds i32, ptr [[OUT]], i64 3
; CHECK-NEXT:    store i32 [[LOAD_9]], ptr [[GEP_9]], align 4
; CHECK-NEXT:    store <2 x i32> [[TMP5]], ptr [[GEP_10]], align 4
; CHECK-NEXT:    store <2 x i32> [[TMP10]], ptr [[OUT]], align 4
; CHECK-NEXT:    ret i32 undef
;
  %load.1 = load i32, ptr %in, align 4
  %gep.1 = getelementptr inbounds i32, ptr %in, i64 1
  %load.2 = load i32, ptr %gep.1, align 4
  %gep.2 = getelementptr inbounds i32, ptr %in, i64 2
  %load.3 = load i32, ptr %gep.2, align 4
  %gep.3 = getelementptr inbounds i32, ptr %in, i64 3
  %load.4 = load i32, ptr %gep.3, align 4
  %load.5 = load i32, ptr %inn, align 4
  %gep.4 = getelementptr inbounds i32, ptr %inn, i64 1
  %load.6 = load i32, ptr %gep.4, align 4
  %gep.5 = getelementptr inbounds i32, ptr %inn, i64 2
  %load.7 = load i32, ptr %gep.5, align 4
  %gep.6 = getelementptr inbounds i32, ptr %inn, i64 3
  %load.8 = load i32, ptr %gep.6, align 4
  %mul.1 = mul i32 %load.1, %load.5
  %mul.2 = mul i32 %load.2, %load.6
  %mul.3 = mul i32 %load.3, %load.7
  %mul.4 = mul i32 %load.4, %load.8
  br label %block1

block1:
  %gep.x = getelementptr inbounds i32, ptr %inn, i64 5
  %load.9 = load i32, ptr %gep.x, align 4
  br label %block2

block2:
  %gep.8 = getelementptr inbounds i32, ptr %out, i64 1
  %gep.9 = getelementptr inbounds i32, ptr %out, i64 2
  %gep.10 = getelementptr inbounds i32, ptr %out, i64 3
  %gep.11 = getelementptr inbounds i32, ptr %out, i64 4
  store i32 %mul.1, ptr %gep.10, align 4
  store i32 %load.9, ptr %gep.9, align 4
  store i32 %mul.2, ptr %out, align 4
  store i32 %mul.3, ptr %gep.11, align 4
  store i32 %mul.4, ptr %gep.8, align 4

  ret i32 undef
}
