; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S -passes=slp-vectorizer < %s | FileCheck %s

define void @foo() {
; CHECK-LABEL: define void @foo() {
; CHECK-NEXT:  [[BB:.*]]:
; CHECK-NEXT:    br label %[[BB1:.*]]
; CHECK:       [[BB1]]:
; CHECK-NEXT:    [[TMP0:%.*]] = phi <2 x i32> [ [[TMP11:%.*]], %[[BB3:.*]] ], [ zeroinitializer, %[[BB]] ]
; CHECK-NEXT:    br label %[[BB3]]
; CHECK:       [[BB3]]:
; CHECK-NEXT:    [[TMP1:%.*]] = trunc <2 x i32> [[TMP0]] to <2 x i1>
; CHECK-NEXT:    [[TMP2:%.*]] = mul <2 x i1> [[TMP1]], zeroinitializer
; CHECK-NEXT:    [[TMP3:%.*]] = or <2 x i1> zeroinitializer, [[TMP2]]
; CHECK-NEXT:    [[TMP4:%.*]] = and <2 x i1> [[TMP3]], zeroinitializer
; CHECK-NEXT:    [[TMP5:%.*]] = extractelement <2 x i1> [[TMP4]], i32 0
; CHECK-NEXT:    [[TMP6:%.*]] = zext i1 [[TMP5]] to i32
; CHECK-NEXT:    [[TMP7:%.*]] = extractelement <2 x i1> [[TMP4]], i32 1
; CHECK-NEXT:    [[TMP8:%.*]] = zext i1 [[TMP7]] to i32
; CHECK-NEXT:    [[I22:%.*]] = or i32 [[TMP6]], [[TMP8]]
; CHECK-NEXT:    [[TMP9:%.*]] = insertelement <2 x i32> <i32 poison, i32 0>, i32 [[I22]], i32 0
; CHECK-NEXT:    [[TMP10:%.*]] = icmp ult <2 x i32> [[TMP9]], zeroinitializer
; CHECK-NEXT:    [[TMP11]] = select <2 x i1> [[TMP10]], <2 x i32> zeroinitializer, <2 x i32> zeroinitializer
; CHECK-NEXT:    br label %[[BB1]]
;
bb:
  br label %bb1

bb1:                                              ; preds = %bb3, %bb
  %i = phi i32 [ %i26, %bb3 ], [ 0, %bb ]
  %i2 = phi i32 [ %i24, %bb3 ], [ 0, %bb ]
  br label %bb3

bb3:                                              ; preds = %bb1
  %i4 = zext i32 %i2 to i64
  %i5 = mul i64 %i4, 0
  %i10 = or i64 0, %i5
  %i11 = trunc i64 %i10 to i32
  %i12 = and i32 %i11, 0
  %i13 = zext i32 %i to i64
  %i14 = mul i64 %i13, 0
  %i19 = or i64 0, %i14
  %i20 = trunc i64 %i19 to i32
  %i21 = and i32 %i20, 0
  %i22 = or i32 %i12, %i21
  %i23 = icmp ult i32 %i22, 0
  %i24 = select i1 %i23, i32 0, i32 0
  %i25 = icmp ult i32 0, 0
  %i26 = select i1 %i25, i32 0, i32 0
  br label %bb1
}
