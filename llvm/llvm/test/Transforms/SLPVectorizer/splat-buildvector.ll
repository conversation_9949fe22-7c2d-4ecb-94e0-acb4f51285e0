; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 4
; RUN: %if x86-registered-target %{ opt --passes=slp-vectorizer -S -mtriple=x86_64-unknown-linux-gnu %s -o - -slp-threshold=-100 | FileCheck %s %}
; RUN: %if aarch64-registered-target %{ opt --passes=slp-vectorizer -S -mtriple=aarch64-unknown-linux-gnu %s -o - -slp-threshold=-100 | FileCheck %s %}

declare i64 @llvm.smax.i64(i64, i64)

define i8 @foo(i64 %val_i64_57) {
; CHECK-LABEL: define i8 @foo(
; CHECK-SAME: i64 [[VAL_I64_57:%.*]]) {
; CHECK-NEXT:  entry_1:
; CHECK-NEXT:    [[VAL_I64_58:%.*]] = call i64 @llvm.smax.i64(i64 0, i64 1)
; CHECK-NEXT:    [[TMP0:%.*]] = insertelement <4 x i64> <i64 0, i64 poison, i64 poison, i64 0>, i64 [[VAL_I64_57]], i32 1
; CHECK-NEXT:    [[TMP1:%.*]] = insertelement <4 x i64> [[TMP0]], i64 [[VAL_I64_58]], i32 2
; CHECK-NEXT:    [[TMP2:%.*]] = shufflevector <4 x i64> [[TMP1]], <4 x i64> <i64 poison, i64 poison, i64 poison, i64 undef>, <4 x i32> <i32 2, i32 2, i32 2, i32 7>
; CHECK-NEXT:    [[TMP3:%.*]] = icmp ule <4 x i64> [[TMP1]], [[TMP2]]
; CHECK-NEXT:    [[TMP4:%.*]] = icmp sle <4 x i64> [[TMP1]], [[TMP2]]
; CHECK-NEXT:    [[TMP5:%.*]] = shufflevector <4 x i1> [[TMP3]], <4 x i1> [[TMP4]], <4 x i32> <i32 0, i32 1, i32 2, i32 7>
; CHECK-NEXT:    ret i8 0
;
entry_1:
  %val_i64_58 = call i64 @llvm.smax.i64(i64 0, i64 1)
  %val_i1_89 = icmp ule i64 %val_i64_57, %val_i64_58
  %val_i1_95 = icmp sle i64 0, undef
  %val_i1_98 = icmp uge i64 %val_i64_58, %val_i64_58
  %val_i1_99 = icmp ule i64 0, %val_i64_58
  ret i8 0
}
