; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 3
; RUN: %if x86-registered-target %{ opt -S --passes=slp-vectorizer -mtriple=x86_64-pc-linux-gnu < %s | FileCheck %s %}
; RUN: %if aarch64-registered-target %{ opt -S --passes=slp-vectorizer -mtriple=aarch64-unknown-linux-gnu < %s | FileCheck %s %}


define void @test(double %add) {
; CHECK-LABEL: define void @test(
; CHECK-SAME: double [[ADD:%.*]]) {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP0:%.*]] = insertelement <2 x double> <double poison, double 0.000000e+00>, double [[ADD]], i32 0
; CHECK-NEXT:    [[TMP1:%.*]] = fmul <2 x double> [[TMP0]], zeroinitializer
; CHECK-NEXT:    [[TMP2:%.*]] = fmul <2 x double> [[TMP0]], zeroinitializer
; CHECK-NEXT:    br label [[COND_TRUE45:%.*]]
; CHECK:       cond.true45:
; CHECK-NEXT:    [[TMP3:%.*]] = fmul <2 x double> [[TMP1]], zeroinitializer
; CHECK-NEXT:    [[TMP4:%.*]] = fmul <2 x double> [[TMP2]], zeroinitializer
; CHECK-NEXT:    [[TMP5:%.*]] = fadd <2 x double> zeroinitializer, [[TMP3]]
; CHECK-NEXT:    [[TMP6:%.*]] = fsub <2 x double> [[TMP5]], zeroinitializer
; CHECK-NEXT:    [[TMP7:%.*]] = fsub <2 x double> [[TMP6]], [[TMP4]]
; CHECK-NEXT:    ret void
;
entry:
  %mul1 = fmul double %add, 0.000000e+00
  %mul2 = fmul double %add, 0.000000e+00
  %mul3 = fmul double 0.000000e+00, 0.000000e+00
  br label %cond.true45

cond.true45:
  %mul26 = fmul double %mul1, 0.000000e+00
  %add27 = fadd double 0.000000e+00, %mul26
  %sub28 = fsub double %add27, 0.000000e+00
  %mul30 = fmul double %mul2, 0.000000e+00
  %sub31 = fsub double %sub28, %mul30
  %mul7 = fmul double %mul3, 0.000000e+00
  %add8 = fadd double %mul7, 0.000000e+00
  %sub9 = fsub double %add8, 0.000000e+00
  %mul11 = fmul double %mul3, 0.000000e+00
  %sub12 = fsub double %sub9, %mul11
  %0 = insertelement <2 x double> zeroinitializer, double %sub31, i32 0
  %1 = insertelement <2 x double> %0, double %sub12, i32 1
  ret void
}
