; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 4
; RUN: %if x86-registered-target %{ opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown-linux-gnu < %s | FileCheck %s %}
; RUN: %if aarch64-registered-target %{ opt -S --passes=slp-vectorizer -mtriple=aarch64-unknown-linux-gnu < %s | FileCheck %s %}

define i32 @test(ptr %sptr, i64 %0) {
; CHECK-LABEL: define i32 @test(
; CHECK-SAME: ptr [[SPTR:%.*]], i64 [[TMP0:%.*]]) {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[CONV_I:%.*]] = trunc i64 [[TMP0]] to i32
; CHECK-NEXT:    [[IV2:%.*]] = getelementptr i8, ptr [[SPTR]], i64 4
; CHECK-NEXT:    [[TMP1:%.*]] = load <4 x i32>, ptr [[IV2]], align 4
; CHECK-NEXT:    [[TMP2:%.*]] = insertelement <4 x i32> [[TMP1]], i32 [[CONV_I]], i32 1
; CHECK-NEXT:    [[TMP3:%.*]] = shufflevector <4 x i32> [[TMP2]], <4 x i32> poison, <4 x i32> <i32 0, i32 1, i32 2, i32 1>
; CHECK-NEXT:    [[TMP4:%.*]] = shufflevector <4 x i32> [[TMP2]], <4 x i32> [[TMP1]], <4 x i32> <i32 1, i32 5, i32 1, i32 poison>
; CHECK-NEXT:    [[TMP5:%.*]] = shufflevector <4 x i32> [[TMP4]], <4 x i32> <i32 poison, i32 poison, i32 poison, i32 0>, <4 x i32> <i32 0, i32 1, i32 2, i32 7>
; CHECK-NEXT:    [[TMP7:%.*]] = icmp slt <4 x i32> [[TMP3]], [[TMP5]]
; CHECK-NEXT:    [[TMP12:%.*]] = icmp sle <4 x i32> [[TMP3]], [[TMP5]]
; CHECK-NEXT:    [[TMP8:%.*]] = shufflevector <4 x i1> [[TMP7]], <4 x i1> [[TMP12]], <4 x i32> <i32 0, i32 1, i32 6, i32 3>
; CHECK-NEXT:    [[TMP9:%.*]] = icmp eq <4 x i32> [[TMP1]], zeroinitializer
; CHECK-NEXT:    [[TMP10:%.*]] = or <4 x i1> [[TMP9]], [[TMP8]]
; CHECK-NEXT:    [[TMP11:%.*]] = call i1 @llvm.vector.reduce.and.v4i1(<4 x i1> [[TMP10]])
; CHECK-NEXT:    [[AND33:%.*]] = zext i1 [[TMP11]] to i32
; CHECK-NEXT:    ret i32 [[AND33]]
;
entry:
  %conv.i = trunc i64 %0 to i32
  %iv2 = getelementptr i8, ptr %sptr, i64 4
  %1 = load i32, ptr %iv2, align 4
  %cmp11 = icmp slt i32 %1, %conv.i
  %cmp.i57 = icmp eq i32 %1, 0
  %or.i5977 = or i1 %cmp.i57, %cmp11
  %iv4 = getelementptr i8, ptr %sptr, i64 12
  %2 = load i32, ptr %iv4, align 4
  %cmp16 = icmp sle i32 %2, %conv.i
  %cmp.i62 = icmp eq i32 %2, 0
  %or.i6478 = or i1 %cmp.i62, %cmp16
  %iv3 = getelementptr i8, ptr %sptr, i64 8
  %3 = load i32, ptr %iv3, align 8
  %cmp21 = icmp sgt i32 %3, %conv.i
  %cmp.i67 = icmp eq i32 %3, 0
  %or.i6979 = or i1 %cmp.i67, %cmp21
  %iv5 = getelementptr i8, ptr %sptr, i64 16
  %4 = load i32, ptr %iv5, align 8
  %cmp26 = icmp slt i32 %conv.i, 0
  %cmp.i72 = icmp eq i32 %4, 0
  %or.i7480 = or i1 %cmp.i72, %cmp26
  %and3183 = and i1 %or.i5977, %or.i6478
  %and3284 = and i1 %and3183, %or.i6979
  %and3385 = and i1 %and3284, %or.i7480
  %and33 = zext i1 %and3385 to i32
  ret i32 %and33
}
