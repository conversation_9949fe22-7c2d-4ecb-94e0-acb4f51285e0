; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: %if x86-registered-target %{ opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown-linux < %s -slp-threshold=-10 | FileCheck %s %}
; RUN: %if aarch64-registered-target %{ opt -S --passes=slp-vectorizer -mtriple=aarch64-unknown-linux < %s -slp-threshold=-10 | FileCheck %s %}

define i64 @src(i32 %a) {
; CHECK-LABEL: define i64 @src(
; CHECK-SAME: i32 [[A:%.*]]) {
; CHECK-NEXT:  [[ENTRY:.*:]]
; CHECK-NEXT:    [[TMP17:%.*]] = sext i32 [[A]] to i64
; CHECK-NEXT:    [[TMP1:%.*]] = insertelement <4 x i32> poison, i32 [[A]], i32 0
; CHECK-NEXT:    [[TMP2:%.*]] = shufflevector <4 x i32> [[TMP1]], <4 x i32> poison, <4 x i32> zeroinitializer
; CHECK-NEXT:    [[TMP3:%.*]] = sext <4 x i32> [[TMP2]] to <4 x i64>
; CHECK-NEXT:    [[TMP4:%.*]] = add nsw <4 x i64> [[TMP3]], splat (i64 4294967297)
; CHECK-NEXT:    [[TMP6:%.*]] = and <4 x i64> [[TMP4]], splat (i64 1)
; CHECK-NEXT:    [[TMP18:%.*]] = call i64 @llvm.vector.reduce.add.v4i64(<4 x i64> [[TMP6]])
; CHECK-NEXT:    [[TMP16:%.*]] = call i64 @llvm.vector.reduce.add.v4i64(<4 x i64> [[TMP4]])
; CHECK-NEXT:    [[TMP8:%.*]] = insertelement <2 x i64> poison, i64 [[TMP16]], i32 0
; CHECK-NEXT:    [[TMP9:%.*]] = insertelement <2 x i64> [[TMP8]], i64 [[TMP18]], i32 1
; CHECK-NEXT:    [[TMP10:%.*]] = insertelement <2 x i64> <i64 poison, i64 4294967297>, i64 [[TMP17]], i32 0
; CHECK-NEXT:    [[TMP11:%.*]] = add <2 x i64> [[TMP9]], [[TMP10]]
; CHECK-NEXT:    [[TMP12:%.*]] = extractelement <2 x i64> [[TMP11]], i32 0
; CHECK-NEXT:    [[TMP13:%.*]] = extractelement <2 x i64> [[TMP11]], i32 1
; CHECK-NEXT:    [[TMP21:%.*]] = add i64 [[TMP12]], [[TMP13]]
; CHECK-NEXT:    ret i64 [[TMP21]]
;
entry:
  %0 = sext i32 %a to i64
  %1 = add nsw i64 %0, 4294967297
  %2 = sext i32 %a to i64
  %3 = add nsw i64 %2, 4294967297
  %4 = add i64 %3, %1
  %5 = and i64 %3, 1
  %6 = add i64 %4, %5
  %7 = sext i32 %a to i64
  %8 = add nsw i64 %7, 4294967297
  %9 = add i64 %8, %6
  %10 = and i64 %8, 1
  %11 = add i64 %9, %10
  %12 = sext i32 %a to i64
  %13 = add nsw i64 %12, 4294967297
  %14 = add i64 %13, %11
  %15 = and i64 %13, 1
  %16 = add i64 %14, %15
  %17 = sext i32 %a to i64
  %18 = add nsw i64 %17, 4294967297
  %19 = add i64 %18, %16
  %20 = and i64 %18, 1
  %21 = add i64 %19, %20
  ret i64 %21
}
