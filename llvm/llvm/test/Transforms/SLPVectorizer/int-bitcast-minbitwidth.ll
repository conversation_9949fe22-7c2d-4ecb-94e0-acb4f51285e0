; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 4
; RUN: %if x86-registered-target %{ opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown-linux-gnu -slp-threshold=-9 < %s | FileCheck %s %}
; RUN: %if aarch64-registered-target %{ opt -S --passes=slp-vectorizer -mtriple=aarch64-unknown-linux-gnu -slp-threshold=-9 < %s | FileCheck %s %}

define void @t(i64 %v) {
; CHECK-LABEL: define void @t(
; CHECK-SAME: i64 [[V:%.*]]) {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP0:%.*]] = trunc i64 [[V]] to i16
; CHECK-NEXT:    [[TMP1:%.*]] = insertelement <4 x i16> poison, i16 [[TMP0]], i32 0
; CHECK-NEXT:    [[TMP2:%.*]] = shufflevector <4 x i16> [[TMP1]], <4 x i16> poison, <4 x i32> zeroinitializer
; CHECK-NEXT:    [[TMP3:%.*]] = mul <4 x i16> [[TMP2]], <i16 2, i16 3, i16 6, i16 5>
; CHECK-NEXT:    [[TMP4:%.*]] = call i16 @llvm.vector.reduce.or.v4i16(<4 x i16> [[TMP3]])
; CHECK-NEXT:    [[TMP5:%.*]] = sext i16 [[TMP4]] to i32
; CHECK-NEXT:    [[TMP6:%.*]] = and i32 [[TMP5]], 65535
; CHECK-NEXT:    store i32 [[TMP6]], ptr null, align 4
; CHECK-NEXT:    ret void
;
entry:
  %conv12.1.i = trunc i64 %v to i32
  %mul.i.1.i = mul i32 %conv12.1.i, 2
  %conv12.i = trunc i64 %v to i32
  %mul.i.i = mul i32 %conv12.i, 3
  %conv14104.i = or i32 %mul.i.1.i, %mul.i.i
  %conv12.1.i.1 = trunc i64 %v to i32
  %mul.i.1.i.1 = mul i32 %conv12.1.i.1, 6
  %conv12.i.1 = trunc i64 %v to i32
  %mul.i.i.1 = mul i32 %conv12.i.1, 5
  %conv14104.i.1 = or i32 %mul.i.1.i.1, %mul.i.i.1
  %0 = or i32 %conv14104.i, %conv14104.i.1
  %1 = and i32 %0, 65535
  store i32 %1, ptr null, align 4
  ret void
}
