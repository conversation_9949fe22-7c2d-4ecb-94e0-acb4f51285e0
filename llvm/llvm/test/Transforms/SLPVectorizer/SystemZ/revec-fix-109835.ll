; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -mtriple=s390x-unknown-linux -mcpu=z16 -passes=slp-vectorizer -S -slp-revec %s | FileCheck %s

@g_155 = external dso_local global i64, align 8
@g_855 = external dso_local global i8, align 2
@g_3_1_0 = external dso_local global i32, align 4
@g_7 = external dso_local global [5 x i32], align 4

; Function Attrs: nofree norecurse noreturn nounwind memory(readwrite, argmem: none)
define void @func_1() {
; CHECK-LABEL: @func_1(
; CHECK-NEXT:    [[DOTPRE:%.*]] = load i32, ptr @g_7, align 4
; CHECK-NEXT:    [[TMP1:%.*]] = load <4 x i32>, ptr getelementptr inbounds nuw (i8, ptr @g_7, i64 4), align 4
; CHECK-NEXT:    br label [[DOTLOOPEXIT:%.*]]
; CHECK:       .loopexit:
; CHECK-NEXT:    [[TMP2:%.*]] = phi i32 [ [[OP_RDX15:%.*]], [[DOTLOOPEXIT]] ], [ [[DOTPRE]], [[TMP0:%.*]] ]
; CHECK-NEXT:    [[TMP3:%.*]] = phi <4 x i32> [ [[TMP71:%.*]], [[DOTLOOPEXIT]] ], [ [[TMP1]], [[TMP0]] ]
; CHECK-NEXT:    [[TMP4:%.*]] = load volatile i32, ptr @g_3_1_0, align 4
; CHECK-NEXT:    [[TMP5:%.*]] = load volatile i32, ptr @g_3_1_0, align 4
; CHECK-NEXT:    [[TMP6:%.*]] = load volatile i32, ptr @g_3_1_0, align 4
; CHECK-NEXT:    [[TMP7:%.*]] = load volatile i32, ptr @g_3_1_0, align 4
; CHECK-NEXT:    [[TMP8:%.*]] = load volatile i32, ptr @g_3_1_0, align 4
; CHECK-NEXT:    [[TMP9:%.*]] = load volatile i32, ptr @g_3_1_0, align 4
; CHECK-NEXT:    [[TMP10:%.*]] = load volatile i32, ptr @g_3_1_0, align 4
; CHECK-NEXT:    [[TMP11:%.*]] = load volatile i32, ptr @g_3_1_0, align 4
; CHECK-NEXT:    [[TMP12:%.*]] = load volatile i32, ptr @g_3_1_0, align 4
; CHECK-NEXT:    [[TMP13:%.*]] = load volatile i32, ptr @g_3_1_0, align 4
; CHECK-NEXT:    [[TMP14:%.*]] = load volatile i32, ptr @g_3_1_0, align 4
; CHECK-NEXT:    [[TMP15:%.*]] = load volatile i32, ptr @g_3_1_0, align 4
; CHECK-NEXT:    [[TMP16:%.*]] = load volatile i32, ptr @g_3_1_0, align 4
; CHECK-NEXT:    [[TMP17:%.*]] = load volatile i32, ptr @g_3_1_0, align 4
; CHECK-NEXT:    [[TMP18:%.*]] = load volatile i32, ptr @g_3_1_0, align 4
; CHECK-NEXT:    [[TMP19:%.*]] = load volatile i32, ptr @g_3_1_0, align 4
; CHECK-NEXT:    [[TMP20:%.*]] = insertelement <4 x i32> poison, i32 [[TMP4]], i32 0
; CHECK-NEXT:    [[TMP21:%.*]] = insertelement <4 x i32> [[TMP20]], i32 [[TMP9]], i32 1
; CHECK-NEXT:    [[TMP22:%.*]] = insertelement <4 x i32> [[TMP21]], i32 [[TMP14]], i32 2
; CHECK-NEXT:    [[TMP23:%.*]] = insertelement <4 x i32> [[TMP22]], i32 [[TMP19]], i32 3
; CHECK-NEXT:    [[TMP24:%.*]] = icmp eq <4 x i32> [[TMP23]], zeroinitializer
; CHECK-NEXT:    [[TMP25:%.*]] = load volatile i32, ptr @g_3_1_0, align 4
; CHECK-NEXT:    [[TMP26:%.*]] = load volatile i32, ptr @g_3_1_0, align 4
; CHECK-NEXT:    [[TMP27:%.*]] = load volatile i32, ptr @g_3_1_0, align 4
; CHECK-NEXT:    [[TMP28:%.*]] = load volatile i32, ptr @g_3_1_0, align 4
; CHECK-NEXT:    [[TMP29:%.*]] = load volatile i32, ptr @g_3_1_0, align 4
; CHECK-NEXT:    [[DOTNOT2_410:%.*]] = icmp eq i32 [[TMP29]], 0
; CHECK-NEXT:    [[TMP30:%.*]] = zext i1 [[DOTNOT2_410]] to i32
; CHECK-NEXT:    [[TMP31:%.*]] = zext <4 x i1> [[TMP24]] to <4 x i32>
; CHECK-NEXT:    [[TMP32:%.*]] = call i32 @llvm.vector.reduce.xor.v4i32(<4 x i32> [[TMP31]])
; CHECK-NEXT:    [[OP_RDX:%.*]] = xor i32 [[TMP32]], [[TMP30]]
; CHECK-NEXT:    [[OP_RDX15]] = xor i32 [[OP_RDX]], [[TMP2]]
; CHECK-NEXT:    [[TMP33:%.*]] = load volatile i32, ptr @g_3_1_0, align 4
; CHECK-NEXT:    [[TMP34:%.*]] = load volatile i32, ptr @g_3_1_0, align 4
; CHECK-NEXT:    [[TMP35:%.*]] = load volatile i32, ptr @g_3_1_0, align 4
; CHECK-NEXT:    [[TMP36:%.*]] = load volatile i32, ptr @g_3_1_0, align 4
; CHECK-NEXT:    [[TMP37:%.*]] = insertelement <4 x i32> poison, i32 [[TMP5]], i32 0
; CHECK-NEXT:    [[TMP38:%.*]] = insertelement <4 x i32> [[TMP37]], i32 [[TMP6]], i32 1
; CHECK-NEXT:    [[TMP39:%.*]] = insertelement <4 x i32> [[TMP38]], i32 [[TMP7]], i32 2
; CHECK-NEXT:    [[TMP40:%.*]] = insertelement <4 x i32> [[TMP39]], i32 [[TMP8]], i32 3
; CHECK-NEXT:    [[TMP41:%.*]] = icmp eq <4 x i32> [[TMP40]], zeroinitializer
; CHECK-NEXT:    [[TMP42:%.*]] = zext <4 x i1> [[TMP41]] to <4 x i32>
; CHECK-NEXT:    [[TMP43:%.*]] = xor <4 x i32> [[TMP3]], [[TMP42]]
; CHECK-NEXT:    [[TMP44:%.*]] = insertelement <4 x i32> poison, i32 [[TMP10]], i32 0
; CHECK-NEXT:    [[TMP45:%.*]] = insertelement <4 x i32> [[TMP44]], i32 [[TMP11]], i32 1
; CHECK-NEXT:    [[TMP46:%.*]] = insertelement <4 x i32> [[TMP45]], i32 [[TMP12]], i32 2
; CHECK-NEXT:    [[TMP47:%.*]] = insertelement <4 x i32> [[TMP46]], i32 [[TMP13]], i32 3
; CHECK-NEXT:    [[TMP48:%.*]] = icmp eq <4 x i32> [[TMP47]], zeroinitializer
; CHECK-NEXT:    [[TMP49:%.*]] = zext <4 x i1> [[TMP48]] to <4 x i32>
; CHECK-NEXT:    [[TMP50:%.*]] = xor <4 x i32> [[TMP43]], [[TMP49]]
; CHECK-NEXT:    [[TMP51:%.*]] = insertelement <4 x i32> poison, i32 [[TMP15]], i32 0
; CHECK-NEXT:    [[TMP52:%.*]] = insertelement <4 x i32> [[TMP51]], i32 [[TMP16]], i32 1
; CHECK-NEXT:    [[TMP53:%.*]] = insertelement <4 x i32> [[TMP52]], i32 [[TMP17]], i32 2
; CHECK-NEXT:    [[TMP54:%.*]] = insertelement <4 x i32> [[TMP53]], i32 [[TMP18]], i32 3
; CHECK-NEXT:    [[TMP55:%.*]] = icmp eq <4 x i32> [[TMP54]], zeroinitializer
; CHECK-NEXT:    [[TMP56:%.*]] = zext <4 x i1> [[TMP55]] to <4 x i32>
; CHECK-NEXT:    [[TMP57:%.*]] = xor <4 x i32> [[TMP50]], [[TMP56]]
; CHECK-NEXT:    [[TMP58:%.*]] = insertelement <4 x i32> poison, i32 [[TMP25]], i32 0
; CHECK-NEXT:    [[TMP59:%.*]] = insertelement <4 x i32> [[TMP58]], i32 [[TMP26]], i32 1
; CHECK-NEXT:    [[TMP60:%.*]] = insertelement <4 x i32> [[TMP59]], i32 [[TMP27]], i32 2
; CHECK-NEXT:    [[TMP61:%.*]] = insertelement <4 x i32> [[TMP60]], i32 [[TMP28]], i32 3
; CHECK-NEXT:    [[TMP62:%.*]] = icmp eq <4 x i32> [[TMP61]], zeroinitializer
; CHECK-NEXT:    [[TMP63:%.*]] = zext <4 x i1> [[TMP62]] to <4 x i32>
; CHECK-NEXT:    [[TMP64:%.*]] = xor <4 x i32> [[TMP57]], [[TMP63]]
; CHECK-NEXT:    [[TMP65:%.*]] = insertelement <4 x i32> poison, i32 [[TMP33]], i32 0
; CHECK-NEXT:    [[TMP66:%.*]] = insertelement <4 x i32> [[TMP65]], i32 [[TMP34]], i32 1
; CHECK-NEXT:    [[TMP67:%.*]] = insertelement <4 x i32> [[TMP66]], i32 [[TMP35]], i32 2
; CHECK-NEXT:    [[TMP68:%.*]] = insertelement <4 x i32> [[TMP67]], i32 [[TMP36]], i32 3
; CHECK-NEXT:    [[TMP69:%.*]] = icmp eq <4 x i32> [[TMP68]], zeroinitializer
; CHECK-NEXT:    [[TMP70:%.*]] = zext <4 x i1> [[TMP69]] to <4 x i32>
; CHECK-NEXT:    [[TMP71]] = xor <4 x i32> [[TMP64]], [[TMP70]]
; CHECK-NEXT:    br label [[DOTLOOPEXIT]]
;
  %.pre = load i32, ptr @g_7, align 4
  %1 = load <4 x i32>, ptr getelementptr inbounds nuw (i8, ptr @g_7, i64 4), align 4
  br label %.loopexit

.loopexit:                                        ; preds = %.loopexit, %0
  %2 = phi i32 [ %op.rdx15, %.loopexit ], [ %.pre, %0 ]
  %3 = phi <4 x i32> [ %71, %.loopexit ], [ %1, %0 ]
  %4 = load volatile i32, ptr @g_3_1_0, align 4
  %5 = load volatile i32, ptr @g_3_1_0, align 4
  %6 = load volatile i32, ptr @g_3_1_0, align 4
  %7 = load volatile i32, ptr @g_3_1_0, align 4
  %8 = load volatile i32, ptr @g_3_1_0, align 4
  %9 = load volatile i32, ptr @g_3_1_0, align 4
  %10 = load volatile i32, ptr @g_3_1_0, align 4
  %11 = load volatile i32, ptr @g_3_1_0, align 4
  %12 = load volatile i32, ptr @g_3_1_0, align 4
  %13 = load volatile i32, ptr @g_3_1_0, align 4
  %14 = load volatile i32, ptr @g_3_1_0, align 4
  %15 = load volatile i32, ptr @g_3_1_0, align 4
  %16 = load volatile i32, ptr @g_3_1_0, align 4
  %17 = load volatile i32, ptr @g_3_1_0, align 4
  %18 = load volatile i32, ptr @g_3_1_0, align 4
  %19 = load volatile i32, ptr @g_3_1_0, align 4
  %20 = insertelement <4 x i32> poison, i32 %4, i32 0
  %21 = insertelement <4 x i32> %20, i32 %9, i32 1
  %22 = insertelement <4 x i32> %21, i32 %14, i32 2
  %23 = insertelement <4 x i32> %22, i32 %19, i32 3
  %24 = icmp eq <4 x i32> %23, zeroinitializer
  %25 = load volatile i32, ptr @g_3_1_0, align 4
  %26 = load volatile i32, ptr @g_3_1_0, align 4
  %27 = load volatile i32, ptr @g_3_1_0, align 4
  %28 = load volatile i32, ptr @g_3_1_0, align 4
  %29 = load volatile i32, ptr @g_3_1_0, align 4
  %.not2.410 = icmp eq i32 %29, 0
  %30 = zext i1 %.not2.410 to i32
  %31 = zext <4 x i1> %24 to <4 x i32>
  %32 = call i32 @llvm.vector.reduce.xor.v4i32(<4 x i32> %31)
  %op.rdx = xor i32 %32, %30
  %op.rdx15 = xor i32 %op.rdx, %2
  %33 = load volatile i32, ptr @g_3_1_0, align 4
  %34 = load volatile i32, ptr @g_3_1_0, align 4
  %35 = load volatile i32, ptr @g_3_1_0, align 4
  %36 = load volatile i32, ptr @g_3_1_0, align 4
  %37 = insertelement <4 x i32> poison, i32 %5, i32 0
  %38 = insertelement <4 x i32> %37, i32 %6, i32 1
  %39 = insertelement <4 x i32> %38, i32 %7, i32 2
  %40 = insertelement <4 x i32> %39, i32 %8, i32 3
  %41 = icmp eq <4 x i32> %40, zeroinitializer
  %42 = zext <4 x i1> %41 to <4 x i32>
  %43 = xor <4 x i32> %3, %42
  %44 = insertelement <4 x i32> poison, i32 %10, i32 0
  %45 = insertelement <4 x i32> %44, i32 %11, i32 1
  %46 = insertelement <4 x i32> %45, i32 %12, i32 2
  %47 = insertelement <4 x i32> %46, i32 %13, i32 3
  %48 = icmp eq <4 x i32> %47, zeroinitializer
  %49 = zext <4 x i1> %48 to <4 x i32>
  %50 = xor <4 x i32> %43, %49
  %51 = insertelement <4 x i32> poison, i32 %15, i32 0
  %52 = insertelement <4 x i32> %51, i32 %16, i32 1
  %53 = insertelement <4 x i32> %52, i32 %17, i32 2
  %54 = insertelement <4 x i32> %53, i32 %18, i32 3
  %55 = icmp eq <4 x i32> %54, zeroinitializer
  %56 = zext <4 x i1> %55 to <4 x i32>
  %57 = xor <4 x i32> %50, %56
  %58 = insertelement <4 x i32> poison, i32 %25, i32 0
  %59 = insertelement <4 x i32> %58, i32 %26, i32 1
  %60 = insertelement <4 x i32> %59, i32 %27, i32 2
  %61 = insertelement <4 x i32> %60, i32 %28, i32 3
  %62 = icmp eq <4 x i32> %61, zeroinitializer
  %63 = zext <4 x i1> %62 to <4 x i32>
  %64 = xor <4 x i32> %57, %63
  %65 = insertelement <4 x i32> poison, i32 %33, i32 0
  %66 = insertelement <4 x i32> %65, i32 %34, i32 1
  %67 = insertelement <4 x i32> %66, i32 %35, i32 2
  %68 = insertelement <4 x i32> %67, i32 %36, i32 3
  %69 = icmp eq <4 x i32> %68, zeroinitializer
  %70 = zext <4 x i1> %69 to <4 x i32>
  %71 = xor <4 x i32> %64, %70
  br label %.loopexit
}
