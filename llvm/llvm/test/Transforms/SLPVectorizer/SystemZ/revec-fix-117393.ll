; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -mtriple=systemz-unknown -mcpu=z15 -passes=slp-vectorizer -S -slp-revec %s | FileCheck %s

define void @h() {
; CHECK-LABEL: @h(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP0:%.*]] = shl <4 x i32> zeroinitializer, zeroinitializer
; CHECK-NEXT:    [[TMP1:%.*]] = or <4 x i32> [[TMP0]], zeroinitializer
; CHECK-NEXT:    [[TMP2:%.*]] = or <4 x i32> splat (i32 1), zeroinitializer
; CHECK-NEXT:    [[TMP3:%.*]] = shl <4 x i32> zeroinitializer, zeroinitializer
; CHECK-NEXT:    [[TMP4:%.*]] = or <4 x i32> [[TMP3]], zeroinitializer
; CHECK-NEXT:    [[TMP5:%.*]] = and <4 x i32> [[TMP2]], [[TMP1]]
; CHECK-NEXT:    [[TMP6:%.*]] = and <4 x i32> zeroinitializer, [[TMP5]]
; CHECK-NEXT:    [[TMP7:%.*]] = and <4 x i32> [[TMP4]], [[TMP6]]
; CHECK-NEXT:    [[TMP8:%.*]] = call i32 @llvm.vector.reduce.and.v4i32(<4 x i32> [[TMP7]])
; CHECK-NEXT:    ret void
;
entry:
  %0 = shl <4 x i32> zeroinitializer, zeroinitializer
  %1 = or <4 x i32> %0, zeroinitializer
  %2 = or <4 x i32> splat (i32 1), zeroinitializer
  %3 = or <4 x i32> zeroinitializer, zeroinitializer
  %4 = shl <4 x i32> zeroinitializer, zeroinitializer
  %5 = or <4 x i32> %4, zeroinitializer
  %6 = and <4 x i32> %2, %1
  %7 = and <4 x i32> %3, %6
  %8 = and <4 x i32> %5, %7
  %9 = call i32 @llvm.vector.reduce.and.v4i32(<4 x i32> %8)
  ret void
}
