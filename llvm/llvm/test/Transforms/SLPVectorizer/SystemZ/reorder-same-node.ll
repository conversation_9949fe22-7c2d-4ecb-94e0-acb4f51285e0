; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 4
; RUN: opt -S --passes=slp-vectorizer -mtriple=s390x-unknown-linux -mcpu=z16 < %s | FileCheck %s

define void @test() {
; CHECK-LABEL: define void @test(
; CHECK-SAME: ) #[[ATTR0:[0-9]+]] {
; CHECK-NEXT:    [[TMP1:%.*]] = call i64 @llvm.vector.reduce.xor.v8i64(<8 x i64> zeroinitializer)
; CHECK-NEXT:    store i64 [[TMP1]], ptr null, align 8
; CHECK-NEXT:    ret void
;
  %1 = zext i8 0 to i32
  %2 = lshr i32 0, %1
  %3 = icmp ult i32 %2, 0
  %4 = shl i32 0, %1
  %5 = and i32 %4, 0
  %narrow = select i1 %3, i32 0, i32 %5
  %6 = zext i32 %narrow to i64
  %7 = zext i8 0 to i32
  %8 = lshr i32 0, %7
  %9 = icmp ult i32 %8, 0
  %10 = shl i32 0, %7
  %11 = and i32 %10, 0
  %narrow.1 = select i1 %9, i32 0, i32 %11
  %12 = zext i32 %narrow.1 to i64
  %13 = xor i64 %6, %12
  %14 = zext i8 0 to i32
  %15 = lshr i32 0, %14
  %16 = icmp ult i32 %15, 0
  %17 = shl i32 0, %14
  %18 = and i32 %17, 0
  %narrow.2 = select i1 %16, i32 0, i32 %18
  %19 = zext i32 %narrow.2 to i64
  %20 = xor i64 %13, %19
  %21 = icmp ult i32 %8, 0
  %22 = shl i32 0, %7
  %23 = and i32 %22, 0
  %narrow.3 = select i1 %21, i32 0, i32 %23
  %24 = zext i32 %narrow.3 to i64
  %25 = xor i64 %20, %24
  %26 = icmp ult i32 %15, 0
  %27 = shl i32 0, %14
  %28 = and i32 %27, 0
  %narrow.4 = select i1 %26, i32 0, i32 %28
  %29 = zext i32 %narrow.4 to i64
  %30 = xor i64 %25, %29
  %31 = icmp ult i32 %8, 0
  %32 = shl i32 0, %7
  %33 = and i32 %32, 0
  %narrow.5 = select i1 %31, i32 0, i32 %33
  %34 = zext i32 %narrow.5 to i64
  %35 = xor i64 %30, %34
  %36 = icmp ult i32 %15, 0
  %37 = shl i32 0, %14
  %38 = and i32 %37, 0
  %narrow.6 = select i1 %36, i32 0, i32 %38
  %39 = zext i32 %narrow.6 to i64
  %40 = xor i64 %35, %39
  %41 = icmp ult i32 %8, 0
  %42 = shl i32 0, %7
  %43 = and i32 %42, 0
  %narrow.7 = select i1 %41, i32 0, i32 %43
  %44 = zext i32 %narrow.7 to i64
  %45 = xor i64 %40, %44
  store i64 %45, ptr null, align 8
  ret void
}

