; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: %if x86-registered-target %{ opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown %s -slp-threshold=-5 | FileCheck %s %}
; RUN: %if aarch64-registered-target %{ opt -S --passes=slp-vectorizer -mtriple=aarch64-unknown %s -slp-threshold=-5 | FileCheck %s %}

define i32 @test(ptr %isec) {
; CHECK-LABEL: @test(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP1:%.*]] = load <2 x i32>, ptr [[ISEC:%.*]], align 8
; CHECK-NEXT:    [[TMP2:%.*]] = shufflevector <2 x i32> [[TMP1]], <2 x i32> poison, <2 x i32> <i32 1, i32 0>
; CHECK-NEXT:    br i1 false, label [[BLOCK1:%.*]], label [[BLOCK3:%.*]]
; CHECK:       block1:
; CHECK-NEXT:    br i1 false, label [[BLOCK2:%.*]], label [[BLOCK3]]
; CHECK:       block2:
; CHECK-NEXT:    br label [[BLOCK3]]
; CHECK:       block3:
; CHECK-NEXT:    [[TMP3:%.*]] = phi <2 x i32> [ [[TMP1]], [[BLOCK1]] ], [ [[TMP1]], [[BLOCK2]] ], [ [[TMP2]], [[ENTRY:%.*]] ]
; CHECK-NEXT:    [[TMP4:%.*]] = extractelement <2 x i32> [[TMP3]], i32 0
; CHECK-NEXT:    [[TMP5:%.*]] = extractelement <2 x i32> [[TMP3]], i32 1
; CHECK-NEXT:    [[TMP6:%.*]] = mul i32 [[TMP5]], [[TMP4]]
; CHECK-NEXT:    ret i32 [[TMP6]]
;
entry:
  %0 = load <2 x i32>, ptr %isec, align 8
  %1 = extractelement <2 x i32> %0, i32 1
  %2 = extractelement <2 x i32> %0, i32 0
  br i1 false, label %block1, label %block3
block1:
  br i1 false, label %block2, label %block3
block2:
  br label %block3
block3:
  %3 = phi i32 [ %1, %block1 ], [ %1, %block2 ], [ %2, %entry ]
  %4 = phi i32 [ %2, %block1 ], [ %2, %block2 ], [ %1, %entry ]
  %5 = mul i32 %3, %4
  ret i32 %5
}
