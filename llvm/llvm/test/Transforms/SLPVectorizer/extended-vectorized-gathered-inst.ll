; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 4
; RUN: %if x86-registered-target %{ opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown-linux < %s | FileCheck %s %}
; RUN: %if aarch64-registered-target %{ opt -S --passes=slp-vectorizer -mtriple=aarch64-unknown-linux < %s | FileCheck %s %}

define void @test(ptr %top) {
; CHECK-LABEL: define void @test(
; CHECK-SAME: ptr [[TOP:%.*]]) {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP0:%.*]] = load <4 x i8>, ptr [[TOP]], align 1
; CHECK-NEXT:    [[TMP1:%.*]] = mul <4 x i8> [[TMP0]], zeroinitializer
; CHECK-NEXT:    [[TMP2:%.*]] = extractelement <4 x i8> [[TMP0]], i32 2
; CHECK-NEXT:    [[TMP3:%.*]] = zext i8 [[TMP2]] to i32
; CHECK-NEXT:    [[TMP4:%.*]] = trunc i32 [[TMP3]] to i8
; CHECK-NEXT:    [[TMP5:%.*]] = insertelement <4 x i8> <i8 0, i8 0, i8 0, i8 poison>, i8 [[TMP4]], i32 3
; CHECK-NEXT:    [[TMP6:%.*]] = or <4 x i8> [[TMP1]], [[TMP5]]
; CHECK-NEXT:    [[TMP7:%.*]] = or <4 x i8> [[TMP6]], zeroinitializer
; CHECK-NEXT:    [[TMP8:%.*]] = lshr <4 x i8> [[TMP7]], splat (i8 2)
; CHECK-NEXT:    br label [[FOR_COND_I:%.*]]
; CHECK:       for.cond.i:
; CHECK-NEXT:    store <4 x i8> [[TMP8]], ptr null, align 1
; CHECK-NEXT:    br label [[FOR_COND_I]]
;
entry:
  %0 = load i8, ptr %top, align 1
  %conv2.i = zext i8 %0 to i32
  %mul.i = mul i32 %conv2.i, 0
  %add.i = or i32 %mul.i, 0
  %arrayidx3.i = getelementptr i8, ptr %top, i64 1
  %1 = load i8, ptr %arrayidx3.i, align 1
  %conv4.i = zext i8 %1 to i32
  %add5.i = or i32 %add.i, 0
  %shr.i = lshr i32 %add5.i, 2
  %conv7.i = trunc i32 %shr.i to i8
  %mul12.i = mul i32 %conv4.i, 0
  %arrayidx14.i = getelementptr i8, ptr %top, i64 2
  %2 = load i8, ptr %arrayidx14.i, align 1
  %conv15.i = zext i8 %2 to i32
  %add16.i = or i32 %mul12.i, 0
  %add17.i = or i32 %add16.i, 0
  %shr18.i = lshr i32 %add17.i, 2
  %conv19.i = trunc i32 %shr18.i to i8
  %mul25.i = mul i32 %conv15.i, 0
  %arrayidx27.i = getelementptr i8, ptr %top, i64 3
  %3 = load i8, ptr %arrayidx27.i, align 1
  %conv28.i = zext i8 %3 to i32
  %add29.i = or i32 %mul25.i, 0
  %add30.i = or i32 %add29.i, 0
  %shr31.i = lshr i32 %add30.i, 2
  %conv32.i = trunc i32 %shr31.i to i8
  %mul38.i = mul i32 %conv28.i, 0
  %add39.i = or i32 %mul38.i, %conv15.i
  %add42.i = or i32 %add39.i, 0
  %shr44.i = lshr i32 %add42.i, 2
  %conv45.i = trunc i32 %shr44.i to i8
  br label %for.cond.i

for.cond.i:
  store i8 %conv7.i, ptr null, align 1
  %vals.sroa.5.0.add.ptr.sroa_idx.i = getelementptr i8, ptr null, i64 1
  store i8 %conv19.i, ptr %vals.sroa.5.0.add.ptr.sroa_idx.i, align 1
  %vals.sroa.7.0.add.ptr.sroa_idx.i = getelementptr i8, ptr null, i64 2
  store i8 %conv32.i, ptr %vals.sroa.7.0.add.ptr.sroa_idx.i, align 1
  %vals.sroa.9.0.add.ptr.sroa_idx.i = getelementptr i8, ptr null, i64 3
  store i8 %conv45.i, ptr %vals.sroa.9.0.add.ptr.sroa_idx.i, align 1
  br label %for.cond.i
}
