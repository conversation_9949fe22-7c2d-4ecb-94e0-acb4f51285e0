; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 4
; RUN: %if x86-registered-target %{ opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown-linux-gnu < %s | FileCheck %s %}
; RUN: %if aarch64-registered-target %{ opt -S --passes=slp-vectorizer -mtriple=aarch64-unknown-linux-gnu < %s | FileCheck %s %}

define <2 x i32> @test(i32 %arg) {
; CHECK-LABEL: define <2 x i32> @test(
; CHECK-SAME: i32 [[ARG:%.*]]) {
; CHECK-NEXT:  bb:
; CHECK-NEXT:    [[OR:%.*]] = or i32 [[ARG]], 0
; CHECK-NEXT:    [[MUL:%.*]] = mul i32 0, 1
; CHECK-NEXT:    [[MUL1:%.*]] = mul i32 [[OR]], [[MUL]]
; CHECK-NEXT:    [[CMP:%.*]] = icmp ugt i32 0, [[MUL1]]
; CHECK-NEXT:    [[TMP0:%.*]] = insertelement <2 x i32> poison, i32 [[OR]], i32 0
; CHECK-NEXT:    [[TMP1:%.*]] = insertelement <2 x i32> [[TMP0]], i32 [[MUL]], i32 1
; CHECK-NEXT:    ret <2 x i32> [[TMP1]]
;
bb:
  %or = or i32 %arg, 0
  %mul = mul i32 0, 1
  %mul1 = mul i32 %or, %mul
  %cmp = icmp ugt i32 0, %mul1
  %0 = insertelement <2 x i32> poison, i32 %or, i32 0
  %1 = insertelement <2 x i32> %0, i32 %mul, i32 1
  ret <2 x i32> %1
}

