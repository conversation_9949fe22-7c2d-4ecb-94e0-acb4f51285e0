; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: %if x86-registered-target %{ opt -S --passes=slp-vectorizer -slp-threshold=-10 < %s -mtriple=x86_64-pc-windows-msvc19.39.33523 | FileCheck %s %}
; RUN: %if aarch64-registered-target %{ opt -S --passes=slp-vectorizer -slp-threshold=-10 < %s -mtriple=aarch64-unknown-linux-gnu | FileCheck %s %}

define void @test(ptr %0) {
; CHECK-LABEL: define void @test(
; CHECK-SAME: ptr [[TMP0:%.*]]) {
; CHECK-NEXT:  [[ENTRY:.*:]]
; CHECK-NEXT:    [[TMP1:%.*]] = insertelement <2 x ptr> <ptr null, ptr poison>, ptr [[TMP0]], i32 1
; CHECK-NEXT:    [[TMP2:%.*]] = ptrtoint <2 x ptr> [[TMP1]] to <2 x i64>
; CHECK-NEXT:    [[TMP8:%.*]] = ptrtoint ptr [[TMP0]] to i64
; CHECK-NEXT:    [[TMP5:%.*]] = trunc i64 [[TMP8]] to i32
; CHECK-NEXT:    [[TMP6:%.*]] = ptrtoint ptr null to i64
; CHECK-NEXT:    [[TMP3:%.*]] = trunc <2 x i64> [[TMP2]] to <2 x i32>
; CHECK-NEXT:    switch i32 0, label %[[NEWFUNCROOT994:.*]] [
; CHECK-NEXT:      i32 1, label %[[NEWFUNCROOT994]]
; CHECK-NEXT:      i32 0, label %[[NEWFUNCROOT584:.*]]
; CHECK-NEXT:    ]
; CHECK:       [[NEWFUNCROOT584]]:
; CHECK-NEXT:    [[TMP4:%.*]] = shufflevector <2 x i32> [[TMP3]], <2 x i32> poison, <4 x i32> <i32 0, i32 1, i32 poison, i32 poison>
; CHECK-NEXT:    ret void
; CHECK:       [[NEWFUNCROOT994]]:
; CHECK-NEXT:    [[TMP7:%.*]] = insertelement <4 x i32> zeroinitializer, i32 [[TMP5]], i64 [[TMP6]]
; CHECK-NEXT:    ret void
;
entry:
  %1 = ptrtoint ptr %0 to i64
  %2 = trunc i64 %1 to i32
  %3 = ptrtoint ptr null to i64
  %4 = trunc i64 %3 to i32
  switch i32 0, label %newFuncRoot994 [
  i32 1, label %newFuncRoot994
  i32 0, label %newFuncRoot584
  ]

newFuncRoot584:
  %5 = insertelement <4 x i32> poison, i32 %4, i64 0
  %6 = insertelement <4 x i32> %5, i32 %2, i64 1
  ret void

newFuncRoot994:
  %7 = insertelement <4 x i32> zeroinitializer, i32 %2, i64 %3
  ret void
}
