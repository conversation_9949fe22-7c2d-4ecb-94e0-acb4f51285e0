; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: %if x86-registered-target %{ opt -S -passes=slp-vectorizer -slp-threshold=-10000 -mtriple=x86_64-unknown-unknown < %s | FileCheck %s --check-prefixes=CHECK,THRESHOLD %}
; RUN: %if x86-registered-target %{ opt -S -passes=slp-vectorizer -slp-threshold=0 -mtriple=x86_64-unknown-unknown < %s | FileCheck %s --check-prefixes=CHECK,NOTHRESHOLD %}
; RUN: %if x86-registered-target %{ opt -S -passes=slp-vectorizer -slp-threshold=-10000 -slp-min-tree-size=0 -mtriple=x86_64-unknown-unknown < %s | FileCheck %s --check-prefixes=CHECK,MINTREESIZE %}
; RUN: %if aarch64-registered-target %{ opt -S -passes=slp-vectorizer -slp-threshold=-10000 -mtriple=aarch64-unknown-linux-gnu < %s | FileCheck %s --check-prefixes=CHECK,THRESHOLD %}
; RUN: %if aarch64-registered-target %{ opt -S -passes=slp-vectorizer -slp-threshold=0 -mtriple=aarch64-unknown-linux-gnu < %s | FileCheck %s --check-prefixes=CHECK,NOTHRESHOLD %}
; RUN: %if aarch64-registered-target %{ opt -S -passes=slp-vectorizer -slp-threshold=-10000 -slp-min-tree-size=0 -mtriple=aarch64-unknown-linux-gnu < %s | FileCheck %s --check-prefixes=CHECK,MINTREESIZE %}

define <4 x float> @simple_select(<4 x float> %a, <4 x float> %b, <4 x i32> %c) #0 {
; CHECK-LABEL: @simple_select(
; CHECK-NEXT:    [[TMP1:%.*]] = icmp ne <4 x i32> [[C:%.*]], zeroinitializer
; CHECK-NEXT:    [[TMP2:%.*]] = select <4 x i1> [[TMP1]], <4 x float> [[A:%.*]], <4 x float> [[B:%.*]]
; CHECK-NEXT:    ret <4 x float> [[TMP2]]
;
  %c0 = extractelement <4 x i32> %c, i32 0
  %c1 = extractelement <4 x i32> %c, i32 1
  %c2 = extractelement <4 x i32> %c, i32 2
  %c3 = extractelement <4 x i32> %c, i32 3
  %a0 = extractelement <4 x float> %a, i32 0
  %a1 = extractelement <4 x float> %a, i32 1
  %a2 = extractelement <4 x float> %a, i32 2
  %a3 = extractelement <4 x float> %a, i32 3
  %b0 = extractelement <4 x float> %b, i32 0
  %b1 = extractelement <4 x float> %b, i32 1
  %b2 = extractelement <4 x float> %b, i32 2
  %b3 = extractelement <4 x float> %b, i32 3
  %cmp0 = icmp ne i32 %c0, 0
  %cmp1 = icmp ne i32 %c1, 0
  %cmp2 = icmp ne i32 %c2, 0
  %cmp3 = icmp ne i32 %c3, 0
  %s0 = select i1 %cmp0, float %a0, float %b0
  %s1 = select i1 %cmp1, float %a1, float %b1
  %s2 = select i1 %cmp2, float %a2, float %b2
  %s3 = select i1 %cmp3, float %a3, float %b3
  %ra = insertelement <4 x float> zeroinitializer, float %s0, i32 0
  %rb = insertelement <4 x float> %ra, float %s1, i32 1
  %rc = insertelement <4 x float> %rb, float %s2, i32 2
  %rd = insertelement <4 x float> %rc, float %s3, i32 3
  ret <4 x float> %rd
}

define <8 x float> @simple_select2(<4 x float> %a, <4 x float> %b, <4 x i32> %c) #0 {
; CHECK-LABEL: @simple_select2(
; CHECK-NEXT:    [[TMP1:%.*]] = icmp ne <4 x i32> [[C:%.*]], zeroinitializer
; CHECK-NEXT:    [[TMP2:%.*]] = select <4 x i1> [[TMP1]], <4 x float> [[A:%.*]], <4 x float> [[B:%.*]]
; CHECK-NEXT:    [[TMP4:%.*]] = shufflevector <4 x float> [[TMP2]], <4 x float> poison, <8 x i32> <i32 0, i32 poison, i32 1, i32 poison, i32 2, i32 poison, i32 poison, i32 3>
; CHECK-NEXT:    [[TMP3:%.*]] = shufflevector <8 x float> zeroinitializer, <8 x float> [[TMP4]], <8 x i32> <i32 8, i32 1, i32 10, i32 3, i32 12, i32 5, i32 6, i32 15>
; CHECK-NEXT:    ret <8 x float> [[TMP3]]
;
  %c0 = extractelement <4 x i32> %c, i32 0
  %c1 = extractelement <4 x i32> %c, i32 1
  %c2 = extractelement <4 x i32> %c, i32 2
  %c3 = extractelement <4 x i32> %c, i32 3
  %a0 = extractelement <4 x float> %a, i32 0
  %a1 = extractelement <4 x float> %a, i32 1
  %a2 = extractelement <4 x float> %a, i32 2
  %a3 = extractelement <4 x float> %a, i32 3
  %b0 = extractelement <4 x float> %b, i32 0
  %b1 = extractelement <4 x float> %b, i32 1
  %b2 = extractelement <4 x float> %b, i32 2
  %b3 = extractelement <4 x float> %b, i32 3
  %cmp0 = icmp ne i32 %c0, 0
  %cmp1 = icmp ne i32 %c1, 0
  %cmp2 = icmp ne i32 %c2, 0
  %cmp3 = icmp ne i32 %c3, 0
  %s0 = select i1 %cmp0, float %a0, float %b0
  %s1 = select i1 %cmp1, float %a1, float %b1
  %s2 = select i1 %cmp2, float %a2, float %b2
  %s3 = select i1 %cmp3, float %a3, float %b3
  %ra = insertelement <8 x float> zeroinitializer, float %s0, i32 0
  %rb = insertelement <8 x float> %ra, float %s1, i32 2
  %rc = insertelement <8 x float> %rb, float %s2, i32 4
  %rd = insertelement <8 x float> %rc, float %s3, i32 7
  ret <8 x float> %rd
}

declare void @llvm.assume(i1) nounwind

; This entire tree is ephemeral, don't vectorize any of it.
define <4 x float> @simple_select_eph(<4 x float> %a, <4 x float> %b, <4 x i32> %c) #0 {
; THRESHOLD-LABEL: @simple_select_eph(
; THRESHOLD-NEXT:    [[C0:%.*]] = extractelement <4 x i32> [[C:%.*]], i32 0
; THRESHOLD-NEXT:    [[C1:%.*]] = extractelement <4 x i32> [[C]], i32 1
; THRESHOLD-NEXT:    [[C2:%.*]] = extractelement <4 x i32> [[C]], i32 2
; THRESHOLD-NEXT:    [[C3:%.*]] = extractelement <4 x i32> [[C]], i32 3
; THRESHOLD-NEXT:    [[A0:%.*]] = extractelement <4 x float> [[A:%.*]], i32 0
; THRESHOLD-NEXT:    [[A1:%.*]] = extractelement <4 x float> [[A]], i32 1
; THRESHOLD-NEXT:    [[A2:%.*]] = extractelement <4 x float> [[A]], i32 2
; THRESHOLD-NEXT:    [[A3:%.*]] = extractelement <4 x float> [[A]], i32 3
; THRESHOLD-NEXT:    [[B0:%.*]] = extractelement <4 x float> [[B:%.*]], i32 0
; THRESHOLD-NEXT:    [[B1:%.*]] = extractelement <4 x float> [[B]], i32 1
; THRESHOLD-NEXT:    [[B2:%.*]] = extractelement <4 x float> [[B]], i32 2
; THRESHOLD-NEXT:    [[B3:%.*]] = extractelement <4 x float> [[B]], i32 3
; THRESHOLD-NEXT:    [[CMP0:%.*]] = icmp ne i32 [[C0]], 0
; THRESHOLD-NEXT:    [[CMP1:%.*]] = icmp ne i32 [[C1]], 0
; THRESHOLD-NEXT:    [[CMP2:%.*]] = icmp ne i32 [[C2]], 0
; THRESHOLD-NEXT:    [[CMP3:%.*]] = icmp ne i32 [[C3]], 0
; THRESHOLD-NEXT:    [[S0:%.*]] = select i1 [[CMP0]], float [[A0]], float [[B0]]
; THRESHOLD-NEXT:    [[S1:%.*]] = select i1 [[CMP1]], float [[A1]], float [[B1]]
; THRESHOLD-NEXT:    [[S2:%.*]] = select i1 [[CMP2]], float [[A2]], float [[B2]]
; THRESHOLD-NEXT:    [[S3:%.*]] = select i1 [[CMP3]], float [[A3]], float [[B3]]
; THRESHOLD-NEXT:    [[RA:%.*]] = insertelement <4 x float> zeroinitializer, float [[S0]], i32 0
; THRESHOLD-NEXT:    [[RB:%.*]] = insertelement <4 x float> [[RA]], float [[S1]], i32 1
; THRESHOLD-NEXT:    [[RC:%.*]] = insertelement <4 x float> [[RB]], float [[S2]], i32 2
; THRESHOLD-NEXT:    [[RD:%.*]] = insertelement <4 x float> [[RC]], float [[S3]], i32 3
; THRESHOLD-NEXT:    [[Q0:%.*]] = extractelement <4 x float> [[RD]], i32 0
; THRESHOLD-NEXT:    [[Q1:%.*]] = extractelement <4 x float> [[RD]], i32 1
; THRESHOLD-NEXT:    [[TMP1:%.*]] = shufflevector <4 x float> [[RD]], <4 x float> poison, <2 x i32> <i32 0, i32 1>
; THRESHOLD-NEXT:    [[Q2:%.*]] = extractelement <4 x float> [[RD]], i32 2
; THRESHOLD-NEXT:    [[Q3:%.*]] = extractelement <4 x float> [[RD]], i32 3
; THRESHOLD-NEXT:    [[TMP2:%.*]] = shufflevector <4 x float> [[RD]], <4 x float> poison, <2 x i32> <i32 2, i32 3>
; THRESHOLD-NEXT:    [[Q4:%.*]] = fadd float [[Q0]], [[Q1]]
; THRESHOLD-NEXT:    [[Q5:%.*]] = fadd float [[Q2]], [[Q3]]
; THRESHOLD-NEXT:    [[Q6:%.*]] = fadd float [[Q4]], [[Q5]]
; THRESHOLD-NEXT:    [[QI:%.*]] = fcmp olt float [[Q6]], [[Q5]]
; THRESHOLD-NEXT:    call void @llvm.assume(i1 [[QI]])
; THRESHOLD-NEXT:    ret <4 x float> zeroinitializer
;
; NOTHRESHOLD-LABEL: @simple_select_eph(
; NOTHRESHOLD-NEXT:    [[C0:%.*]] = extractelement <4 x i32> [[C:%.*]], i32 0
; NOTHRESHOLD-NEXT:    [[C1:%.*]] = extractelement <4 x i32> [[C]], i32 1
; NOTHRESHOLD-NEXT:    [[C2:%.*]] = extractelement <4 x i32> [[C]], i32 2
; NOTHRESHOLD-NEXT:    [[C3:%.*]] = extractelement <4 x i32> [[C]], i32 3
; NOTHRESHOLD-NEXT:    [[A0:%.*]] = extractelement <4 x float> [[A:%.*]], i32 0
; NOTHRESHOLD-NEXT:    [[A1:%.*]] = extractelement <4 x float> [[A]], i32 1
; NOTHRESHOLD-NEXT:    [[A2:%.*]] = extractelement <4 x float> [[A]], i32 2
; NOTHRESHOLD-NEXT:    [[A3:%.*]] = extractelement <4 x float> [[A]], i32 3
; NOTHRESHOLD-NEXT:    [[B0:%.*]] = extractelement <4 x float> [[B:%.*]], i32 0
; NOTHRESHOLD-NEXT:    [[B1:%.*]] = extractelement <4 x float> [[B]], i32 1
; NOTHRESHOLD-NEXT:    [[B2:%.*]] = extractelement <4 x float> [[B]], i32 2
; NOTHRESHOLD-NEXT:    [[B3:%.*]] = extractelement <4 x float> [[B]], i32 3
; NOTHRESHOLD-NEXT:    [[CMP0:%.*]] = icmp ne i32 [[C0]], 0
; NOTHRESHOLD-NEXT:    [[CMP1:%.*]] = icmp ne i32 [[C1]], 0
; NOTHRESHOLD-NEXT:    [[CMP2:%.*]] = icmp ne i32 [[C2]], 0
; NOTHRESHOLD-NEXT:    [[CMP3:%.*]] = icmp ne i32 [[C3]], 0
; NOTHRESHOLD-NEXT:    [[S0:%.*]] = select i1 [[CMP0]], float [[A0]], float [[B0]]
; NOTHRESHOLD-NEXT:    [[S1:%.*]] = select i1 [[CMP1]], float [[A1]], float [[B1]]
; NOTHRESHOLD-NEXT:    [[S2:%.*]] = select i1 [[CMP2]], float [[A2]], float [[B2]]
; NOTHRESHOLD-NEXT:    [[S3:%.*]] = select i1 [[CMP3]], float [[A3]], float [[B3]]
; NOTHRESHOLD-NEXT:    [[RA:%.*]] = insertelement <4 x float> zeroinitializer, float [[S0]], i32 0
; NOTHRESHOLD-NEXT:    [[RB:%.*]] = insertelement <4 x float> [[RA]], float [[S1]], i32 1
; NOTHRESHOLD-NEXT:    [[RC:%.*]] = insertelement <4 x float> [[RB]], float [[S2]], i32 2
; NOTHRESHOLD-NEXT:    [[RD:%.*]] = insertelement <4 x float> [[RC]], float [[S3]], i32 3
; NOTHRESHOLD-NEXT:    [[Q0:%.*]] = extractelement <4 x float> [[RD]], i32 0
; NOTHRESHOLD-NEXT:    [[Q1:%.*]] = extractelement <4 x float> [[RD]], i32 1
; NOTHRESHOLD-NEXT:    [[Q2:%.*]] = extractelement <4 x float> [[RD]], i32 2
; NOTHRESHOLD-NEXT:    [[Q3:%.*]] = extractelement <4 x float> [[RD]], i32 3
; NOTHRESHOLD-NEXT:    [[Q4:%.*]] = fadd float [[Q0]], [[Q1]]
; NOTHRESHOLD-NEXT:    [[Q5:%.*]] = fadd float [[Q2]], [[Q3]]
; NOTHRESHOLD-NEXT:    [[Q6:%.*]] = fadd float [[Q4]], [[Q5]]
; NOTHRESHOLD-NEXT:    [[QI:%.*]] = fcmp olt float [[Q6]], [[Q5]]
; NOTHRESHOLD-NEXT:    call void @llvm.assume(i1 [[QI]])
; NOTHRESHOLD-NEXT:    ret <4 x float> zeroinitializer
;
; MINTREESIZE-LABEL: @simple_select_eph(
; MINTREESIZE-NEXT:    [[C0:%.*]] = extractelement <4 x i32> [[C:%.*]], i32 0
; MINTREESIZE-NEXT:    [[C1:%.*]] = extractelement <4 x i32> [[C]], i32 1
; MINTREESIZE-NEXT:    [[C2:%.*]] = extractelement <4 x i32> [[C]], i32 2
; MINTREESIZE-NEXT:    [[C3:%.*]] = extractelement <4 x i32> [[C]], i32 3
; MINTREESIZE-NEXT:    [[A0:%.*]] = extractelement <4 x float> [[A:%.*]], i32 0
; MINTREESIZE-NEXT:    [[A1:%.*]] = extractelement <4 x float> [[A]], i32 1
; MINTREESIZE-NEXT:    [[A2:%.*]] = extractelement <4 x float> [[A]], i32 2
; MINTREESIZE-NEXT:    [[A3:%.*]] = extractelement <4 x float> [[A]], i32 3
; MINTREESIZE-NEXT:    [[B0:%.*]] = extractelement <4 x float> [[B:%.*]], i32 0
; MINTREESIZE-NEXT:    [[B1:%.*]] = extractelement <4 x float> [[B]], i32 1
; MINTREESIZE-NEXT:    [[B2:%.*]] = extractelement <4 x float> [[B]], i32 2
; MINTREESIZE-NEXT:    [[B3:%.*]] = extractelement <4 x float> [[B]], i32 3
; MINTREESIZE-NEXT:    [[CMP0:%.*]] = icmp ne i32 [[C0]], 0
; MINTREESIZE-NEXT:    [[CMP1:%.*]] = icmp ne i32 [[C1]], 0
; MINTREESIZE-NEXT:    [[CMP2:%.*]] = icmp ne i32 [[C2]], 0
; MINTREESIZE-NEXT:    [[CMP3:%.*]] = icmp ne i32 [[C3]], 0
; MINTREESIZE-NEXT:    [[TMP1:%.*]] = insertelement <4 x i1> poison, i1 [[CMP3]], i32 0
; MINTREESIZE-NEXT:    [[TMP2:%.*]] = insertelement <4 x i1> [[TMP1]], i1 [[CMP2]], i32 1
; MINTREESIZE-NEXT:    [[TMP3:%.*]] = insertelement <4 x i1> [[TMP2]], i1 [[CMP1]], i32 2
; MINTREESIZE-NEXT:    [[TMP4:%.*]] = insertelement <4 x i1> [[TMP3]], i1 [[CMP0]], i32 3
; MINTREESIZE-NEXT:    [[S0:%.*]] = select i1 [[CMP0]], float [[A0]], float [[B0]]
; MINTREESIZE-NEXT:    [[S1:%.*]] = select i1 [[CMP1]], float [[A1]], float [[B1]]
; MINTREESIZE-NEXT:    [[S2:%.*]] = select i1 [[CMP2]], float [[A2]], float [[B2]]
; MINTREESIZE-NEXT:    [[S3:%.*]] = select i1 [[CMP3]], float [[A3]], float [[B3]]
; MINTREESIZE-NEXT:    [[RA:%.*]] = insertelement <4 x float> zeroinitializer, float [[S0]], i32 0
; MINTREESIZE-NEXT:    [[RB:%.*]] = insertelement <4 x float> [[RA]], float [[S1]], i32 1
; MINTREESIZE-NEXT:    [[RC:%.*]] = insertelement <4 x float> [[RB]], float [[S2]], i32 2
; MINTREESIZE-NEXT:    [[RD:%.*]] = insertelement <4 x float> [[RC]], float [[S3]], i32 3
; MINTREESIZE-NEXT:    [[Q0:%.*]] = extractelement <4 x float> [[RD]], i32 0
; MINTREESIZE-NEXT:    [[Q1:%.*]] = extractelement <4 x float> [[RD]], i32 1
; MINTREESIZE-NEXT:    [[TMP5:%.*]] = shufflevector <4 x float> [[RD]], <4 x float> poison, <2 x i32> <i32 0, i32 1>
; MINTREESIZE-NEXT:    [[Q2:%.*]] = extractelement <4 x float> [[RD]], i32 2
; MINTREESIZE-NEXT:    [[Q3:%.*]] = extractelement <4 x float> [[RD]], i32 3
; MINTREESIZE-NEXT:    [[TMP6:%.*]] = shufflevector <4 x float> [[RD]], <4 x float> poison, <2 x i32> <i32 2, i32 3>
; MINTREESIZE-NEXT:    [[Q4:%.*]] = fadd float [[Q0]], [[Q1]]
; MINTREESIZE-NEXT:    [[Q5:%.*]] = fadd float [[Q2]], [[Q3]]
; MINTREESIZE-NEXT:    [[TMP7:%.*]] = insertelement <2 x float> poison, float [[Q4]], i32 0
; MINTREESIZE-NEXT:    [[TMP8:%.*]] = insertelement <2 x float> [[TMP7]], float [[Q5]], i32 1
; MINTREESIZE-NEXT:    [[Q6:%.*]] = fadd float [[Q4]], [[Q5]]
; MINTREESIZE-NEXT:    [[QI:%.*]] = fcmp olt float [[Q6]], [[Q5]]
; MINTREESIZE-NEXT:    call void @llvm.assume(i1 [[QI]])
; MINTREESIZE-NEXT:    ret <4 x float> zeroinitializer
;
  %c0 = extractelement <4 x i32> %c, i32 0
  %c1 = extractelement <4 x i32> %c, i32 1
  %c2 = extractelement <4 x i32> %c, i32 2
  %c3 = extractelement <4 x i32> %c, i32 3
  %a0 = extractelement <4 x float> %a, i32 0
  %a1 = extractelement <4 x float> %a, i32 1
  %a2 = extractelement <4 x float> %a, i32 2
  %a3 = extractelement <4 x float> %a, i32 3
  %b0 = extractelement <4 x float> %b, i32 0
  %b1 = extractelement <4 x float> %b, i32 1
  %b2 = extractelement <4 x float> %b, i32 2
  %b3 = extractelement <4 x float> %b, i32 3
  %cmp0 = icmp ne i32 %c0, 0
  %cmp1 = icmp ne i32 %c1, 0
  %cmp2 = icmp ne i32 %c2, 0
  %cmp3 = icmp ne i32 %c3, 0
  %s0 = select i1 %cmp0, float %a0, float %b0
  %s1 = select i1 %cmp1, float %a1, float %b1
  %s2 = select i1 %cmp2, float %a2, float %b2
  %s3 = select i1 %cmp3, float %a3, float %b3
  %ra = insertelement <4 x float> zeroinitializer, float %s0, i32 0
  %rb = insertelement <4 x float> %ra, float %s1, i32 1
  %rc = insertelement <4 x float> %rb, float %s2, i32 2
  %rd = insertelement <4 x float> %rc, float %s3, i32 3
  %q0 = extractelement <4 x float> %rd, i32 0
  %q1 = extractelement <4 x float> %rd, i32 1
  %q2 = extractelement <4 x float> %rd, i32 2
  %q3 = extractelement <4 x float> %rd, i32 3
  %q4 = fadd float %q0, %q1
  %q5 = fadd float %q2, %q3
  %q6 = fadd float %q4, %q5
  %qi = fcmp olt float %q6, %q5
  call void @llvm.assume(i1 %qi)
  ret <4 x float> zeroinitializer
}

; Insert in an order different from the vector indices to make sure it
; doesn't matter
define <4 x float> @simple_select_insert_out_of_order(<4 x float> %a, <4 x float> %b, <4 x i32> %c) #0 {
; CHECK-LABEL: @simple_select_insert_out_of_order(
; CHECK-NEXT:    [[TMP1:%.*]] = icmp ne <4 x i32> [[C:%.*]], zeroinitializer
; CHECK-NEXT:    [[TMP2:%.*]] = select <4 x i1> [[TMP1]], <4 x float> [[A:%.*]], <4 x float> [[B:%.*]]
; CHECK-NEXT:    [[TMP3:%.*]] = shufflevector <4 x float> [[TMP2]], <4 x float> poison, <4 x i32> <i32 2, i32 1, i32 0, i32 3>
; CHECK-NEXT:    ret <4 x float> [[TMP3]]
;
  %c0 = extractelement <4 x i32> %c, i32 0
  %c1 = extractelement <4 x i32> %c, i32 1
  %c2 = extractelement <4 x i32> %c, i32 2
  %c3 = extractelement <4 x i32> %c, i32 3
  %a0 = extractelement <4 x float> %a, i32 0
  %a1 = extractelement <4 x float> %a, i32 1
  %a2 = extractelement <4 x float> %a, i32 2
  %a3 = extractelement <4 x float> %a, i32 3
  %b0 = extractelement <4 x float> %b, i32 0
  %b1 = extractelement <4 x float> %b, i32 1
  %b2 = extractelement <4 x float> %b, i32 2
  %b3 = extractelement <4 x float> %b, i32 3
  %cmp0 = icmp ne i32 %c0, 0
  %cmp1 = icmp ne i32 %c1, 0
  %cmp2 = icmp ne i32 %c2, 0
  %cmp3 = icmp ne i32 %c3, 0
  %s0 = select i1 %cmp0, float %a0, float %b0
  %s1 = select i1 %cmp1, float %a1, float %b1
  %s2 = select i1 %cmp2, float %a2, float %b2
  %s3 = select i1 %cmp3, float %a3, float %b3
  %ra = insertelement <4 x float> zeroinitializer, float %s0, i32 2
  %rb = insertelement <4 x float> %ra, float %s1, i32 1
  %rc = insertelement <4 x float> %rb, float %s2, i32 0
  %rd = insertelement <4 x float> %rc, float %s3, i32 3
  ret <4 x float> %rd
}

declare void @v4f32_user(<4 x float>) #0
declare void @f32_user(float) #0

; Multiple users of the final constructed vector
define <4 x float> @simple_select_users(<4 x float> %a, <4 x float> %b, <4 x i32> %c) #0 {
; CHECK-LABEL: @simple_select_users(
; CHECK-NEXT:    [[TMP1:%.*]] = icmp ne <4 x i32> [[C:%.*]], zeroinitializer
; CHECK-NEXT:    [[TMP2:%.*]] = select <4 x i1> [[TMP1]], <4 x float> [[A:%.*]], <4 x float> [[B:%.*]]
; CHECK-NEXT:    call void @v4f32_user(<4 x float> [[TMP2]]) #[[ATTR0:[0-9]+]]
; CHECK-NEXT:    ret <4 x float> [[TMP2]]
;
  %c0 = extractelement <4 x i32> %c, i32 0
  %c1 = extractelement <4 x i32> %c, i32 1
  %c2 = extractelement <4 x i32> %c, i32 2
  %c3 = extractelement <4 x i32> %c, i32 3
  %a0 = extractelement <4 x float> %a, i32 0
  %a1 = extractelement <4 x float> %a, i32 1
  %a2 = extractelement <4 x float> %a, i32 2
  %a3 = extractelement <4 x float> %a, i32 3
  %b0 = extractelement <4 x float> %b, i32 0
  %b1 = extractelement <4 x float> %b, i32 1
  %b2 = extractelement <4 x float> %b, i32 2
  %b3 = extractelement <4 x float> %b, i32 3
  %cmp0 = icmp ne i32 %c0, 0
  %cmp1 = icmp ne i32 %c1, 0
  %cmp2 = icmp ne i32 %c2, 0
  %cmp3 = icmp ne i32 %c3, 0
  %s0 = select i1 %cmp0, float %a0, float %b0
  %s1 = select i1 %cmp1, float %a1, float %b1
  %s2 = select i1 %cmp2, float %a2, float %b2
  %s3 = select i1 %cmp3, float %a3, float %b3
  %ra = insertelement <4 x float> zeroinitializer, float %s0, i32 0
  %rb = insertelement <4 x float> %ra, float %s1, i32 1
  %rc = insertelement <4 x float> %rb, float %s2, i32 2
  %rd = insertelement <4 x float> %rc, float %s3, i32 3
  call void @v4f32_user(<4 x float> %rd) #0
  ret <4 x float> %rd
}

; Unused insertelement
define <4 x float> @simple_select_no_users(<4 x float> %a, <4 x float> %b, <4 x i32> %c) #0 {
; CHECK-LABEL: @simple_select_no_users(
; CHECK-NEXT:    [[TMP1:%.*]] = shufflevector <4 x i32> [[C:%.*]], <4 x i32> poison, <2 x i32> <i32 0, i32 1>
; CHECK-NEXT:    [[TMP2:%.*]] = icmp ne <2 x i32> [[TMP1]], zeroinitializer
; CHECK-NEXT:    [[TMP3:%.*]] = shufflevector <4 x float> [[A:%.*]], <4 x float> poison, <2 x i32> <i32 0, i32 1>
; CHECK-NEXT:    [[TMP4:%.*]] = shufflevector <4 x float> [[B:%.*]], <4 x float> poison, <2 x i32> <i32 0, i32 1>
; CHECK-NEXT:    [[TMP5:%.*]] = select <2 x i1> [[TMP2]], <2 x float> [[TMP3]], <2 x float> [[TMP4]]
; CHECK-NEXT:    [[TMP6:%.*]] = shufflevector <4 x i32> [[C]], <4 x i32> poison, <2 x i32> <i32 2, i32 3>
; CHECK-NEXT:    [[TMP7:%.*]] = icmp ne <2 x i32> [[TMP6]], zeroinitializer
; CHECK-NEXT:    [[TMP8:%.*]] = shufflevector <4 x float> [[A]], <4 x float> poison, <2 x i32> <i32 2, i32 3>
; CHECK-NEXT:    [[TMP9:%.*]] = shufflevector <4 x float> [[B]], <4 x float> poison, <2 x i32> <i32 2, i32 3>
; CHECK-NEXT:    [[TMP10:%.*]] = select <2 x i1> [[TMP7]], <2 x float> [[TMP8]], <2 x float> [[TMP9]]
; CHECK-NEXT:    [[TMP11:%.*]] = shufflevector <2 x float> [[TMP5]], <2 x float> poison, <4 x i32> <i32 0, i32 1, i32 poison, i32 poison>
; CHECK-NEXT:    [[RB2:%.*]] = shufflevector <4 x float> zeroinitializer, <4 x float> [[TMP11]], <4 x i32> <i32 4, i32 5, i32 2, i32 3>
; CHECK-NEXT:    [[TMP12:%.*]] = shufflevector <2 x float> [[TMP10]], <2 x float> poison, <4 x i32> <i32 0, i32 1, i32 poison, i32 poison>
; CHECK-NEXT:    [[RD1:%.*]] = shufflevector <4 x float> zeroinitializer, <4 x float> [[TMP12]], <4 x i32> <i32 0, i32 1, i32 4, i32 5>
; CHECK-NEXT:    ret <4 x float> [[RD1]]
;
  %c0 = extractelement <4 x i32> %c, i32 0
  %c1 = extractelement <4 x i32> %c, i32 1
  %c2 = extractelement <4 x i32> %c, i32 2
  %c3 = extractelement <4 x i32> %c, i32 3
  %a0 = extractelement <4 x float> %a, i32 0
  %a1 = extractelement <4 x float> %a, i32 1
  %a2 = extractelement <4 x float> %a, i32 2
  %a3 = extractelement <4 x float> %a, i32 3
  %b0 = extractelement <4 x float> %b, i32 0
  %b1 = extractelement <4 x float> %b, i32 1
  %b2 = extractelement <4 x float> %b, i32 2
  %b3 = extractelement <4 x float> %b, i32 3
  %cmp0 = icmp ne i32 %c0, 0
  %cmp1 = icmp ne i32 %c1, 0
  %cmp2 = icmp ne i32 %c2, 0
  %cmp3 = icmp ne i32 %c3, 0
  %s0 = select i1 %cmp0, float %a0, float %b0
  %s1 = select i1 %cmp1, float %a1, float %b1
  %s2 = select i1 %cmp2, float %a2, float %b2
  %s3 = select i1 %cmp3, float %a3, float %b3
  %ra = insertelement <4 x float> zeroinitializer, float %s0, i32 0
  %rb = insertelement <4 x float> %ra, float %s1, i32 1
  %rc = insertelement <4 x float> zeroinitializer, float %s2, i32 2
  %rd = insertelement <4 x float> %rc, float %s3, i32 3
  ret <4 x float> %rd
}

; Make sure infinite loop doesn't happen which I ran into when trying
; to do this backwards this backwards
define <4 x i32> @reconstruct(<4 x i32> %c) #0 {
; CHECK-LABEL: @reconstruct(
; CHECK-NEXT:    [[C0:%.*]] = extractelement <4 x i32> [[C:%.*]], i32 0
; CHECK-NEXT:    [[C1:%.*]] = extractelement <4 x i32> [[C]], i32 1
; CHECK-NEXT:    [[C2:%.*]] = extractelement <4 x i32> [[C]], i32 2
; CHECK-NEXT:    [[C3:%.*]] = extractelement <4 x i32> [[C]], i32 3
; CHECK-NEXT:    [[RA:%.*]] = insertelement <4 x i32> zeroinitializer, i32 [[C0]], i32 0
; CHECK-NEXT:    [[RB:%.*]] = insertelement <4 x i32> [[RA]], i32 [[C1]], i32 1
; CHECK-NEXT:    [[RC:%.*]] = insertelement <4 x i32> [[RB]], i32 [[C2]], i32 2
; CHECK-NEXT:    [[RD:%.*]] = insertelement <4 x i32> [[RC]], i32 [[C3]], i32 3
; CHECK-NEXT:    ret <4 x i32> [[RD]]
;
  %c0 = extractelement <4 x i32> %c, i32 0
  %c1 = extractelement <4 x i32> %c, i32 1
  %c2 = extractelement <4 x i32> %c, i32 2
  %c3 = extractelement <4 x i32> %c, i32 3
  %ra = insertelement <4 x i32> zeroinitializer, i32 %c0, i32 0
  %rb = insertelement <4 x i32> %ra, i32 %c1, i32 1
  %rc = insertelement <4 x i32> %rb, i32 %c2, i32 2
  %rd = insertelement <4 x i32> %rc, i32 %c3, i32 3
  ret <4 x i32> %rd
}

define <2 x float> @simple_select_v2(<2 x float> %a, <2 x float> %b, <2 x i32> %c) #0 {
; CHECK-LABEL: @simple_select_v2(
; CHECK-NEXT:    [[TMP1:%.*]] = icmp ne <2 x i32> [[C:%.*]], zeroinitializer
; CHECK-NEXT:    [[TMP2:%.*]] = select <2 x i1> [[TMP1]], <2 x float> [[A:%.*]], <2 x float> [[B:%.*]]
; CHECK-NEXT:    ret <2 x float> [[TMP2]]
;
  %c0 = extractelement <2 x i32> %c, i32 0
  %c1 = extractelement <2 x i32> %c, i32 1
  %a0 = extractelement <2 x float> %a, i32 0
  %a1 = extractelement <2 x float> %a, i32 1
  %b0 = extractelement <2 x float> %b, i32 0
  %b1 = extractelement <2 x float> %b, i32 1
  %cmp0 = icmp ne i32 %c0, 0
  %cmp1 = icmp ne i32 %c1, 0
  %s0 = select i1 %cmp0, float %a0, float %b0
  %s1 = select i1 %cmp1, float %a1, float %b1
  %ra = insertelement <2 x float> zeroinitializer, float %s0, i32 0
  %rb = insertelement <2 x float> %ra, float %s1, i32 1
  ret <2 x float> %rb
}

; Make sure when we construct partial vectors, we don't keep
; re-visiting the insertelement chains starting with zeroinitializer
; (low cost threshold needed to force this to happen)
define <4 x float> @simple_select_partial_vector(<4 x float> %a, <4 x float> %b, <4 x i32> %c) #0 {
; CHECK-LABEL: @simple_select_partial_vector(
; CHECK-NEXT:    [[C0:%.*]] = extractelement <4 x i32> [[C:%.*]], i32 0
; CHECK-NEXT:    [[C1:%.*]] = extractelement <4 x i32> [[C]], i32 1
; CHECK-NEXT:    [[A0:%.*]] = extractelement <4 x float> [[A:%.*]], i32 0
; CHECK-NEXT:    [[A1:%.*]] = extractelement <4 x float> [[A]], i32 1
; CHECK-NEXT:    [[B0:%.*]] = extractelement <4 x float> [[B:%.*]], i32 0
; CHECK-NEXT:    [[B1:%.*]] = extractelement <4 x float> [[B]], i32 1
; CHECK-NEXT:    [[TMP1:%.*]] = insertelement <2 x i32> zeroinitializer, i32 [[C0]], i32 0
; CHECK-NEXT:    [[TMP2:%.*]] = insertelement <2 x i32> [[TMP1]], i32 [[C1]], i32 1
; CHECK-NEXT:    [[TMP3:%.*]] = icmp ne <2 x i32> [[TMP2]], zeroinitializer
; CHECK-NEXT:    [[TMP4:%.*]] = insertelement <2 x float> zeroinitializer, float [[A0]], i32 0
; CHECK-NEXT:    [[TMP5:%.*]] = insertelement <2 x float> [[TMP4]], float [[A1]], i32 1
; CHECK-NEXT:    [[TMP6:%.*]] = insertelement <2 x float> zeroinitializer, float [[B0]], i32 0
; CHECK-NEXT:    [[TMP7:%.*]] = insertelement <2 x float> [[TMP6]], float [[B1]], i32 1
; CHECK-NEXT:    [[TMP8:%.*]] = select <2 x i1> [[TMP3]], <2 x float> [[TMP5]], <2 x float> [[TMP7]]
; CHECK-NEXT:    [[TMP9:%.*]] = extractelement <2 x float> [[TMP8]], i32 0
; CHECK-NEXT:    [[RA:%.*]] = insertelement <4 x float> zeroinitializer, float [[TMP9]], i32 0
; CHECK-NEXT:    [[TMP10:%.*]] = extractelement <2 x float> [[TMP8]], i32 1
; CHECK-NEXT:    [[RB:%.*]] = insertelement <4 x float> [[RA]], float [[TMP10]], i32 1
; CHECK-NEXT:    ret <4 x float> [[RB]]
;
  %c0 = extractelement <4 x i32> %c, i32 0
  %c1 = extractelement <4 x i32> %c, i32 1
  %a0 = extractelement <4 x float> %a, i32 0
  %a1 = extractelement <4 x float> %a, i32 1
  %b0 = extractelement <4 x float> %b, i32 0
  %b1 = extractelement <4 x float> %b, i32 1
  %1 = insertelement <2 x i32> zeroinitializer, i32 %c0, i32 0
  %2 = insertelement <2 x i32> %1, i32 %c1, i32 1
  %3 = icmp ne <2 x i32> %2, zeroinitializer
  %4 = insertelement <2 x float> zeroinitializer, float %a0, i32 0
  %5 = insertelement <2 x float> %4, float %a1, i32 1
  %6 = insertelement <2 x float> zeroinitializer, float %b0, i32 0
  %7 = insertelement <2 x float> %6, float %b1, i32 1
  %8 = select <2 x i1> %3, <2 x float> %5, <2 x float> %7
  %9 = extractelement <2 x float> %8, i32 0
  %ra = insertelement <4 x float> zeroinitializer, float %9, i32 0
  %10 = extractelement <2 x float> %8, i32 1
  %rb = insertelement <4 x float> %ra, float %10, i32 1
  ret <4 x float> %rb
}

; Make sure that vectorization happens even if insertelements operations
; must be rescheduled. The case here is from compiling Julia.
define <4 x float> @reschedule_extract(<4 x float> %a, <4 x float> %b) {
; CHECK-LABEL: @reschedule_extract(
; CHECK-NEXT:    [[TMP1:%.*]] = fadd <4 x float> [[A:%.*]], [[B:%.*]]
; CHECK-NEXT:    ret <4 x float> [[TMP1]]
;
  %a0 = extractelement <4 x float> %a, i32 0
  %b0 = extractelement <4 x float> %b, i32 0
  %c0 = fadd float %a0, %b0
  %v0 = insertelement <4 x float> zeroinitializer, float %c0, i32 0
  %a1 = extractelement <4 x float> %a, i32 1
  %b1 = extractelement <4 x float> %b, i32 1
  %c1 = fadd float %a1, %b1
  %v1 = insertelement <4 x float> %v0, float %c1, i32 1
  %a2 = extractelement <4 x float> %a, i32 2
  %b2 = extractelement <4 x float> %b, i32 2
  %c2 = fadd float %a2, %b2
  %v2 = insertelement <4 x float> %v1, float %c2, i32 2
  %a3 = extractelement <4 x float> %a, i32 3
  %b3 = extractelement <4 x float> %b, i32 3
  %c3 = fadd float %a3, %b3
  %v3 = insertelement <4 x float> %v2, float %c3, i32 3
  ret <4 x float> %v3
}

; Check that cost model for vectorization takes credit for
; instructions that are erased.
define <4 x float> @take_credit(<4 x float> %a, <4 x float> %b) {
; CHECK-LABEL: @take_credit(
; CHECK-NEXT:    [[TMP1:%.*]] = fadd <4 x float> [[A:%.*]], [[B:%.*]]
; CHECK-NEXT:    ret <4 x float> [[TMP1]]
;
  %a0 = extractelement <4 x float> %a, i32 0
  %b0 = extractelement <4 x float> %b, i32 0
  %c0 = fadd float %a0, %b0
  %a1 = extractelement <4 x float> %a, i32 1
  %b1 = extractelement <4 x float> %b, i32 1
  %c1 = fadd float %a1, %b1
  %a2 = extractelement <4 x float> %a, i32 2
  %b2 = extractelement <4 x float> %b, i32 2
  %c2 = fadd float %a2, %b2
  %a3 = extractelement <4 x float> %a, i32 3
  %b3 = extractelement <4 x float> %b, i32 3
  %c3 = fadd float %a3, %b3
  %v0 = insertelement <4 x float> zeroinitializer, float %c0, i32 0
  %v1 = insertelement <4 x float> %v0, float %c1, i32 1
  %v2 = insertelement <4 x float> %v1, float %c2, i32 2
  %v3 = insertelement <4 x float> %v2, float %c3, i32 3
  ret <4 x float> %v3
}

; Make sure we handle multiple trees that feed one build vector correctly.
define <4 x double> @multi_tree(double %w, double %x, double %y, double %z) {
; CHECK-LABEL: @multi_tree(
; CHECK-NEXT:    [[TMP1:%.*]] = insertelement <4 x double> poison, double [[Z:%.*]], i32 0
; CHECK-NEXT:    [[TMP2:%.*]] = insertelement <4 x double> [[TMP1]], double [[Y:%.*]], i32 1
; CHECK-NEXT:    [[TMP3:%.*]] = insertelement <4 x double> [[TMP2]], double [[X:%.*]], i32 2
; CHECK-NEXT:    [[TMP4:%.*]] = insertelement <4 x double> [[TMP3]], double [[W:%.*]], i32 3
; CHECK-NEXT:    [[TMP5:%.*]] = fadd <4 x double> [[TMP4]], <double 3.000000e+00, double 2.000000e+00, double 1.000000e+00, double 0.000000e+00>
; CHECK-NEXT:    [[TMP6:%.*]] = fmul <4 x double> [[TMP5]], splat (double 1.000000e+00)
; CHECK-NEXT:    ret <4 x double> [[TMP6]]
;
  %t0 = fadd double %w , 0.000000e+00
  %t1 = fadd double %x , 1.000000e+00
  %t2 = fadd double %y , 2.000000e+00
  %t3 = fadd double %z , 3.000000e+00
  %t4 = fmul double %t0, 1.000000e+00
  %i1 = insertelement <4 x double> zeroinitializer, double %t4, i32 3
  %t5 = fmul double %t1, 1.000000e+00
  %i2 = insertelement <4 x double> %i1, double %t5, i32 2
  %t6 = fmul double %t2, 1.000000e+00
  %i3 = insertelement <4 x double> %i2, double %t6, i32 1
  %t7 = fmul double %t3, 1.000000e+00
  %i4 = insertelement <4 x double> %i3, double %t7, i32 0
  ret <4 x double> %i4
}

define <8 x float> @_vadd256(<8 x float> %a, <8 x float> %b) local_unnamed_addr #0 {
; CHECK-LABEL: @_vadd256(
; CHECK-NEXT:    [[TMP1:%.*]] = fadd <8 x float> [[A:%.*]], [[B:%.*]]
; CHECK-NEXT:    ret <8 x float> [[TMP1]]
;
  %vecext = extractelement <8 x float> %a, i32 0
  %vecext1 = extractelement <8 x float> %b, i32 0
  %add = fadd float %vecext, %vecext1
  %vecext2 = extractelement <8 x float> %a, i32 1
  %vecext3 = extractelement <8 x float> %b, i32 1
  %add4 = fadd float %vecext2, %vecext3
  %vecext5 = extractelement <8 x float> %a, i32 2
  %vecext6 = extractelement <8 x float> %b, i32 2
  %add7 = fadd float %vecext5, %vecext6
  %vecext8 = extractelement <8 x float> %a, i32 3
  %vecext9 = extractelement <8 x float> %b, i32 3
  %add10 = fadd float %vecext8, %vecext9
  %vecext11 = extractelement <8 x float> %a, i32 4
  %vecext12 = extractelement <8 x float> %b, i32 4
  %add13 = fadd float %vecext11, %vecext12
  %vecext14 = extractelement <8 x float> %a, i32 5
  %vecext15 = extractelement <8 x float> %b, i32 5
  %add16 = fadd float %vecext14, %vecext15
  %vecext17 = extractelement <8 x float> %a, i32 6
  %vecext18 = extractelement <8 x float> %b, i32 6
  %add19 = fadd float %vecext17, %vecext18
  %vecext20 = extractelement <8 x float> %a, i32 7
  %vecext21 = extractelement <8 x float> %b, i32 7
  %add22 = fadd float %vecext20, %vecext21
  %vecinit.i = insertelement <8 x float> zeroinitializer, float %add, i32 0
  %vecinit1.i = insertelement <8 x float> %vecinit.i, float %add4, i32 1
  %vecinit2.i = insertelement <8 x float> %vecinit1.i, float %add7, i32 2
  %vecinit3.i = insertelement <8 x float> %vecinit2.i, float %add10, i32 3
  %vecinit4.i = insertelement <8 x float> %vecinit3.i, float %add13, i32 4
  %vecinit5.i = insertelement <8 x float> %vecinit4.i, float %add16, i32 5
  %vecinit6.i = insertelement <8 x float> %vecinit5.i, float %add19, i32 6
  %vecinit7.i = insertelement <8 x float> %vecinit6.i, float %add22, i32 7
  ret <8 x float> %vecinit7.i
}

attributes #0 = { nounwind ssp uwtable "less-precise-fpmad"="false" "frame-pointer"="all" "no-infs-fp-math"="false" "no-nans-fp-math"="false" "stack-protector-buffer-size"="8" "unsafe-fp-math"="false" "use-soft-float"="false" }
