; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 4
; RUN: %if x86-registered-target %{ opt -passes=slp-vectorizer -S -slp-threshold=-10 -mtriple=x86_64-unknown-linux-gnu < %s | FileCheck %s %}
; RUN: %if aarch64-registered-target %{ opt -passes=slp-vectorizer -S -slp-threshold=-10 -mtriple=aarch64-unknown-linux-gnu < %s | FileCheck %s %}

define void @test(i8 %0) {
; CHECK-LABEL: define void @test(
; CHECK-SAME: i8 [[TMP0:%.*]]) {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP1:%.*]] = insertelement <2 x i8> <i8 0, i8 poison>, i8 [[TMP0]], i32 1
; CHECK-NEXT:    [[TMP2:%.*]] = sext <2 x i8> [[TMP1]] to <2 x i32>
; CHECK-NEXT:    [[TMP3:%.*]] = mul <2 x i8> [[TMP1]], zeroinitializer
; CHECK-NEXT:    [[TMP4:%.*]] = extractelement <2 x i8> [[TMP3]], i32 0
; CHECK-NEXT:    [[TMP5:%.*]] = zext i8 [[TMP4]] to i32
; CHECK-NEXT:    [[TMP6:%.*]] = extractelement <2 x i8> [[TMP3]], i32 1
; CHECK-NEXT:    [[TMP7:%.*]] = zext i8 [[TMP6]] to i32
; CHECK-NEXT:    [[ADD:%.*]] = or i32 [[TMP5]], [[TMP7]]
; CHECK-NEXT:    [[SHR:%.*]] = lshr i32 [[ADD]], 1
; CHECK-NEXT:    [[CONV9:%.*]] = trunc i32 [[SHR]] to i8
; CHECK-NEXT:    store i8 [[CONV9]], ptr null, align 1
; CHECK-NEXT:    [[TMP8:%.*]] = shufflevector <2 x i32> [[TMP2]], <2 x i32> poison, <8 x i32> <i32 1, i32 poison, i32 poison, i32 poison, i32 poison, i32 poison, i32 poison, i32 poison>
; CHECK-NEXT:    ret void
;
entry:
  %conv3 = sext i8 %0 to i32
  %conv7 = sext i8 0 to i32
  %conv = zext i16 0 to i32
  %mul = mul i32 %conv3, %conv
  %conv6 = zext i16 0 to i32
  %mul8 = mul i32 %conv7, %conv6
  %add = or i32 %mul8, %mul
  %shr = lshr i32 %add, 1
  %conv9 = trunc i32 %shr to i8
  store i8 %conv9, ptr null, align 1
  %broadcast.splatinsert = insertelement <8 x i32> poison, i32 %conv3, i64 0
  ret void
}
