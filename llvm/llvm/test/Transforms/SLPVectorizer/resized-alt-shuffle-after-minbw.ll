; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S --passes=slp-vectorizer -slp-vectorize-hor=false < %s | FileCheck %s

define void @func(i32 %0) {
; CHECK-LABEL: define void @func(
; CHECK-SAME: i32 [[TMP0:%.*]]) {
; CHECK-NEXT:    [[TMP2:%.*]] = insertelement <4 x i32> <i32 0, i32 poison, i32 0, i32 0>, i32 [[TMP0]], i32 1
; CHECK-NEXT:    [[TMP3:%.*]] = shl <4 x i32> [[TMP2]], zeroinitializer
; CHECK-NEXT:    [[TMP4:%.*]] = or <4 x i32> [[TMP2]], zeroinitializer
; CHECK-NEXT:    [[TMP5:%.*]] = shufflevector <4 x i32> [[TMP3]], <4 x i32> [[TMP4]], <4 x i32> <i32 0, i32 1, i32 6, i32 7>
; CHECK-NEXT:    [[TMP6:%.*]] = shl i32 [[TMP0]], 0
; CHECK-NEXT:    [[TMP7:%.*]] = icmp eq i32 [[TMP6]], 0
; CHECK-NEXT:    [[TMP8:%.*]] = shufflevector <4 x i32> [[TMP5]], <4 x i32> poison, <32 x i32> <i32 0, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 2, i32 3>
; CHECK-NEXT:    [[TMP9:%.*]] = sext i32 [[TMP6]] to i64
; CHECK-NEXT:    [[TMP10:%.*]] = or i64 [[TMP9]], 0
; CHECK-NEXT:    [[TMP11:%.*]] = trunc i64 [[TMP9]] to i32
; CHECK-NEXT:    [[TMP12:%.*]] = insertelement <32 x i32> <i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 poison, i32 poison, i32 poison, i32 poison, i32 poison, i32 poison, i32 poison, i32 poison, i32 poison, i32 poison, i32 poison, i32 poison, i32 poison, i32 poison, i32 poison, i32 poison, i32 poison, i32 poison>, i32 [[TMP11]], i32 30
; CHECK-NEXT:    [[TMP13:%.*]] = shufflevector <32 x i32> [[TMP12]], <32 x i32> poison, <32 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7, i32 8, i32 9, i32 10, i32 11, i32 12, i32 13, i32 poison, i32 poison, i32 poison, i32 poison, i32 poison, i32 poison, i32 poison, i32 poison, i32 poison, i32 poison, i32 poison, i32 poison, i32 poison, i32 poison, i32 poison, i32 poison, i32 30, i32 30>
; CHECK-NEXT:    [[TMP14:%.*]] = call <32 x i32> @llvm.vector.insert.v32i32.v8i32(<32 x i32> [[TMP13]], <8 x i32> zeroinitializer, i64 16)
; CHECK-NEXT:    [[TMP15:%.*]] = call <32 x i32> @llvm.vector.insert.v32i32.v4i32(<32 x i32> [[TMP14]], <4 x i32> zeroinitializer, i64 24)
; CHECK-NEXT:    [[TMP16:%.*]] = call <32 x i32> @llvm.vector.insert.v32i32.v2i32(<32 x i32> [[TMP15]], <2 x i32> zeroinitializer, i64 14)
; CHECK-NEXT:    [[TMP17:%.*]] = call <32 x i32> @llvm.vector.insert.v32i32.v2i32(<32 x i32> [[TMP16]], <2 x i32> zeroinitializer, i64 28)
; CHECK-NEXT:    [[TMP18:%.*]] = or <32 x i32> [[TMP8]], [[TMP17]]
; CHECK-NEXT:    [[TMP19:%.*]] = sext <32 x i32> [[TMP18]] to <32 x i64>
; CHECK-NEXT:    [[TMP20:%.*]] = icmp slt <32 x i64> [[TMP19]], zeroinitializer
; CHECK-NEXT:    [[TMP21:%.*]] = extractelement <32 x i1> [[TMP20]], i32 31
; CHECK-NEXT:    [[TMP22:%.*]] = and i1 false, [[TMP21]]
; CHECK-NEXT:    [[TMP23:%.*]] = extractelement <32 x i1> [[TMP20]], i32 30
; CHECK-NEXT:    [[TMP24:%.*]] = and i1 false, [[TMP23]]
; CHECK-NEXT:    [[TMP25:%.*]] = extractelement <32 x i1> [[TMP20]], i32 29
; CHECK-NEXT:    [[TMP26:%.*]] = and i1 false, [[TMP25]]
; CHECK-NEXT:    [[TMP27:%.*]] = extractelement <32 x i1> [[TMP20]], i32 28
; CHECK-NEXT:    [[TMP28:%.*]] = and i1 false, [[TMP27]]
; CHECK-NEXT:    [[TMP29:%.*]] = extractelement <32 x i1> [[TMP20]], i32 27
; CHECK-NEXT:    [[TMP30:%.*]] = and i1 false, [[TMP29]]
; CHECK-NEXT:    [[TMP31:%.*]] = extractelement <32 x i1> [[TMP20]], i32 26
; CHECK-NEXT:    [[TMP32:%.*]] = and i1 false, [[TMP31]]
; CHECK-NEXT:    [[TMP33:%.*]] = extractelement <32 x i1> [[TMP20]], i32 25
; CHECK-NEXT:    [[TMP34:%.*]] = and i1 false, [[TMP33]]
; CHECK-NEXT:    [[TMP35:%.*]] = extractelement <32 x i1> [[TMP20]], i32 24
; CHECK-NEXT:    [[TMP36:%.*]] = and i1 false, [[TMP35]]
; CHECK-NEXT:    [[TMP37:%.*]] = extractelement <32 x i1> [[TMP20]], i32 23
; CHECK-NEXT:    [[TMP38:%.*]] = and i1 false, [[TMP37]]
; CHECK-NEXT:    [[TMP39:%.*]] = extractelement <32 x i1> [[TMP20]], i32 22
; CHECK-NEXT:    [[TMP40:%.*]] = and i1 false, [[TMP39]]
; CHECK-NEXT:    [[TMP41:%.*]] = extractelement <32 x i1> [[TMP20]], i32 21
; CHECK-NEXT:    [[TMP42:%.*]] = and i1 false, [[TMP41]]
; CHECK-NEXT:    [[TMP43:%.*]] = extractelement <32 x i1> [[TMP20]], i32 20
; CHECK-NEXT:    [[TMP44:%.*]] = and i1 false, [[TMP43]]
; CHECK-NEXT:    [[TMP45:%.*]] = extractelement <32 x i1> [[TMP20]], i32 19
; CHECK-NEXT:    [[TMP46:%.*]] = and i1 false, [[TMP45]]
; CHECK-NEXT:    [[TMP47:%.*]] = extractelement <32 x i1> [[TMP20]], i32 18
; CHECK-NEXT:    [[TMP48:%.*]] = and i1 false, [[TMP47]]
; CHECK-NEXT:    [[TMP49:%.*]] = extractelement <32 x i1> [[TMP20]], i32 17
; CHECK-NEXT:    [[TMP50:%.*]] = and i1 false, [[TMP49]]
; CHECK-NEXT:    [[TMP51:%.*]] = extractelement <32 x i1> [[TMP20]], i32 16
; CHECK-NEXT:    [[TMP52:%.*]] = and i1 false, [[TMP51]]
; CHECK-NEXT:    [[TMP53:%.*]] = extractelement <32 x i1> [[TMP20]], i32 15
; CHECK-NEXT:    [[TMP54:%.*]] = and i1 false, [[TMP53]]
; CHECK-NEXT:    [[TMP55:%.*]] = extractelement <32 x i1> [[TMP20]], i32 14
; CHECK-NEXT:    [[TMP56:%.*]] = and i1 false, [[TMP55]]
; CHECK-NEXT:    [[TMP57:%.*]] = extractelement <32 x i1> [[TMP20]], i32 13
; CHECK-NEXT:    [[TMP58:%.*]] = and i1 false, [[TMP57]]
; CHECK-NEXT:    [[TMP59:%.*]] = extractelement <32 x i1> [[TMP20]], i32 12
; CHECK-NEXT:    [[TMP60:%.*]] = and i1 false, [[TMP59]]
; CHECK-NEXT:    [[TMP61:%.*]] = extractelement <32 x i1> [[TMP20]], i32 11
; CHECK-NEXT:    [[TMP62:%.*]] = and i1 false, [[TMP61]]
; CHECK-NEXT:    [[TMP63:%.*]] = extractelement <32 x i1> [[TMP20]], i32 10
; CHECK-NEXT:    [[TMP64:%.*]] = and i1 false, [[TMP63]]
; CHECK-NEXT:    [[TMP65:%.*]] = extractelement <32 x i1> [[TMP20]], i32 9
; CHECK-NEXT:    [[TMP66:%.*]] = and i1 false, [[TMP65]]
; CHECK-NEXT:    [[TMP67:%.*]] = extractelement <32 x i1> [[TMP20]], i32 8
; CHECK-NEXT:    [[TMP68:%.*]] = and i1 false, [[TMP67]]
; CHECK-NEXT:    [[TMP69:%.*]] = extractelement <32 x i1> [[TMP20]], i32 7
; CHECK-NEXT:    [[TMP70:%.*]] = and i1 false, [[TMP69]]
; CHECK-NEXT:    [[TMP71:%.*]] = extractelement <32 x i1> [[TMP20]], i32 6
; CHECK-NEXT:    [[TMP72:%.*]] = and i1 false, [[TMP71]]
; CHECK-NEXT:    [[TMP73:%.*]] = extractelement <32 x i1> [[TMP20]], i32 5
; CHECK-NEXT:    [[TMP74:%.*]] = and i1 false, [[TMP73]]
; CHECK-NEXT:    [[TMP75:%.*]] = extractelement <32 x i1> [[TMP20]], i32 4
; CHECK-NEXT:    [[TMP76:%.*]] = and i1 false, [[TMP75]]
; CHECK-NEXT:    [[TMP77:%.*]] = extractelement <32 x i32> [[TMP18]], i32 0
; CHECK-NEXT:    [[TMP78:%.*]] = zext i32 [[TMP77]] to i64
; CHECK-NEXT:    [[TMP79:%.*]] = getelementptr float, ptr addrspace(1) null, i64 [[TMP78]]
; CHECK-NEXT:    ret void
;
  %2 = shl i32 %0, 0
  %3 = sext i32 %2 to i64
  %4 = shl i32 0, 0
  %5 = sext i32 %4 to i64
  %6 = or i32 0, 0
  %7 = or i32 0, 0
  %8 = zext i32 %6 to i64
  %9 = zext i32 %7 to i64
  %10 = zext i32 0 to i64
  %11 = zext i32 0 to i64
  %12 = zext i32 0 to i64
  %13 = zext i32 0 to i64
  %14 = zext i32 0 to i64
  %15 = zext i32 0 to i64
  %16 = zext i32 0 to i64
  %17 = zext i32 0 to i64
  %18 = zext i32 0 to i64
  %19 = zext i32 0 to i64
  %20 = zext i32 0 to i64
  %21 = zext i32 0 to i64
  %22 = zext i32 0 to i64
  %23 = zext i32 0 to i64
  %24 = zext i32 0 to i64
  %25 = zext i32 0 to i64
  %26 = zext i32 0 to i64
  %27 = or i64 %3, 0
  %28 = or i64 %3, %8
  %29 = or i64 %3, %9
  %30 = or i64 %3, %10
  %31 = or i64 %3, %11
  %32 = or i64 %3, %12
  %33 = or i64 %3, %13
  %34 = or i64 %3, %14
  %35 = or i64 %3, %15
  %36 = or i64 %3, %16
  %37 = or i64 %3, %17
  %38 = or i64 %3, %18
  %39 = or i64 %3, %19
  %40 = or i64 %3, %20
  %41 = or i64 %3, %21
  %42 = or i64 %3, %22
  %43 = or i64 %3, %23
  %44 = or i64 %3, %24
  %45 = or i64 %3, %25
  %46 = or i64 %3, 0
  %47 = or i64 %3, 0
  %48 = or i64 %3, 0
  %49 = or i64 %3, 0
  %50 = or i64 %3, 0
  %51 = or i64 %3, 0
  %52 = or i64 %3, 0
  %53 = or i64 %3, 0
  %54 = or i64 %3, 0
  %55 = or i64 %3, 0
  %56 = or i64 %3, 0
  %57 = or i64 %3, 0
  %58 = or i64 %3, 0
  %59 = icmp slt i64 %28, 0
  %60 = icmp slt i64 %29, 0
  %61 = icmp slt i64 %30, 0
  %62 = icmp slt i64 %31, 0
  %63 = icmp slt i64 %32, 0
  %64 = icmp slt i64 %33, 0
  %65 = icmp slt i64 %34, 0
  %66 = icmp slt i64 %35, 0
  %67 = icmp slt i64 %36, 0
  %68 = icmp slt i64 %37, 0
  %69 = icmp slt i64 %38, 0
  %70 = icmp slt i64 %39, 0
  %71 = icmp slt i64 %40, 0
  %72 = icmp slt i64 %41, 0
  %73 = icmp slt i64 %42, 0
  %74 = icmp slt i64 %43, 0
  %75 = icmp slt i64 %44, 0
  %76 = icmp slt i64 %45, 0
  %77 = icmp slt i64 %46, 0
  %78 = icmp slt i64 %47, 0
  %79 = icmp slt i64 %48, 0
  %80 = icmp slt i64 %49, 0
  %81 = icmp slt i64 %50, 0
  %82 = icmp slt i64 %51, 0
  %83 = icmp slt i64 %52, 0
  %84 = icmp slt i64 %53, 0
  %85 = icmp slt i64 %54, 0
  %86 = icmp slt i64 %55, 0
  %87 = icmp slt i64 %56, 0
  %88 = icmp slt i64 %57, 0
  %89 = icmp slt i64 %58, 0
  %90 = and i1 false, %59
  %91 = and i1 false, %60
  %92 = and i1 false, %61
  %93 = and i1 false, %62
  %94 = and i1 false, %63
  %95 = and i1 false, %64
  %96 = and i1 false, %65
  %97 = and i1 false, %66
  %98 = and i1 false, %67
  %99 = and i1 false, %68
  %100 = and i1 false, %69
  %101 = and i1 false, %70
  %102 = and i1 false, %71
  %103 = and i1 false, %72
  %104 = and i1 false, %73
  %105 = and i1 false, %74
  %106 = and i1 false, %75
  %107 = and i1 false, %76
  %108 = icmp eq i32 %2, 0
  %109 = and i1 false, %77
  %110 = and i1 false, %78
  %111 = and i1 false, %79
  %112 = and i1 false, %80
  %113 = and i1 false, %81
  %114 = and i1 false, %82
  %115 = and i1 false, %83
  %116 = and i1 false, %84
  %117 = and i1 false, %85
  %118 = and i1 false, %86
  %119 = or i64 %5, %26
  %120 = getelementptr float, ptr addrspace(1) null, i64 %119
  %121 = icmp slt i64 %119, 0
  ret void
}
