; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: %if x86-registered-target %{ opt < %s -passes=slp-vectorizer -S -mtriple=x86_64-unknown-linux -slp-threshold=-1 | FileCheck %s %}
; RUN: %if aarch64-registered-target %{ opt < %s -passes=slp-vectorizer -S -mtriple=aarch64-unknown-linux-gnu -slp-threshold=-1 | FileCheck %s %}

define i32 @diamond_broadcast(ptr noalias nocapture %B, ptr noalias nocapture %A) {
; CHECK-LABEL: @diamond_broadcast(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[LD:%.*]] = load i32, ptr [[A:%.*]], align 4
; CHECK-NEXT:    [[TMP0:%.*]] = insertelement <4 x i32> poison, i32 [[LD]], i32 0
; CHECK-NEXT:    [[SHUFFLE:%.*]] = shufflevector <4 x i32> [[TMP0]], <4 x i32> poison, <4 x i32> zeroinitializer
; CHECK-NEXT:    [[TMP1:%.*]] = mul <4 x i32> [[SHUFFLE]], [[SHUFFLE]]
; CHECK-NEXT:    store <4 x i32> [[TMP1]], ptr [[B:%.*]], align 4
; CHECK-NEXT:    ret i32 0
;
entry:
  %ld = load i32, ptr %A, align 4
  %mul = mul i32 %ld, %ld
  store i32 %mul, ptr %B, align 4
  %mul8 = mul i32 %ld, %ld
  %arrayidx9 = getelementptr inbounds i32, ptr %B, i64 1
  store i32 %mul8, ptr %arrayidx9, align 4
  %mul14 = mul i32 %ld, %ld
  %arrayidx15 = getelementptr inbounds i32, ptr %B, i64 2
  store i32 %mul14, ptr %arrayidx15, align 4
  %mul20 = mul i32 %ld, %ld
  %arrayidx21 = getelementptr inbounds i32, ptr %B, i64 3
  store i32 %mul20, ptr %arrayidx21, align 4
  ret i32 0
}


