; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 3
; RUN: %if x86-registered-target %{ opt -S --passes=slp-vectorizer -mtriple=x86_64-sie-ps5 < %s | FileCheck %s %}
; RUN: %if aarch64-registered-target %{ opt -S --passes=slp-vectorizer -mtriple=aarch64-unknown-linux-gnu < %s | FileCheck %s %}

define void @tes() {
; CHECK-LABEL: define void @tes() {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP0:%.*]] = fcmp ole <2 x double> zeroinitializer, zeroinitializer
; CHECK-NEXT:    br label [[TMP1:%.*]]
; CHECK:       1:
; CHECK-NEXT:    [[TMP3:%.*]] = shufflevector <2 x i1> zeroinitializer, <2 x i1> [[TMP0]], <4 x i32> <i32 0, i32 0, i32 0, i32 2>
; CHECK-NEXT:    [[TMP4:%.*]] = call i1 @llvm.vector.reduce.and.v4i1(<4 x i1> [[TMP3]])
; CHECK-NEXT:    [[OP_RDX:%.*]] = select i1 false, i1 [[TMP4]], i1 false
; CHECK-NEXT:    [[OP_RDX1:%.*]] = select i1 false, i1 [[OP_RDX]], i1 false
; CHECK-NEXT:    br i1 [[OP_RDX1]], label [[TMP6:%.*]], label [[TMP5:%.*]]
; CHECK:       4:
; CHECK-NEXT:    ret void
; CHECK:       5:
; CHECK-NEXT:    ret void
;
entry:
  %0 = extractelement <2 x i1> zeroinitializer, i64 0
  %1 = extractelement <2 x i1> zeroinitializer, i64 0
  %2 = fcmp ole <2 x double> zeroinitializer, zeroinitializer
  %3 = extractelement <2 x i1> %2, i64 0
  %4 = extractelement <2 x i1> zeroinitializer, i64 0
  br label %5

5:
  %6 = select i1 false, i1 false, i1 false
  %7 = select i1 %6, i1 %0, i1 false
  %8 = select i1 %7, i1 %1, i1 false
  %9 = select i1 %8, i1 false, i1 false
  %10 = select i1 %9, i1 %3, i1 false
  %11 = select i1 %10, i1 %4, i1 false
  br i1 %11, label %12, label %13

12:
  ret void

13:
  ret void
}
