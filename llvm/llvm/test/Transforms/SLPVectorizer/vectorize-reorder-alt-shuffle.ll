; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: %if x86-registered-target %{ opt -passes=slp-vectorizer -S -mtriple=x86_64-unknown-linux-gnu < %s | FileCheck %s %}
; RUN: %if aarch64-registered-target %{ opt -passes=slp-vectorizer -S -mtriple=aarch64-unknown-linux-gnu < %s | FileCheck %s %}

define void @foo(ptr %c, ptr %d) {
; CHECK-LABEL: @foo(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[ARRAYIDX4:%.*]] = getelementptr inbounds i8, ptr [[C:%.*]], i64 1
; CHECK-NEXT:    [[ADD_PTR53:%.*]] = getelementptr inbounds float, ptr [[D:%.*]], i64 -4
; CHECK-NEXT:    [[TMP1:%.*]] = load <4 x i8>, ptr [[ARRAYIDX4]], align 1
; CHECK-NEXT:    [[TMP2:%.*]] = zext <4 x i8> [[TMP1]] to <4 x i32>
; CHECK-NEXT:    [[TMP3:%.*]] = shl nuw nsw <4 x i32> [[TMP2]], <i32 2, i32 2, i32 2, i32 3>
; CHECK-NEXT:    [[TMP4:%.*]] = and <4 x i32> [[TMP2]], <i32 2, i32 2, i32 2, i32 3>
; CHECK-NEXT:    [[TMP5:%.*]] = shufflevector <4 x i32> [[TMP3]], <4 x i32> [[TMP4]], <4 x i32> <i32 1, i32 2, i32 7, i32 0>
; CHECK-NEXT:    [[TMP6:%.*]] = add nsw <4 x i32> undef, [[TMP5]]
; CHECK-NEXT:    [[TMP7:%.*]] = sitofp <4 x i32> [[TMP6]] to <4 x float>
; CHECK-NEXT:    [[TMP8:%.*]] = fdiv <4 x float> [[TMP7]], undef
; CHECK-NEXT:    store <4 x float> [[TMP8]], ptr [[ADD_PTR53]], align 4
; CHECK-NEXT:    ret void
;
entry:
  %arrayidx1 = getelementptr inbounds i8, ptr %c, i64 4
  %0 = load i8, ptr %arrayidx1, align 1
  %conv2 = zext i8 %0 to i32
  %and = and i32 %conv2, 3
  %arrayidx4 = getelementptr inbounds i8, ptr %c, i64 1
  %1 = load i8, ptr %arrayidx4, align 1
  %conv5 = zext i8 %1 to i32
  %shl6 = shl nuw nsw i32 %conv5, 2
  %arrayidx12 = getelementptr inbounds i8, ptr %c, i64 2
  %2 = load i8, ptr %arrayidx12, align 1
  %conv13 = zext i8 %2 to i32
  %shl14 = shl nuw nsw i32 %conv13, 2
  %arrayidx17 = getelementptr inbounds i8, ptr %c, i64 3
  %3 = load i8, ptr %arrayidx17, align 1
  %conv18 = zext i8 %3 to i32
  %shl19 = shl nuw nsw i32 %conv18, 2
  %sub = add nsw i32 undef, %shl6
  %conv27 = sitofp i32 %sub to float
  %div = fdiv float %conv27, undef
  %add.ptr = getelementptr inbounds float, ptr %d, i64 -1
  store float %div, ptr %add.ptr, align 4
  %sub32 = add nsw i32 undef, %and
  %conv33 = sitofp i32 %sub32 to float
  %div36 = fdiv float %conv33, undef
  %add.ptr37 = getelementptr inbounds float, ptr %d, i64 -2
  store float %div36, ptr %add.ptr37, align 4
  %sub40 = add nsw i32 undef, %shl19
  %conv41 = sitofp i32 %sub40 to float
  %div44 = fdiv float %conv41, undef
  %add.ptr45 = getelementptr inbounds float, ptr %d, i64 -3
  store float %div44, ptr %add.ptr45, align 4
  %sub48 = add nsw i32 undef, %shl14
  %conv49 = sitofp i32 %sub48 to float
  %div52 = fdiv float %conv49, undef
  %add.ptr53 = getelementptr inbounds float, ptr %d, i64 -4
  store float %div52, ptr %add.ptr53, align 4
  ret void
}
