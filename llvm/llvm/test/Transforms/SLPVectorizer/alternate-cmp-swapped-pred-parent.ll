; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: %if aarch64-registered-target %{ opt < %s -mtriple=aarch64-unknown-linux-gnu -passes=slp-vectorizer -S -slp-threshold=-1000 | FileCheck %s %}
; RUN: %if x86-registered-target %{ opt < %s -mtriple=x86_64-unknown -passes=slp-vectorizer -S -slp-threshold=-1000 | FileCheck %s %}

define void @test() {
; CHECK-LABEL: @test(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[CALL37:%.*]] = load i16, ptr poison, align 2
; CHECK-NEXT:    br label [[BB:%.*]]
; CHECK:       bb:
; CHECK-NEXT:    [[CALL:%.*]] = load i16, ptr poison, align 2
; CHECK-NEXT:    [[TMP0:%.*]] = insertelement <8 x i16> <i16 0, i16 0, i16 0, i16 poison, i16 poison, i16 poison, i16 0, i16 poison>, i16 [[CALL37]], i32 3
; CHECK-NEXT:    [[TMP1:%.*]] = insertelement <8 x i16> [[TMP0]], i16 [[CALL]], i32 5
; CHECK-NEXT:    [[TMP2:%.*]] = shufflevector <8 x i16> [[TMP1]], <8 x i16> poison, <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 3, i32 5, i32 6, i32 3>
; CHECK-NEXT:    [[TMP5:%.*]] = insertelement <8 x i16> <i16 0, i16 0, i16 0, i16 0, i16 0, i16 0, i16 poison, i16 poison>, i16 [[CALL37]], i32 6
; CHECK-NEXT:    [[TMP3:%.*]] = insertelement <8 x i16> [[TMP5]], i16 [[CALL]], i32 7
; CHECK-NEXT:    [[TMP4:%.*]] = icmp slt <8 x i16> [[TMP2]], [[TMP3]]
; CHECK-NEXT:    ret void
;
entry:
  %call37 = load i16, ptr poison, align 2
  br label %bb

bb:
  %call = load i16, ptr poison, align 2
  %0 = icmp slt i16 %call, 0
  %1 = icmp slt i16 %call37, %call
  %.not = icmp sgt i16 0, %call37
  %2 = icmp sgt i16 %call37, 0
  %3 = icmp slt i16 0, 0
  %.not206 = icmp sgt i16 0, %call37
  %4 = icmp sgt i16 0, 0
  %5 = icmp slt i16 0, 0
  ret void
}

define void @test1() {
; CHECK-LABEL: @test1(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[CALL:%.*]] = load i16, ptr poison, align 2
; CHECK-NEXT:    br label [[BB:%.*]]
; CHECK:       bb:
; CHECK-NEXT:    [[CALL37:%.*]] = load i16, ptr poison, align 2
; CHECK-NEXT:    [[TMP0:%.*]] = insertelement <8 x i16> <i16 0, i16 0, i16 0, i16 poison, i16 poison, i16 poison, i16 poison, i16 0>, i16 [[CALL]], i32 3
; CHECK-NEXT:    [[TMP1:%.*]] = insertelement <8 x i16> [[TMP0]], i16 [[CALL37]], i32 4
; CHECK-NEXT:    [[TMP2:%.*]] = shufflevector <8 x i16> [[TMP1]], <8 x i16> poison, <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 4, i32 4, i32 7>
; CHECK-NEXT:    [[TMP5:%.*]] = insertelement <8 x i16> <i16 0, i16 0, i16 0, i16 0, i16 0, i16 0, i16 poison, i16 poison>, i16 [[CALL]], i32 6
; CHECK-NEXT:    [[TMP3:%.*]] = insertelement <8 x i16> [[TMP5]], i16 [[CALL37]], i32 7
; CHECK-NEXT:    [[TMP4:%.*]] = icmp slt <8 x i16> [[TMP2]], [[TMP3]]
; CHECK-NEXT:    ret void
;
entry:
  %call = load i16, ptr poison, align 2
  br label %bb

bb:
  %call37 = load i16, ptr poison, align 2
  %0 = icmp slt i16 %call, 0
  %1 = icmp slt i16 %call37, %call
  %.not = icmp sgt i16 0, %call37
  %2 = icmp sgt i16 %call37, 0
  %3 = icmp slt i16 0, 0
  %.not206 = icmp sgt i16 0, %call37
  %4 = icmp sgt i16 0, 0
  %5 = icmp slt i16 0, 0
  ret void
}
