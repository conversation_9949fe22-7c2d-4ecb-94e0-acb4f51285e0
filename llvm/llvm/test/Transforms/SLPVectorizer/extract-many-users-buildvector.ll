; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 2
; RUN: %if x86-registered-target %{ opt -S -passes=slp-vectorizer -mtriple=x86_64-unknown-linux-gnu < %s | FileCheck %s --check-prefix X86 %}
; RUN: %if aarch64-registered-target %{ opt -S -passes=slp-vectorizer -mtriple=aarch64-unknown-linux-gnu < %s | FileCheck %s --check-prefix AARCH64 %}

define i1 @test(float %0, double %1) {
; X86-LABEL: define i1 @test
; X86-SAME: (float [[TMP0:%.*]], double [[TMP1:%.*]]) {
; X86-NEXT:    [[TMP3:%.*]] = insertelement <4 x float> <float 0.000000e+00, float 0.000000e+00, float 0.000000e+00, float poison>, float [[TMP0]], i32 3
; X86-NEXT:    [[TMP4:%.*]] = fpext <4 x float> [[TMP3]] to <4 x double>
; X86-NEXT:    [[TMP5:%.*]] = insertelement <6 x double> <double poison, double poison, double poison, double poison, double poison, double 0.000000e+00>, double [[TMP1]], i32 4
; X86-NEXT:    [[TMP6:%.*]] = shufflevector <4 x double> [[TMP4]], <4 x double> poison, <6 x i32> <i32 0, i32 1, i32 2, i32 3, i32 poison, i32 poison>
; X86-NEXT:    [[TMP7:%.*]] = shufflevector <6 x double> [[TMP5]], <6 x double> [[TMP6]], <6 x i32> <i32 6, i32 7, i32 8, i32 9, i32 4, i32 5>
; X86-NEXT:    [[TMP8:%.*]] = fmul <6 x double> zeroinitializer, [[TMP7]]
; X86-NEXT:    [[TMP9:%.*]] = shufflevector <6 x double> [[TMP7]], <6 x double> [[TMP8]], <4 x i32> <i32 poison, i32 4, i32 11, i32 11>
; X86-NEXT:    [[TMP10:%.*]] = shufflevector <4 x double> [[TMP9]], <4 x double> <double 0.000000e+00, double poison, double poison, double poison>, <4 x i32> <i32 4, i32 1, i32 2, i32 3>
; X86-NEXT:    [[TMP11:%.*]] = shufflevector <6 x double> [[TMP7]], <6 x double> poison, <4 x i32> <i32 2, i32 0, i32 1, i32 poison>
; X86-NEXT:    [[TMP12:%.*]] = shufflevector <4 x double> [[TMP11]], <4 x double> <double poison, double poison, double poison, double 0.000000e+00>, <4 x i32> <i32 0, i32 1, i32 2, i32 7>
; X86-NEXT:    [[TMP13:%.*]] = fmul <4 x double> [[TMP10]], [[TMP12]]
; X86-NEXT:    [[TMP14:%.*]] = call <8 x double> @llvm.vector.insert.v8f64.v4f64(<8 x double> <double poison, double poison, double poison, double poison, double 0.000000e+00, double 0.000000e+00, double 0.000000e+00, double 0.000000e+00>, <4 x double> [[TMP13]], i64 0)
; X86-NEXT:    [[TMP15:%.*]] = call <8 x double> @llvm.vector.insert.v8f64.v6f64(<8 x double> <double poison, double poison, double poison, double poison, double poison, double poison, double 0.000000e+00, double 0.000000e+00>, <6 x double> [[TMP8]], i64 0)
; X86-NEXT:    [[TMP16:%.*]] = fsub <8 x double> [[TMP14]], [[TMP15]]
; X86-NEXT:    [[TMP17:%.*]] = fmul <8 x double> [[TMP14]], [[TMP15]]
; X86-NEXT:    [[TMP18:%.*]] = shufflevector <8 x double> [[TMP16]], <8 x double> [[TMP17]], <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 13, i32 14, i32 15>
; X86-NEXT:    [[TMP19:%.*]] = fptrunc <8 x double> [[TMP18]] to <8 x float>
; X86-NEXT:    [[TMP20:%.*]] = fmul <8 x float> [[TMP19]], zeroinitializer
; X86-NEXT:    [[TMP21:%.*]] = fcmp oeq <8 x float> [[TMP20]], zeroinitializer
; X86-NEXT:    [[TMP22:%.*]] = freeze <8 x i1> [[TMP21]]
; X86-NEXT:    [[TMP23:%.*]] = call i1 @llvm.vector.reduce.and.v8i1(<8 x i1> [[TMP22]])
; X86-NEXT:    ret i1 [[TMP23]]
;
; AARCH64-LABEL: define i1 @test
; AARCH64-SAME: (float [[TMP0:%.*]], double [[TMP1:%.*]]) {
; AARCH64-NEXT:    [[TMP3:%.*]] = insertelement <4 x float> <float 0.000000e+00, float 0.000000e+00, float 0.000000e+00, float poison>, float [[TMP0]], i32 3
; AARCH64-NEXT:    [[TMP4:%.*]] = fpext <4 x float> [[TMP3]] to <4 x double>
; AARCH64-NEXT:    [[TMP5:%.*]] = insertelement <6 x double> <double poison, double poison, double poison, double poison, double poison, double 0.000000e+00>, double [[TMP1]], i32 4
; AARCH64-NEXT:    [[TMP6:%.*]] = shufflevector <4 x double> [[TMP4]], <4 x double> poison, <6 x i32> <i32 0, i32 1, i32 2, i32 3, i32 poison, i32 poison>
; AARCH64-NEXT:    [[TMP7:%.*]] = shufflevector <6 x double> [[TMP5]], <6 x double> [[TMP6]], <6 x i32> <i32 6, i32 7, i32 8, i32 9, i32 4, i32 5>
; AARCH64-NEXT:    [[TMP8:%.*]] = fmul <6 x double> zeroinitializer, [[TMP7]]
; AARCH64-NEXT:    [[TMP9:%.*]] = shufflevector <6 x double> [[TMP7]], <6 x double> [[TMP8]], <4 x i32> <i32 poison, i32 4, i32 11, i32 11>
; AARCH64-NEXT:    [[TMP10:%.*]] = shufflevector <4 x double> [[TMP9]], <4 x double> <double 0.000000e+00, double poison, double poison, double poison>, <4 x i32> <i32 4, i32 1, i32 2, i32 3>
; AARCH64-NEXT:    [[TMP11:%.*]] = shufflevector <6 x double> [[TMP7]], <6 x double> poison, <4 x i32> <i32 2, i32 0, i32 poison, i32 poison>
; AARCH64-NEXT:    [[TMP12:%.*]] = shufflevector <4 x double> [[TMP11]], <4 x double> <double poison, double poison, double poison, double 0.000000e+00>, <4 x i32> <i32 0, i32 1, i32 poison, i32 7>
; AARCH64-NEXT:    [[TMP13:%.*]] = shufflevector <4 x double> [[TMP12]], <4 x double> [[TMP4]], <4 x i32> <i32 0, i32 1, i32 5, i32 3>
; AARCH64-NEXT:    [[TMP14:%.*]] = fmul <4 x double> [[TMP10]], [[TMP13]]
; AARCH64-NEXT:    [[TMP15:%.*]] = call <8 x double> @llvm.vector.insert.v8f64.v4f64(<8 x double> <double poison, double poison, double poison, double poison, double 0.000000e+00, double 0.000000e+00, double 0.000000e+00, double 0.000000e+00>, <4 x double> [[TMP14]], i64 0)
; AARCH64-NEXT:    [[TMP16:%.*]] = call <8 x double> @llvm.vector.insert.v8f64.v6f64(<8 x double> <double poison, double poison, double poison, double poison, double poison, double poison, double 0.000000e+00, double 0.000000e+00>, <6 x double> [[TMP8]], i64 0)
; AARCH64-NEXT:    [[TMP17:%.*]] = fsub <8 x double> [[TMP15]], [[TMP16]]
; AARCH64-NEXT:    [[TMP18:%.*]] = fmul <8 x double> [[TMP15]], [[TMP16]]
; AARCH64-NEXT:    [[TMP19:%.*]] = shufflevector <8 x double> [[TMP17]], <8 x double> [[TMP18]], <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 13, i32 14, i32 15>
; AARCH64-NEXT:    [[TMP20:%.*]] = fptrunc <8 x double> [[TMP19]] to <8 x float>
; AARCH64-NEXT:    [[TMP21:%.*]] = fmul <8 x float> [[TMP20]], zeroinitializer
; AARCH64-NEXT:    [[TMP22:%.*]] = fcmp oeq <8 x float> [[TMP21]], zeroinitializer
; AARCH64-NEXT:    [[TMP23:%.*]] = freeze <8 x i1> [[TMP22]]
; AARCH64-NEXT:    [[TMP24:%.*]] = call i1 @llvm.vector.reduce.and.v8i1(<8 x i1> [[TMP23]])
; AARCH64-NEXT:    ret i1 [[TMP24]]
;
  %3 = fpext float %0 to double
  %4 = fpext float 0.000000e+00 to double
  %5 = fpext float 0.000000e+00 to double
  %6 = fpext float 0.000000e+00 to double
  %7 = fmul double 0.000000e+00, 0.000000e+00
  %8 = fmul double 0.000000e+00, %1
  %9 = fmul double 0.000000e+00, 0.000000e+00
  %10 = fmul double 0.000000e+00, %5
  %11 = fmul double 0.000000e+00, %6
  %12 = fsub double %10, %11
  %13 = fptrunc double %12 to float
  %14 = fmul double %9, 0.000000e+00
  %15 = fmul double 0.000000e+00, %3
  %16 = fsub double %14, %15
  %17 = fptrunc double %16 to float
  %18 = fptrunc double %7 to float
  %19 = fmul double %1, %6
  %20 = fmul double 0.000000e+00, %4
  %21 = fsub double %19, %20
  %22 = fptrunc double %21 to float
  %23 = fsub double 0.000000e+00, %8
  %24 = fptrunc double %23 to float
  %25 = fmul double 0.000000e+00, 0.000000e+00
  %26 = fptrunc double %25 to float
  %27 = fmul double %9, %4
  %28 = fmul double 0.000000e+00, %5
  %29 = fsub double %27, %28
  %30 = fptrunc double %29 to float
  %31 = fmul double %9, 0.000000e+00
  %32 = fptrunc double %31 to float
  %33 = fmul float %13, 0.000000e+00
  %34 = fcmp oeq float %33, 0.000000e+00
  %35 = fmul float %22, 0.000000e+00
  %36 = fcmp oeq float %35, 0.000000e+00
  %37 = select i1 %34, i1 %36, i1 false
  %38 = fmul float %30, 0.000000e+00
  %39 = fcmp oeq float %38, 0.000000e+00
  %40 = select i1 %37, i1 %39, i1 false
  %41 = fmul float %17, 0.000000e+00
  %42 = fcmp oeq float %41, 0.000000e+00
  %43 = select i1 %40, i1 %42, i1 false
  %44 = fmul float %24, 0.000000e+00
  %45 = fcmp oeq float %44, 0.000000e+00
  %46 = select i1 %43, i1 %45, i1 false
  %47 = fmul float %32, 0.000000e+00
  %48 = fcmp oeq float %47, 0.000000e+00
  %49 = select i1 %46, i1 %48, i1 false
  %50 = fmul float %18, 0.000000e+00
  %51 = fcmp oeq float %50, 0.000000e+00
  %52 = select i1 %49, i1 %51, i1 false
  %53 = fmul float %26, 0.000000e+00
  %54 = fcmp oeq float %53, 0.000000e+00
  %55 = select i1 %52, i1 %54, i1 false
  ret i1 %55
}
