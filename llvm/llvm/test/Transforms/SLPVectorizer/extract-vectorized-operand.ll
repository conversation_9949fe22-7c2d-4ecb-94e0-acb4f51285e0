; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: %if x86-registered-target %{ opt -S --passes=slp-vectorizer -slp-threshold=-99999 < %s -mtriple=x86_64-unknown-linux-gnu | FileCheck %s %}
; RUN: %if aarch64-registered-target %{ opt -S --passes=slp-vectorizer -slp-threshold=-99999 < %s -mtriple=aarch64-unknown-linux-gnu | FileCheck %s %}

define void @test() {
; CHECK-LABEL: define void @test() {
; CHECK-NEXT:  [[BB:.*]]:
; CHECK-NEXT:    [[TMP0:%.*]] = shufflevector <2 x ptr addrspace(1)> zeroinitializer, <2 x ptr addrspace(1)> zeroinitializer, <2 x i32> <i32 1, i32 0>
; CHECK-NEXT:    [[TMP1:%.*]] = extractelement <2 x ptr addrspace(1)> [[TMP0]], i32 0
; CHECK-NEXT:    br label %[[BB43:.*]]
; CHECK:       [[BB20:.*]]:
; CHECK-NEXT:    br label %[[BB105:.*]]
; CHECK:       [[BB43]]:
; CHECK-NEXT:    [[TMP2:%.*]] = phi <2 x ptr addrspace(1)> [ [[TMP3:%.*]], %[[BB51:.*]] ], [ zeroinitializer, %[[BB]] ]
; CHECK-NEXT:    br i1 false, label %[[BB105]], label %[[BB51]]
; CHECK:       [[BB51]]:
; CHECK-NEXT:    [[TMP3]] = phi <2 x ptr addrspace(1)> [ poison, %[[BB54:.*]] ], [ zeroinitializer, %[[BB43]] ]
; CHECK-NEXT:    br label %[[BB43]]
; CHECK:       [[BB54]]:
; CHECK-NEXT:    br label %[[BB51]]
; CHECK:       [[BB105]]:
; CHECK-NEXT:    [[PHI106:%.*]] = phi ptr addrspace(1) [ [[TMP1]], %[[BB20]] ], [ null, %[[BB43]] ]
; CHECK-NEXT:    ret void
;
bb:
  %0 = shufflevector <2 x ptr addrspace(1)> zeroinitializer, <2 x ptr addrspace(1)> zeroinitializer, <2 x i32> <i32 1, i32 0>
  %1 = extractelement <2 x ptr addrspace(1)> %0, i32 0
  %2 = extractelement <2 x ptr addrspace(1)> %0, i32 1
  br label %bb43

bb20:
  br label %bb105

bb43:
  %phi441 = phi ptr addrspace(1) [ %4, %bb51 ], [ %2, %bb ]
  %phi452 = phi ptr addrspace(1) [ %5, %bb51 ], [ %1, %bb ]
  br i1 false, label %bb105, label %bb51

bb51:
  %3 = phi <2 x ptr addrspace(1)> [ poison, %bb54 ], [ zeroinitializer, %bb43 ]
  %4 = extractelement <2 x ptr addrspace(1)> %3, i32 0
  %5 = extractelement <2 x ptr addrspace(1)> %3, i32 1
  br label %bb43

bb54:
  br label %bb51

bb105:
  %phi106 = phi ptr addrspace(1) [ %1, %bb20 ], [ null, %bb43 ]
  ret void
}

