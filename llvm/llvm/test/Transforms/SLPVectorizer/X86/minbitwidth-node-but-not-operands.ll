; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 4
; RUN: opt --passes=slp-vectorizer -S -mtriple=x86_64-unknown-linux-gnu < %s | FileCheck %s

define void @test() {
; CHECK-LABEL: define void @test() {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    store <8 x i16> zeroinitializer, ptr null, align 2
; CHECK-NEXT:    ret void
;
entry:
  %arrayidx8 = getelementptr i8, ptr null, i64 2
  %shr10 = ashr i32 0, 0
  %shr19 = lshr i32 0, 0
  %sub20 = or i32 %shr19, %shr10
  %xor21 = xor i32 %sub20, 0
  %conv22 = trunc i32 %xor21 to i16
  store i16 %conv22, ptr %arrayidx8, align 2
  %arrayidx28 = getelementptr i8, ptr null, i64 4
  %shr34 = lshr i32 0, 0
  %sub35 = or i32 %shr34, %shr10
  %xor36 = xor i32 %sub35, 0
  %conv37 = trunc i32 %xor36 to i16
  store i16 %conv37, ptr %arrayidx28, align 2
  %arrayidx43 = getelementptr i8, ptr null, i64 6
  %shr49 = lshr i32 0, 0
  %sub50 = or i32 %shr49, %shr10
  %xor51 = xor i32 %sub50, 0
  %conv52 = trunc i32 %xor51 to i16
  store i16 %conv52, ptr %arrayidx43, align 2
  %arrayidx.1 = getelementptr i8, ptr null, i64 8
  %shr.1 = lshr i32 0, 0
  %xor2.1 = xor i32 %shr.1, %shr10
  %sub3.1 = or i32 %xor2.1, 0
  %conv4.1 = trunc i32 %sub3.1 to i16
  store i16 %conv4.1, ptr %arrayidx.1, align 2
  %arrayidx8.1 = getelementptr i8, ptr null, i64 10
  %shr10.1 = ashr i32 0, 0
  %shr19.1 = lshr i32 0, 0
  %sub20.1 = or i32 %shr19.1, %shr10.1
  %xor21.1 = xor i32 %sub20.1, 0
  %conv22.1 = trunc i32 %xor21.1 to i16
  store i16 %conv22.1, ptr %arrayidx8.1, align 2
  %arrayidx28.1 = getelementptr i8, ptr null, i64 12
  %shr34.1 = lshr i32 0, 0
  %sub35.1 = or i32 %shr34.1, %shr10.1
  %xor36.1 = xor i32 %sub35.1, 0
  %conv37.1 = trunc i32 %xor36.1 to i16
  store i16 %conv37.1, ptr %arrayidx28.1, align 2
  %arrayidx43.1 = getelementptr i8, ptr null, i64 14
  %shr49.1 = lshr i32 0, 0
  %sub50.1 = or i32 %shr49.1, %shr10.1
  %xor51.1 = xor i32 %sub50.1, 0
  %conv52.1 = trunc i32 %xor51.1 to i16
  store i16 %conv52.1, ptr %arrayidx43.1, align 2
  %shr.2 = lshr i32 0, 0
  %xor2.2 = xor i32 %shr.2, %shr10.1
  %sub3.2 = or i32 %xor2.2, 0
  %conv4.2 = trunc i32 %sub3.2 to i16
  store i16 %conv4.2, ptr null, align 2
  ret void
}
