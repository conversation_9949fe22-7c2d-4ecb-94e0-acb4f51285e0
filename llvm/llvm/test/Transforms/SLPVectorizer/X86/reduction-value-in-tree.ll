; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -S -passes=slp-vectorizer -mtriple=x86_64-unknown-linux < %s | FileCheck %s
define void @test() {
; CHECK-LABEL: @test(
; CHECK-NEXT:  bb:
; CHECK-NEXT:    br i1 false, label [[PH:%.*]], label [[EXIT:%.*]]
; CHECK:       ph:
; CHECK-NEXT:    [[TMP0:%.*]] = call i8 @llvm.vector.reduce.and.v8i8(<8 x i8> zeroinitializer)
; CHECK-NEXT:    [[OP_RDX:%.*]] = and i8 0, [[TMP0]]
; CHECK-NEXT:    [[OP_RDX1:%.*]] = and i8 [[OP_RDX]], 0
; CHECK-NEXT:    br label [[EXIT]]
; CHECK:       exit:
; CHECK-NEXT:    [[PHI:%.*]] = phi i8 [ [[OP_RDX1]], [[PH]] ], [ 0, [[BB:%.*]] ]
; CHECK-NEXT:    ret void
;
bb:
  br i1 false, label %ph, label %exit

ph:
  %add1 = add i8 0, 0
  %add2 = add i8 %add1, 0
  %add3 = add i8 %add1, 0
  %add4 = add i8 %add1, 0
  %add5 = add i8 %add1, 0
  %add6 = add i8 %add1, 0
  %add7 = add i8 %add1, 0
  %add8 = add i8 %add1, 0
  %add9 = add i8 0, 0
  %add10 = add i8 0, %add9
  %0 = and i8 %add10, %add3
  %1 = and i8 %0, %add2
  %2 = and i8 %1, %add4
  %3 = and i8 %2, 0
  %4 = and i8 %3, %add5
  %5 = and i8 %4, %add6
  %6 = and i8 %5, 0
  %7 = and i8 %6, 0
  %8 = and i8 %7, 0
  %9 = and i8 %8, 0
  %10 = and i8 %9, 0
  %11 = and i8 %10, 0
  %12 = and i8 %11, 0
  %13 = and i8 %12, %add7
  %14 = and i8 %13, %add8
  %15 = and i8 %14, 0
  %16 = and i8 %15, 0
  %17 = and i8 %16, 0
  %18 = and i8 %17, 0
  %and = and i8 %18, %add1
  br label %exit

exit:
  %phi = phi i8 [ %and, %ph ], [ 0, %bb ]
  ret void
}
