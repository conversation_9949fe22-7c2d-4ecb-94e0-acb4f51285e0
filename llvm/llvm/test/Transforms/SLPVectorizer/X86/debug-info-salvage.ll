; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown-linux -mattr=+avx2 < %s | FileCheck %s

define void @test() {
; CHECK-LABEL: define void @test(
; CHECK-SAME: ) #[[ATTR0:[0-9]+]] {
; CHECK-NEXT:  [[ENTRY:.*:]]
; CHECK-NEXT:    br label %[[COND_END_I:.*]]
; CHECK:       [[COND_END_I]]:
; CHECK-NEXT:      #dbg_value(!DIArgList(i32 0, i32 undef), [[META3:![0-9]+]], !DIExpression(DW_OP_LLVM_arg, 0, DW_OP_LLVM_arg, 1, DW_OP_or, DW_OP_stack_value), [[META5:![0-9]+]])
; CHECK-NEXT:    [[TMP0:%.*]] = call <2 x i32> @llvm.umin.v2i32(<2 x i32> zeroinitializer, <2 x i32> zeroinitializer)
; CHECK-NEXT:    [[TMP1:%.*]] = select <2 x i1> zeroinitializer, <2 x i32> zeroinitializer, <2 x i32> [[TMP0]]
; CHECK-NEXT:    [[TMP2:%.*]] = shl <2 x i32> [[TMP1]], <i32 0, i32 16>
; CHECK-NEXT:    [[TMP3:%.*]] = or <2 x i32> [[TMP2]], zeroinitializer
; CHECK-NEXT:    [[TMP4:%.*]] = or <2 x i32> [[TMP3]], zeroinitializer
; CHECK-NEXT:    [[TMP5:%.*]] = or <2 x i32> [[TMP4]], zeroinitializer
; CHECK-NEXT:    store <2 x i32> [[TMP5]], ptr null, align 4
; CHECK-NEXT:    ret void
;
entry:
  %arrayidx51.1.i = getelementptr i8, ptr null, i64 4
  %retval.sroa.3.0.insert.ext.i.i = zext i8 0 to i32
  %retval.sroa.2.0.insert.ext.i.i = zext i8 0 to i32
  br label %cond.end.i

cond.end.i:
  %add46.i30 = or i32 0, %retval.sroa.2.0.insert.ext.i.i
  %add49.i = or i32 0, %retval.sroa.3.0.insert.ext.i.i
  #dbg_value(i32 %add49.i, !8, !DIExpression(), !16)
  %0 = tail call i32 @llvm.umin.i32(i32 %add46.i30, i32 0)
  %cmp.i14.i.i.i = icmp slt i32 %add49.i, 0
  %block_color.sroa.7.0.insert.ext.i = select i1 %cmp.i14.i.i.i, i32 0, i32 0
  %block_color.sroa.7.0.insert.shift.i = shl i32 %block_color.sroa.7.0.insert.ext.i, 16
  %block_color.sroa.5.0.insert.ext.i = select i1 false, i32 0, i32 %0
  %block_color.sroa.5.0.insert.shift.i = shl i32 %block_color.sroa.5.0.insert.ext.i, 0
  %block_color.sroa.7.0.insert.insert.i = or i32 %block_color.sroa.7.0.insert.shift.i, %block_color.sroa.5.0.insert.shift.i
  %block_color.sroa.5.0.insert.insert.i = or i32 %block_color.sroa.7.0.insert.insert.i, 0
  %block_color.sroa.0.0.insert.insert.i = or i32 %block_color.sroa.5.0.insert.insert.i, 0
  store i32 %block_color.sroa.0.0.insert.insert.i, ptr null, align 4
  %add46.1.i = or i32 0, %retval.sroa.2.0.insert.ext.i.i
  %add49.1.i = or i32 0, %retval.sroa.3.0.insert.ext.i.i
  %cmp.i11.i.i.1.i = icmp slt i32 %add46.1.i, 0
  %1 = tail call i32 @llvm.umin.i32(i32 %add49.1.i, i32 0)
  %block_color.sroa.7.0.insert.ext.1.i = select i1 false, i32 0, i32 %1
  %block_color.sroa.7.0.insert.shift.1.i = shl i32 %block_color.sroa.7.0.insert.ext.1.i, 16
  %block_color.sroa.5.0.insert.ext.1.i = select i1 %cmp.i11.i.i.1.i, i32 0, i32 0
  %block_color.sroa.5.0.insert.shift.1.i = shl i32 %block_color.sroa.5.0.insert.ext.1.i, 0
  %block_color.sroa.7.0.insert.insert.1.i = or i32 %block_color.sroa.7.0.insert.shift.1.i, %block_color.sroa.5.0.insert.shift.1.i
  %block_color.sroa.5.0.insert.insert.1.i = or i32 %block_color.sroa.7.0.insert.insert.1.i, 0
  %block_color.sroa.0.0.insert.insert.1.i = or i32 %block_color.sroa.5.0.insert.insert.1.i, 0
  store i32 %block_color.sroa.0.0.insert.insert.1.i, ptr %arrayidx51.1.i, align 4
  ret void
}

!llvm.dbg.cu = !{!0}
!llvm.module.flags = !{!7}

!0 = distinct !DICompileUnit(language: DW_LANG_C_plus_plus_14, file: !1)
!1 = !DIFile(filename: "q.cpp", directory: "/tmp")
!7 = !{i32 2, !"Debug Info Version", i32 3}
!8 = !DILocalVariable(name: "sb", arg: 4, scope: !9)
!9 = distinct !DISubprogram(name: "color_rgba", unit: !0)
!16 = !DILocation(scope: !9)
;.
; CHECK: [[META0:![0-9]+]] = distinct !DICompileUnit(language: DW_LANG_C_plus_plus_14, file: [[META1:![0-9]+]], isOptimized: false, runtimeVersion: 0, emissionKind: NoDebug)
; CHECK: [[META1]] = !DIFile(filename: "q.cpp", directory: {{.*}})
; CHECK: [[META3]] = !DILocalVariable(name: "sb", arg: 4, scope: [[META4:![0-9]+]])
; CHECK: [[META4]] = distinct !DISubprogram(name: "color_rgba", scope: null, spFlags: DISPFlagDefinition, unit: [[META0]])
; CHECK: [[META5]] = !DILocation(line: 0, scope: [[META4]])
;.
