; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown-linux-gnu -mcpu=core-avx2 < %s | FileCheck %s

define i32 @a() {
; CHECK-LABEL: define i32 @a(
; CHECK-SAME: ) #[[ATTR0:[0-9]+]] {
; CHECK-NEXT:    br label %[[BB1:.*]]
; CHECK:       [[BB1]]:
; CHECK-NEXT:    [[TMP4:%.*]] = phi <4 x i8> [ zeroinitializer, [[TMP0:%.*]] ], [ [[TMP6:%.*]], %[[BB1]] ]
; CHECK-NEXT:    [[TMP3:%.*]] = phi <2 x i8> [ zeroinitializer, [[TMP0]] ], [ [[TMP17:%.*]], %[[BB1]] ]
; CHECK-NEXT:    [[TMP5:%.*]] = shufflevector <4 x i8> [[TMP4]], <4 x i8> poison, <8 x i32> <i32 0, i32 0, i32 1, i32 1, i32 2, i32 2, i32 3, i32 3>
; CHECK-NEXT:    [[TMP6]] = load <4 x i8>, ptr null, align 4
; CHECK-NEXT:    [[TMP12:%.*]] = shufflevector <4 x i8> [[TMP6]], <4 x i8> poison, <4 x i32> <i32 poison, i32 poison, i32 0, i32 1>
; CHECK-NEXT:    [[TMP7:%.*]] = shufflevector <2 x i8> [[TMP3]], <2 x i8> poison, <4 x i32> <i32 0, i32 1, i32 poison, i32 poison>
; CHECK-NEXT:    [[TMP8:%.*]] = shufflevector <4 x i8> [[TMP12]], <4 x i8> [[TMP7]], <4 x i32> <i32 4, i32 5, i32 2, i32 3>
; CHECK-NEXT:    [[TMP9:%.*]] = xor <4 x i8> [[TMP6]], [[TMP8]]
; CHECK-NEXT:    [[TMP10:%.*]] = shufflevector <4 x i8> [[TMP6]], <4 x i8> poison, <8 x i32> <i32 poison, i32 0, i32 poison, i32 1, i32 poison, i32 2, i32 poison, i32 3>
; CHECK-NEXT:    [[TMP11:%.*]] = shufflevector <4 x i8> [[TMP9]], <4 x i8> poison, <8 x i32> <i32 0, i32 poison, i32 1, i32 poison, i32 2, i32 poison, i32 3, i32 poison>
; CHECK-NEXT:    [[TMP18:%.*]] = shufflevector <8 x i8> [[TMP10]], <8 x i8> [[TMP11]], <8 x i32> <i32 8, i32 1, i32 10, i32 3, i32 12, i32 5, i32 14, i32 7>
; CHECK-NEXT:    [[TMP19:%.*]] = shufflevector <4 x i8> [[TMP4]], <4 x i8> poison, <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 poison, i32 poison, i32 poison, i32 poison>
; CHECK-NEXT:    [[TMP21:%.*]] = shufflevector <8 x i8> [[TMP19]], <8 x i8> [[TMP18]], <8 x i32> <i32 1, i32 3, i32 2, i32 9, i32 3, i32 11, i32 9, i32 13>
; CHECK-NEXT:    [[TMP22:%.*]] = xor <8 x i8> [[TMP18]], [[TMP21]]
; CHECK-NEXT:    [[TMP23:%.*]] = xor <8 x i8> [[TMP22]], [[TMP5]]
; CHECK-NEXT:    store <8 x i8> [[TMP23]], ptr null, align 4
; CHECK-NEXT:    [[TMP17]] = shufflevector <4 x i8> [[TMP6]], <4 x i8> poison, <2 x i32> <i32 2, i32 3>
; CHECK-NEXT:    br label %[[BB1]]
;
  br label %1

1:
  %2 = phi i8 [ 0, %0 ], [ %40, %1 ]
  %3 = phi i8 [ 0, %0 ], [ %28, %1 ]
  %4 = phi i8 [ 0, %0 ], [ %16, %1 ]
  %5 = phi i8 [ 0, %0 ], [ %6, %1 ]
  %6 = load i8, ptr null, align 4
  %7 = xor i8 %6, %3
  %8 = xor i8 %7, %4
  %9 = xor i8 %8, %5
  store i8 %9, ptr null, align 4
  %10 = xor i8 %6, %2
  %11 = xor i8 %10, %5
  %12 = add i64 0, 1
  %13 = getelementptr i8, ptr null, i64 %12
  store i8 %11, ptr %13, align 1
  %14 = add i64 0, 1
  %15 = getelementptr i8, ptr null, i64 %14
  %16 = load i8, ptr %15, align 1
  %17 = xor i8 %16, %2
  %18 = xor i8 %17, %3
  %19 = xor i8 %18, %4
  %20 = add i64 0, 2
  %21 = getelementptr i8, ptr null, i64 %20
  store i8 %19, ptr %21, align 2
  %22 = xor i8 %16, %6
  %23 = xor i8 %22, %4
  %24 = add i64 0, 3
  %25 = getelementptr i8, ptr null, i64 %24
  store i8 %23, ptr %25, align 1
  %26 = add i64 0, 2
  %27 = getelementptr i8, ptr null, i64 %26
  %28 = load i8, ptr %27, align 2
  %29 = xor i8 %28, %6
  %30 = xor i8 %29, %2
  %31 = xor i8 %30, %3
  %32 = add i64 0, 4
  %33 = getelementptr i8, ptr null, i64 %32
  store i8 %31, ptr %33, align 4
  %34 = xor i8 %28, %16
  %35 = xor i8 %34, %3
  %36 = add i64 0, 5
  %37 = getelementptr i8, ptr null, i64 %36
  store i8 %35, ptr %37, align 1
  %38 = add i64 0, 3
  %39 = getelementptr i8, ptr null, i64 %38
  %40 = load i8, ptr %39, align 1
  %41 = xor i8 %40, %16
  %42 = xor i8 %41, %6
  %43 = xor i8 %42, %2
  %44 = add i64 0, 6
  %45 = getelementptr i8, ptr null, i64 %44
  store i8 %43, ptr %45, align 2
  %46 = xor i8 %40, %28
  %47 = xor i8 %46, %2
  %48 = add i64 0, 7
  %49 = getelementptr i8, ptr null, i64 %48
  store i8 %47, ptr %49, align 1
  br label %1
}

