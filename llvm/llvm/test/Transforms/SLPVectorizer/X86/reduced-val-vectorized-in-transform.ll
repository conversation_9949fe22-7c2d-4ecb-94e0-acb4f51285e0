; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown-linux-gnu -slp-threshold=-10 < %s | FileCheck %s

define i32 @test(i1 %cond) {
; CHECK-LABEL: define i32 @test(
; CHECK-SAME: i1 [[COND:%.*]]) {
; CHECK-NEXT:  [[ENTRY:.*]]:
; CHECK-NEXT:    br label %[[BB:.*]]
; CHECK:       [[BB]]:
; CHECK-NEXT:    [[P1:%.*]] = phi i32 [ [[OR92:%.*]], %[[BB]] ], [ 0, %[[ENTRY]] ]
; CHECK-NEXT:    [[TMP0:%.*]] = phi <2 x i32> [ [[TMP8:%.*]], %[[BB]] ], [ zeroinitializer, %[[ENTRY]] ]
; CHECK-NEXT:    [[TMP1:%.*]] = or i32 1, 0
; CHECK-NEXT:    [[TMP2:%.*]] = shufflevector <2 x i32> [[TMP0]], <2 x i32> poison, <4 x i32> <i32 0, i32 1, i32 poison, i32 poison>
; CHECK-NEXT:    [[TMP3:%.*]] = shufflevector <4 x i32> [[TMP2]], <4 x i32> <i32 poison, i32 poison, i32 0, i32 0>, <4 x i32> <i32 poison, i32 1, i32 6, i32 7>
; CHECK-NEXT:    [[TMP4:%.*]] = insertelement <4 x i32> [[TMP3]], i32 [[P1]], i32 0
; CHECK-NEXT:    [[TMP5:%.*]] = or <4 x i32> zeroinitializer, [[TMP4]]
; CHECK-NEXT:    [[OR92]] = or i32 1, 0
; CHECK-NEXT:    [[TMP6:%.*]] = call i32 @llvm.vector.reduce.xor.v4i32(<4 x i32> [[TMP5]])
; CHECK-NEXT:    [[OP_RDX:%.*]] = xor i32 [[TMP6]], [[OR92]]
; CHECK-NEXT:    [[TMP7:%.*]] = insertelement <2 x i32> poison, i32 [[OP_RDX]], i32 0
; CHECK-NEXT:    [[TMP8]] = insertelement <2 x i32> [[TMP7]], i32 [[TMP1]], i32 1
; CHECK-NEXT:    br i1 [[COND]], label %[[EXIT:.*]], label %[[BB]]
; CHECK:       [[EXIT]]:
; CHECK-NEXT:    ret i32 [[OP_RDX]]
;
entry:
  br label %bb

bb:
  %p1 = phi i32 [ %or92, %bb ], [ 0, %entry ]
  %p2 = phi i32 [ %0, %bb ], [ 0, %entry ]
  %p3 = phi i32 [ %4, %bb ], [ 0, %entry ]
  %0 = or i32 1, 0
  %or8.i = or i32 0, 0
  %or9.i = or i32 0, 0
  %or91 = or i32 %p1, 0
  %or12.i = or i32 %p2, 0
  %or92 = or i32 1, 0
  %1 = xor i32 %or91, %or12.i
  %2 = xor i32 %1, %or9.i
  %3 = xor i32 %2, %or8.i
  %4 = xor i32 %3, %or92
  br i1 %cond, label %exit, label %bb

exit:
  ret i32 %4
}
