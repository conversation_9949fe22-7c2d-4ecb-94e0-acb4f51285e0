; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 3
; RUN: opt -S -passes=slp-vectorizer -slp-threshold=-9999 -mtriple=x86_64-unknown-linux-gnu < %s | FileCheck %s
; RUN: opt -S -passes=slp-vectorizer -slp-threshold=-9999 -mtriple=x86_64-unknown-linux-gnu\
; RUN: -slp-skip-early-profitability-check < %s | FileCheck %s --check-prefixes=FORCED

define void @foo() {
; FORCED-LABEL: define void @foo() {
; FORCED-NEXT:  bb:
; FORCED-NEXT:    [[TMP0:%.*]] = insertelement <2 x i32> <i32 poison, i32 0>, i32 0, i32 0
; FORCED-NEXT:    br label [[BB1:%.*]]
; FORCED:       bb1:
; FORCED-NEXT:    [[TMP1:%.*]] = phi <2 x i32> [ zeroinitializer, [[BB:%.*]] ], [ [[TMP6:%.*]], [[BB4:%.*]] ]
; FORCED-NEXT:    [[TMP2:%.*]] = shl <2 x i32> [[TMP1]], [[TMP0]]
; FORCED-NEXT:    [[TMP3:%.*]] = or <2 x i32> [[TMP1]], [[TMP0]]
; FORCED-NEXT:    [[TMP4:%.*]] = shufflevector <2 x i32> [[TMP2]], <2 x i32> [[TMP3]], <2 x i32> <i32 0, i32 3>
; FORCED-NEXT:    [[TMP5:%.*]] = shufflevector <2 x i32> [[TMP4]], <2 x i32> [[TMP1]], <2 x i32> <i32 0, i32 3>
; FORCED-NEXT:    [[TMP6]] = or <2 x i32> [[TMP5]], zeroinitializer
; FORCED-NEXT:    [[TMP7:%.*]] = extractelement <2 x i32> [[TMP6]], i32 0
; FORCED-NEXT:    [[CALL:%.*]] = call i64 null(i32 [[TMP7]])
; FORCED-NEXT:    br label [[BB4]]
; FORCED:       bb4:
; FORCED-NEXT:    br i1 false, label [[BB5:%.*]], label [[BB1]]
; FORCED:       bb5:
; FORCED-NEXT:    [[TMP8:%.*]] = phi <2 x i32> [ [[TMP4]], [[BB4]] ]
; FORCED-NEXT:    ret void
;
; CHECK-LABEL: define void @foo() {
; CHECK-NEXT:  bb:
; CHECK-NEXT:    br label [[BB1:%.*]]
; CHECK:       bb1:
; CHECK-NEXT:    [[TMP1:%.*]] = phi <2 x i32> [ zeroinitializer, [[BB:%.*]] ], [ [[TMP6:%.*]], [[BB4:%.*]] ]
; CHECK-NEXT:    [[TMP2:%.*]] = extractelement <2 x i32> [[TMP1]], i32 0
; CHECK-NEXT:    [[SHL:%.*]] = shl i32 [[TMP2]], 0
; CHECK-NEXT:    [[TMP5:%.*]] = insertelement <2 x i32> [[TMP1]], i32 [[SHL]], i32 0
; CHECK-NEXT:    [[TMP6]] = or <2 x i32> [[TMP5]], zeroinitializer
; CHECK-NEXT:    [[TMP7:%.*]] = extractelement <2 x i32> [[TMP6]], i32 0
; CHECK-NEXT:    [[CALL:%.*]] = call i64 null(i32 [[TMP7]])
; CHECK-NEXT:    br label [[BB4]]
; CHECK:       bb4:
; CHECK-NEXT:    [[TMP8:%.*]] = extractelement <2 x i32> [[TMP6]], i32 1
; CHECK-NEXT:    br i1 false, label [[BB5:%.*]], label [[BB1]]
; CHECK:       bb5:
; CHECK-NEXT:    [[PHI6:%.*]] = phi i32 [ [[SHL]], [[BB4]] ]
; CHECK-NEXT:    [[PHI7:%.*]] = phi i32 [ [[TMP8]], [[BB4]] ]
; CHECK-NEXT:    ret void
;
bb:
  br label %bb1

bb1:
  %phi = phi i32 [ 0, %bb ], [ %or, %bb4 ]
  %phi2 = phi i32 [ 0, %bb ], [ %or3, %bb4 ]
  %and = and i32 0, 0
  %shl = shl i32 %phi, %and
  %or = or i32 %shl, 0
  %call = call i64 null(i32 %or)
  %or3 = or i32 %phi2, 0
  br label %bb4

bb4:
  br i1 false, label %bb5, label %bb1

bb5:
  %phi6 = phi i32 [ %shl, %bb4 ]
  %phi7 = phi i32 [ %or3, %bb4 ]
  ret void
}
