; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S --passes=slp-vectorizer -slp-threshold=-99999 -mtriple=x86_64-unknown-linux-gnu < %s | FileCheck %s

define void @test() {
; CHECK-LABEL: define void @test() {
; CHECK-NEXT:  [[BB:.*]]:
; CHECK-NEXT:    br label %[[BB6:.*]]
; CHECK:       [[BB1:.*]]:
; CHECK-NEXT:    br label %[[BB2:.*]]
; CHECK:       [[BB2]]:
; CHECK-NEXT:    [[TMP0:%.*]] = phi <4 x i32> [ poison, %[[BB1]] ], [ [[TMP5:%.*]], %[[BB6]] ]
; CHECK-NEXT:    ret void
; CHECK:       [[BB6]]:
; CHECK-NEXT:    [[TMP1:%.*]] = phi <2 x i32> [ zeroinitializer, %[[BB]] ], [ [[TMP8:%.*]], %[[BB6]] ]
; CHECK-NEXT:    [[TMP6:%.*]] = shufflevector <2 x i32> [[TMP1]], <2 x i32> poison, <4 x i32> <i32 0, i32 1, i32 poison, i32 poison>
; CHECK-NEXT:    [[TMP2:%.*]] = shufflevector <4 x i32> <i32 0, i32 0, i32 poison, i32 poison>, <4 x i32> [[TMP6]], <4 x i32> <i32 0, i32 1, i32 5, i32 4>
; CHECK-NEXT:    [[TMP3:%.*]] = ashr <4 x i32> zeroinitializer, [[TMP2]]
; CHECK-NEXT:    [[TMP4:%.*]] = mul <4 x i32> zeroinitializer, [[TMP2]]
; CHECK-NEXT:    [[TMP5]] = shufflevector <4 x i32> [[TMP3]], <4 x i32> [[TMP4]], <4 x i32> <i32 0, i32 5, i32 6, i32 7>
; CHECK-NEXT:    [[TMP7:%.*]] = shufflevector <2 x i32> [[TMP1]], <2 x i32> <i32 0, i32 poison>, <2 x i32> <i32 2, i32 1>
; CHECK-NEXT:    [[TMP8]] = mul <2 x i32> zeroinitializer, [[TMP7]]
; CHECK-NEXT:    br i1 false, label %[[BB2]], label %[[BB6]]
;
bb:
  br label %bb6

bb1:
  %ashr = ashr i32 0, 0
  br label %bb2

bb2:
  %phi = phi i32 [ %ashr, %bb1 ], [ %ashr9, %bb6 ]
  %phi3 = phi i32 [ 0, %bb1 ], [ %mul10, %bb6 ]
  %phi4 = phi i32 [ 0, %bb1 ], [ %mul11, %bb6 ]
  %phi5 = phi i32 [ 0, %bb1 ], [ %mul, %bb6 ]
  ret void

bb6:
  %phi7 = phi i32 [ 0, %bb ], [ %mul11, %bb6 ]
  %phi8 = phi i32 [ 0, %bb ], [ %mul10, %bb6 ]
  %mul = mul i32 0, %phi8
  %ashr9 = ashr i32 0, 0
  %mul10 = mul i32 0, 0
  %mul11 = mul i32 %phi7, 0
  br i1 false, label %bb2, label %bb6
}
