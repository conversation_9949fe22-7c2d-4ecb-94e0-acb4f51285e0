; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown-linux-gnu -mcpu=cascadelake < %s | FileCheck %s

define void @test(ptr %0, i32 %add651) {
; CHECK-LABEL: define void @test(
; CHECK-SAME: ptr [[TMP0:%.*]], i32 [[ADD651:%.*]]) #[[ATTR0:[0-9]+]] {
; CHECK-NEXT:  [[ENTRY:.*:]]
; CHECK-NEXT:    [[PREDPEL11:%.*]] = alloca [0 x [0 x [25 x i32]]], i32 0, align 16
; CHECK-NEXT:    [[ARRAYIDX469_6:%.*]] = getelementptr i8, ptr [[PREDPEL11]], i64 28
; CHECK-NEXT:    [[ARRAYIDX469_7:%.*]] = getelementptr i8, ptr [[PREDPEL11]], i64 32
; CHECK-NEXT:    [[TMP1:%.*]] = getelementptr i8, ptr [[PREDPEL11]], i64 36
; CHECK-NEXT:    [[TMP2:%.*]] = load i32, ptr [[ARRAYIDX469_7]], align 16
; CHECK-NEXT:    [[TMP3:%.*]] = load <2 x i32>, ptr [[ARRAYIDX469_6]], align 4
; CHECK-NEXT:    [[CONV470_7:%.*]] = trunc i32 [[TMP2]] to i16
; CHECK-NEXT:    store i16 [[CONV470_7]], ptr [[TMP0]], align 2
; CHECK-NEXT:    [[TMP4:%.*]] = load ptr, ptr [[TMP0]], align 8
; CHECK-NEXT:    [[ARRAYIDX660:%.*]] = getelementptr i8, ptr [[TMP4]], i64 7800
; CHECK-NEXT:    [[ARRAYIDX689:%.*]] = getelementptr i8, ptr [[TMP4]], i64 7816
; CHECK-NEXT:    [[TMP5:%.*]] = load <2 x i32>, ptr [[TMP1]], align 4
; CHECK-NEXT:    [[TMP6:%.*]] = add <2 x i32> [[TMP3]], splat (i32 1)
; CHECK-NEXT:    [[TMP7:%.*]] = shufflevector <2 x i32> [[TMP3]], <2 x i32> [[TMP5]], <2 x i32> <i32 1, i32 2>
; CHECK-NEXT:    [[TMP8:%.*]] = add <2 x i32> [[TMP6]], [[TMP7]]
; CHECK-NEXT:    [[TMP9:%.*]] = shufflevector <2 x i32> [[TMP5]], <2 x i32> <i32 1, i32 poison>, <2 x i32> <i32 2, i32 1>
; CHECK-NEXT:    [[TMP10:%.*]] = add <2 x i32> [[TMP8]], [[TMP9]]
; CHECK-NEXT:    [[TMP11:%.*]] = insertelement <4 x i32> poison, i32 [[ADD651]], i32 0
; CHECK-NEXT:    [[TMP13:%.*]] = insertelement <4 x i32> [[TMP11]], i32 [[TMP2]], i32 1
; CHECK-NEXT:    [[TMP14:%.*]] = call <4 x i32> @llvm.vector.insert.v4i32.v2i32(<4 x i32> [[TMP13]], <2 x i32> [[TMP10]], i64 2)
; CHECK-NEXT:    [[TMP15:%.*]] = lshr <4 x i32> [[TMP14]], splat (i32 1)
; CHECK-NEXT:    [[SHR685:%.*]] = lshr i32 [[TMP2]], 1
; CHECK-NEXT:    [[TMP16:%.*]] = trunc <4 x i32> [[TMP15]] to <4 x i16>
; CHECK-NEXT:    [[CONV686:%.*]] = trunc i32 [[SHR685]] to i16
; CHECK-NEXT:    store i16 [[CONV686]], ptr [[ARRAYIDX689]], align 8
; CHECK-NEXT:    [[ARRAYIDX727:%.*]] = getelementptr i8, ptr [[TMP4]], i64 7818
; CHECK-NEXT:    [[TMP17:%.*]] = extractelement <4 x i16> [[TMP16]], i32 2
; CHECK-NEXT:    store i16 [[TMP17]], ptr [[ARRAYIDX727]], align 2
; CHECK-NEXT:    [[TMP18:%.*]] = extractelement <4 x i16> [[TMP16]], i32 3
; CHECK-NEXT:    store i16 [[TMP18]], ptr [[TMP4]], align 8
; CHECK-NEXT:    store <4 x i16> [[TMP16]], ptr [[ARRAYIDX660]], align 8
; CHECK-NEXT:    ret void
;
entry:
  %PredPel11 = alloca [0 x [0 x [25 x i32]]], i32 0, align 16
  %arrayidx469.6 = getelementptr i8, ptr %PredPel11, i64 28
  %1 = load i32, ptr %arrayidx469.6, align 4
  %arrayidx469.7 = getelementptr i8, ptr %PredPel11, i64 32
  %2 = load i32, ptr %arrayidx469.7, align 16
  %conv470.7 = trunc i32 %2 to i16
  store i16 %conv470.7, ptr %0, align 2
  %3 = getelementptr i8, ptr %PredPel11, i64 36
  %4 = getelementptr i8, ptr %PredPel11, i64 40
  %5 = load ptr, ptr %0, align 8
  %add6511 = add i32 %1, 1
  %shr656 = lshr i32 %add651, 1
  %conv657 = trunc i32 %shr656 to i16
  %arrayidx660 = getelementptr i8, ptr %5, i64 7800
  store i16 %conv657, ptr %arrayidx660, align 8
  %shr685 = lshr i32 %2, 1
  %conv686 = trunc i32 %shr685 to i16
  %arrayidx689 = getelementptr i8, ptr %5, i64 7816
  store i16 %conv686, ptr %arrayidx689, align 8
  %arrayidx694 = getelementptr i8, ptr %5, i64 7802
  store i16 %conv686, ptr %arrayidx694, align 2
  %6 = load i32, ptr %3, align 4
  %add716 = add i32 %add6511, %2
  %add717 = add i32 %add716, 1
  %shr718 = lshr i32 %add717, 1
  %conv719 = trunc i32 %shr718 to i16
  %arrayidx727 = getelementptr i8, ptr %5, i64 7818
  store i16 %conv719, ptr %arrayidx727, align 2
  %arrayidx731 = getelementptr i8, ptr %5, i64 7804
  store i16 %conv719, ptr %arrayidx731, align 4
  %7 = load i32, ptr %4, align 8
  %add750 = add i32 %2, 1
  %add753 = add i32 %add750, %6
  %add754 = add i32 %add753, %7
  %shr755 = lshr i32 %add754, 1
  %conv756 = trunc i32 %shr755 to i16
  store i16 %conv756, ptr %5, align 8
  %arrayidx772 = getelementptr i8, ptr %5, i64 7806
  store i16 %conv756, ptr %arrayidx772, align 2
  ret void
}
