; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown-linux-gnu < %s | FileCheck %s

define i8 @test() {
; CHECK-LABEL: define i8 @test() {
; CHECK-NEXT:  [[ENTRY:.*:]]
; CHECK-NEXT:    [[TMP0:%.*]] = trunc i32 0 to i8
; CHECK-NEXT:    [[TMP1:%.*]] = trunc i32 0 to i8
; CHECK-NEXT:    [[TMP2:%.*]] = trunc i32 0 to i8
; CHECK-NEXT:    [[TMP3:%.*]] = trunc i32 0 to i8
; CHECK-NEXT:    [[TMP4:%.*]] = call i8 @llvm.vector.reduce.or.v8i8(<8 x i8> zeroinitializer)
; CHECK-NEXT:    [[OP_RDX:%.*]] = or i8 [[TMP4]], [[TMP0]]
; CHECK-NEXT:    [[OP_RDX1:%.*]] = or i8 [[OP_RDX]], [[TMP2]]
; CHECK-NEXT:    [[OP_RDX2:%.*]] = or i8 [[OP_RDX1]], [[TMP0]]
; CHECK-NEXT:    [[OP_RDX3:%.*]] = or i8 [[OP_RDX2]], [[TMP1]]
; CHECK-NEXT:    [[OP_RDX4:%.*]] = or i8 [[OP_RDX3]], [[TMP3]]
; CHECK-NEXT:    ret i8 [[OP_RDX4]]
;
entry:
  %0 = trunc i32 0 to i8
  %1 = add i8 %0, 0
  %2 = add i8 %0, 0
  %3 = add i8 %0, 0
  %4 = add i8 %0, 0
  %5 = trunc i32 0 to i8
  %6 = or i8 %5, %0
  %7 = or i8 %6, %2
  %8 = or i8 %7, %3
  %9 = or i8 %8, %0
  %10 = or i8 %9, %4
  %conv4 = or i8 %10, %1
  %11 = trunc i32 0 to i8
  %12 = add i8 %11, 0
  %conv7 = or i8 %conv4, %12
  %13 = add i8 %11, 0
  %14 = add i8 %11, 0
  %15 = add i8 %11, 0
  %16 = trunc i32 0 to i8
  %17 = or i8 %13, %16
  %18 = or i8 %17, %14
  %19 = or i8 %18, %11
  %20 = or i8 %19, %15
  %conv5 = or i8 %20, %conv7
  %21 = trunc i32 0 to i8
  %conv6 = or i8 %21, %conv5
  ret i8 %conv6
}
