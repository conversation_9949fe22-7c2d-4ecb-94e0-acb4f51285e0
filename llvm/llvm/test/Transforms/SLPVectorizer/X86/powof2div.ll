; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt < %s -passes=slp-vectorizer -S -mtriple=x86_64-unknown-linux-gnu -mattr=+sse2 | FileCheck %s --check-prefixes=CHECK,SSE
; RUN: opt < %s -passes=slp-vectorizer -S -mtriple=x86_64-unknown-linux-gnu -mattr=+avx  | FileCheck %s --check-prefixes=CHECK,AVX
; RUN: opt < %s -passes=slp-vectorizer -S -mtriple=x86_64-unknown-linux-gnu -mattr=+avx2 | FileCheck %s --check-prefixes=CHECK,AVX

define void @powof2div_uniform(ptr noalias nocapture %a, ptr noalias nocapture readonly %b, ptr noalias nocapture readonly %c){
; CHECK-LABEL: @powof2div_uniform(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP1:%.*]] = load <4 x i32>, ptr [[B:%.*]], align 4
; CHECK-NEXT:    [[TMP3:%.*]] = load <4 x i32>, ptr [[C:%.*]], align 4
; CHECK-NEXT:    [[TMP4:%.*]] = add nsw <4 x i32> [[TMP3]], [[TMP1]]
; CHECK-NEXT:    [[TMP5:%.*]] = sdiv <4 x i32> [[TMP4]], splat (i32 2)
; CHECK-NEXT:    store <4 x i32> [[TMP5]], ptr [[A:%.*]], align 4
; CHECK-NEXT:    ret void
;
entry:
  %0 = load i32, ptr %b, align 4
  %1 = load i32, ptr %c, align 4
  %add = add nsw i32 %1, %0
  %div = sdiv i32 %add, 2
  store i32 %div, ptr %a, align 4
  %arrayidx3 = getelementptr inbounds i32, ptr %b, i64 1
  %2 = load i32, ptr %arrayidx3, align 4
  %arrayidx4 = getelementptr inbounds i32, ptr %c, i64 1
  %3 = load i32, ptr %arrayidx4, align 4
  %add5 = add nsw i32 %3, %2
  %div6 = sdiv i32 %add5, 2
  %arrayidx7 = getelementptr inbounds i32, ptr %a, i64 1
  store i32 %div6, ptr %arrayidx7, align 4
  %arrayidx8 = getelementptr inbounds i32, ptr %b, i64 2
  %4 = load i32, ptr %arrayidx8, align 4
  %arrayidx9 = getelementptr inbounds i32, ptr %c, i64 2
  %5 = load i32, ptr %arrayidx9, align 4
  %add10 = add nsw i32 %5, %4
  %div11 = sdiv i32 %add10, 2
  %arrayidx12 = getelementptr inbounds i32, ptr %a, i64 2
  store i32 %div11, ptr %arrayidx12, align 4
  %arrayidx13 = getelementptr inbounds i32, ptr %b, i64 3
  %6 = load i32, ptr %arrayidx13, align 4
  %arrayidx14 = getelementptr inbounds i32, ptr %c, i64 3
  %7 = load i32, ptr %arrayidx14, align 4
  %add15 = add nsw i32 %7, %6
  %div16 = sdiv i32 %add15, 2
  %arrayidx17 = getelementptr inbounds i32, ptr %a, i64 3
  store i32 %div16, ptr %arrayidx17, align 4
  ret void
}

define void @powof2div_nonuniform(ptr noalias nocapture %a, ptr noalias nocapture readonly %b, ptr noalias nocapture readonly %c){
; SSE-LABEL: @powof2div_nonuniform(
; SSE-NEXT:  entry:
; SSE-NEXT:    [[TMP0:%.*]] = load i32, ptr [[B:%.*]], align 4
; SSE-NEXT:    [[TMP1:%.*]] = load i32, ptr [[C:%.*]], align 4
; SSE-NEXT:    [[ADD:%.*]] = add nsw i32 [[TMP1]], [[TMP0]]
; SSE-NEXT:    [[DIV:%.*]] = sdiv i32 [[ADD]], 2
; SSE-NEXT:    store i32 [[DIV]], ptr [[A:%.*]], align 4
; SSE-NEXT:    [[ARRAYIDX3:%.*]] = getelementptr inbounds i32, ptr [[B]], i64 1
; SSE-NEXT:    [[TMP2:%.*]] = load i32, ptr [[ARRAYIDX3]], align 4
; SSE-NEXT:    [[ARRAYIDX4:%.*]] = getelementptr inbounds i32, ptr [[C]], i64 1
; SSE-NEXT:    [[TMP3:%.*]] = load i32, ptr [[ARRAYIDX4]], align 4
; SSE-NEXT:    [[ADD5:%.*]] = add nsw i32 [[TMP3]], [[TMP2]]
; SSE-NEXT:    [[DIV6:%.*]] = sdiv i32 [[ADD5]], 4
; SSE-NEXT:    [[ARRAYIDX7:%.*]] = getelementptr inbounds i32, ptr [[A]], i64 1
; SSE-NEXT:    store i32 [[DIV6]], ptr [[ARRAYIDX7]], align 4
; SSE-NEXT:    [[ARRAYIDX8:%.*]] = getelementptr inbounds i32, ptr [[B]], i64 2
; SSE-NEXT:    [[TMP4:%.*]] = load i32, ptr [[ARRAYIDX8]], align 4
; SSE-NEXT:    [[ARRAYIDX9:%.*]] = getelementptr inbounds i32, ptr [[C]], i64 2
; SSE-NEXT:    [[TMP5:%.*]] = load i32, ptr [[ARRAYIDX9]], align 4
; SSE-NEXT:    [[ADD10:%.*]] = add nsw i32 [[TMP5]], [[TMP4]]
; SSE-NEXT:    [[DIV11:%.*]] = sdiv i32 [[ADD10]], 8
; SSE-NEXT:    [[ARRAYIDX12:%.*]] = getelementptr inbounds i32, ptr [[A]], i64 2
; SSE-NEXT:    store i32 [[DIV11]], ptr [[ARRAYIDX12]], align 4
; SSE-NEXT:    [[ARRAYIDX13:%.*]] = getelementptr inbounds i32, ptr [[B]], i64 3
; SSE-NEXT:    [[TMP6:%.*]] = load i32, ptr [[ARRAYIDX13]], align 4
; SSE-NEXT:    [[ARRAYIDX14:%.*]] = getelementptr inbounds i32, ptr [[C]], i64 3
; SSE-NEXT:    [[TMP7:%.*]] = load i32, ptr [[ARRAYIDX14]], align 4
; SSE-NEXT:    [[ADD15:%.*]] = add nsw i32 [[TMP7]], [[TMP6]]
; SSE-NEXT:    [[DIV16:%.*]] = sdiv i32 [[ADD15]], 16
; SSE-NEXT:    [[ARRAYIDX17:%.*]] = getelementptr inbounds i32, ptr [[A]], i64 3
; SSE-NEXT:    store i32 [[DIV16]], ptr [[ARRAYIDX17]], align 4
; SSE-NEXT:    ret void
;
; AVX-LABEL: @powof2div_nonuniform(
; AVX-NEXT:  entry:
; AVX-NEXT:    [[TMP1:%.*]] = load <4 x i32>, ptr [[B:%.*]], align 4
; AVX-NEXT:    [[TMP3:%.*]] = load <4 x i32>, ptr [[C:%.*]], align 4
; AVX-NEXT:    [[TMP4:%.*]] = add nsw <4 x i32> [[TMP3]], [[TMP1]]
; AVX-NEXT:    [[TMP5:%.*]] = sdiv <4 x i32> [[TMP4]], <i32 2, i32 4, i32 8, i32 16>
; AVX-NEXT:    store <4 x i32> [[TMP5]], ptr [[A:%.*]], align 4
; AVX-NEXT:    ret void
;
entry:
  %0 = load i32, ptr %b, align 4
  %1 = load i32, ptr %c, align 4
  %add = add nsw i32 %1, %0
  %div = sdiv i32 %add, 2
  store i32 %div, ptr %a, align 4
  %arrayidx3 = getelementptr inbounds i32, ptr %b, i64 1
  %2 = load i32, ptr %arrayidx3, align 4
  %arrayidx4 = getelementptr inbounds i32, ptr %c, i64 1
  %3 = load i32, ptr %arrayidx4, align 4
  %add5 = add nsw i32 %3, %2
  %div6 = sdiv i32 %add5, 4
  %arrayidx7 = getelementptr inbounds i32, ptr %a, i64 1
  store i32 %div6, ptr %arrayidx7, align 4
  %arrayidx8 = getelementptr inbounds i32, ptr %b, i64 2
  %4 = load i32, ptr %arrayidx8, align 4
  %arrayidx9 = getelementptr inbounds i32, ptr %c, i64 2
  %5 = load i32, ptr %arrayidx9, align 4
  %add10 = add nsw i32 %5, %4
  %div11 = sdiv i32 %add10, 8
  %arrayidx12 = getelementptr inbounds i32, ptr %a, i64 2
  store i32 %div11, ptr %arrayidx12, align 4
  %arrayidx13 = getelementptr inbounds i32, ptr %b, i64 3
  %6 = load i32, ptr %arrayidx13, align 4
  %arrayidx14 = getelementptr inbounds i32, ptr %c, i64 3
  %7 = load i32, ptr %arrayidx14, align 4
  %add15 = add nsw i32 %7, %6
  %div16 = sdiv i32 %add15, 16
  %arrayidx17 = getelementptr inbounds i32, ptr %a, i64 3
  store i32 %div16, ptr %arrayidx17, align 4
  ret void
}

