; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt < %s -S -mtriple=x86_64-unknown -mattr=+avx512vl -passes=slp-vectorizer -slp-threshold=-3 | FileCheck %s


define i32 @test(ptr noalias %in, ptr noalias %inn, ptr %out) {
; CHECK-LABEL: @test(
; CHECK-NEXT:    [[TMP7:%.*]] = load <4 x i8>, ptr [[IN:%.*]], align 1
; CHECK-NEXT:    [[TMP11:%.*]] = load <4 x i8>, ptr [[INN:%.*]], align 1
; CHECK-NEXT:    [[TMP8:%.*]] = sext <4 x i8> [[TMP7]] to <4 x i16>
; CHECK-NEXT:    [[TMP12:%.*]] = sext <4 x i8> [[TMP11]] to <4 x i16>
; CHECK-NEXT:    [[TMP13:%.*]] = sub <4 x i16> [[TMP12]], [[TMP8]]
; CHECK-NEXT:    [[TMP15:%.*]] = call <4 x i16> @llvm.abs.v4i16(<4 x i16> [[TMP13]], i1 false)
; CHECK-NEXT:    store <4 x i16> [[TMP15]], ptr [[OUT:%.*]], align 2
; CHECK-NEXT:    ret i32 undef
;
  %load.1 = load i8, ptr %in, align 1
  %gep.1 = getelementptr inbounds i8, ptr %in, i64 1
  %load.2 = load i8, ptr %gep.1, align 1
  %gep.2 = getelementptr inbounds i8, ptr %in, i64 2
  %load.3 = load i8, ptr %gep.2, align 1
  %gep.3 = getelementptr inbounds i8, ptr %in, i64 3
  %load.4 = load i8, ptr %gep.3, align 1
  %load.5 = load i8, ptr %inn, align 1
  %gep.4 = getelementptr inbounds i8, ptr %inn, i64 1
  %load.6 = load i8, ptr %gep.4, align 1
  %gep.5 = getelementptr inbounds i8, ptr %inn, i64 2
  %load.7 = load i8, ptr %gep.5, align 1
  %gep.6 = getelementptr inbounds i8, ptr %inn, i64 3
  %load.8 = load i8, ptr %gep.6, align 1
  %sext1 = sext i8 %load.1 to i32
  %sext2 = sext i8 %load.2 to i32
  %sext3 = sext i8 %load.3 to i32
  %sext4 = sext i8 %load.4 to i32
  %sext5 = sext i8 %load.5 to i32
  %sext6 = sext i8 %load.6 to i32
  %sext7 = sext i8 %load.7 to i32
  %sext8 = sext i8 %load.8 to i32
  %sub1 = sub i32 %sext1, %sext5
  %sub2 = sub i32 %sext2, %sext6
  %sub3 = sub i32 %sext7, %sext3
  %sub4 = sub i32 %sext8, %sext4
  %call1 = call i32 @llvm.abs(i32 %sub1, i1 true)
  %call2 = call i32 @llvm.abs(i32 %sub2, i1 true)
  %call3 = call i32 @llvm.abs(i32 %sub3, i1 true)
  %call4 = call i32 @llvm.abs(i32 %sub4, i1 true)
  %t1 = trunc i32 %call1 to i16
  %t2 = trunc i32 %call2 to i16
  %t3 = trunc i32 %call3 to i16
  %t4 = trunc i32 %call4 to i16
  %gep.8 = getelementptr inbounds i16, ptr %out, i64 1
  %gep.9 = getelementptr inbounds i16, ptr %out, i64 2
  %gep.10 = getelementptr inbounds i16, ptr %out, i64 3
  store i16 %t1, ptr %out, align 2
  store i16 %t2, ptr %gep.8, align 2
  store i16 %t3, ptr %gep.9, align 2
  store i16 %t4, ptr %gep.10, align 2

  ret i32 undef
}
