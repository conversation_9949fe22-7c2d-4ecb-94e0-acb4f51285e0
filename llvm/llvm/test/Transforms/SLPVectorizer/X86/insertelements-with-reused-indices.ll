; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -S -mtriple=x86_64-unknown-linux-gnu --passes=slp-vectorizer < %s | FileCheck %s

define void @test() {
; CHECK-LABEL: @test(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP0:%.*]] = insertelement <4 x float> undef, float 0.000000e+00, i32 0
; CHECK-NEXT:    [[TMP1:%.*]] = insertelement <4 x float> [[TMP0]], float 0.000000e+00, i32 1
; CHECK-NEXT:    [[TMP2:%.*]] = insertelement <4 x float> [[TMP1]], float 0.000000e+00, i32 0
; CHECK-NEXT:    [[TMP3:%.*]] = insertelement <4 x float> [[TMP2]], float 0.000000e+00, i32 0
; CHECK-NEXT:    [[TMP4:%.*]] = fsub float 0.000000e+00, 0.000000e+00
; CHECK-NEXT:    [[TMP5:%.*]] = insertelement <2 x float> zeroinitializer, float [[TMP4]], i32 0
; CHECK-NEXT:    ret void
;
entry:
  %0 = fsub float 0.000000e+00, 0.000000e+00
  %1 = insertelement <4 x float> undef, float %0, i32 0
  %2 = insertelement <4 x float> %1, float %0, i32 1
  %3 = fsub float 0.000000e+00, 0.000000e+00
  %4 = fmul float 0.000000e+00, %3
  %5 = insertelement <4 x float> %2, float 0.000000e+00, i32 0
  %6 = insertelement <4 x float> %5, float %4, i32 0
  %7 = fsub float 0.000000e+00, %0
  %8 = fsub float %7, %4
  %9 = insertelement <2 x float> zeroinitializer, float %8, i32 0
  ret void
}
