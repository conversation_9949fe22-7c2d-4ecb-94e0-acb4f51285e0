; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
;RUN: opt < %s -passes=slp-vectorizer -S | FileCheck %s

target datalayout = "e-m:e-i64:64-f80:128-n8:16:32:64-S128"
target triple = "x86_64-unknown-linux-gnu"

; This test checks that SLP vectorizer does not fail on vector GEP.
; The GEP has scalar and vector parameters and returns vector of pointers.

; Function Attrs: noreturn readonly uwtable
define void @_Z3fn1v(i32 %x, <16 x ptr>%y) local_unnamed_addr #0 {
; CHECK-LABEL: @_Z3fn1v(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[CONV42_LE:%.*]] = sext i32 [[X:%.*]] to i64
; CHECK-NEXT:    [[CONV36109_LE:%.*]] = zext i32 2 to i64
; CHECK-NEXT:    [[VECTORGEP:%.*]] = getelementptr i32, <16 x ptr> [[Y:%.*]], i64 [[CONV36109_LE]]
; CHECK-NEXT:    [[VECTORGEP208:%.*]] = getelementptr i32, <16 x ptr> [[Y]], i64 [[CONV42_LE]]
; CHECK-NEXT:    unreachable
;

entry:
  %conv42.le = sext i32 %x to i64
  %conv36109.le = zext i32 2 to i64
  %VectorGep = getelementptr i32, <16 x ptr> %y, i64 %conv36109.le
  %VectorGep208 = getelementptr i32, <16 x ptr> %y, i64 %conv42.le
  unreachable
}

attributes #0 = { noreturn readonly uwtable "disable-tail-calls"="false" "less-precise-fpmad"="false" "frame-pointer"="none" "no-infs-fp-math"="false" "no-jump-tables"="false" "no-nans-fp-math"="false" "stack-protector-buffer-size"="8" "target-cpu"="broadwell" "target-features"="+adx,+aes,+avx,+avx2,+avx512cd,+avx512f,+bmi,+bmi2,+cx16,+f16c,+fma,+fsgsbase,+fxsr,+lzcnt,+mmx,+movbe,+pclmul,+popcnt,+rdrnd,+rdseed,+rtm,+sse,+sse2,+sse3,+sse4.1,+sse4.2,+ssse3,+x87,+xsave,+xsaveopt" "unsafe-fp-math"="false" "use-soft-float"="false" }

