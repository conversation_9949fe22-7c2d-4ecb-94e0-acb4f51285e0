; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -passes=slp-vectorizer -S -o - -mtriple=x86_64-unknown-linux -mcpu=haswell -slp-threshold=-3 < %s | FileCheck %s
@e = dso_local local_unnamed_addr global i32 0, align 4
@f = dso_local local_unnamed_addr global i32 0, align 4

; Function Attrs: nofree norecurse nounwind uwtable
define dso_local i32 @g() local_unnamed_addr {
; CHECK-LABEL: @g(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP0:%.*]] = load i32, ptr @e, align 4
; CHECK-NEXT:    [[TOBOOL_NOT19:%.*]] = icmp eq i32 [[TMP0]], 0
; CHECK-NEXT:    br i1 [[TOBOOL_NOT19]], label [[WHILE_END:%.*]], label [[WHILE_BODY:%.*]]
; CHECK:       while.body:
; CHECK-NEXT:    [[A_020:%.*]] = phi ptr [ [[A_020_BE:%.*]], [[WHILE_BODY_BACKEDGE:%.*]] ], [ undef, [[ENTRY:%.*]] ]
; CHECK-NEXT:    [[TMP1:%.*]] = phi <2 x ptr> [ [[TMP14:%.*]], [[WHILE_BODY_BACKEDGE]] ], [ undef, [[ENTRY]] ]
; CHECK-NEXT:    [[TMP2:%.*]] = extractelement <2 x ptr> [[TMP1]], i32 1
; CHECK-NEXT:    [[INCDEC_PTR:%.*]] = getelementptr inbounds i32, ptr [[TMP2]], i64 1
; CHECK-NEXT:    [[TMP3:%.*]] = ptrtoint ptr [[TMP2]] to i64
; CHECK-NEXT:    [[TMP4:%.*]] = trunc i64 [[TMP3]] to i32
; CHECK-NEXT:    [[INCDEC_PTR1:%.*]] = getelementptr inbounds i32, ptr [[A_020]], i64 1
; CHECK-NEXT:    [[TMP5:%.*]] = getelementptr i32, <2 x ptr> [[TMP1]], <2 x i64> splat (i64 1)
; CHECK-NEXT:    switch i32 [[TMP4]], label [[WHILE_BODY_BACKEDGE]] [
; CHECK-NEXT:      i32 2, label [[SW_BB:%.*]]
; CHECK-NEXT:      i32 4, label [[SW_BB6:%.*]]
; CHECK-NEXT:    ]
; CHECK:       sw.bb:
; CHECK-NEXT:    [[TMP6:%.*]] = extractelement <2 x ptr> [[TMP5]], i32 0
; CHECK-NEXT:    [[TMP7:%.*]] = ptrtoint ptr [[TMP6]] to i64
; CHECK-NEXT:    [[TMP8:%.*]] = trunc i64 [[TMP7]] to i32
; CHECK-NEXT:    [[INCDEC_PTR4:%.*]] = getelementptr inbounds i32, ptr [[A_020]], i64 2
; CHECK-NEXT:    store i32 [[TMP8]], ptr [[INCDEC_PTR1]], align 4
; CHECK-NEXT:    [[TMP9:%.*]] = getelementptr i32, <2 x ptr> [[TMP1]], <2 x i64> splat (i64 2)
; CHECK-NEXT:    br label [[WHILE_BODY_BACKEDGE]]
; CHECK:       sw.bb6:
; CHECK-NEXT:    [[INCDEC_PTR7:%.*]] = getelementptr inbounds i32, ptr [[A_020]], i64 2
; CHECK-NEXT:    [[TMP10:%.*]] = ptrtoint ptr [[INCDEC_PTR]] to i64
; CHECK-NEXT:    [[TMP11:%.*]] = trunc i64 [[TMP10]] to i32
; CHECK-NEXT:    [[TMP12:%.*]] = getelementptr i32, <2 x ptr> [[TMP1]], <2 x i64> splat (i64 2)
; CHECK-NEXT:    [[TMP13:%.*]] = extractelement <2 x ptr> [[TMP5]], i32 0
; CHECK-NEXT:    store i32 [[TMP11]], ptr [[TMP13]], align 4
; CHECK-NEXT:    br label [[WHILE_BODY_BACKEDGE]]
; CHECK:       while.body.backedge:
; CHECK-NEXT:    [[A_020_BE]] = phi ptr [ [[INCDEC_PTR1]], [[WHILE_BODY]] ], [ [[INCDEC_PTR7]], [[SW_BB6]] ], [ [[INCDEC_PTR4]], [[SW_BB]] ]
; CHECK-NEXT:    [[TMP14]] = phi <2 x ptr> [ [[TMP5]], [[WHILE_BODY]] ], [ [[TMP12]], [[SW_BB6]] ], [ [[TMP9]], [[SW_BB]] ]
; CHECK-NEXT:    br label [[WHILE_BODY]]
; CHECK:       while.end:
; CHECK-NEXT:    ret i32 undef
;
entry:
  %0 = load i32, ptr @e, align 4
  %tobool.not19 = icmp eq i32 %0, 0
  br i1 %tobool.not19, label %while.end, label %while.body

while.body:                                       ; preds = %entry, %while.body.backedge
  %c.022 = phi ptr [ %c.022.be, %while.body.backedge ], [ undef, %entry ]
  %b.021 = phi ptr [ %b.021.be, %while.body.backedge ], [ undef, %entry ]
  %a.020 = phi ptr [ %a.020.be, %while.body.backedge ], [ undef, %entry ]
  %incdec.ptr = getelementptr inbounds i32, ptr %c.022, i64 1
  %1 = ptrtoint ptr %c.022 to i64
  %2 = trunc i64 %1 to i32
  %incdec.ptr1 = getelementptr inbounds i32, ptr %a.020, i64 1
  %incdec.ptr2 = getelementptr inbounds i32, ptr %b.021, i64 1
  switch i32 %2, label %while.body.backedge [
  i32 2, label %sw.bb
  i32 4, label %sw.bb6
  ]

sw.bb:                                            ; preds = %while.body
  %incdec.ptr3 = getelementptr inbounds i32, ptr %b.021, i64 2
  %3 = ptrtoint ptr %incdec.ptr2 to i64
  %4 = trunc i64 %3 to i32
  %incdec.ptr4 = getelementptr inbounds i32, ptr %a.020, i64 2
  store i32 %4, ptr %incdec.ptr1, align 4
  %incdec.ptr5 = getelementptr inbounds i32, ptr %c.022, i64 2
  br label %while.body.backedge

sw.bb6:                                           ; preds = %while.body
  %incdec.ptr7 = getelementptr inbounds i32, ptr %a.020, i64 2
  %incdec.ptr8 = getelementptr inbounds i32, ptr %c.022, i64 2
  %5 = ptrtoint ptr %incdec.ptr to i64
  %6 = trunc i64 %5 to i32
  %incdec.ptr9 = getelementptr inbounds i32, ptr %b.021, i64 2
  store i32 %6, ptr %incdec.ptr2, align 4
  br label %while.body.backedge

while.body.backedge:                              ; preds = %sw.bb6, %while.body, %sw.bb
  %c.022.be = phi ptr [ %incdec.ptr, %while.body ], [ %incdec.ptr8, %sw.bb6 ], [ %incdec.ptr5, %sw.bb ]
  %b.021.be = phi ptr [ %incdec.ptr2, %while.body ], [ %incdec.ptr9, %sw.bb6 ], [ %incdec.ptr3, %sw.bb ]
  %a.020.be = phi ptr [ %incdec.ptr1, %while.body ], [ %incdec.ptr7, %sw.bb6 ], [ %incdec.ptr4, %sw.bb ]
  br label %while.body

while.end:                                        ; preds = %entry
  ret i32 undef
}

