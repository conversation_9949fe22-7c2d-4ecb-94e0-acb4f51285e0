; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S --passes=slp-vectorizer -mtriple=i386-pc-windows-msvc19.34.0 -mcpu=pentium4 < %s | FileCheck %s

define i32 @test(double %mul321.i) {
; CHECK-LABEL: define i32 @test(
; CHECK-SAME: double [[MUL321_I:%.*]]) #[[ATTR0:[0-9]+]] {
; CHECK-NEXT:  [[ENTRY:.*]]:
; CHECK-NEXT:    [[TMP0:%.*]] = insertelement <2 x double> poison, double [[MUL321_I]], i32 0
; CHECK-NEXT:    br label %[[DO_BODY220_I:.*]]
; CHECK:       [[DO_BODY220_I]]:
; CHECK-NEXT:    [[TMP1:%.*]] = phi <2 x double> [ [[TMP6:%.*]], %[[DO_BODY221_I:.*]] ], [ zeroinitializer, %[[ENTRY]] ]
; CHECK-NEXT:    br label %[[DO_BODY221_I]]
; CHECK:       [[DO_BODY221_I]]:
; CHECK-NEXT:    [[TMP2:%.*]] = fadd <2 x double> [[TMP1]], zeroinitializer
; CHECK-NEXT:    [[TMP3:%.*]] = fmul <2 x double> [[TMP2]], zeroinitializer
; CHECK-NEXT:    [[TMP4:%.*]] = extractelement <2 x double> [[TMP3]], i32 0
; CHECK-NEXT:    [[TMP5:%.*]] = extractelement <2 x double> [[TMP3]], i32 1
; CHECK-NEXT:    [[ADD318_I:%.*]] = fadd double [[TMP4]], [[TMP5]]
; CHECK-NEXT:    [[TMP6]] = insertelement <2 x double> [[TMP0]], double [[ADD318_I]], i32 1
; CHECK-NEXT:    br label %[[DO_BODY220_I]]
;
entry:
  br label %do.body220.i

do.body220.i:
  %c1.2.i = phi double [ %mul321.i, %do.body221.i ], [ 0.000000e+00, %entry ]
  %s1.1.i = phi double [ %add318.i, %do.body221.i ], [ 0.000000e+00, %entry ]
  br label %do.body221.i

do.body221.i:                                     ; preds = %do.body220.i
  %sub311.i1 = fadd double %c1.2.i, 0.000000e+00
  %add315.i = fadd double %s1.1.i, 0.000000e+00
  %mul316.i = fmul double %sub311.i1, 0.000000e+00
  %mul317.i = fmul double %add315.i, 0.000000e+00
  %add318.i = fadd double %mul316.i, %mul317.i
  br label %do.body220.i
}
