; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown-linux-gnu < %s | FileCheck %s

define i16 @test(i16 %v1, i16 %v2) {
; CHECK-LABEL: define i16 @test(
; CHECK-SAME: i16 [[V1:%.*]], i16 [[V2:%.*]]) {
; CHECK-NEXT:  [[ENTRY:.*:]]
; CHECK-NEXT:    [[TMP0:%.*]] = insertelement <4 x i16> <i16 0, i16 0, i16 0, i16 poison>, i16 [[V2]], i32 3
; CHECK-NEXT:    [[TMP1:%.*]] = insertelement <4 x i16> <i16 0, i16 0, i16 0, i16 poison>, i16 [[V1]], i32 3
; CHECK-NEXT:    [[TMP2:%.*]] = or <4 x i16> [[TMP0]], [[TMP1]]
; CHECK-NEXT:    [[TMP3:%.*]] = and <4 x i16> [[TMP0]], [[TMP1]]
; CHECK-NEXT:    [[TMP4:%.*]] = shufflevector <4 x i16> [[TMP2]], <4 x i16> [[TMP3]], <4 x i32> <i32 0, i32 1, i32 2, i32 7>
; CHECK-NEXT:    [[TMP5:%.*]] = shufflevector <4 x i16> [[TMP0]], <4 x i16> poison, <2 x i32> <i32 poison, i32 3>
; CHECK-NEXT:    [[TMP6:%.*]] = insertelement <2 x i16> [[TMP5]], i16 [[V1]], i32 0
; CHECK-NEXT:    [[TMP7:%.*]] = shufflevector <2 x i16> [[TMP6]], <2 x i16> poison, <4 x i32> <i32 0, i32 0, i32 0, i32 1>
; CHECK-NEXT:    [[TMP8:%.*]] = or <4 x i16> [[TMP7]], zeroinitializer
; CHECK-NEXT:    [[TMP9:%.*]] = and <4 x i16> [[TMP4]], zeroinitializer
; CHECK-NEXT:    [[TMP10:%.*]] = and <4 x i16> [[TMP9]], zeroinitializer
; CHECK-NEXT:    [[TMP11:%.*]] = icmp ne <4 x i16> [[TMP10]], zeroinitializer
; CHECK-NEXT:    [[TMP12:%.*]] = or <4 x i1> [[TMP11]], zeroinitializer
; CHECK-NEXT:    [[TMP13:%.*]] = or <4 x i16> [[TMP8]], zeroinitializer
; CHECK-NEXT:    [[TMP14:%.*]] = or <4 x i16> [[TMP13]], zeroinitializer
; CHECK-NEXT:    [[TMP15:%.*]] = or <4 x i16> [[TMP14]], zeroinitializer
; CHECK-NEXT:    [[TMP16:%.*]] = icmp ne <4 x i16> [[TMP15]], zeroinitializer
; CHECK-NEXT:    [[TMP17:%.*]] = or <4 x i1> zeroinitializer, [[TMP16]]
; CHECK-NEXT:    [[TMP18:%.*]] = or <4 x i1> [[TMP12]], [[TMP17]]
; CHECK-NEXT:    [[TMP19:%.*]] = extractelement <4 x i1> [[TMP18]], i32 2
; CHECK-NEXT:    [[TMP20:%.*]] = extractelement <4 x i1> [[TMP18]], i32 3
; CHECK-NEXT:    [[TMP21:%.*]] = or i1 [[TMP20]], [[TMP19]]
; CHECK-NEXT:    [[TMP22:%.*]] = extractelement <4 x i1> [[TMP18]], i32 1
; CHECK-NEXT:    [[TMP23:%.*]] = or i1 false, [[TMP22]]
; CHECK-NEXT:    [[TMP24:%.*]] = freeze <4 x i1> [[TMP18]]
; CHECK-NEXT:    [[TMP25:%.*]] = call i1 @llvm.vector.reduce.or.v4i1(<4 x i1> [[TMP24]])
; CHECK-NEXT:    [[SPEC_SELECT31:%.*]] = select i1 [[TMP25]], i32 0, i32 0
; CHECK-NEXT:    [[TMP26:%.*]] = extractelement <4 x i1> [[TMP18]], i32 0
; CHECK-NEXT:    [[TMP27:%.*]] = or i1 false, [[TMP26]]
; CHECK-NEXT:    store i32 [[SPEC_SELECT31]], ptr null, align 4
; CHECK-NEXT:    ret i16 0
;
entry:
  %0 = and i16 %v2, %v1
  %1 = and i16 %0, 0
  %2 = and i16 %1, 0
  %3 = icmp ne i16 %2, 0
  %.not5.not = or i1 %3, false
  %inc.1.1.i82.i.i = or i16 %v2, 0
  %inc.143.1.i98.i.i = or i16 0, 0
  %4 = or i16 %inc.1.1.i82.i.i, 0
  %5 = or i16 %4, 0
  %6 = or i16 %5, 0
  %7 = icmp ne i16 %6, 0
  %.not7.not = or i1 false, %7
  %8 = or i1 %.not5.not, %.not7.not
  %9 = and i16 0, %inc.143.1.i98.i.i
  %10 = and i16 %9, 0
  %11 = icmp ne i16 %10, 0
  %.not5.not.1 = or i1 %11, false
  %inc.143.i76.i.i.1 = or i16 %v1, 0
  %inc.143.1.i98.i.i.1 = or i16 0, 0
  %12 = or i16 0, %inc.143.i76.i.i.1
  %13 = or i16 %12, 0
  %14 = or i16 %13, 0
  %15 = icmp ne i16 %14, 0
  %.not7.not.1 = or i1 false, %15
  %16 = or i1 %.not5.not.1, %.not7.not.1
  %17 = or i1 %8, %16
  %18 = and i16 0, %inc.143.1.i98.i.i.1
  %19 = and i16 %18, 0
  %20 = icmp ne i16 %19, 0
  %.not5.not.2 = or i1 %20, false
  %inc.143.i76.i.i.2 = or i16 %v1, 0
  %inc.143.1.i98.i.i.2 = or i16 0, 0
  %21 = or i16 0, %inc.143.i76.i.i.2
  %22 = or i16 %21, 0
  %23 = or i16 %22, 0
  %24 = icmp ne i16 %23, 0
  %.not7.not.2 = or i1 false, %24
  %25 = or i1 %.not5.not.2, %.not7.not.2
  %26 = or i1 false, %25
  %27 = and i16 0, %inc.143.1.i98.i.i.2
  %28 = and i16 %27, 0
  %29 = icmp ne i16 %28, 0
  %.not5.not.3 = or i1 %29, false
  %inc.143.i76.i.i.3 = or i16 %v1, 0
  %30 = or i16 0, %inc.143.i76.i.i.3
  %31 = or i16 %30, 0
  %32 = or i16 %31, 0
  %33 = icmp ne i16 %32, 0
  %.not7.not.3 = or i1 false, %33
  %34 = or i1 %.not5.not.3, %.not7.not.3
  %35 = select i1 %34, i1 true, i1 %25
  %36 = select i1 %35, i1 true, i1 %16
  %37 = select i1 %36, i1 true, i1 %8
  %spec.select31 = select i1 %37, i32 0, i32 0
  %38 = or i1 false, %34
  store i32 %spec.select31, ptr null, align 4
  ret i16 0
}
