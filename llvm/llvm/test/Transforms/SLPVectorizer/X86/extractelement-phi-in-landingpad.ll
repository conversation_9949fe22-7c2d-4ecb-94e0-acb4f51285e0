; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S --passes=slp-vectorizer -slp-threshold=-99999 -mtriple=x86_64-unknown-linux-gnu < %s | FileCheck %s

define void @test() personality ptr null {
; CHECK-LABEL: define void @test() personality ptr null {
; CHECK-NEXT:  [[BB:.*]]:
; CHECK-NEXT:    invoke void null()
; CHECK-NEXT:            to label %[[BB65:.*]] unwind label %[[BB4:.*]]
; CHECK:       [[BB2:.*]]:
; CHECK-NEXT:    invoke void null()
; CHECK-NEXT:            to label %[[BB65]] unwind label %[[BB4]]
; CHECK:       [[BB4]]:
; CHECK-NEXT:    [[TMP1:%.*]] = phi i32 [ 0, %[[BB]] ], [ 0, %[[BB2]] ]
; CHECK-NEXT:    [[PHI6:%.*]] = phi i32 [ 0, %[[BB]] ], [ 0, %[[BB2]] ]
; CHECK-NEXT:    [[LANDINGPAD:%.*]] = landingpad { ptr, i32 }
; CHECK-NEXT:            cleanup
; CHECK-NEXT:    call void null(i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]], i32 [[TMP1]])
; CHECK-NEXT:    ret void
; CHECK:       [[BB65]]:
; CHECK-NEXT:    ret void
;
bb:
  invoke void null()
  to label %bb65 unwind label %bb4

bb2:
  invoke void null()
  to label %bb65 unwind label %bb4

bb4:
  %phi5 = phi i32 [ 0, %bb ], [ 0, %bb2 ]
  %phi6 = phi i32 [ 0, %bb ], [ 0, %bb2 ]
  %landingpad = landingpad { ptr, i32 }
  cleanup
  call void null(i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5, i32 %phi5)
  ret void

bb65:
  ret void
}
