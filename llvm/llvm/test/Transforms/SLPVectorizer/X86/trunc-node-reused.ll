; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown-linux-gnu < %s | FileCheck %s

define i16 @test() {
; CHECK-LABEL: define i16 @test() {
; CHECK-NEXT:  [[ENTRY:.*:]]
; CHECK-NEXT:    [[TMP0:%.*]] = call <4 x i1> @llvm.vector.insert.v4i1.v2i1(<4 x i1> <i1 false, i1 false, i1 poison, i1 poison>, <2 x i1> zeroinitializer, i64 2)
; CHECK-NEXT:    [[TMP1:%.*]] = xor <4 x i1> zeroinitializer, [[TMP0]]
; CHECK-NEXT:    [[TMP2:%.*]] = xor <4 x i1> zeroinitializer, [[TMP1]]
; CHECK-NEXT:    [[TMP3:%.*]] = and <4 x i1> [[TMP2]], zeroinitializer
; CHECK-NEXT:    [[TMP4:%.*]] = zext <4 x i1> [[TMP3]] to <4 x i64>
; CHECK-NEXT:    [[TMP5:%.*]] = icmp slt <4 x i64> [[TMP4]], zeroinitializer
; CHECK-NEXT:    [[TMP6:%.*]] = or <4 x i1> zeroinitializer, [[TMP1]]
; CHECK-NEXT:    [[TMP7:%.*]] = select <4 x i1> [[TMP5]], <4 x i1> zeroinitializer, <4 x i1> [[TMP6]]
; CHECK-NEXT:    [[TMP8:%.*]] = sext <4 x i1> [[TMP7]] to <4 x i16>
; CHECK-NEXT:    [[TMP9:%.*]] = call i16 @llvm.vector.reduce.and.v4i16(<4 x i16> [[TMP8]])
; CHECK-NEXT:    ret i16 [[TMP9]]
;
entry:
  %conv73 = xor i64 0, 0
  %and.i = and i64 0, 0
  %xor2.i = or i64 %and.i, 0
  %sub.i = or i64 %xor2.i, 0
  %xor3.i = xor i64 %sub.i, %conv73
  %and4.i = and i64 %xor3.i, 0
  %cmp.i = icmp slt i64 %and4.i, 0
  %0 = trunc i64 %conv73 to i16
  %1 = or i16 0, %0
  %conv73i = xor i64 0, 0
  %andi.i = and i64 0, 0
  %xor2i.i = or i64 %andi.i, 0
  %subi.i = or i64 %xor2i.i, 0
  %xor3i.i = xor i64 %subi.i, %conv73i
  %and4i.i = and i64 %xor3i.i, 0
  %cmpi.i = icmp slt i64 %and4i.i, 0
  %2 = trunc i64 %conv73i to i16
  %3 = or i16 0, %2
  %4 = select i1 %cmpi.i, i16 0, i16 %3
  %5 = select i1 %cmp.i, i16 0, i16 %1
  %6 = zext i32 0 to i64
  %add.ip = or i64 %6, 0
  %orp = or i64 %add.ip, 0
  %conv72p = shl i64 %orp, 0
  %sextp = ashr i64 %conv72p, 0
  %conv73p = xor i64 %sextp, 0
  %and.ip = and i64 0, 0
  %xor2.ip = or i64 %and.ip, 0
  %sub.ip = or i64 %xor2.ip, 0
  %xor3.ip = xor i64 %sub.ip, %conv73p
  %and4.ip = and i64 %xor3.ip, 0
  %cmp.ip = icmp slt i64 %and4.ip, 0
  %7 = trunc i64 %conv73p to i16
  %8 = or i16 0, %7
  %9 = select i1 %cmp.ip, i16 0, i16 %8
  %conv76i = and i16 %4, %5
  %conv76p = and i16 %conv76i, %9
  %10 = zext i32 0 to i64
  %add.ip1 = or i64 %10, 0
  %orp1 = or i64 %add.ip1, 0
  %conv72p1 = shl i64 %orp1, 0
  %sextp1 = ashr i64 %conv72p1, 0
  %conv73p1 = xor i64 %sextp1, 0
  %and.ip1 = and i64 0, 0
  %xor2.ip1 = or i64 %and.ip1, 0
  %sub.ip1 = or i64 %xor2.ip1, 0
  %xor3.ip1 = xor i64 %sub.ip1, %conv73p1
  %and4.ip1 = and i64 %xor3.ip1, 0
  %cmp.ip1 = icmp slt i64 %and4.ip1, 0
  %11 = trunc i64 %conv73p1 to i16
  %12 = or i16 0, %11
  %13 = select i1 %cmp.ip1, i16 0, i16 %12
  %conv76p2 = and i16 %conv76p, %13
  ret i16 %conv76p2
}
