; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 4
; RUN: opt -p slp-vectorizer -S %s | FileCheck %s

target datalayout = "e-m:e-p270:32:32-p271:32:32-p272:64:64-i64:64-i128:128-f80:128-n8:16:32:64-S128"
target triple = "x86_64-unknown-linux-gnu"

define void @test_2_i24_stores(ptr %A) {
; CHECK-LABEL: define void @test_2_i24_stores(
; CHECK-SAME: ptr [[A:%.*]]) {
; CHECK-NEXT:    [[L:%.*]] = load i24, ptr [[A]], align 4
; CHECK-NEXT:    store i24 [[L]], ptr [[A]], align 1
; CHECK-NEXT:    [[GEP:%.*]] = getelementptr i24, ptr [[A]], i64 1
; CHECK-NEXT:    store i24 0, ptr [[GEP]], align 1
; CHECK-NEXT:    ret void
;
  %l = load i24, ptr %A
  store i24 %l, ptr %A, align 1
  %gep = getelementptr i24, ptr %A, i64 1
  store i24 0, ptr %gep, align 1
  ret void
}

define void @test_2_trunc_i24_to_i8(i24 %x, ptr %A) {
; CHECK-LABEL: define void @test_2_trunc_i24_to_i8(
; CHECK-SAME: i24 [[X:%.*]], ptr [[A:%.*]]) {
; CHECK-NEXT:    [[T:%.*]] = trunc i24 [[X]] to i8
; CHECK-NEXT:    store i8 [[T]], ptr [[A]], align 1
; CHECK-NEXT:    [[GEP:%.*]] = getelementptr i8, ptr [[A]], i64 1
; CHECK-NEXT:    store i8 0, ptr [[GEP]], align 1
; CHECK-NEXT:    ret void
;
  %t = trunc i24 %x to i8
  store i8 %t, ptr %A, align 1
  %gep = getelementptr i8, ptr %A, i64 1
  store i8 0, ptr %gep, align 1
  ret void
}

define void @test_4_trunc_i24_to_i8(i24 %x, ptr %A) {
; CHECK-LABEL: define void @test_4_trunc_i24_to_i8(
; CHECK-SAME: i24 [[X:%.*]], ptr [[A:%.*]]) {
; CHECK-NEXT:    [[T:%.*]] = trunc i24 [[X]] to i8
; CHECK-NEXT:    store i8 [[T]], ptr [[A]], align 1
; CHECK-NEXT:    [[GEP_1:%.*]] = getelementptr i8, ptr [[A]], i64 1
; CHECK-NEXT:    store i8 [[T]], ptr [[GEP_1]], align 1
; CHECK-NEXT:    [[GEP_2:%.*]] = getelementptr i8, ptr [[A]], i64 2
; CHECK-NEXT:    store i8 [[T]], ptr [[GEP_2]], align 1
; CHECK-NEXT:    [[GEP_3:%.*]] = getelementptr i8, ptr [[A]], i64 3
; CHECK-NEXT:    store i8 [[T]], ptr [[GEP_3]], align 1
; CHECK-NEXT:    ret void
;
  %t = trunc i24 %x to i8
  store i8 %t, ptr %A, align 1
  %gep.1 = getelementptr i8, ptr %A, i64 1
  store i8 %t, ptr %gep.1, align 1
  %gep.2 = getelementptr i8, ptr %A, i64 2
  store i8 %t, ptr %gep.2, align 1
  %gep.3 = getelementptr i8, ptr %A, i64 3
  store i8 %t, ptr %gep.3, align 1
  ret void
}

define void @test_8_trunc_i24_to_i8(i24 %x, ptr %A) {
; CHECK-LABEL: define void @test_8_trunc_i24_to_i8(
; CHECK-SAME: i24 [[X:%.*]], ptr [[A:%.*]]) {
; CHECK-NEXT:    [[T:%.*]] = trunc i24 [[X]] to i8
; CHECK-NEXT:    [[TMP1:%.*]] = insertelement <8 x i8> poison, i8 [[T]], i32 0
; CHECK-NEXT:    [[TMP2:%.*]] = shufflevector <8 x i8> [[TMP1]], <8 x i8> poison, <8 x i32> zeroinitializer
; CHECK-NEXT:    store <8 x i8> [[TMP2]], ptr [[A]], align 1
; CHECK-NEXT:    ret void
;
  %t = trunc i24 %x to i8
  store i8 %t, ptr %A, align 1
  %gep.1 = getelementptr i8, ptr %A, i64 1
  store i8 %t, ptr %gep.1, align 1
  %gep.2 = getelementptr i8, ptr %A, i64 2
  store i8 %t, ptr %gep.2, align 1
  %gep.3 = getelementptr i8, ptr %A, i64 3
  store i8 %t, ptr %gep.3, align 1
  %gep.4 = getelementptr i8, ptr %A, i64 4
  store i8 %t, ptr %gep.4, align 1
  %gep.5 = getelementptr i8, ptr %A, i64 5
  store i8 %t, ptr %gep.5, align 1
  %gep.6 = getelementptr i8, ptr %A, i64 6
  store i8 %t, ptr %gep.6, align 1
  %gep.7 = getelementptr i8, ptr %A, i64 7
  store i8 %t, ptr %gep.7, align 1
  ret void
}

define void @test_4_trunc_i24_to_i16(i24 %x, ptr %A) {
; CHECK-LABEL: define void @test_4_trunc_i24_to_i16(
; CHECK-SAME: i24 [[X:%.*]], ptr [[A:%.*]]) {
; CHECK-NEXT:    [[T:%.*]] = trunc i24 [[X]] to i16
; CHECK-NEXT:    [[TMP1:%.*]] = insertelement <4 x i16> poison, i16 [[T]], i32 0
; CHECK-NEXT:    [[TMP2:%.*]] = shufflevector <4 x i16> [[TMP1]], <4 x i16> poison, <4 x i32> zeroinitializer
; CHECK-NEXT:    store <4 x i16> [[TMP2]], ptr [[A]], align 1
; CHECK-NEXT:    ret void
;
  %t = trunc i24 %x to i16
  store i16 %t, ptr %A, align 1
  %gep.1 = getelementptr i16, ptr %A, i64 1
  store i16 %t, ptr %gep.1, align 1
  %gep.2 = getelementptr i16, ptr %A, i64 2
  store i16 %t, ptr %gep.2, align 1
  %gep.3 = getelementptr i16, ptr %A, i64 3
  store i16 %t, ptr %gep.3, align 1
  ret void
}

%struct.d = type { [3 x i8], [3 x i8], [2 x i8] }

; Test case for https://github.com/llvm/llvm-project/issues/88640.
define void @test_access_i24_directly(ptr %src, ptr noalias %dst) "target-cpu"="btver2" {
; CHECK-LABEL: define void @test_access_i24_directly(
; CHECK-SAME: ptr [[SRC:%.*]], ptr noalias [[DST:%.*]]) #[[ATTR0:[0-9]+]] {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP0:%.*]] = load i64, ptr [[SRC]], align 8
; CHECK-NEXT:    [[TMP1:%.*]] = trunc i64 [[TMP0]] to i24
; CHECK-NEXT:    [[GEP_SRC:%.*]] = getelementptr inbounds [[STRUCT_D:%.*]], ptr [[SRC]], i64 0, i32 1
; CHECK-NEXT:    [[BF_LOAD:%.*]] = load i24, ptr [[GEP_SRC]], align 1
; CHECK-NEXT:    [[BF_VALUE:%.*]] = and i24 [[TMP1]], 8388607
; CHECK-NEXT:    [[BF_CLEAR:%.*]] = and i24 [[BF_LOAD]], -8388608
; CHECK-NEXT:    [[BF_SET:%.*]] = or disjoint i24 [[BF_CLEAR]], [[BF_VALUE]]
; CHECK-NEXT:    [[GEP_DST:%.*]] = getelementptr inbounds [[STRUCT_D]], ptr [[DST]], i64 0, i32 1
; CHECK-NEXT:    store i24 [[BF_SET]], ptr [[GEP_DST]], align 1
; CHECK-NEXT:    store i24 0, ptr [[DST]], align 8
; CHECK-NEXT:    ret void
;
entry:
  %0 = load i64, ptr %src, align 8
  %1 = trunc i64 %0 to i24
  %gep.src = getelementptr inbounds %struct.d, ptr %src, i64 0, i32 1
  %bf.load = load i24, ptr %gep.src, align 1
  %bf.value = and i24 %1, 8388607
  %bf.clear = and i24 %bf.load, -8388608
  %bf.set = or disjoint i24 %bf.clear, %bf.value
  %gep.dst = getelementptr inbounds %struct.d, ptr %dst, i64 0, i32 1
  store i24 %bf.set, ptr %gep.dst, align 1
  store i24 0, ptr %dst, align 8
  ret void
}
