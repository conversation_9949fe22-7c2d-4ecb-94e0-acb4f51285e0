; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -S -mtriple=x86_64-pc-linux-gnu -mcpu=generic -passes=slp-vectorizer --slp-max-reg-size=256 -pass-remarks-output=%t < %s | FileCheck %s
; RUN: FileCheck --input-file=%t --check-prefix=YAML %s

; This type is not supported by SLP
define i1 @test(ptr %i1, ptr %i2, i1 %arg) {
; CHECK-LABEL: @test(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[I1_0:%.*]] = load x86_fp80, ptr [[I1:%.*]], align 16
; CHECK-NEXT:    [[I1_GEP1:%.*]] = getelementptr x86_fp80, ptr [[I1]], i64 1
; CHECK-NEXT:    [[I1_1:%.*]] = load x86_fp80, ptr [[I1_GEP1]], align 16
; CHECK-NEXT:    br i1 %arg, label [[THEN:%.*]], label [[END:%.*]]
; CHECK:       then:
; CHECK-NEXT:    [[I2_0:%.*]] = load x86_fp80, ptr [[I2:%.*]], align 16
; CHECK-NEXT:    [[I2_GEP1:%.*]] = getelementptr inbounds x86_fp80, ptr [[I2]], i64 1
; CHECK-NEXT:    [[I2_1:%.*]] = load x86_fp80, ptr [[I2_GEP1]], align 16
; CHECK-NEXT:    br label [[END]]
; CHECK:       end:
; CHECK-NEXT:    [[PHI0:%.*]] = phi x86_fp80 [ [[I1_0]], [[ENTRY:%.*]] ], [ [[I2_0]], [[THEN]] ]
; CHECK-NEXT:    [[PHI1:%.*]] = phi x86_fp80 [ [[I1_1]], [[ENTRY]] ], [ [[I2_1]], [[THEN]] ]
; CHECK-NEXT:    [[RES:%.*]] = fcmp oeq x86_fp80 [[PHI0]], [[PHI1]]
; CHECK-NEXT:    ret i1 [[RES]]
;
entry:
  %i1.0 = load x86_fp80, ptr %i1, align 16
  %i1.gep1 = getelementptr x86_fp80, ptr %i1, i64 1
  %i1.1 = load x86_fp80, ptr %i1.gep1, align 16
  br i1 %arg, label %then, label %end
then:
  %i2.0 = load x86_fp80, ptr %i2, align 16
  %i2.gep1 = getelementptr inbounds x86_fp80, ptr %i2, i64 1
  %i2.1 = load x86_fp80, ptr %i2.gep1, align 16
  br label %end
end:
  %phi0 = phi x86_fp80 [ %i1.0, %entry ], [ %i2.0, %then ]
  %phi1 = phi x86_fp80 [ %i1.1, %entry ], [ %i2.1, %then ]
  %res = fcmp oeq x86_fp80 %phi0, %phi1
  ; YAML:      Pass:            slp-vectorizer
  ; YAML-NEXT: Name:            UnsupportedType
  ; YAML-NEXT: Function:        test
  ; YAML-NEXT: Args:
  ; YAML-NEXT:   - String:          'Cannot SLP vectorize list: type '
  ; YAML-NEXT:   - String:          x86_fp80 is unsupported by vectorizer

  ret i1 %res
}
