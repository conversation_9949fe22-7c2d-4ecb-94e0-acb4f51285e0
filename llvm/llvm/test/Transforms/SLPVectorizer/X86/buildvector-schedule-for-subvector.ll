; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown-linux-gnu -slp-threshold=-99999 < %s | FileCheck %s

define void @test() {
; CHECK-LABEL: define void @test() {
; CHECK-NEXT:  [[BB:.*:]]
; CHECK-NEXT:    [[ADD:%.*]] = add i32 1, 0
; CHECK-NEXT:    [[TMP0:%.*]] = insertelement <4 x i32> <i32 0, i32 0, i32 0, i32 poison>, i32 [[ADD]], i32 3
; CHECK-NEXT:    [[TMP1:%.*]] = icmp ult <4 x i32> [[TMP0]], zeroinitializer
; CHECK-NEXT:    [[ICMP:%.*]] = extractelement <4 x i1> [[TMP1]], i32 2
; CHECK-NEXT:    [[SELECT:%.*]] = select i1 [[ICMP]], i32 0, i32 0
; CHECK-NEXT:    [[ZEXT:%.*]] = zext i32 [[SELECT]] to i64
; CHECK-NEXT:    [[GETELEMENTPTR:%.*]] = getelementptr ptr addrspace(1), ptr addrspace(1) null, i64 [[ZEXT]]
; CHECK-NEXT:    store ptr addrspace(1) null, ptr addrspace(1) [[GETELEMENTPTR]], align 8
; CHECK-NEXT:    store volatile i32 0, ptr addrspace(1) null, align 4
; CHECK-NEXT:    [[CALL:%.*]] = call i32 null(<2 x double> zeroinitializer)
; CHECK-NEXT:    [[TMP2:%.*]] = insertelement <4 x i32> <i32 0, i32 0, i32 0, i32 poison>, i32 [[CALL]], i32 3
; CHECK-NEXT:    [[TMP3:%.*]] = icmp eq <4 x i32> [[TMP2]], zeroinitializer
; CHECK-NEXT:    ret void
;
bb:
  %icmp = icmp samesign ult i32 0, 0
  %select = select i1 %icmp, i32 0, i32 0
  %zext = zext i32 %select to i64
  %getelementptr = getelementptr ptr addrspace(1), ptr addrspace(1) null, i64 %zext
  store ptr addrspace(1) null, ptr addrspace(1) %getelementptr, align 8
  %icmp1 = icmp eq i32 0, 0
  %icmp2 = icmp eq i32 0, 0
  %icmp3 = icmp samesign ult i32 0, 0
  %icmp4 = icmp eq i32 0, 0
  %add = add i32 1, 0
  %icmp5 = icmp samesign ult i32 %add, 0
  store volatile i32 0, ptr addrspace(1) null, align 4
  %call = call i32 null(<2 x double> zeroinitializer)
  %icmp6 = icmp eq i32 %call, 0
  %icmp7 = icmp samesign ult i32 0, 0
  ret void
}
