; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -S -passes=slp-vectorizer -mattr=+sse  < %s | FileCheck %s --check-prefixes=CHECK,SSE
; RUN: opt -S -passes=slp-vectorizer -mattr=+avx512f < %s | FileCheck %s --check-prefixes=CHECK,AVX512

target datalayout = "e-m:e-i64:64-f80:128-n8:16:32:64-S128"
target triple = "x86_64-unknown-linux-gnu"

; Function Attrs: norecurse nounwind readnone uwtable
define zeroext i8 @foo(i32 %x, i32 %y, i32 %a, i32 %b) local_unnamed_addr #0 {
; CHECK-LABEL: @foo(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[CMP:%.*]] = icmp slt i32 [[X:%.*]], [[Y:%.*]]
; CHECK-NEXT:    [[B_A:%.*]] = select i1 [[CMP]], i32 [[B:%.*]], i32 [[A:%.*]]
; CHECK-NEXT:    [[RETVAL_0:%.*]] = trunc i32 [[B_A]] to i8
; CHECK-NEXT:    ret i8 [[RETVAL_0]]
;
entry:
  %cmp = icmp slt i32 %x, %y
  %b.a = select i1 %cmp, i32 %b, i32 %a
  %retval.0 = trunc i32 %b.a to i8
  ret i8 %retval.0
}

define void @bar(ptr noalias nocapture readonly %a, ptr noalias nocapture readonly %b, ptr noalias nocapture readonly %c, ptr noalias nocapture readonly %d, ptr noalias nocapture %e, i32 %w) local_unnamed_addr #1 {
; CHECK-LABEL: @bar(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP0:%.*]] = insertelement <16 x i32> poison, i32 [[W:%.*]], i32 0
; CHECK-NEXT:    [[SHUFFLE:%.*]] = shufflevector <16 x i32> [[TMP0]], <16 x i32> poison, <16 x i32> zeroinitializer
; CHECK-NEXT:    br label [[FOR_BODY:%.*]]
; CHECK:       for.body:
; CHECK-NEXT:    [[I_0356:%.*]] = phi i32 [ 0, [[ENTRY:%.*]] ], [ [[INC:%.*]], [[FOR_BODY]] ]
; CHECK-NEXT:    [[A_ADDR_0355:%.*]] = phi ptr [ [[A:%.*]], [[ENTRY]] ], [ [[ADD_PTR:%.*]], [[FOR_BODY]] ]
; CHECK-NEXT:    [[E_ADDR_0354:%.*]] = phi ptr [ [[E:%.*]], [[ENTRY]] ], [ [[ADD_PTR192:%.*]], [[FOR_BODY]] ]
; CHECK-NEXT:    [[D_ADDR_0353:%.*]] = phi ptr [ [[D:%.*]], [[ENTRY]] ], [ [[ADD_PTR191:%.*]], [[FOR_BODY]] ]
; CHECK-NEXT:    [[C_ADDR_0352:%.*]] = phi ptr [ [[C:%.*]], [[ENTRY]] ], [ [[ADD_PTR190:%.*]], [[FOR_BODY]] ]
; CHECK-NEXT:    [[B_ADDR_0351:%.*]] = phi ptr [ [[B:%.*]], [[ENTRY]] ], [ [[ADD_PTR189:%.*]], [[FOR_BODY]] ]
; CHECK-NEXT:    [[TMP2:%.*]] = load <16 x i8>, ptr [[C_ADDR_0352]], align 1
; CHECK-NEXT:    [[TMP4:%.*]] = load <16 x i8>, ptr [[D_ADDR_0353]], align 1
; CHECK-NEXT:    [[TMP6:%.*]] = load <16 x i8>, ptr [[A_ADDR_0355]], align 1
; CHECK-NEXT:    [[TMP8:%.*]] = load <16 x i8>, ptr [[B_ADDR_0351]], align 1
; CHECK-NEXT:    [[TMP9:%.*]] = icmp ult <16 x i8> [[TMP2]], [[TMP4]]
; CHECK-NEXT:    [[TMP10:%.*]] = select <16 x i1> [[TMP9]], <16 x i8> [[TMP8]], <16 x i8> [[TMP6]]
; CHECK-NEXT:    [[TMP11:%.*]] = zext <16 x i8> [[TMP10]] to <16 x i32>
; CHECK-NEXT:    [[TMP12:%.*]] = mul <16 x i32> [[TMP11]], [[SHUFFLE]]
; CHECK-NEXT:    [[TMP13:%.*]] = trunc <16 x i32> [[TMP12]] to <16 x i8>
; CHECK-NEXT:    store <16 x i8> [[TMP13]], ptr [[E_ADDR_0354]], align 1
; CHECK-NEXT:    [[INC]] = add nuw nsw i32 [[I_0356]], 1
; CHECK-NEXT:    [[ADD_PTR]] = getelementptr inbounds i8, ptr [[A_ADDR_0355]], i64 16
; CHECK-NEXT:    [[ADD_PTR189]] = getelementptr inbounds i8, ptr [[B_ADDR_0351]], i64 16
; CHECK-NEXT:    [[ADD_PTR190]] = getelementptr inbounds i8, ptr [[C_ADDR_0352]], i64 16
; CHECK-NEXT:    [[ADD_PTR191]] = getelementptr inbounds i8, ptr [[D_ADDR_0353]], i64 16
; CHECK-NEXT:    [[ADD_PTR192]] = getelementptr inbounds i8, ptr [[E_ADDR_0354]], i64 16
; CHECK-NEXT:    [[EXITCOND:%.*]] = icmp eq i32 [[INC]], 8
; CHECK-NEXT:    br i1 [[EXITCOND]], label [[FOR_END:%.*]], label [[FOR_BODY]]
; CHECK:       for.end:
; CHECK-NEXT:    ret void
;
entry:
  br label %for.body

for.body:                                         ; preds = %for.body, %entry
  %i.0356 = phi i32 [ 0, %entry ], [ %inc, %for.body ]
  %a.addr.0355 = phi ptr [ %a, %entry ], [ %add.ptr, %for.body ]
  %e.addr.0354 = phi ptr [ %e, %entry ], [ %add.ptr192, %for.body ]
  %d.addr.0353 = phi ptr [ %d, %entry ], [ %add.ptr191, %for.body ]
  %c.addr.0352 = phi ptr [ %c, %entry ], [ %add.ptr190, %for.body ]
  %b.addr.0351 = phi ptr [ %b, %entry ], [ %add.ptr189, %for.body ]
  %0 = load i8, ptr %c.addr.0352, align 1
  %1 = load i8, ptr %d.addr.0353, align 1
  %2 = load i8, ptr %a.addr.0355, align 1
  %3 = load i8, ptr %b.addr.0351, align 1
  %cmp.i = icmp ult i8 %0, %1
  %b.a.i.v.v = select i1 %cmp.i, i8 %3, i8 %2
  %b.a.i.v = zext i8 %b.a.i.v.v to i32
  %b.a.i = mul i32 %b.a.i.v, %w
  %retval.0.i = trunc i32 %b.a.i to i8
  store i8 %retval.0.i, ptr %e.addr.0354, align 1
  %arrayidx9 = getelementptr inbounds i8, ptr %c.addr.0352, i64 1
  %4 = load i8, ptr %arrayidx9, align 1
  %arrayidx11 = getelementptr inbounds i8, ptr %d.addr.0353, i64 1
  %5 = load i8, ptr %arrayidx11, align 1
  %arrayidx13 = getelementptr inbounds i8, ptr %a.addr.0355, i64 1
  %6 = load i8, ptr %arrayidx13, align 1
  %arrayidx16 = getelementptr inbounds i8, ptr %b.addr.0351, i64 1
  %7 = load i8, ptr %arrayidx16, align 1
  %cmp.i348 = icmp ult i8 %4, %5
  %b.a.i349.v.v = select i1 %cmp.i348, i8 %7, i8 %6
  %b.a.i349.v = zext i8 %b.a.i349.v.v to i32
  %b.a.i349 = mul i32 %b.a.i349.v, %w
  %retval.0.i350 = trunc i32 %b.a.i349 to i8
  %arrayidx20 = getelementptr inbounds i8, ptr %e.addr.0354, i64 1
  store i8 %retval.0.i350, ptr %arrayidx20, align 1
  %arrayidx21 = getelementptr inbounds i8, ptr %c.addr.0352, i64 2
  %8 = load i8, ptr %arrayidx21, align 1
  %arrayidx23 = getelementptr inbounds i8, ptr %d.addr.0353, i64 2
  %9 = load i8, ptr %arrayidx23, align 1
  %arrayidx25 = getelementptr inbounds i8, ptr %a.addr.0355, i64 2
  %10 = load i8, ptr %arrayidx25, align 1
  %arrayidx28 = getelementptr inbounds i8, ptr %b.addr.0351, i64 2
  %11 = load i8, ptr %arrayidx28, align 1
  %cmp.i345 = icmp ult i8 %8, %9
  %b.a.i346.v.v = select i1 %cmp.i345, i8 %11, i8 %10
  %b.a.i346.v = zext i8 %b.a.i346.v.v to i32
  %b.a.i346 = mul i32 %b.a.i346.v, %w
  %retval.0.i347 = trunc i32 %b.a.i346 to i8
  %arrayidx32 = getelementptr inbounds i8, ptr %e.addr.0354, i64 2
  store i8 %retval.0.i347, ptr %arrayidx32, align 1
  %arrayidx33 = getelementptr inbounds i8, ptr %c.addr.0352, i64 3
  %12 = load i8, ptr %arrayidx33, align 1
  %arrayidx35 = getelementptr inbounds i8, ptr %d.addr.0353, i64 3
  %13 = load i8, ptr %arrayidx35, align 1
  %arrayidx37 = getelementptr inbounds i8, ptr %a.addr.0355, i64 3
  %14 = load i8, ptr %arrayidx37, align 1
  %arrayidx40 = getelementptr inbounds i8, ptr %b.addr.0351, i64 3
  %15 = load i8, ptr %arrayidx40, align 1
  %cmp.i342 = icmp ult i8 %12, %13
  %b.a.i343.v.v = select i1 %cmp.i342, i8 %15, i8 %14
  %b.a.i343.v = zext i8 %b.a.i343.v.v to i32
  %b.a.i343 = mul i32 %b.a.i343.v, %w
  %retval.0.i344 = trunc i32 %b.a.i343 to i8
  %arrayidx44 = getelementptr inbounds i8, ptr %e.addr.0354, i64 3
  store i8 %retval.0.i344, ptr %arrayidx44, align 1
  %arrayidx45 = getelementptr inbounds i8, ptr %c.addr.0352, i64 4
  %16 = load i8, ptr %arrayidx45, align 1
  %arrayidx47 = getelementptr inbounds i8, ptr %d.addr.0353, i64 4
  %17 = load i8, ptr %arrayidx47, align 1
  %arrayidx49 = getelementptr inbounds i8, ptr %a.addr.0355, i64 4
  %18 = load i8, ptr %arrayidx49, align 1
  %arrayidx52 = getelementptr inbounds i8, ptr %b.addr.0351, i64 4
  %19 = load i8, ptr %arrayidx52, align 1
  %cmp.i339 = icmp ult i8 %16, %17
  %b.a.i340.v.v = select i1 %cmp.i339, i8 %19, i8 %18
  %b.a.i340.v = zext i8 %b.a.i340.v.v to i32
  %b.a.i340 = mul i32 %b.a.i340.v, %w
  %retval.0.i341 = trunc i32 %b.a.i340 to i8
  %arrayidx56 = getelementptr inbounds i8, ptr %e.addr.0354, i64 4
  store i8 %retval.0.i341, ptr %arrayidx56, align 1
  %arrayidx57 = getelementptr inbounds i8, ptr %c.addr.0352, i64 5
  %20 = load i8, ptr %arrayidx57, align 1
  %arrayidx59 = getelementptr inbounds i8, ptr %d.addr.0353, i64 5
  %21 = load i8, ptr %arrayidx59, align 1
  %arrayidx61 = getelementptr inbounds i8, ptr %a.addr.0355, i64 5
  %22 = load i8, ptr %arrayidx61, align 1
  %arrayidx64 = getelementptr inbounds i8, ptr %b.addr.0351, i64 5
  %23 = load i8, ptr %arrayidx64, align 1
  %cmp.i336 = icmp ult i8 %20, %21
  %b.a.i337.v.v = select i1 %cmp.i336, i8 %23, i8 %22
  %b.a.i337.v = zext i8 %b.a.i337.v.v to i32
  %b.a.i337 = mul i32 %b.a.i337.v, %w
  %retval.0.i338 = trunc i32 %b.a.i337 to i8
  %arrayidx68 = getelementptr inbounds i8, ptr %e.addr.0354, i64 5
  store i8 %retval.0.i338, ptr %arrayidx68, align 1
  %arrayidx69 = getelementptr inbounds i8, ptr %c.addr.0352, i64 6
  %24 = load i8, ptr %arrayidx69, align 1
  %arrayidx71 = getelementptr inbounds i8, ptr %d.addr.0353, i64 6
  %25 = load i8, ptr %arrayidx71, align 1
  %arrayidx73 = getelementptr inbounds i8, ptr %a.addr.0355, i64 6
  %26 = load i8, ptr %arrayidx73, align 1
  %arrayidx76 = getelementptr inbounds i8, ptr %b.addr.0351, i64 6
  %27 = load i8, ptr %arrayidx76, align 1
  %cmp.i333 = icmp ult i8 %24, %25
  %b.a.i334.v.v = select i1 %cmp.i333, i8 %27, i8 %26
  %b.a.i334.v = zext i8 %b.a.i334.v.v to i32
  %b.a.i334 = mul i32 %b.a.i334.v, %w
  %retval.0.i335 = trunc i32 %b.a.i334 to i8
  %arrayidx80 = getelementptr inbounds i8, ptr %e.addr.0354, i64 6
  store i8 %retval.0.i335, ptr %arrayidx80, align 1
  %arrayidx81 = getelementptr inbounds i8, ptr %c.addr.0352, i64 7
  %28 = load i8, ptr %arrayidx81, align 1
  %arrayidx83 = getelementptr inbounds i8, ptr %d.addr.0353, i64 7
  %29 = load i8, ptr %arrayidx83, align 1
  %arrayidx85 = getelementptr inbounds i8, ptr %a.addr.0355, i64 7
  %30 = load i8, ptr %arrayidx85, align 1
  %arrayidx88 = getelementptr inbounds i8, ptr %b.addr.0351, i64 7
  %31 = load i8, ptr %arrayidx88, align 1
  %cmp.i330 = icmp ult i8 %28, %29
  %b.a.i331.v.v = select i1 %cmp.i330, i8 %31, i8 %30
  %b.a.i331.v = zext i8 %b.a.i331.v.v to i32
  %b.a.i331 = mul i32 %b.a.i331.v, %w
  %retval.0.i332 = trunc i32 %b.a.i331 to i8
  %arrayidx92 = getelementptr inbounds i8, ptr %e.addr.0354, i64 7
  store i8 %retval.0.i332, ptr %arrayidx92, align 1
  %arrayidx93 = getelementptr inbounds i8, ptr %c.addr.0352, i64 8
  %32 = load i8, ptr %arrayidx93, align 1
  %arrayidx95 = getelementptr inbounds i8, ptr %d.addr.0353, i64 8
  %33 = load i8, ptr %arrayidx95, align 1
  %arrayidx97 = getelementptr inbounds i8, ptr %a.addr.0355, i64 8
  %34 = load i8, ptr %arrayidx97, align 1
  %arrayidx100 = getelementptr inbounds i8, ptr %b.addr.0351, i64 8
  %35 = load i8, ptr %arrayidx100, align 1
  %cmp.i327 = icmp ult i8 %32, %33
  %b.a.i328.v.v = select i1 %cmp.i327, i8 %35, i8 %34
  %b.a.i328.v = zext i8 %b.a.i328.v.v to i32
  %b.a.i328 = mul i32 %b.a.i328.v, %w
  %retval.0.i329 = trunc i32 %b.a.i328 to i8
  %arrayidx104 = getelementptr inbounds i8, ptr %e.addr.0354, i64 8
  store i8 %retval.0.i329, ptr %arrayidx104, align 1
  %arrayidx105 = getelementptr inbounds i8, ptr %c.addr.0352, i64 9
  %36 = load i8, ptr %arrayidx105, align 1
  %arrayidx107 = getelementptr inbounds i8, ptr %d.addr.0353, i64 9
  %37 = load i8, ptr %arrayidx107, align 1
  %arrayidx109 = getelementptr inbounds i8, ptr %a.addr.0355, i64 9
  %38 = load i8, ptr %arrayidx109, align 1
  %arrayidx112 = getelementptr inbounds i8, ptr %b.addr.0351, i64 9
  %39 = load i8, ptr %arrayidx112, align 1
  %cmp.i324 = icmp ult i8 %36, %37
  %b.a.i325.v.v = select i1 %cmp.i324, i8 %39, i8 %38
  %b.a.i325.v = zext i8 %b.a.i325.v.v to i32
  %b.a.i325 = mul i32 %b.a.i325.v, %w
  %retval.0.i326 = trunc i32 %b.a.i325 to i8
  %arrayidx116 = getelementptr inbounds i8, ptr %e.addr.0354, i64 9
  store i8 %retval.0.i326, ptr %arrayidx116, align 1
  %arrayidx117 = getelementptr inbounds i8, ptr %c.addr.0352, i64 10
  %40 = load i8, ptr %arrayidx117, align 1
  %arrayidx119 = getelementptr inbounds i8, ptr %d.addr.0353, i64 10
  %41 = load i8, ptr %arrayidx119, align 1
  %arrayidx121 = getelementptr inbounds i8, ptr %a.addr.0355, i64 10
  %42 = load i8, ptr %arrayidx121, align 1
  %arrayidx124 = getelementptr inbounds i8, ptr %b.addr.0351, i64 10
  %43 = load i8, ptr %arrayidx124, align 1
  %cmp.i321 = icmp ult i8 %40, %41
  %b.a.i322.v.v = select i1 %cmp.i321, i8 %43, i8 %42
  %b.a.i322.v = zext i8 %b.a.i322.v.v to i32
  %b.a.i322 = mul i32 %b.a.i322.v, %w
  %retval.0.i323 = trunc i32 %b.a.i322 to i8
  %arrayidx128 = getelementptr inbounds i8, ptr %e.addr.0354, i64 10
  store i8 %retval.0.i323, ptr %arrayidx128, align 1
  %arrayidx129 = getelementptr inbounds i8, ptr %c.addr.0352, i64 11
  %44 = load i8, ptr %arrayidx129, align 1
  %arrayidx131 = getelementptr inbounds i8, ptr %d.addr.0353, i64 11
  %45 = load i8, ptr %arrayidx131, align 1
  %arrayidx133 = getelementptr inbounds i8, ptr %a.addr.0355, i64 11
  %46 = load i8, ptr %arrayidx133, align 1
  %arrayidx136 = getelementptr inbounds i8, ptr %b.addr.0351, i64 11
  %47 = load i8, ptr %arrayidx136, align 1
  %cmp.i318 = icmp ult i8 %44, %45
  %b.a.i319.v.v = select i1 %cmp.i318, i8 %47, i8 %46
  %b.a.i319.v = zext i8 %b.a.i319.v.v to i32
  %b.a.i319 = mul i32 %b.a.i319.v, %w
  %retval.0.i320 = trunc i32 %b.a.i319 to i8
  %arrayidx140 = getelementptr inbounds i8, ptr %e.addr.0354, i64 11
  store i8 %retval.0.i320, ptr %arrayidx140, align 1
  %arrayidx141 = getelementptr inbounds i8, ptr %c.addr.0352, i64 12
  %48 = load i8, ptr %arrayidx141, align 1
  %arrayidx143 = getelementptr inbounds i8, ptr %d.addr.0353, i64 12
  %49 = load i8, ptr %arrayidx143, align 1
  %arrayidx145 = getelementptr inbounds i8, ptr %a.addr.0355, i64 12
  %50 = load i8, ptr %arrayidx145, align 1
  %arrayidx148 = getelementptr inbounds i8, ptr %b.addr.0351, i64 12
  %51 = load i8, ptr %arrayidx148, align 1
  %cmp.i315 = icmp ult i8 %48, %49
  %b.a.i316.v.v = select i1 %cmp.i315, i8 %51, i8 %50
  %b.a.i316.v = zext i8 %b.a.i316.v.v to i32
  %b.a.i316 = mul i32 %b.a.i316.v, %w
  %retval.0.i317 = trunc i32 %b.a.i316 to i8
  %arrayidx152 = getelementptr inbounds i8, ptr %e.addr.0354, i64 12
  store i8 %retval.0.i317, ptr %arrayidx152, align 1
  %arrayidx153 = getelementptr inbounds i8, ptr %c.addr.0352, i64 13
  %52 = load i8, ptr %arrayidx153, align 1
  %arrayidx155 = getelementptr inbounds i8, ptr %d.addr.0353, i64 13
  %53 = load i8, ptr %arrayidx155, align 1
  %arrayidx157 = getelementptr inbounds i8, ptr %a.addr.0355, i64 13
  %54 = load i8, ptr %arrayidx157, align 1
  %arrayidx160 = getelementptr inbounds i8, ptr %b.addr.0351, i64 13
  %55 = load i8, ptr %arrayidx160, align 1
  %cmp.i312 = icmp ult i8 %52, %53
  %b.a.i313.v.v = select i1 %cmp.i312, i8 %55, i8 %54
  %b.a.i313.v = zext i8 %b.a.i313.v.v to i32
  %b.a.i313 = mul i32 %b.a.i313.v, %w
  %retval.0.i314 = trunc i32 %b.a.i313 to i8
  %arrayidx164 = getelementptr inbounds i8, ptr %e.addr.0354, i64 13
  store i8 %retval.0.i314, ptr %arrayidx164, align 1
  %arrayidx165 = getelementptr inbounds i8, ptr %c.addr.0352, i64 14
  %56 = load i8, ptr %arrayidx165, align 1
  %arrayidx167 = getelementptr inbounds i8, ptr %d.addr.0353, i64 14
  %57 = load i8, ptr %arrayidx167, align 1
  %arrayidx169 = getelementptr inbounds i8, ptr %a.addr.0355, i64 14
  %58 = load i8, ptr %arrayidx169, align 1
  %arrayidx172 = getelementptr inbounds i8, ptr %b.addr.0351, i64 14
  %59 = load i8, ptr %arrayidx172, align 1
  %cmp.i309 = icmp ult i8 %56, %57
  %b.a.i310.v.v = select i1 %cmp.i309, i8 %59, i8 %58
  %b.a.i310.v = zext i8 %b.a.i310.v.v to i32
  %b.a.i310 = mul i32 %b.a.i310.v, %w
  %retval.0.i311 = trunc i32 %b.a.i310 to i8
  %arrayidx176 = getelementptr inbounds i8, ptr %e.addr.0354, i64 14
  store i8 %retval.0.i311, ptr %arrayidx176, align 1
  %arrayidx177 = getelementptr inbounds i8, ptr %c.addr.0352, i64 15
  %60 = load i8, ptr %arrayidx177, align 1
  %arrayidx179 = getelementptr inbounds i8, ptr %d.addr.0353, i64 15
  %61 = load i8, ptr %arrayidx179, align 1
  %arrayidx181 = getelementptr inbounds i8, ptr %a.addr.0355, i64 15
  %62 = load i8, ptr %arrayidx181, align 1
  %arrayidx184 = getelementptr inbounds i8, ptr %b.addr.0351, i64 15
  %63 = load i8, ptr %arrayidx184, align 1
  %cmp.i306 = icmp ult i8 %60, %61
  %b.a.i307.v.v = select i1 %cmp.i306, i8 %63, i8 %62
  %b.a.i307.v = zext i8 %b.a.i307.v.v to i32
  %b.a.i307 = mul i32 %b.a.i307.v, %w
  %retval.0.i308 = trunc i32 %b.a.i307 to i8
  %arrayidx188 = getelementptr inbounds i8, ptr %e.addr.0354, i64 15
  store i8 %retval.0.i308, ptr %arrayidx188, align 1
  %inc = add nuw nsw i32 %i.0356, 1
  %add.ptr = getelementptr inbounds i8, ptr %a.addr.0355, i64 16
  %add.ptr189 = getelementptr inbounds i8, ptr %b.addr.0351, i64 16
  %add.ptr190 = getelementptr inbounds i8, ptr %c.addr.0352, i64 16
  %add.ptr191 = getelementptr inbounds i8, ptr %d.addr.0353, i64 16
  %add.ptr192 = getelementptr inbounds i8, ptr %e.addr.0354, i64 16
  %exitcond = icmp eq i32 %inc, 8
  br i1 %exitcond, label %for.end, label %for.body

for.end:                                          ; preds = %for.body
  ret void
}

@ib = local_unnamed_addr global [64 x i32] [i32 1, i32 1, i32 0, i32 0, i32 1, i32 0, i32 1, i32 0, i32 1, i32 1, i32 0, i32 0, i32 1, i32 0, i32 1, i32 0, i32 1, i32 1, i32 0, i32 0, i32 1, i32 0, i32 1, i32 0, i32 1, i32 1, i32 0, i32 0, i32 1, i32 0, i32 1, i32 0, i32 1, i32 1, i32 0, i32 0, i32 1, i32 0, i32 1, i32 0, i32 1, i32 1, i32 0, i32 0, i32 1, i32 0, i32 1, i32 0, i32 1, i32 1, i32 0, i32 0, i32 1, i32 0, i32 1, i32 0, i32 1, i32 1, i32 0, i32 0, i32 1, i32 0, i32 1, i32 0], align 16
@ia = common local_unnamed_addr global [64 x i32] zeroinitializer, align 16

define i32 @foo1() local_unnamed_addr #0 {
; SSE-LABEL: @foo1(
; SSE-NEXT:  entry:
; SSE-NEXT:    [[TMP0:%.*]] = load <4 x i32>, ptr @ib, align 16
; SSE-NEXT:    [[TMP1:%.*]] = xor <4 x i32> [[TMP0]], splat (i32 -1)
; SSE-NEXT:    store <4 x i32> [[TMP1]], ptr @ia, align 16
; SSE-NEXT:    [[TMP2:%.*]] = load <4 x i32>, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 4), align 16
; SSE-NEXT:    [[TMP3:%.*]] = xor <4 x i32> [[TMP2]], splat (i32 -1)
; SSE-NEXT:    store <4 x i32> [[TMP3]], ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 4), align 16
; SSE-NEXT:    [[TMP4:%.*]] = load <4 x i32>, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 8), align 16
; SSE-NEXT:    [[TMP5:%.*]] = xor <4 x i32> [[TMP4]], splat (i32 -1)
; SSE-NEXT:    store <4 x i32> [[TMP5]], ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 8), align 16
; SSE-NEXT:    [[TMP6:%.*]] = load <4 x i32>, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 12), align 16
; SSE-NEXT:    [[TMP7:%.*]] = xor <4 x i32> [[TMP6]], splat (i32 -1)
; SSE-NEXT:    store <4 x i32> [[TMP7]], ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 12), align 16
; SSE-NEXT:    [[TMP8:%.*]] = load <4 x i32>, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 16), align 16
; SSE-NEXT:    [[TMP9:%.*]] = xor <4 x i32> [[TMP8]], splat (i32 -1)
; SSE-NEXT:    store <4 x i32> [[TMP9]], ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 16), align 16
; SSE-NEXT:    [[TMP10:%.*]] = load <4 x i32>, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 20), align 16
; SSE-NEXT:    [[TMP11:%.*]] = xor <4 x i32> [[TMP10]], splat (i32 -1)
; SSE-NEXT:    store <4 x i32> [[TMP11]], ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 20), align 16
; SSE-NEXT:    [[TMP12:%.*]] = load <4 x i32>, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 24), align 16
; SSE-NEXT:    [[TMP13:%.*]] = xor <4 x i32> [[TMP12]], splat (i32 -1)
; SSE-NEXT:    store <4 x i32> [[TMP13]], ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 24), align 16
; SSE-NEXT:    [[TMP14:%.*]] = load <4 x i32>, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 28), align 16
; SSE-NEXT:    [[TMP15:%.*]] = xor <4 x i32> [[TMP14]], splat (i32 -1)
; SSE-NEXT:    store <4 x i32> [[TMP15]], ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 28), align 16
; SSE-NEXT:    [[TMP16:%.*]] = load <4 x i32>, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 32), align 16
; SSE-NEXT:    [[TMP17:%.*]] = xor <4 x i32> [[TMP16]], splat (i32 -1)
; SSE-NEXT:    store <4 x i32> [[TMP17]], ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 32), align 16
; SSE-NEXT:    [[TMP18:%.*]] = load <4 x i32>, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 36), align 16
; SSE-NEXT:    [[TMP19:%.*]] = xor <4 x i32> [[TMP18]], splat (i32 -1)
; SSE-NEXT:    store <4 x i32> [[TMP19]], ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 36), align 16
; SSE-NEXT:    [[TMP20:%.*]] = load <4 x i32>, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 40), align 16
; SSE-NEXT:    [[TMP21:%.*]] = xor <4 x i32> [[TMP20]], splat (i32 -1)
; SSE-NEXT:    store <4 x i32> [[TMP21]], ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 40), align 16
; SSE-NEXT:    [[TMP22:%.*]] = load <4 x i32>, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 44), align 16
; SSE-NEXT:    [[TMP23:%.*]] = xor <4 x i32> [[TMP22]], splat (i32 -1)
; SSE-NEXT:    store <4 x i32> [[TMP23]], ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 44), align 16
; SSE-NEXT:    [[TMP24:%.*]] = load <4 x i32>, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 48), align 16
; SSE-NEXT:    [[TMP25:%.*]] = xor <4 x i32> [[TMP24]], splat (i32 -1)
; SSE-NEXT:    store <4 x i32> [[TMP25]], ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 48), align 16
; SSE-NEXT:    [[TMP26:%.*]] = load <4 x i32>, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 52), align 16
; SSE-NEXT:    [[TMP27:%.*]] = xor <4 x i32> [[TMP26]], splat (i32 -1)
; SSE-NEXT:    store <4 x i32> [[TMP27]], ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 52), align 16
; SSE-NEXT:    [[TMP28:%.*]] = load <4 x i32>, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 56), align 16
; SSE-NEXT:    [[TMP29:%.*]] = xor <4 x i32> [[TMP28]], splat (i32 -1)
; SSE-NEXT:    store <4 x i32> [[TMP29]], ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 56), align 16
; SSE-NEXT:    [[TMP30:%.*]] = load <4 x i32>, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 60), align 16
; SSE-NEXT:    [[TMP31:%.*]] = xor <4 x i32> [[TMP30]], splat (i32 -1)
; SSE-NEXT:    store <4 x i32> [[TMP31]], ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 60), align 16
; SSE-NEXT:    br label [[FOR_BODY5:%.*]]
; SSE:       for.cond3:
; SSE-NEXT:    [[INDVARS_IV_NEXT:%.*]] = add nuw nsw i64 [[INDVARS_IV:%.*]], 1
; SSE-NEXT:    [[CMP4:%.*]] = icmp ult i64 [[INDVARS_IV]], 63
; SSE-NEXT:    br i1 [[CMP4]], label [[FOR_BODY5]], label [[FOR_END14:%.*]]
; SSE:       for.body5:
; SSE-NEXT:    [[INDVARS_IV]] = phi i64 [ 0, [[ENTRY:%.*]] ], [ [[INDVARS_IV_NEXT]], [[FOR_COND3:%.*]] ]
; SSE-NEXT:    [[ARRAYIDX7:%.*]] = getelementptr inbounds [64 x i32], ptr @ia, i64 0, i64 [[INDVARS_IV]]
; SSE-NEXT:    [[TMP32:%.*]] = load i32, ptr [[ARRAYIDX7]], align 4
; SSE-NEXT:    [[ARRAYIDX9:%.*]] = getelementptr inbounds [64 x i32], ptr @ib, i64 0, i64 [[INDVARS_IV]]
; SSE-NEXT:    [[TMP33:%.*]] = load i32, ptr [[ARRAYIDX9]], align 4
; SSE-NEXT:    [[NEG10:%.*]] = xor i32 [[TMP33]], -1
; SSE-NEXT:    [[CMP11:%.*]] = icmp eq i32 [[TMP32]], [[NEG10]]
; SSE-NEXT:    br i1 [[CMP11]], label [[FOR_COND3]], label [[IF_THEN:%.*]]
; SSE:       if.then:
; SSE-NEXT:    tail call void @abort()
; SSE-NEXT:    unreachable
; SSE:       for.end14:
; SSE-NEXT:    ret i32 0
;
; AVX512-LABEL: @foo1(
; AVX512-NEXT:  entry:
; AVX512-NEXT:    [[TMP0:%.*]] = load <16 x i32>, ptr @ib, align 16
; AVX512-NEXT:    [[TMP1:%.*]] = xor <16 x i32> [[TMP0]], splat (i32 -1)
; AVX512-NEXT:    store <16 x i32> [[TMP1]], ptr @ia, align 16
; AVX512-NEXT:    [[TMP2:%.*]] = load <16 x i32>, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 16), align 16
; AVX512-NEXT:    [[TMP3:%.*]] = xor <16 x i32> [[TMP2]], splat (i32 -1)
; AVX512-NEXT:    store <16 x i32> [[TMP3]], ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 16), align 16
; AVX512-NEXT:    [[TMP4:%.*]] = load <16 x i32>, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 32), align 16
; AVX512-NEXT:    [[TMP5:%.*]] = xor <16 x i32> [[TMP4]], splat (i32 -1)
; AVX512-NEXT:    store <16 x i32> [[TMP5]], ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 32), align 16
; AVX512-NEXT:    [[TMP6:%.*]] = load <16 x i32>, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 48), align 16
; AVX512-NEXT:    [[TMP7:%.*]] = xor <16 x i32> [[TMP6]], splat (i32 -1)
; AVX512-NEXT:    store <16 x i32> [[TMP7]], ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 48), align 16
; AVX512-NEXT:    br label [[FOR_BODY5:%.*]]
; AVX512:       for.cond3:
; AVX512-NEXT:    [[INDVARS_IV_NEXT:%.*]] = add nuw nsw i64 [[INDVARS_IV:%.*]], 1
; AVX512-NEXT:    [[CMP4:%.*]] = icmp ult i64 [[INDVARS_IV]], 63
; AVX512-NEXT:    br i1 [[CMP4]], label [[FOR_BODY5]], label [[FOR_END14:%.*]]
; AVX512:       for.body5:
; AVX512-NEXT:    [[INDVARS_IV]] = phi i64 [ 0, [[ENTRY:%.*]] ], [ [[INDVARS_IV_NEXT]], [[FOR_COND3:%.*]] ]
; AVX512-NEXT:    [[ARRAYIDX7:%.*]] = getelementptr inbounds [64 x i32], ptr @ia, i64 0, i64 [[INDVARS_IV]]
; AVX512-NEXT:    [[TMP8:%.*]] = load i32, ptr [[ARRAYIDX7]], align 4
; AVX512-NEXT:    [[ARRAYIDX9:%.*]] = getelementptr inbounds [64 x i32], ptr @ib, i64 0, i64 [[INDVARS_IV]]
; AVX512-NEXT:    [[TMP9:%.*]] = load i32, ptr [[ARRAYIDX9]], align 4
; AVX512-NEXT:    [[NEG10:%.*]] = xor i32 [[TMP9]], -1
; AVX512-NEXT:    [[CMP11:%.*]] = icmp eq i32 [[TMP8]], [[NEG10]]
; AVX512-NEXT:    br i1 [[CMP11]], label [[FOR_COND3]], label [[IF_THEN:%.*]]
; AVX512:       if.then:
; AVX512-NEXT:    tail call void @abort()
; AVX512-NEXT:    unreachable
; AVX512:       for.end14:
; AVX512-NEXT:    ret i32 0
;
entry:
  %0 = load i32, ptr @ib, align 16
  %neg = xor i32 %0, -1
  store i32 %neg, ptr @ia, align 16
  %1 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 1), align 4
  %neg.1 = xor i32 %1, -1
  store i32 %neg.1, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 1), align 4
  %2 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 2), align 8
  %neg.2 = xor i32 %2, -1
  store i32 %neg.2, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 2), align 8
  %3 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 3), align 4
  %neg.3 = xor i32 %3, -1
  store i32 %neg.3, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 3), align 4
  %4 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 4), align 16
  %neg.4 = xor i32 %4, -1
  store i32 %neg.4, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 4), align 16
  %5 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 5), align 4
  %neg.5 = xor i32 %5, -1
  store i32 %neg.5, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 5), align 4
  %6 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 6), align 8
  %neg.6 = xor i32 %6, -1
  store i32 %neg.6, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 6), align 8
  %7 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 7), align 4
  %neg.7 = xor i32 %7, -1
  store i32 %neg.7, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 7), align 4
  %8 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 8), align 16
  %neg.8 = xor i32 %8, -1
  store i32 %neg.8, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 8), align 16
  %9 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 9), align 4
  %neg.9 = xor i32 %9, -1
  store i32 %neg.9, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 9), align 4
  %10 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 10), align 8
  %neg.10 = xor i32 %10, -1
  store i32 %neg.10, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 10), align 8
  %11 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 11), align 4
  %neg.11 = xor i32 %11, -1
  store i32 %neg.11, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 11), align 4
  %12 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 12), align 16
  %neg.12 = xor i32 %12, -1
  store i32 %neg.12, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 12), align 16
  %13 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 13), align 4
  %neg.13 = xor i32 %13, -1
  store i32 %neg.13, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 13), align 4
  %14 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 14), align 8
  %neg.14 = xor i32 %14, -1
  store i32 %neg.14, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 14), align 8
  %15 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 15), align 4
  %neg.15 = xor i32 %15, -1
  store i32 %neg.15, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 15), align 4
  %16 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 16), align 16
  %neg.16 = xor i32 %16, -1
  store i32 %neg.16, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 16), align 16
  %17 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 17), align 4
  %neg.17 = xor i32 %17, -1
  store i32 %neg.17, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 17), align 4
  %18 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 18), align 8
  %neg.18 = xor i32 %18, -1
  store i32 %neg.18, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 18), align 8
  %19 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 19), align 4
  %neg.19 = xor i32 %19, -1
  store i32 %neg.19, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 19), align 4
  %20 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 20), align 16
  %neg.20 = xor i32 %20, -1
  store i32 %neg.20, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 20), align 16
  %21 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 21), align 4
  %neg.21 = xor i32 %21, -1
  store i32 %neg.21, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 21), align 4
  %22 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 22), align 8
  %neg.22 = xor i32 %22, -1
  store i32 %neg.22, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 22), align 8
  %23 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 23), align 4
  %neg.23 = xor i32 %23, -1
  store i32 %neg.23, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 23), align 4
  %24 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 24), align 16
  %neg.24 = xor i32 %24, -1
  store i32 %neg.24, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 24), align 16
  %25 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 25), align 4
  %neg.25 = xor i32 %25, -1
  store i32 %neg.25, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 25), align 4
  %26 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 26), align 8
  %neg.26 = xor i32 %26, -1
  store i32 %neg.26, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 26), align 8
  %27 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 27), align 4
  %neg.27 = xor i32 %27, -1
  store i32 %neg.27, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 27), align 4
  %28 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 28), align 16
  %neg.28 = xor i32 %28, -1
  store i32 %neg.28, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 28), align 16
  %29 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 29), align 4
  %neg.29 = xor i32 %29, -1
  store i32 %neg.29, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 29), align 4
  %30 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 30), align 8
  %neg.30 = xor i32 %30, -1
  store i32 %neg.30, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 30), align 8
  %31 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 31), align 4
  %neg.31 = xor i32 %31, -1
  store i32 %neg.31, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 31), align 4
  %32 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 32), align 16
  %neg.32 = xor i32 %32, -1
  store i32 %neg.32, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 32), align 16
  %33 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 33), align 4
  %neg.33 = xor i32 %33, -1
  store i32 %neg.33, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 33), align 4
  %34 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 34), align 8
  %neg.34 = xor i32 %34, -1
  store i32 %neg.34, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 34), align 8
  %35 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 35), align 4
  %neg.35 = xor i32 %35, -1
  store i32 %neg.35, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 35), align 4
  %36 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 36), align 16
  %neg.36 = xor i32 %36, -1
  store i32 %neg.36, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 36), align 16
  %37 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 37), align 4
  %neg.37 = xor i32 %37, -1
  store i32 %neg.37, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 37), align 4
  %38 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 38), align 8
  %neg.38 = xor i32 %38, -1
  store i32 %neg.38, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 38), align 8
  %39 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 39), align 4
  %neg.39 = xor i32 %39, -1
  store i32 %neg.39, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 39), align 4
  %40 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 40), align 16
  %neg.40 = xor i32 %40, -1
  store i32 %neg.40, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 40), align 16
  %41 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 41), align 4
  %neg.41 = xor i32 %41, -1
  store i32 %neg.41, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 41), align 4
  %42 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 42), align 8
  %neg.42 = xor i32 %42, -1
  store i32 %neg.42, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 42), align 8
  %43 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 43), align 4
  %neg.43 = xor i32 %43, -1
  store i32 %neg.43, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 43), align 4
  %44 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 44), align 16
  %neg.44 = xor i32 %44, -1
  store i32 %neg.44, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 44), align 16
  %45 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 45), align 4
  %neg.45 = xor i32 %45, -1
  store i32 %neg.45, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 45), align 4
  %46 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 46), align 8
  %neg.46 = xor i32 %46, -1
  store i32 %neg.46, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 46), align 8
  %47 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 47), align 4
  %neg.47 = xor i32 %47, -1
  store i32 %neg.47, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 47), align 4
  %48 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 48), align 16
  %neg.48 = xor i32 %48, -1
  store i32 %neg.48, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 48), align 16
  %49 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 49), align 4
  %neg.49 = xor i32 %49, -1
  store i32 %neg.49, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 49), align 4
  %50 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 50), align 8
  %neg.50 = xor i32 %50, -1
  store i32 %neg.50, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 50), align 8
  %51 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 51), align 4
  %neg.51 = xor i32 %51, -1
  store i32 %neg.51, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 51), align 4
  %52 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 52), align 16
  %neg.52 = xor i32 %52, -1
  store i32 %neg.52, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 52), align 16
  %53 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 53), align 4
  %neg.53 = xor i32 %53, -1
  store i32 %neg.53, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 53), align 4
  %54 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 54), align 8
  %neg.54 = xor i32 %54, -1
  store i32 %neg.54, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 54), align 8
  %55 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 55), align 4
  %neg.55 = xor i32 %55, -1
  store i32 %neg.55, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 55), align 4
  %56 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 56), align 16
  %neg.56 = xor i32 %56, -1
  store i32 %neg.56, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 56), align 16
  %57 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 57), align 4
  %neg.57 = xor i32 %57, -1
  store i32 %neg.57, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 57), align 4
  %58 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 58), align 8
  %neg.58 = xor i32 %58, -1
  store i32 %neg.58, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 58), align 8
  %59 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 59), align 4
  %neg.59 = xor i32 %59, -1
  store i32 %neg.59, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 59), align 4
  %60 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 60), align 16
  %neg.60 = xor i32 %60, -1
  store i32 %neg.60, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 60), align 16
  %61 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 61), align 4
  %neg.61 = xor i32 %61, -1
  store i32 %neg.61, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 61), align 4
  %62 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 62), align 8
  %neg.62 = xor i32 %62, -1
  store i32 %neg.62, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 62), align 8
  %63 = load i32, ptr getelementptr inbounds ([64 x i32], ptr @ib, i64 0, i64 63), align 4
  %neg.63 = xor i32 %63, -1
  store i32 %neg.63, ptr getelementptr inbounds ([64 x i32], ptr @ia, i64 0, i64 63), align 4
  br label %for.body5

for.cond3:                                        ; preds = %for.body5
  %indvars.iv.next = add nuw nsw i64 %indvars.iv, 1
  %cmp4 = icmp ult i64 %indvars.iv, 63
  br i1 %cmp4, label %for.body5, label %for.end14

for.body5:                                        ; preds = %entry, %for.cond3
  %indvars.iv = phi i64 [ 0, %entry ], [ %indvars.iv.next, %for.cond3 ]
  %arrayidx7 = getelementptr inbounds [64 x i32], ptr @ia, i64 0, i64 %indvars.iv
  %64 = load i32, ptr %arrayidx7, align 4
  %arrayidx9 = getelementptr inbounds [64 x i32], ptr @ib, i64 0, i64 %indvars.iv
  %65 = load i32, ptr %arrayidx9, align 4
  %neg10 = xor i32 %65, -1
  %cmp11 = icmp eq i32 %64, %neg10
  br i1 %cmp11, label %for.cond3, label %if.then

if.then:                                          ; preds = %for.body5
  tail call void @abort() #2
  unreachable

for.end14:                                        ; preds = %for.cond3
  ret i32 0
}

declare void @abort() #2
