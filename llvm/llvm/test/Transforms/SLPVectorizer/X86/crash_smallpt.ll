; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt < %s -passes=slp-vectorizer,dce -S -mtriple=x86_64-apple-macosx10.8.0 -mcpu=corei7 | FileCheck %s

%struct.Ray = type { %struct.Vec, %struct.Vec }
%struct.Vec = type { double, double, double }

define void @main(i1 %arg) {
; CHECK-LABEL: @main(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    br i1 %arg, label [[COND_TRUE:%.*]], label [[COND_END:%.*]]
; CHECK:       cond.true:
; CHECK-NEXT:    unreachable
; CHECK:       cond.end:
; CHECK-NEXT:    br label [[INVOKE_CONT:%.*]]
; CHECK:       invoke.cont:
; CHECK-NEXT:    br i1 %arg, label [[ARRAYCTOR_CONT:%.*]], label [[INVOKE_CONT]]
; CHECK:       arrayctor.cont:
; CHECK-NEXT:    [[AGG_TMP101211_SROA_0_0_IDX:%.*]] = getelementptr inbounds [[STRUCT_RAY:%.*]], ptr undef, i64 0, i32 1, i32 0
; CHECK-NEXT:    br label [[FOR_COND36_PREHEADER:%.*]]
; CHECK:       for.cond36.preheader:
; CHECK-NEXT:    br i1 %arg, label [[FOR_BODY42_LR_PH_US:%.*]], label [[_Z5CLAMPD_EXIT_1:%.*]]
; CHECK:       cond.false51.us:
; CHECK-NEXT:    unreachable
; CHECK:       cond.true48.us:
; CHECK-NEXT:    br i1 %arg, label [[COND_TRUE63_US:%.*]], label [[COND_FALSE66_US:%.*]]
; CHECK:       cond.false66.us:
; CHECK-NEXT:    [[ADD_I276_US:%.*]] = fadd double 0.000000e+00, 0x3EB0C6F7A0B5ED8D
; CHECK-NEXT:    [[TMP0:%.*]] = insertelement <2 x double> <double poison, double 0xBFA5CC2D1960285F>, double [[ADD_I276_US]], i32 0
; CHECK-NEXT:    [[TMP1:%.*]] = fadd <2 x double> <double 0.000000e+00, double 1.000000e-01>, [[TMP0]]
; CHECK-NEXT:    [[TMP2:%.*]] = fmul <2 x double> [[TMP1]], splat (double 1.400000e+02)
; CHECK-NEXT:    [[TMP3:%.*]] = fadd <2 x double> [[TMP2]], <double 5.000000e+01, double 5.200000e+01>
; CHECK-NEXT:    store <2 x double> [[TMP3]], ptr undef, align 8
; CHECK-NEXT:    [[TMP4:%.*]] = fmul <2 x double> <double 2.000000e-01, double 3.000000e-01>, [[TMP1]]
; CHECK-NEXT:    store <2 x double> [[TMP4]], ptr [[AGG_TMP101211_SROA_0_0_IDX]], align 8
; CHECK-NEXT:    ret void
; CHECK:       cond.true63.us:
; CHECK-NEXT:    unreachable
; CHECK:       for.body42.lr.ph.us:
; CHECK-NEXT:    br i1 %arg, label [[COND_TRUE48_US:%.*]], label [[COND_FALSE51_US:%.*]]
; CHECK:       _Z5clampd.exit.1:
; CHECK-NEXT:    br label [[FOR_COND36_PREHEADER]]
;
entry:
  br i1 %arg, label %cond.true, label %cond.end

cond.true:
  unreachable

cond.end:
  br label %invoke.cont

invoke.cont:
  br i1 %arg, label %arrayctor.cont, label %invoke.cont

arrayctor.cont:
  %agg.tmp99208.sroa.1.8.idx388 = getelementptr inbounds %struct.Ray, ptr undef, i64 0, i32 0, i32 1
  %agg.tmp101211.sroa.0.0.idx = getelementptr inbounds %struct.Ray, ptr undef, i64 0, i32 1, i32 0
  %agg.tmp101211.sroa.1.8.idx390 = getelementptr inbounds %struct.Ray, ptr undef, i64 0, i32 1, i32 1
  br label %for.cond36.preheader

for.cond36.preheader:
  br i1 %arg, label %for.body42.lr.ph.us, label %_Z5clampd.exit.1

cond.false51.us:
  unreachable

cond.true48.us:
  br i1 %arg, label %cond.true63.us, label %cond.false66.us

cond.false66.us:
  %add.i276.us = fadd double 0.000000e+00, 0.000001e+00
  %add.i264.us = fadd double %add.i276.us, 0.000000e+00
  %add4.i267.us = fadd double 0.1e+00, 0xBFA5CC2D1960285F
  %mul.i254.us = fmul double %add.i264.us, 1.400000e+02
  %mul2.i256.us = fmul double %add4.i267.us, 1.400000e+02
  %add.i243.us = fadd double %mul.i254.us, 5.000000e+01
  %add4.i246.us = fadd double %mul2.i256.us, 5.200000e+01
  %mul.i.i.us = fmul double 0.2e+00, %add.i264.us
  %mul2.i.i.us = fmul double 0.3e+00, %add4.i267.us
  store double %add.i243.us, ptr undef, align 8
  store double %add4.i246.us, ptr %agg.tmp99208.sroa.1.8.idx388, align 8
  store double %mul.i.i.us, ptr %agg.tmp101211.sroa.0.0.idx, align 8
  store double %mul2.i.i.us, ptr %agg.tmp101211.sroa.1.8.idx390, align 8
  ret void

cond.true63.us:
  unreachable

for.body42.lr.ph.us:
  br i1 %arg, label %cond.true48.us, label %cond.false51.us

_Z5clampd.exit.1:
  br label %for.cond36.preheader
}

define void @test(i1 %arg) {
; CHECK-LABEL: @test(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    br i1 %arg, label [[IF_THEN78:%.*]], label [[IF_THEN38:%.*]]
; CHECK:       if.then38:
; CHECK-NEXT:    [[AGG_TMP74663_SROA_0_0_IDX:%.*]] = getelementptr inbounds [[STRUCT_RAY:%.*]], ptr undef, i64 0, i32 1, i32 0
; CHECK-NEXT:    store <2 x double> <double 0x3FFA356C1D8A7F76, double 0x3FFDC4F38B38BEF4>, ptr [[AGG_TMP74663_SROA_0_0_IDX]], align 8
; CHECK-NEXT:    br label [[IF_THEN78]]
; CHECK:       if.then78:
; CHECK-NEXT:    ret void
;
entry:
  br i1 %arg, label %if.then78, label %if.then38

if.then38:
  %mul.i.i790 = fmul double 0.0, 0.1
  %mul3.i.i792 = fmul double 0.2, 0.3
  %mul.i764 = fmul double 0.4, %mul3.i.i792
  %mul4.i767 = fmul double 0.5, 0.6
  %sub.i768 = fsub double %mul.i764, %mul4.i767
  %mul6.i770 = fmul double 0.7, %mul.i.i790
  %mul9.i772 = fmul double 0.8, %mul3.i.i792
  %sub10.i773 = fsub double %mul6.i770, %mul9.i772
  %mul.i736 = fmul double 0.9, %sub.i768
  %mul2.i738 = fmul double 0.91, %sub10.i773
  %mul.i727 = fmul double 0.92, %mul.i736
  %mul2.i729 = fmul double 0.93, %mul2.i738
  %add.i716 = fadd double 0.94, %mul.i727
  %add4.i719 = fadd double 0.95, %mul2.i729
  %add.i695 = fadd double 0.96, %add.i716
  %add4.i698 = fadd double 0.97, %add4.i719
  %mul.i.i679 = fmul double 0.98, %add.i695
  %mul2.i.i680 = fmul double 0.99, %add4.i698
  %agg.tmp74663.sroa.0.0.idx = getelementptr inbounds %struct.Ray, ptr undef, i64 0, i32 1, i32 0
  store double %mul.i.i679, ptr %agg.tmp74663.sroa.0.0.idx, align 8
  %agg.tmp74663.sroa.1.8.idx943 = getelementptr inbounds %struct.Ray, ptr undef, i64 0, i32 1, i32 1
  store double %mul2.i.i680, ptr %agg.tmp74663.sroa.1.8.idx943, align 8
  br label %if.then78

if.then78:
  ret void
}

