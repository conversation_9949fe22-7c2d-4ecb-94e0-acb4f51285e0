; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown-linux-gnu < %s | FileCheck %s

define void @foo() {
; CHECK-LABEL: define void @foo() {
; CHECK-NEXT:  [[ENTRY:.*]]:
; CHECK-NEXT:      #dbg_value(float 0.000000e+00, [[META3:![0-9]+]], !DIExpression(), [[META5:![0-9]+]])
; CHECK-NEXT:    br label %[[FOR_BODY:.*]]
; CHECK:       [[FOR_BODY]]:
; CHECK-NEXT:    [[TMP0:%.*]] = phi <2 x float> [ zeroinitializer, %[[ENTRY]] ], [ zeroinitializer, %[[FOR_BODY]] ]
; CHECK-NEXT:    br label %[[FOR_BODY]]
;
entry:
  #dbg_value(float 0.000000e+00, !3, !DIExpression(), !5)
  br label %for.body

for.body:
  %s1.016 = phi float [ 0.000000e+00, %entry ], [ %cond7, %for.body ]
  %s0.015 = phi float [ 0.000000e+00, %entry ], [ %cond, %for.body ]
  %cond = select i1 false, float 0.000000e+00, float 0.000000e+00
  %cond7 = select i1 false, float 0.000000e+00, float 0.000000e+00
  br label %for.body
}

!llvm.dbg.cu = !{!0}
!llvm.module.flags = !{!2}

!0 = distinct !DICompileUnit(language: DW_LANG_C11, file: !1)
!1 = !DIFile(filename: "repro.c", directory: "/")
!2 = !{i32 2, !"Debug Info Version", i32 3}
!3 = !DILocalVariable(name: "s0", scope: !4)
!4 = distinct !DISubprogram(name: "foo", scope: !1, unit: !0)
!5 = !DILocation(line: 0, scope: !4)
;.
; CHECK: [[META0:![0-9]+]] = distinct !DICompileUnit(language: DW_LANG_C11, file: [[META1:![0-9]+]], isOptimized: false, runtimeVersion: 0, emissionKind: NoDebug)
; CHECK: [[META1]] = !DIFile(filename: "repro.c", directory: {{.*}})
; CHECK: [[META3]] = !DILocalVariable(name: "s0", scope: [[META4:![0-9]+]])
; CHECK: [[META4]] = distinct !DISubprogram(name: "foo", scope: [[META1]], spFlags: DISPFlagDefinition, unit: [[META0]])
; CHECK: [[META5]] = !DILocation(line: 0, scope: [[META4]])
;.
