; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -passes=slp-vectorizer -mattr=+avx512f -mtriple=x86_64 -S < %s | FileCheck %s

define void @test(ptr noalias %0, ptr %p) {
; CHECK-LABEL: @test(
; CHECK-NEXT:    [[TMP2:%.*]] = insertelement <8 x ptr> poison, ptr [[P:%.*]], i32 0
; CHECK-NEXT:    [[TMP3:%.*]] = shufflevector <8 x ptr> [[TMP2]], <8 x ptr> poison, <8 x i32> zeroinitializer
; CHECK-NEXT:    [[TMP4:%.*]] = getelementptr float, <8 x ptr> [[TMP3]], <8 x i64> <i64 15, i64 4, i64 5, i64 0, i64 2, i64 6, i64 7, i64 8>
; CHECK-NEXT:    [[TMP5:%.*]] = getelementptr inbounds float, ptr [[TMP0:%.*]], i64 2
; CHECK-NEXT:    [[TMP6:%.*]] = call <8 x float> @llvm.masked.gather.v8f32.v8p0(<8 x ptr> [[TMP4]], i32 4, <8 x i1> splat (i1 true), <8 x float> poison)
; CHECK-NEXT:    [[TMP7:%.*]] = shufflevector <8 x float> [[TMP6]], <8 x float> poison, <16 x i32> <i32 0, i32 1, i32 2, i32 0, i32 1, i32 2, i32 0, i32 3, i32 2, i32 4, i32 5, i32 6, i32 7, i32 5, i32 6, i32 7>
; CHECK-NEXT:    [[TMP8:%.*]] = shufflevector <8 x float> [[TMP6]], <8 x float> poison, <16 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7, i32 poison, i32 poison, i32 poison, i32 poison, i32 poison, i32 poison, i32 poison, i32 poison>
; CHECK-NEXT:    [[TMP9:%.*]] = shufflevector <16 x float> [[TMP8]], <16 x float> <float 0.000000e+00, float 0.000000e+00, float 0.000000e+00, float 0.000000e+00, float 0.000000e+00, float 0.000000e+00, float 0.000000e+00, float poison, float 0.000000e+00, float poison, float 0.000000e+00, float 0.000000e+00, float 0.000000e+00, float 0.000000e+00, float 0.000000e+00, float 0.000000e+00>, <16 x i32> <i32 16, i32 17, i32 18, i32 19, i32 20, i32 21, i32 22, i32 1, i32 24, i32 0, i32 26, i32 27, i32 28, i32 29, i32 30, i32 31>
; CHECK-NEXT:    [[TMP10:%.*]] = fadd reassoc nsz arcp contract afn <16 x float> [[TMP7]], [[TMP9]]
; CHECK-NEXT:    store <16 x float> [[TMP10]], ptr [[TMP5]], align 4
; CHECK-NEXT:    ret void
;
  %2 = getelementptr inbounds float, ptr %p, i64 2
  %3 = getelementptr inbounds float, ptr %p, i64 4
  %4 = load float, ptr %3, align 4
  %5 = getelementptr inbounds float, ptr %p, i64 5
  %6 = load float, ptr %5, align 16
  %7 = getelementptr inbounds float, ptr %p, i64 15
  %8 = load float, ptr %7, align 4
  %9 = fadd reassoc nsz arcp contract afn float %8, 0.000000e+00
  %10 = getelementptr inbounds float, ptr %0, i64 2
  store float %9, ptr %10, align 4
  %11 = fadd reassoc nsz arcp contract afn float %4, 0.000000e+00
  %12 = getelementptr inbounds float, ptr %0, i64 3
  store float %11, ptr %12, align 4
  %13 = fadd reassoc nsz arcp contract afn float %6, 0.000000e+00
  %14 = getelementptr inbounds float, ptr %0, i64 4
  store float %13, ptr %14, align 4
  %15 = fadd reassoc nsz arcp contract afn float %8, 0.000000e+00
  %16 = getelementptr inbounds float, ptr %0, i64 5
  store float %15, ptr %16, align 4
  %17 = fadd reassoc nsz arcp contract afn float %4, 0.000000e+00
  %18 = load float, ptr %p, align 16
  %19 = getelementptr inbounds float, ptr %0, i64 6
  store float %17, ptr %19, align 4
  %20 = fadd reassoc nsz arcp contract afn float %6, 0.000000e+00
  %21 = getelementptr inbounds float, ptr %0, i64 7
  store float %20, ptr %21, align 4
  %22 = fadd reassoc nsz arcp contract afn float %8, 0.000000e+00
  %23 = load float, ptr %2, align 8
  %24 = getelementptr inbounds float, ptr %0, i64 8
  store float %22, ptr %24, align 4
  %25 = fadd reassoc nsz arcp contract afn float %4, %18
  %26 = getelementptr inbounds float, ptr %0, i64 9
  store float %25, ptr %26, align 4
  %27 = fadd reassoc nsz arcp contract afn float %6, 0.000000e+00
  %28 = getelementptr inbounds float, ptr %0, i64 10
  store float %27, ptr %28, align 4
  %29 = fadd reassoc nsz arcp contract afn float %8, %23
  %30 = getelementptr inbounds float, ptr %0, i64 11
  store float %29, ptr %30, align 4
  %31 = getelementptr inbounds float, ptr %p, i64 6
  %32 = load float, ptr %31, align 4
  %33 = fadd reassoc nsz arcp contract afn float %32, 0.000000e+00
  %34 = getelementptr inbounds float, ptr %0, i64 12
  store float %33, ptr %34, align 4
  %35 = getelementptr inbounds float, ptr %p, i64 7
  %36 = load float, ptr %35, align 8
  %37 = fadd reassoc nsz arcp contract afn float %36, 0.000000e+00
  %38 = getelementptr inbounds float, ptr %0, i64 13
  store float %37, ptr %38, align 4
  %39 = getelementptr inbounds float, ptr %p, i64 8
  %40 = load float, ptr %39, align 4
  %41 = fadd reassoc nsz arcp contract afn float %40, 0.000000e+00
  %42 = getelementptr inbounds float, ptr %0, i64 14
  store float %41, ptr %42, align 4
  %43 = fadd reassoc nsz arcp contract afn float %32, 0.000000e+00
  %44 = getelementptr inbounds float, ptr %0, i64 15
  store float %43, ptr %44, align 4
  %45 = fadd reassoc nsz arcp contract afn float %36, 0.000000e+00
  %46 = getelementptr inbounds float, ptr %0, i64 16
  store float %45, ptr %46, align 4
  %47 = fadd reassoc nsz arcp contract afn float %40, 0.000000e+00
  %48 = getelementptr inbounds float, ptr %0, i64 17
  store float %47, ptr %48, align 4
  ret void
}
