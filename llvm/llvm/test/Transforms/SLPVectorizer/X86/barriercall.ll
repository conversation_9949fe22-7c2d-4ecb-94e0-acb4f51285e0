; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt < %s -passes=slp-vectorizer,dce -S -mtriple=x86_64-apple-macosx10.8.0 -mcpu=corei7-avx | FileCheck %s

target datalayout = "e-p:64:64:64-i1:8:8-i8:8:8-i16:16:16-i32:32:32-i64:64:64-f32:32:32-f64:64:64-v64:64:64-v128:128:128-a0:0:64-s0:64:64-f80:128:128-n8:16:32:64-S128"
target triple = "x86_64-apple-macosx10.8.0"

define i32 @foo(ptr nocapture %A, i32 %n) {
; CHECK-LABEL: @foo(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[CALL:%.*]] = tail call i32 (...) @bar()
; CHECK-NEXT:    [[TMP0:%.*]] = insertelement <4 x i32> poison, i32 [[N:%.*]], i32 0
; CHECK-NEXT:    [[SHUFFLE:%.*]] = shufflevector <4 x i32> [[TMP0]], <4 x i32> poison, <4 x i32> zeroinitializer
; CHECK-NEXT:    [[TMP1:%.*]] = mul nsw <4 x i32> [[SHUFFLE]], <i32 5, i32 9, i32 3, i32 10>
; CHECK-NEXT:    [[TMP2:%.*]] = shl <4 x i32> [[SHUFFLE]], <i32 5, i32 9, i32 3, i32 10>
; CHECK-NEXT:    [[TMP3:%.*]] = shufflevector <4 x i32> [[TMP1]], <4 x i32> [[TMP2]], <4 x i32> <i32 0, i32 1, i32 6, i32 3>
; CHECK-NEXT:    [[TMP4:%.*]] = add nsw <4 x i32> [[TMP3]], splat (i32 9)
; CHECK-NEXT:    store <4 x i32> [[TMP4]], ptr [[A:%.*]], align 4
; CHECK-NEXT:    ret i32 undef
;
entry:
  %call = tail call i32 (...) @bar() #2
  %mul = mul nsw i32 %n, 5
  %add = add nsw i32 %mul, 9
  store i32 %add, ptr %A, align 4
  %mul1 = mul nsw i32 %n, 9
  %add2 = add nsw i32 %mul1, 9
  %arrayidx3 = getelementptr inbounds i32, ptr %A, i64 1
  store i32 %add2, ptr %arrayidx3, align 4
  %mul4 = shl i32 %n, 3
  %add5 = add nsw i32 %mul4, 9
  %arrayidx6 = getelementptr inbounds i32, ptr %A, i64 2
  store i32 %add5, ptr %arrayidx6, align 4
  %mul7 = mul nsw i32 %n, 10
  %add8 = add nsw i32 %mul7, 9
  %arrayidx9 = getelementptr inbounds i32, ptr %A, i64 3
  store i32 %add8, ptr %arrayidx9, align 4
  ret i32 undef
}

  ; We can still vectorize the stores below.

declare i32 @bar(...)
