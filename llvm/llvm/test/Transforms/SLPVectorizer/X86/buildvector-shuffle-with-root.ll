; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S --passes=slp-vectorizer -slp-threshold=-99999 -mtriple=x86_64-unknown-linux-gnu < %s | FileCheck %s

define void @test(i16 %arg) {
; CHECK-LABEL: define void @test(
; CHECK-SAME: i16 [[ARG:%.*]]) {
; CHECK-NEXT:  [[BB:.*:]]
; CHECK-NEXT:    [[TMP0:%.*]] = insertelement <2 x i16> <i16 0, i16 poison>, i16 [[ARG]], i32 1
; CHECK-NEXT:    [[TMP1:%.*]] = sitofp <2 x i16> [[TMP0]] to <2 x float>
; CHECK-NEXT:    [[TMP2:%.*]] = shufflevector <2 x float> [[TMP1]], <2 x float> poison, <4 x i32> <i32 0, i32 1, i32 1, i32 1>
; CHECK-NEXT:    [[TMP3:%.*]] = shufflevector <4 x float> [[TMP2]], <4 x float> <float 0.000000e+00, float poison, float poison, float poison>, <4 x i32> <i32 4, i32 1, i32 poison, i32 poison>
; CHECK-NEXT:    [[TMP4:%.*]] = shufflevector <4 x float> [[TMP3]], <4 x float> poison, <4 x i32> <i32 0, i32 1, i32 1, i32 1>
; CHECK-NEXT:    [[TMP5:%.*]] = fadd <4 x float> zeroinitializer, [[TMP4]]
; CHECK-NEXT:    [[TMP6:%.*]] = fsub <4 x float> zeroinitializer, [[TMP4]]
; CHECK-NEXT:    [[TMP7:%.*]] = shufflevector <4 x float> [[TMP5]], <4 x float> [[TMP6]], <4 x i32> <i32 0, i32 5, i32 6, i32 7>
; CHECK-NEXT:    [[TMP8:%.*]] = fsub <4 x float> [[TMP7]], [[TMP2]]
; CHECK-NEXT:    store <4 x float> [[TMP8]], ptr addrspace(1) getelementptr inbounds (i8, ptr addrspace(1) null, i64 20), align 4
; CHECK-NEXT:    ret void
;
bb:
  %sitofp = sitofp i16 %arg to float
  %fadd = fadd float 0.000000e+00, 0.000000e+00
  %fsub = fsub float 0.000000e+00, %sitofp
  %fsub1 = fsub float 0.000000e+00, %sitofp
  %fsub2 = fsub float 0.000000e+00, %sitofp
  %sitofp3 = sitofp i16 0 to float
  %fsub4 = fsub float %fadd, %sitofp3
  store float %fsub4, ptr addrspace(1) getelementptr inbounds (i8, ptr addrspace(1) null, i64 20), align 4
  %fsub5 = fsub float %fsub, %sitofp
  store float %fsub5, ptr addrspace(1) getelementptr inbounds (i8, ptr addrspace(1) null, i64 24), align 4
  %fsub6 = fsub float %fsub1, %sitofp
  store float %fsub6, ptr addrspace(1) getelementptr inbounds (i8, ptr addrspace(1) null, i64 28), align 4
  %fsub7 = fsub float %fsub2, %sitofp
  store float %fsub7, ptr addrspace(1) getelementptr inbounds (i8, ptr addrspace(1) null, i64 32), align 4
  ret void
}
