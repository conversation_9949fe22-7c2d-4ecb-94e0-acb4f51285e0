; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt < %s -passes=slp-vectorizer -S -mtriple=x86_64-- -mcpu=corei7 | FileCheck %s

define void @foo(ptr %v0, ptr readonly %v1) {
; CHECK-LABEL: @foo(
; CHECK-NEXT:    [[T14:%.*]] = getelementptr inbounds i32, ptr [[V1:%.*]], i64 4
; CHECK-NEXT:    [[T142:%.*]] = getelementptr inbounds i64, ptr [[V1]], i64 8
; CHECK-NEXT:    [[T222:%.*]] = getelementptr inbounds i64, ptr [[V1]], i64 10
; CHECK-NEXT:    [[T21:%.*]] = getelementptr inbounds i32, ptr [[V0:%.*]], i64 4
; CHECK-NEXT:    [[T212:%.*]] = getelementptr inbounds i64, ptr [[V0]], i64 8
; CHECK-NEXT:    [[T292:%.*]] = getelementptr inbounds i64, ptr [[V0]], i64 10
; CHECK-NEXT:    [[TMP1:%.*]] = load <4 x i32>, ptr [[T14]], align 4
; CHECK-NEXT:    [[TMP2:%.*]] = add nsw <4 x i32> [[TMP1]], <i32 4, i32 4, i32 6, i32 7>
; CHECK-NEXT:    [[TMP3:%.*]] = load <2 x i64>, ptr [[T142]], align 8
; CHECK-NEXT:    [[TMP4:%.*]] = add nsw <2 x i64> [[TMP3]], splat (i64 4)
; CHECK-NEXT:    [[TMP5:%.*]] = load <2 x i64>, ptr [[T222]], align 8
; CHECK-NEXT:    [[TMP6:%.*]] = add nsw <2 x i64> [[TMP5]], <i64 6, i64 7>
; CHECK-NEXT:    store <2 x i64> [[TMP4]], ptr [[T212]], align 8
; CHECK-NEXT:    store <2 x i64> [[TMP6]], ptr [[T292]], align 8
; CHECK-NEXT:    store <4 x i32> [[TMP2]], ptr [[T21]], align 4
; CHECK-NEXT:    ret void
;


  %t14 = getelementptr inbounds i32, ptr %v1, i64 4
  %t18 = getelementptr inbounds i32, ptr %v1, i64 5
  %t22 = getelementptr inbounds i32, ptr %v1, i64 6
  %t26 = getelementptr inbounds i32, ptr %v1, i64 7

  %t142 = getelementptr inbounds i64, ptr %v1, i64 8
  %t182 = getelementptr inbounds i64, ptr %v1, i64 9
  %t222 = getelementptr inbounds i64, ptr %v1, i64 10
  %t262 = getelementptr inbounds i64, ptr %v1, i64 11

  %t21 = getelementptr inbounds i32, ptr %v0, i64 4
  %t25 = getelementptr inbounds i32, ptr %v0, i64 5
  %t29 = getelementptr inbounds i32, ptr %v0, i64 6
  %t32 = getelementptr inbounds i32, ptr %v0, i64 7

  %t212 = getelementptr inbounds i64, ptr %v0, i64 8
  %t252 = getelementptr inbounds i64, ptr %v0, i64 9
  %t292 = getelementptr inbounds i64, ptr %v0, i64 10
  %t322 = getelementptr inbounds i64, ptr %v0, i64 11

  %t19 = load i32, ptr %t14, align 4
  %t23 = load i32, ptr %t18, align 4
  %t27 = load i32, ptr %t22, align 4
  %t30 = load i32, ptr %t26, align 4

  %t192 = load i64, ptr %t142, align 8
  %t232 = load i64, ptr %t182, align 8
  %t272 = load i64, ptr %t222, align 8
  %t302 = load i64, ptr %t262, align 8

  %t20 = add nsw i32 %t19, 4
  %t24 = add nsw i32 %t23, 4
  %t28 = add nsw i32 %t27, 6
  %t31 = add nsw i32 %t30, 7

  %t202 = add nsw i64 %t192, 4
  %t242 = add nsw i64 %t232, 4
  %t282 = add nsw i64 %t272, 6
  %t312 = add nsw i64 %t302, 7

  store i64 %t202, ptr %t212, align 8
  store i64 %t242, ptr %t252, align 8
  store i64 %t282, ptr %t292, align 8
  store i64 %t312, ptr %t322, align 8

  store i32 %t20, ptr %t21, align 4
  store i32 %t24, ptr %t25, align 4
  store i32 %t28, ptr %t29, align 4
  store i32 %t31, ptr %t32, align 4

  ret void
}
