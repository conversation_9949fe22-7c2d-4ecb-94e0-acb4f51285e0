; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S --passes=slp-vectorizer -slp-threshold=-99999 < %s -mtriple=x86_64-unknown-linux-gnu | FileCheck %s

define void @test() {
; CHECK-LABEL: define void @test() {
; CHECK-NEXT:  [[BB:.*]]:
; CHECK-NEXT:    [[TRUNC:%.*]] = trunc i64 0 to i32
; CHECK-NEXT:    br label %[[BB1:.*]]
; CHECK:       [[BB1]]:
; CHECK-NEXT:    [[TMP0:%.*]] = phi <2 x i32> [ zeroinitializer, %[[BB]] ], [ [[TMP4:%.*]], %[[BB1]] ]
; CHECK-NEXT:    [[TMP1:%.*]] = extractelement <2 x i32> [[TMP0]], i32 1
; CHECK-NEXT:    [[TMP2:%.*]] = call i8 @llvm.vector.reduce.mul.v4i8(<4 x i8> zeroinitializer)
; CHECK-NEXT:    [[TMP3:%.*]] = zext i8 [[TMP2]] to i32
; CHECK-NEXT:    [[OP_RDX:%.*]] = mul i32 [[TMP3]], [[TMP1]]
; CHECK-NEXT:    [[OP_RDX1:%.*]] = mul i32 [[OP_RDX]], [[TRUNC]]
; CHECK-NEXT:    [[TMP4]] = insertelement <2 x i32> <i32 0, i32 poison>, i32 [[OP_RDX1]], i32 1
; CHECK-NEXT:    br label %[[BB1]]
;
bb:
  br label %bb1

bb1:
  %phi = phi i32 [ 0, %bb ], [ %mul9, %bb1 ]
  %phi2 = phi i32 [ 0, %bb ], [ 0, %bb1 ]
  %trunc = trunc i64 0 to i32
  %mul = mul i32 0, %trunc
  %mul3 = mul i32 %trunc, %phi
  %mul4 = mul i32 %mul3, %mul
  %mul5 = mul i32 %mul4, %mul
  %trunc6 = trunc i64 0 to i32
  %mul7 = mul i32 0, %trunc6
  %mul8 = mul i32 %mul5, %mul7
  %mul9 = mul i32 %mul8, %mul7
  br label %bb1
}
