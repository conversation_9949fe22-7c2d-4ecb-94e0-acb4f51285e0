; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 2
; RUN: opt -passes=slp-vectorizer -S < %s -mtriple=x86_64-unknown-linux-gnu -mcpu=skylake -slp-threshold=-10 | FileCheck %s

define double @test01() {
; CHECK-LABEL: define double @test01
; CHECK-SAME: () #[[ATTR0:[0-9]+]] {
; CHECK-NEXT:    [[TMP1:%.*]] = load <2 x i32>, ptr null, align 8
; CHECK-NEXT:    [[TMP2:%.*]] = getelementptr double, <2 x ptr> zeroinitializer, <2 x i32> [[TMP1]]
; CHECK-NEXT:    [[TMP3:%.*]] = call <2 x double> @llvm.masked.gather.v2f64.v2p0(<2 x ptr> [[TMP2]], i32 8, <2 x i1> splat (i1 true), <2 x double> poison)
; CHECK-NEXT:    [[TMP4:%.*]] = shufflevector <2 x double> [[TMP3]], <2 x double> <double 0.000000e+00, double poison>, <2 x i32> <i32 2, i32 0>
; CHECK-NEXT:    [[TMP5:%.*]] = fadd <2 x double> [[TMP4]], [[TMP4]]
; CHECK-NEXT:    [[TMP6:%.*]] = fadd <2 x double> [[TMP3]], [[TMP5]]
; CHECK-NEXT:    [[TMP7:%.*]] = extractelement <2 x double> [[TMP6]], i32 0
; CHECK-NEXT:    [[TMP8:%.*]] = extractelement <2 x double> [[TMP6]], i32 1
; CHECK-NEXT:    [[TMP9:%.*]] = fadd double [[TMP7]], [[TMP8]]
; CHECK-NEXT:    ret double [[TMP9]]
;
  %1 = load i32, ptr null, align 8
  %2 = load i32, ptr getelementptr inbounds (i32, ptr null, i32 1), align 4
  %3 = getelementptr double, ptr null, i32 %2
  %4 = load double, ptr %3, align 8
  %5 = getelementptr double, ptr null, i32 %1
  %6 = load double, ptr %5, align 8
  %7 = fadd double %6, %6
  %8 = fadd double %4, %7
  %9 = fadd double 0.000000e+00, 0.000000e+00
  %10 = fadd double %6, %9
  %11 = fadd double %10, %8
  ret double %11
}

