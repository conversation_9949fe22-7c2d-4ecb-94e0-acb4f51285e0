; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S --mtriple=x86_64-yoe-linux --passes=slp-vectorizer < %s | FileCheck %s

declare void @fn(ptr)

define void @test(ptr %0, i32 %1, i1 %2) {
; CHECK-LABEL: define void @test(
; CHECK-SAME: ptr [[TMP0:%.*]], i32 [[TMP1:%.*]], i1 [[TMP2:%.*]]) {
; CHECK-NEXT:    [[TMP4:%.*]] = icmp eq ptr [[TMP0]], null
; CHECK-NEXT:    [[TMP5:%.*]] = select i1 [[TMP4]], i64 0, i64 33554432
; CHECK-NEXT:    [[TMP6:%.*]] = select i1 [[TMP2]], i64 0, i64 67108864
; CHECK-NEXT:    [[TMP7:%.*]] = icmp eq i32 [[TMP1]], 0
; CHECK-NEXT:    [[TMP8:%.*]] = select i1 [[TMP7]], i64 0, i64 134217728
; CHECK-NEXT:    [[TMP9:%.*]] = icmp eq i32 [[TMP1]], 0
; CHECK-NEXT:    [[TMP10:%.*]] = select i1 [[TMP9]], i64 0, i64 268435456
; CHECK-NEXT:    [[TMP11:%.*]] = or i64 [[TMP5]], [[TMP6]]
; CHECK-NEXT:    [[TMP12:%.*]] = or i64 [[TMP11]], [[TMP8]]
; CHECK-NEXT:    [[TMP13:%.*]] = or i64 [[TMP12]], [[TMP10]]
; CHECK-NEXT:    [[TMP14:%.*]] = inttoptr i64 [[TMP13]] to ptr
; CHECK-NEXT:    call void @fn(ptr [[TMP14]])
; CHECK-NEXT:    ret void
;
  %4 = icmp eq ptr %0, null
  %5 = select i1 %4, i64 0, i64 33554432
  %6 = select i1 %2, i64 0, i64 67108864
  %7 = icmp eq i32 %1, 0
  %8 = select i1 %7, i64 0, i64 134217728
  %9 = icmp eq i32 %1, 0
  %10 = select i1 %9, i64 0, i64 268435456
  %11 = or i64 %5, %6
  %12 = or i64 %11, %8
  %13 = or i64 %12, %10
  %14 = inttoptr i64 %13 to ptr
  call void @fn(ptr %14)
  ret void
}
