; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt < %s -passes=slp-vectorizer -S | FileCheck %s
; RUN: opt < %s -passes=slp-vectorizer -S -mattr=+avx | FileCheck %s

target datalayout = "e-m:o-i64:64-f80:128-n8:16:32:64-S128"
target triple = "x86_64-apple-macosx10.10.0"

define void @testfunc(ptr nocapture %dest, ptr nocapture readonly %src) {
; CHECK-LABEL: @testfunc(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    br label [[FOR_BODY:%.*]]
; CHECK:       for.body:
; CHECK-NEXT:    [[INDVARS_IV:%.*]] = phi i64 [ 0, [[ENTRY:%.*]] ], [ [[INDVARS_IV_NEXT:%.*]], [[FOR_BODY]] ]
; CHECK-NEXT:    [[ACC1_056:%.*]] = phi float [ 0.000000e+00, [[ENTRY]] ], [ [[ADD13:%.*]], [[FOR_BODY]] ]
; CHECK-NEXT:    [[TMP0:%.*]] = phi <2 x float> [ zeroinitializer, [[ENTRY]] ], [ [[TMP18:%.*]], [[FOR_BODY]] ]
; CHECK-NEXT:    [[ARRAYIDX:%.*]] = getelementptr inbounds float, ptr [[SRC:%.*]], i64 [[INDVARS_IV]]
; CHECK-NEXT:    [[TMP1:%.*]] = load float, ptr [[ARRAYIDX]], align 4
; CHECK-NEXT:    [[INDVARS_IV_NEXT]] = add nuw nsw i64 [[INDVARS_IV]], 1
; CHECK-NEXT:    [[ARRAYIDX2:%.*]] = getelementptr inbounds float, ptr [[DEST:%.*]], i64 [[INDVARS_IV]]
; CHECK-NEXT:    store float [[ACC1_056]], ptr [[ARRAYIDX2]], align 4
; CHECK-NEXT:    [[TMP2:%.*]] = insertelement <2 x float> poison, float [[TMP1]], i32 0
; CHECK-NEXT:    [[SHUFFLE:%.*]] = shufflevector <2 x float> [[TMP2]], <2 x float> poison, <2 x i32> zeroinitializer
; CHECK-NEXT:    [[TMP3:%.*]] = fadd <2 x float> [[TMP0]], [[SHUFFLE]]
; CHECK-NEXT:    [[SHUFFLE1:%.*]] = shufflevector <2 x float> [[TMP3]], <2 x float> poison, <2 x i32> <i32 1, i32 0>
; CHECK-NEXT:    [[TMP4:%.*]] = fmul <2 x float> [[TMP0]], zeroinitializer
; CHECK-NEXT:    [[TMP5:%.*]] = fadd <2 x float> [[TMP4]], [[SHUFFLE1]]
; CHECK-NEXT:    [[TMP8:%.*]] = fcmp olt <2 x float> [[TMP5]], splat (float 1.000000e+00)
; CHECK-NEXT:    [[TMP7:%.*]] = select <2 x i1> [[TMP8]], <2 x float> [[TMP5]], <2 x float> splat (float 1.000000e+00)
; CHECK-NEXT:    [[TMP15:%.*]] = fcmp olt <2 x float> [[TMP7]], splat (float -1.000000e+00)
; CHECK-NEXT:    [[TMP9:%.*]] = fmul <2 x float> [[TMP7]], zeroinitializer
; CHECK-NEXT:    [[TMP10:%.*]] = select <2 x i1> [[TMP15]], <2 x float> splat (float -0.000000e+00), <2 x float> [[TMP9]]
; CHECK-NEXT:    [[TMP11:%.*]] = extractelement <2 x float> [[TMP10]], i32 0
; CHECK-NEXT:    [[TMP12:%.*]] = extractelement <2 x float> [[TMP10]], i32 1
; CHECK-NEXT:    [[ADD13]] = fadd float [[TMP11]], [[TMP12]]
; CHECK-NEXT:    [[TMP13:%.*]] = shufflevector <2 x float> [[TMP10]], <2 x float> poison, <2 x i32> <i32 1, i32 poison>
; CHECK-NEXT:    [[TMP14:%.*]] = insertelement <2 x float> [[TMP13]], float [[ADD13]], i32 1
; CHECK-NEXT:    [[TMP17:%.*]] = fcmp olt <2 x float> [[TMP14]], splat (float 1.000000e+00)
; CHECK-NEXT:    [[TMP20:%.*]] = select <2 x i1> [[TMP17]], <2 x float> [[TMP14]], <2 x float> splat (float 1.000000e+00)
; CHECK-NEXT:    [[TMP19:%.*]] = fcmp olt <2 x float> [[TMP20]], splat (float -1.000000e+00)
; CHECK-NEXT:    [[TMP18]] = select <2 x i1> [[TMP19]], <2 x float> splat (float -1.000000e+00), <2 x float> [[TMP20]]
; CHECK-NEXT:    [[EXITCOND:%.*]] = icmp eq i64 [[INDVARS_IV_NEXT]], 32
; CHECK-NEXT:    br i1 [[EXITCOND]], label [[FOR_END:%.*]], label [[FOR_BODY]]
; CHECK:       for.end:
; CHECK-NEXT:    ret void
;
entry:
  br label %for.body

for.body:
  %indvars.iv = phi i64 [ 0, %entry ], [ %indvars.iv.next, %for.body ]
  %acc1.056 = phi float [ 0.000000e+00, %entry ], [ %add13, %for.body ]
  %s1.055 = phi float [ 0.000000e+00, %entry ], [ %cond.i40, %for.body ]
  %s0.054 = phi float [ 0.000000e+00, %entry ], [ %cond.i44, %for.body ]
  %arrayidx = getelementptr inbounds float, ptr %src, i64 %indvars.iv
  %0 = load float, ptr %arrayidx, align 4
  %indvars.iv.next = add nuw nsw i64 %indvars.iv, 1
  %arrayidx2 = getelementptr inbounds float, ptr %dest, i64 %indvars.iv
  store float %acc1.056, ptr %arrayidx2, align 4
  %add = fadd float %s0.054, %0
  %add3 = fadd float %s1.055, %0
  %mul = fmul float %s0.054, 0.000000e+00
  %add4 = fadd float %mul, %add3
  %mul5 = fmul float %s1.055, 0.000000e+00
  %add6 = fadd float %mul5, %add
  %cmp.i = fcmp olt float %add6, 1.000000e+00
  %cond.i = select i1 %cmp.i, float %add6, float 1.000000e+00
  %cmp.i51 = fcmp olt float %cond.i, -1.000000e+00
  %cmp.i49 = fcmp olt float %add4, 1.000000e+00
  %cond.i50 = select i1 %cmp.i49, float %add4, float 1.000000e+00
  %cmp.i47 = fcmp olt float %cond.i50, -1.000000e+00
  %cond.i.op = fmul float %cond.i, 0.000000e+00
  %mul10 = select i1 %cmp.i51, float -0.000000e+00, float %cond.i.op
  %cond.i50.op = fmul float %cond.i50, 0.000000e+00
  %mul11 = select i1 %cmp.i47, float -0.000000e+00, float %cond.i50.op
  %add13 = fadd float %mul10, %mul11

  ; The SLPVectorizer crashed in vectorizeChainsInBlock() because it tried
  ; to access the second operand of the following cmp after the cmp itself
  ; was already vectorized and deleted.
  %cmp.i45 = fcmp olt float %add13, 1.000000e+00

  %cond.i46 = select i1 %cmp.i45, float %add13, float 1.000000e+00
  %cmp.i43 = fcmp olt float %cond.i46, -1.000000e+00
  %cond.i44 = select i1 %cmp.i43, float -1.000000e+00, float %cond.i46
  %cmp.i41 = fcmp olt float %mul11, 1.000000e+00
  %cond.i42 = select i1 %cmp.i41, float %mul11, float 1.000000e+00
  %cmp.i39 = fcmp olt float %cond.i42, -1.000000e+00
  %cond.i40 = select i1 %cmp.i39, float -1.000000e+00, float %cond.i42
  %exitcond = icmp eq i64 %indvars.iv.next, 32
  br i1 %exitcond, label %for.end, label %for.body

for.end:
  ret void
}

