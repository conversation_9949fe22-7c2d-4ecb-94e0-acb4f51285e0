; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -passes=slp-vectorizer -S -mtriple=x86_64 < %s -mattr=+avx512vl | FileCheck %s

define void @test(ptr %p, i32 %a, i32 %b, i32 %c, i32 %d, i32 %e, i32 %f) {
; CHECK-LABEL: @test(
; CHECK-NEXT:    [[TMP1:%.*]] = insertelement <2 x i32> poison, i32 [[A:%.*]], i32 0
; CHECK-NEXT:    [[TMP2:%.*]] = insertelement <2 x i32> [[TMP1]], i32 [[B:%.*]], i32 1
; CHECK-NEXT:    [[TMP3:%.*]] = sub <2 x i32> [[TMP2]], splat (i32 1)
; CHECK-NEXT:    [[TMP4:%.*]] = shufflevector <2 x i32> [[TMP3]], <2 x i32> poison, <4 x i32> <i32 0, i32 1, i32 0, i32 1>
; CHECK-NEXT:    [[TMP5:%.*]] = insertelement <4 x i32> poison, i32 [[E:%.*]], i32 0
; CHECK-NEXT:    [[TMP6:%.*]] = insertelement <4 x i32> [[TMP5]], i32 [[F:%.*]], i32 1
; CHECK-NEXT:    [[TMP7:%.*]] = insertelement <4 x i32> [[TMP6]], i32 [[C:%.*]], i32 2
; CHECK-NEXT:    [[TMP8:%.*]] = insertelement <4 x i32> [[TMP7]], i32 [[D:%.*]], i32 3
; CHECK-NEXT:    [[TMP9:%.*]] = mul <4 x i32> [[TMP4]], [[TMP8]]
; CHECK-NEXT:    [[TMP10:%.*]] = shufflevector <2 x i32> [[TMP2]], <2 x i32> poison, <4 x i32> <i32 0, i32 1, i32 poison, i32 poison>
; CHECK-NEXT:    [[TMP11:%.*]] = shufflevector <4 x i32> [[TMP10]], <4 x i32> [[TMP8]], <4 x i32> <i32 0, i32 1, i32 6, i32 7>
; CHECK-NEXT:    [[TMP12:%.*]] = add <4 x i32> [[TMP11]], [[TMP9]]
; CHECK-NEXT:    store <4 x i32> [[TMP12]], ptr [[P:%.*]], align 4
; CHECK-NEXT:    ret void
;
  %s1 = sub i32 %a, 1
  %s2 = sub i32 %b, 1
  %m1 = mul i32 %s1, %e
  %m2 = mul i32 %s2, %f
  %m3 = mul i32 %s1, %c
  %m4 = mul i32 %s2, %d
  %a1 = add i32 %a, %m1
  %a2 = add i32 %b, %m2
  %a3 = add i32 %c, %m3
  %a4 = add i32 %d, %m4
  store i32 %a1, ptr %p, align 4
  %gep = getelementptr inbounds i32, ptr %p, i32 1
  store i32 %a2, ptr %gep, align 4
  %gep1 = getelementptr inbounds i32, ptr %p, i32 2
  store i32 %a3, ptr %gep1, align 4
  %gep2 = getelementptr inbounds i32, ptr %p, i32 3
  store i32 %a4, ptr %gep2, align 4
  ret void
}
