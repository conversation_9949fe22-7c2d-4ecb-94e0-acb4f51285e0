; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 4
; RUN: opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown-linux-gnu -mcpu=znver2 < %s | FileCheck %s

define void @test() {
; CHECK-LABEL: define void @test(
; CHECK-SAME: ) #[[ATTR0:[0-9]+]] {
; CHECK-NEXT:  bb:
; CHECK-NEXT:    br label [[BB1:%.*]]
; CHECK:       bb1:
; CHECK-NEXT:    [[PHI:%.*]] = phi i32 [ 0, [[BB:%.*]] ], [ [[TMP2:%.*]], [[BB1]] ]
; CHECK-NEXT:    [[TMP0:%.*]] = phi <2 x i32> [ zeroinitializer, [[BB]] ], [ [[TMP3:%.*]], [[BB1]] ]
; CHECK-NEXT:    [[TMP1:%.*]] = shufflevector <2 x i32> [[TMP0]], <2 x i32> poison, <32 x i32> <i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1>
; CHECK-NEXT:    [[TMP2]] = call i32 @llvm.vector.reduce.mul.v32i32(<32 x i32> [[TMP1]])
; CHECK-NEXT:    [[TMP3]] = shufflevector <2 x i32> [[TMP0]], <2 x i32> poison, <2 x i32> <i32 1, i32 0>
; CHECK-NEXT:    br label [[BB1]]
;
bb:
  br label %bb1

bb1:
  %phi = phi i32 [ 0, %bb ], [ %mul33, %bb1 ]
  %phi2 = phi i32 [ 0, %bb ], [ %phi3, %bb1 ]
  %phi3 = phi i32 [ 0, %bb ], [ %phi2, %bb1 ]
  %mul = mul i32 %phi2, %phi2
  %mul4 = mul i32 %phi3, %mul
  %mul5 = mul i32 %phi2, %mul4
  %mul6 = mul i32 %phi3, %mul5
  %mul7 = mul i32 %phi2, %mul6
  %mul8 = mul i32 %phi3, %mul7
  %mul9 = mul i32 %phi2, %mul8
  %mul10 = mul i32 %phi3, %mul9
  %mul11 = mul i32 %phi2, %mul10
  %mul12 = mul i32 %phi3, %mul11
  %mul13 = mul i32 %phi2, %mul12
  %mul14 = mul i32 %phi3, %mul13
  %mul15 = mul i32 %phi2, %mul14
  %mul16 = mul i32 %phi3, %mul15
  %mul17 = mul i32 %phi2, %mul16
  %mul18 = mul i32 %phi3, %mul17
  %mul19 = mul i32 %phi2, %mul18
  %mul20 = mul i32 %phi3, %mul19
  %mul21 = mul i32 %phi2, %mul20
  %mul22 = mul i32 %phi3, %mul21
  %mul23 = mul i32 %phi2, %mul22
  %mul24 = mul i32 %phi3, %mul23
  %mul25 = mul i32 %phi2, %mul24
  %mul26 = mul i32 %phi3, %mul25
  %mul27 = mul i32 %phi2, %mul26
  %mul28 = mul i32 %phi3, %mul27
  %mul29 = mul i32 %phi2, %mul28
  %mul30 = mul i32 %phi3, %mul29
  %mul31 = mul i32 %phi2, %mul30
  %mul32 = mul i32 %phi3, %mul31
  %mul33 = mul i32 %phi2, %mul32
  br label %bb1
}

