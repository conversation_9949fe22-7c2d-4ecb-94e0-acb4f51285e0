; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown-linux-gnu < %s -slp-threshold=-100 | FileCheck %s

define void @test(ptr %i) {
; CHECK-LABEL: define void @test(
; CHECK-SAME: ptr [[I:%.*]]) {
; CHECK-NEXT:  [[BB:.*]]:
; CHECK-NEXT:    br label %[[BB2:.*]]
; CHECK:       [[BB2]]:
; CHECK-NEXT:    [[TMP0:%.*]] = phi <2 x i32> [ [[TMP3:%.*]], %[[BB2]] ], [ zeroinitializer, %[[BB]] ]
; CHECK-NEXT:    store <2 x i32> [[TMP0]], ptr [[I]], align 4
; CHECK-NEXT:    [[TMP1:%.*]] = shufflevector <2 x i32> [[TMP0]], <2 x i32> <i32 0, i32 poison>, <2 x i32> <i32 2, i32 1>
; CHECK-NEXT:    [[TMP2:%.*]] = trunc <2 x i32> [[TMP1]] to <2 x i1>
; CHECK-NEXT:    [[TMP3]] = select <2 x i1> [[TMP2]], <2 x i32> zeroinitializer, <2 x i32> zeroinitializer
; CHECK-NEXT:    br label %[[BB2]]
;
bb:
  %i1 = getelementptr i8, ptr %i, i64 4
  br label %bb2

bb2:
  %i3 = phi i32 [ %i6, %bb2 ], [ 0, %bb ]
  %i4 = phi i32 [ %i8, %bb2 ], [ 0, %bb ]
  store i32 %i3, ptr %i
  store i32 %i4, ptr %i1
  %i5 = trunc i32 0 to i1
  %i6 = select i1 %i5, i32 0, i32 0
  %i7 = trunc i32 %i4 to i1
  %i8 = select i1 %i7, i32 0, i32 0
  br label %bb2
}
