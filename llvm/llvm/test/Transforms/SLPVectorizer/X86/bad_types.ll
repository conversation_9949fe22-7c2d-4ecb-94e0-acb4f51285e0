; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt < %s -passes=slp-vectorizer -S -mcpu=corei7-avx | FileCheck %s

target datalayout = "e-m:e-i64:64-f80:128-n8:16:32:64-S128"
target triple = "x86_64-unknown-linux-gnu"

define i8 @test3(ptr %addr) {
; Check that we do not vectorize types that are padded to a bigger ones.
;
; CHECK-LABEL: @test3(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[A1:%.*]] = getelementptr inbounds i2, ptr [[ADDR:%.*]], i64 1
; CHECK-NEXT:    [[A2:%.*]] = getelementptr inbounds i2, ptr [[ADDR]], i64 2
; CHECK-NEXT:    [[A3:%.*]] = getelementptr inbounds i2, ptr [[ADDR]], i64 3
; CHECK-NEXT:    [[L0:%.*]] = load i2, ptr [[ADDR]], align 1
; CHECK-NEXT:    [[L1:%.*]] = load i2, ptr [[A1]], align 1
; CHECK-NEXT:    [[L2:%.*]] = load i2, ptr [[A2]], align 1
; CHECK-NEXT:    [[L3:%.*]] = load i2, ptr [[A3]], align 1
; CHECK-NEXT:    br label [[BB1:%.*]]
; CHECK:       bb1:
; CHECK-NEXT:    [[P0:%.*]] = phi i2 [ [[L0]], [[ENTRY:%.*]] ]
; CHECK-NEXT:    [[P1:%.*]] = phi i2 [ [[L1]], [[ENTRY]] ]
; CHECK-NEXT:    [[P2:%.*]] = phi i2 [ [[L2]], [[ENTRY]] ]
; CHECK-NEXT:    [[P3:%.*]] = phi i2 [ [[L3]], [[ENTRY]] ]
; CHECK-NEXT:    [[R:%.*]] = zext i2 [[P2]] to i8
; CHECK-NEXT:    ret i8 [[R]]
;
entry:
  %a1 = getelementptr inbounds i2, ptr %addr, i64 1
  %a2 = getelementptr inbounds i2, ptr %addr, i64 2
  %a3 = getelementptr inbounds i2, ptr %addr, i64 3
  %l0 = load i2, ptr %addr, align 1
  %l1 = load i2, ptr %a1, align 1
  %l2 = load i2, ptr %a2, align 1
  %l3 = load i2, ptr %a3, align 1
  br label %bb1
bb1:                                              ; preds = %entry
  %p0 = phi i2 [ %l0, %entry ]
  %p1 = phi i2 [ %l1, %entry ]
  %p2 = phi i2 [ %l2, %entry ]
  %p3 = phi i2 [ %l3, %entry ]
  %r  = zext i2 %p2 to i8
  ret i8 %r
}

declare void @f(i64, i64)

define void @test4(i32 %a, ptr %ptr) {
; Check that we do not vectorize types that are padded to a bigger ones.
;
; CHECK-LABEL: @test4(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TRUNC:%.*]] = trunc i32 [[A:%.*]] to i28
; CHECK-NEXT:    [[GEP1:%.*]] = getelementptr i28, ptr [[PTR:%.*]], i32 1
; CHECK-NEXT:    [[GEP2:%.*]] = getelementptr i28, ptr [[PTR]], i32 2
; CHECK-NEXT:    [[GEP3:%.*]] = getelementptr i28, ptr [[PTR]], i32 3
; CHECK-NEXT:    store i28 [[TRUNC]], ptr [[PTR]], align 4
; CHECK-NEXT:    store i28 [[TRUNC]], ptr [[GEP1]], align 4
; CHECK-NEXT:    store i28 [[TRUNC]], ptr [[GEP2]], align 4
; CHECK-NEXT:    store i28 [[TRUNC]], ptr [[GEP3]], align 4
; CHECK-NEXT:    ret void
;
entry:
  %trunc = trunc i32 %a to i28
  %gep1 = getelementptr i28, ptr %ptr, i32 1
  %gep2 = getelementptr i28, ptr %ptr, i32 2
  %gep3 = getelementptr i28, ptr %ptr, i32 3
  store i28 %trunc, ptr %ptr
  store i28 %trunc, ptr %gep1
  store i28 %trunc, ptr %gep2
  store i28 %trunc, ptr %gep3
  ret void
}
