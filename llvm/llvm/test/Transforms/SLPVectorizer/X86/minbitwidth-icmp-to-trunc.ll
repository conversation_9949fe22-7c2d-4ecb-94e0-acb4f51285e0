; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 4
; RUN: opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown-linux -mcpu=cascadelake < %s | FileCheck %s

define i1 @test(ptr noalias %0, i64 %1, ptr noalias %p, ptr %p1) {
; CHECK-LABEL: define i1 @test(
; CHECK-SAME: ptr noalias [[TMP0:%.*]], i64 [[TMP1:%.*]], ptr noalias [[P:%.*]], ptr [[P1:%.*]]) #[[ATTR0:[0-9]+]] {
; CHECK-NEXT:  newFuncRoot:
; CHECK-NEXT:    [[TMP2:%.*]] = getelementptr inbounds i8, ptr [[TMP0]], i64 16
; CHECK-NEXT:    [[BF_LOAD_I1336:%.*]] = load i24, ptr [[TMP2]], align 16
; CHECK-NEXT:    [[AND_I_I_I1342:%.*]] = and i64 [[TMP1]], -16
; CHECK-NEXT:    [[TMP3:%.*]] = inttoptr i64 [[AND_I_I_I1342]] to ptr
; CHECK-NEXT:    store ptr [[TMP3]], ptr [[P]], align 8
; CHECK-NEXT:    [[TMP4:%.*]] = load ptr, ptr [[TMP3]], align 16
; CHECK-NEXT:    [[TMP5:%.*]] = getelementptr inbounds i8, ptr [[TMP4]], i64 16
; CHECK-NEXT:    [[BF_LOAD_I1345:%.*]] = load i24, ptr [[TMP5]], align 16
; CHECK-NEXT:    [[TMP6:%.*]] = insertelement <2 x i24> poison, i24 [[BF_LOAD_I1336]], i32 0
; CHECK-NEXT:    [[TMP7:%.*]] = insertelement <2 x i24> [[TMP6]], i24 [[BF_LOAD_I1345]], i32 1
; CHECK-NEXT:    [[TMP8:%.*]] = and <2 x i24> [[TMP7]], splat (i24 255)
; CHECK-NEXT:    [[TMP9:%.*]] = icmp eq <2 x i24> [[TMP8]], splat (i24 24)
; CHECK-NEXT:    [[TMP10:%.*]] = select <2 x i1> [[TMP9]], <2 x i24> splat (i24 23), <2 x i24> [[TMP8]]
; CHECK-NEXT:    [[TMP23:%.*]] = trunc <2 x i24> [[TMP10]] to <2 x i8>
; CHECK-NEXT:    [[TMP26:%.*]] = zext <2 x i8> [[TMP23]] to <2 x i32>
; CHECK-NEXT:    [[TMP13:%.*]] = and <2 x i32> [[TMP26]], splat (i32 254)
; CHECK-NEXT:    [[TMP15:%.*]] = icmp eq <2 x i32> [[TMP13]], splat (i32 4)
; CHECK-NEXT:    [[TMP25:%.*]] = select <2 x i1> [[TMP15]], <2 x i8> splat (i8 2), <2 x i8> [[TMP23]]
; CHECK-NEXT:    [[TMP14:%.*]] = zext <2 x i8> [[TMP25]] to <2 x i32>
; CHECK-NEXT:    [[TMP17:%.*]] = icmp eq <2 x i32> [[TMP14]], splat (i32 32)
; CHECK-NEXT:    [[TMP18:%.*]] = select <2 x i1> [[TMP17]], <2 x i8> splat (i8 31), <2 x i8> [[TMP25]]
; CHECK-NEXT:    [[TMP16:%.*]] = zext <2 x i8> [[TMP18]] to <2 x i32>
; CHECK-NEXT:    [[TMP27:%.*]] = icmp eq <2 x i32> [[TMP16]], splat (i32 54)
; CHECK-NEXT:    [[TMP21:%.*]] = select <2 x i1> [[TMP27]], <2 x i8> splat (i8 53), <2 x i8> [[TMP18]]
; CHECK-NEXT:    [[TMP22:%.*]] = extractelement <2 x i8> [[TMP21]], i32 0
; CHECK-NEXT:    [[TMP19:%.*]] = zext i8 [[TMP22]] to i32
; CHECK-NEXT:    store i32 [[TMP19]], ptr [[P1]], align 4
; CHECK-NEXT:    [[TMP24:%.*]] = extractelement <2 x i8> [[TMP21]], i32 1
; CHECK-NEXT:    [[TMP20:%.*]] = zext i8 [[TMP24]] to i32
; CHECK-NEXT:    [[CMP210_NOT:%.*]] = icmp eq i32 [[TMP19]], [[TMP20]]
; CHECK-NEXT:    ret i1 [[CMP210_NOT]]
;
newFuncRoot:
  %2 = getelementptr inbounds i8, ptr %0, i64 16
  %bf.load.i1336 = load i24, ptr %2, align 16
  %bf.clear.i1337 = and i24 %bf.load.i1336, 255
  %and.i.i.i1342 = and i64 %1, -16
  %3 = inttoptr i64 %and.i.i.i1342 to ptr
  store ptr %3, ptr %p, align 8
  %4 = load ptr, ptr %3, align 16
  %5 = getelementptr inbounds i8, ptr %4, i64 16
  %bf.load.i1345 = load i24, ptr %5, align 16
  %bf.clear.i1346 = and i24 %bf.load.i1345, 255
  %cmp182 = icmp eq i24 %bf.clear.i1337, 24
  %narrow = select i1 %cmp182, i24 23, i24 %bf.clear.i1337
  %s = zext nneg i24 %narrow to i32
  %cmp185 = icmp eq i24 %bf.clear.i1346, 24
  %narrow1790 = select i1 %cmp185, i24 23, i24 %bf.clear.i1346
  %s1139 = zext nneg i24 %narrow1790 to i32
  %6 = and i32 %s, 254
  %or.cond1132 = icmp eq i32 %6, 4
  %s1142 = select i1 %or.cond1132, i32 2, i32 %s
  %7 = and i32 %s1139, 254
  %or.cond1133 = icmp eq i32 %7, 4
  %s1140 = select i1 %or.cond1133, i32 2, i32 %s1139
  %cmp198 = icmp eq i32 %s1142, 32
  %s1134 = select i1 %cmp198, i32 31, i32 %s1142
  %cmp201 = icmp eq i32 %s1140, 32
  %s1143 = select i1 %cmp201, i32 31, i32 %s1140
  %cmp204 = icmp eq i32 %s1134, 54
  %s1135 = select i1 %cmp204, i32 53, i32 %s1134
  store i32 %s1135, ptr %p1, align 4
  %cmp207 = icmp eq i32 %s1143, 54
  %s1141 = select i1 %cmp207, i32 53, i32 %s1143
  %cmp210.not = icmp eq i32 %s1135, %s1141
  ret i1 %cmp210.not
}

