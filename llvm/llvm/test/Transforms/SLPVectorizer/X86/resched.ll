; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -passes=slp-vectorizer -S -mtriple=x86_64-unknown-linux-gnu -mcpu=bdver2 < %s | FileCheck %s

%"struct.std::array" = type { [32 x i8] }

; Function Attrs: nounwind uwtable
define fastcc void @_ZN12_GLOBAL__N_127PolynomialMultiplyRecognize9recognizeEv(i1 %arg) unnamed_addr #0 align 2 {
; CHECK-LABEL: @_ZN12_GLOBAL__N_127PolynomialMultiplyRecognize9recognizeEv(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    br i1 %arg, label [[IF_END50_I:%.*]], label [[IF_THEN22_I:%.*]]
; CHECK:       if.then22.i:
; CHECK-NEXT:    [[SUB_I:%.*]] = add nsw i32 undef, -1
; CHECK-NEXT:    [[CONV31_I:%.*]] = and i32 undef, [[SUB_I]]
; CHECK-NEXT:    [[SHR_I_I:%.*]] = lshr i32 [[CONV31_I]], 1
; CHECK-NEXT:    [[TMP0:%.*]] = insertelement <2 x i32> poison, i32 [[CONV31_I]], i32 0
; CHECK-NEXT:    [[TMP1:%.*]] = shufflevector <2 x i32> [[TMP0]], <2 x i32> poison, <2 x i32> zeroinitializer
; CHECK-NEXT:    [[TMP2:%.*]] = lshr <2 x i32> [[TMP1]], <i32 2, i32 3>
; CHECK-NEXT:    [[TMP3:%.*]] = shufflevector <2 x i32> [[TMP1]], <2 x i32> poison, <4 x i32> zeroinitializer
; CHECK-NEXT:    [[TMP4:%.*]] = lshr <4 x i32> [[TMP3]], <i32 4, i32 5, i32 6, i32 7>
; CHECK-NEXT:    [[TMP5:%.*]] = shufflevector <4 x i32> [[TMP3]], <4 x i32> poison, <8 x i32> zeroinitializer
; CHECK-NEXT:    [[TMP6:%.*]] = lshr <8 x i32> [[TMP5]], <i32 8, i32 9, i32 10, i32 11, i32 12, i32 13, i32 14, i32 15>
; CHECK-NEXT:    [[TMP7:%.*]] = trunc i32 [[SUB_I]] to i8
; CHECK-NEXT:    [[TMP8:%.*]] = insertelement <16 x i8> poison, i8 [[TMP7]], i32 0
; CHECK-NEXT:    [[TMP9:%.*]] = trunc i32 [[SHR_I_I]] to i8
; CHECK-NEXT:    [[TMP10:%.*]] = insertelement <16 x i8> [[TMP8]], i8 [[TMP9]], i32 1
; CHECK-NEXT:    [[TMP11:%.*]] = trunc <8 x i32> [[TMP6]] to <8 x i8>
; CHECK-NEXT:    [[TMP12:%.*]] = call <16 x i8> @llvm.vector.insert.v16i8.v8i8(<16 x i8> [[TMP10]], <8 x i8> [[TMP11]], i64 8)
; CHECK-NEXT:    [[TMP13:%.*]] = trunc <4 x i32> [[TMP4]] to <4 x i8>
; CHECK-NEXT:    [[TMP14:%.*]] = call <16 x i8> @llvm.vector.insert.v16i8.v4i8(<16 x i8> [[TMP12]], <4 x i8> [[TMP13]], i64 4)
; CHECK-NEXT:    [[TMP15:%.*]] = trunc <2 x i32> [[TMP2]] to <2 x i8>
; CHECK-NEXT:    [[TMP16:%.*]] = call <16 x i8> @llvm.vector.insert.v16i8.v2i8(<16 x i8> [[TMP14]], <2 x i8> [[TMP15]], i64 2)
; CHECK-NEXT:    [[TMP17:%.*]] = and <16 x i8> [[TMP16]], splat (i8 1)
; CHECK-NEXT:    store <16 x i8> [[TMP17]], ptr undef, align 1
; CHECK-NEXT:    ret void
; CHECK:       if.end50.i:
; CHECK-NEXT:    ret void
;
entry:
  br i1 %arg, label %if.end50.i, label %if.then22.i

if.then22.i:                                      ; preds = %entry
  %sub.i = add nsw i32 undef, -1
  %conv31.i = and i32 undef, %sub.i
  %0 = trunc i32 %sub.i to i8
  %conv.i.i1199 = and i8 %0, 1
  store i8 %conv.i.i1199, ptr undef, align 1
  %shr.i.i = lshr i32 %conv31.i, 1
  %1 = trunc i32 %shr.i.i to i8
  %conv.1.i.i = and i8 %1, 1
  %arrayidx.i.i7.1.i.i = getelementptr inbounds %"struct.std::array", ptr undef, i64 0, i32 0, i64 1
  store i8 %conv.1.i.i, ptr %arrayidx.i.i7.1.i.i, align 1
  %shr.1.i.i = lshr i32 %conv31.i, 2
  %2 = trunc i32 %shr.1.i.i to i8
  %conv.2.i.i = and i8 %2, 1
  %arrayidx.i.i7.2.i.i = getelementptr inbounds %"struct.std::array", ptr undef, i64 0, i32 0, i64 2
  store i8 %conv.2.i.i, ptr %arrayidx.i.i7.2.i.i, align 1
  %shr.2.i.i = lshr i32 %conv31.i, 3
  %3 = trunc i32 %shr.2.i.i to i8
  %conv.3.i.i = and i8 %3, 1
  %arrayidx.i.i7.3.i.i = getelementptr inbounds %"struct.std::array", ptr undef, i64 0, i32 0, i64 3
  store i8 %conv.3.i.i, ptr %arrayidx.i.i7.3.i.i, align 1
  %shr.3.i.i = lshr i32 %conv31.i, 4
  %4 = trunc i32 %shr.3.i.i to i8
  %conv.4.i.i = and i8 %4, 1
  %arrayidx.i.i7.4.i.i = getelementptr inbounds %"struct.std::array", ptr undef, i64 0, i32 0, i64 4
  store i8 %conv.4.i.i, ptr %arrayidx.i.i7.4.i.i, align 1
  %shr.4.i.i = lshr i32 %conv31.i, 5
  %5 = trunc i32 %shr.4.i.i to i8
  %conv.5.i.i = and i8 %5, 1
  %arrayidx.i.i7.5.i.i = getelementptr inbounds %"struct.std::array", ptr undef, i64 0, i32 0, i64 5
  store i8 %conv.5.i.i, ptr %arrayidx.i.i7.5.i.i, align 1
  %shr.5.i.i = lshr i32 %conv31.i, 6
  %6 = trunc i32 %shr.5.i.i to i8
  %conv.6.i.i = and i8 %6, 1
  %arrayidx.i.i7.6.i.i = getelementptr inbounds %"struct.std::array", ptr undef, i64 0, i32 0, i64 6
  store i8 %conv.6.i.i, ptr %arrayidx.i.i7.6.i.i, align 1
  %shr.6.i.i = lshr i32 %conv31.i, 7
  %7 = trunc i32 %shr.6.i.i to i8
  %conv.7.i.i = and i8 %7, 1
  %arrayidx.i.i7.7.i.i = getelementptr inbounds %"struct.std::array", ptr undef, i64 0, i32 0, i64 7
  store i8 %conv.7.i.i, ptr %arrayidx.i.i7.7.i.i, align 1
  %shr.7.i.i = lshr i32 %conv31.i, 8
  %8 = trunc i32 %shr.7.i.i to i8
  %conv.8.i.i = and i8 %8, 1
  %arrayidx.i.i7.8.i.i = getelementptr inbounds %"struct.std::array", ptr undef, i64 0, i32 0, i64 8
  store i8 %conv.8.i.i, ptr %arrayidx.i.i7.8.i.i, align 1
  %shr.8.i.i = lshr i32 %conv31.i, 9
  %9 = trunc i32 %shr.8.i.i to i8
  %conv.9.i.i = and i8 %9, 1
  %arrayidx.i.i7.9.i.i = getelementptr inbounds %"struct.std::array", ptr undef, i64 0, i32 0, i64 9
  store i8 %conv.9.i.i, ptr %arrayidx.i.i7.9.i.i, align 1
  %shr.9.i.i = lshr i32 %conv31.i, 10
  %10 = trunc i32 %shr.9.i.i to i8
  %conv.10.i.i = and i8 %10, 1
  %arrayidx.i.i7.10.i.i = getelementptr inbounds %"struct.std::array", ptr undef, i64 0, i32 0, i64 10
  store i8 %conv.10.i.i, ptr %arrayidx.i.i7.10.i.i, align 1
  %shr.10.i.i = lshr i32 %conv31.i, 11
  %11 = trunc i32 %shr.10.i.i to i8
  %conv.11.i.i = and i8 %11, 1
  %arrayidx.i.i7.11.i.i = getelementptr inbounds %"struct.std::array", ptr undef, i64 0, i32 0, i64 11
  store i8 %conv.11.i.i, ptr %arrayidx.i.i7.11.i.i, align 1
  %shr.11.i.i = lshr i32 %conv31.i, 12
  %12 = trunc i32 %shr.11.i.i to i8
  %conv.12.i.i = and i8 %12, 1
  %arrayidx.i.i7.12.i.i = getelementptr inbounds %"struct.std::array", ptr undef, i64 0, i32 0, i64 12
  store i8 %conv.12.i.i, ptr %arrayidx.i.i7.12.i.i, align 1
  %shr.12.i.i = lshr i32 %conv31.i, 13
  %13 = trunc i32 %shr.12.i.i to i8
  %conv.13.i.i = and i8 %13, 1
  %arrayidx.i.i7.13.i.i = getelementptr inbounds %"struct.std::array", ptr undef, i64 0, i32 0, i64 13
  store i8 %conv.13.i.i, ptr %arrayidx.i.i7.13.i.i, align 1
  %shr.13.i.i = lshr i32 %conv31.i, 14
  %14 = trunc i32 %shr.13.i.i to i8
  %conv.14.i.i = and i8 %14, 1
  %arrayidx.i.i7.14.i.i = getelementptr inbounds %"struct.std::array", ptr undef, i64 0, i32 0, i64 14
  store i8 %conv.14.i.i, ptr %arrayidx.i.i7.14.i.i, align 1
  %shr.14.i.i = lshr i32 %conv31.i, 15
  %15 = trunc i32 %shr.14.i.i to i8
  %conv.15.i.i = and i8 %15, 1
  %arrayidx.i.i7.15.i.i = getelementptr inbounds %"struct.std::array", ptr undef, i64 0, i32 0, i64 15
  store i8 %conv.15.i.i, ptr %arrayidx.i.i7.15.i.i, align 1
  ret void

if.end50.i:                                       ; preds = %entry
  ret void
}
