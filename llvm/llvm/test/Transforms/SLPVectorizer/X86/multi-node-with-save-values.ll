; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 3
; RUN: opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown-linux-gnu < %s | FileCheck %s

define void @foo() {
; CHECK-LABEL: define void @foo() {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    br label [[BB1:%.*]]
; CHECK:       bb1:
; CHECK-NEXT:    switch i32 0, label [[BB3:%.*]] [
; CHECK-NEXT:      i32 0, label [[BB2:%.*]]
; CHECK-NEXT:      i32 4, label [[BB4:%.*]]
; CHECK-NEXT:      i32 1, label [[BB4]]
; CHECK-NEXT:    ]
; CHECK:       bb2:
; CHECK-NEXT:    br label [[BB4]]
; CHECK:       bb3:
; CHECK-NEXT:    br label [[BB4]]
; CHECK:       bb4:
; CHECK-NEXT:    [[TMP0:%.*]] = phi <2 x i32> [ zeroinitializer, [[BB3]] ], [ zeroinitializer, [[BB2]] ], [ zeroinitializer, [[BB1]] ], [ zeroinitializer, [[BB1]] ]
; CHECK-NEXT:    br label [[BB1]]
;
entry:
  %add1 = add i32 0, 0
  %add2 = add i32 0, 0
  %add3 = add i32 0, 0
  %add4 = add i32 0, 0
  %add5 = add i32 0, 0
  br label %bb1

bb1:
  switch i32 0, label %bb3 [
  i32 0, label %bb2
  i32 4, label %bb4
  i32 1, label %bb4
  ]

bb2:
  br label %bb4

bb3:
  br label %bb4

bb4:
  %p1 = phi i32 [ %add1, %bb3 ], [ %add4, %bb2 ], [ %add2, %bb1 ], [ %add2, %bb1 ]
  %p2 = phi i32 [ %add2, %bb3 ], [ %add5, %bb2 ], [ %add3, %bb1 ], [ %add3, %bb1 ]
  br label %bb1
}
