; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown-linux-gnu < %s | FileCheck %s

define void @test() {
; CHECK-LABEL: define void @test() {
; CHECK-NEXT:  [[BB:.*]]:
; CHECK-NEXT:    [[TMP0:%.*]] = shufflevector <4 x float> <float 0.000000e+00, float undef, float 0.000000e+00, float 0.000000e+00>, <4 x float> poison, <4 x i32> <i32 0, i32 0, i32 2, i32 3>
; CHECK-NEXT:    [[TMP1:%.*]] = shufflevector <4 x float> <float 0.000000e+00, float undef, float 0.000000e+00, float 0.000000e+00>, <4 x float> <float poison, float 0.000000e+00, float poison, float poison>, <4 x i32> <i32 0, i32 5, i32 2, i32 3>
; CHECK-NEXT:    br label %[[BB1:.*]]
; CHECK:       [[BB1]]:
; CHECK-NEXT:    [[PHI:%.*]] = phi i32 [ 0, %[[BB]] ], [ [[TMP9:%.*]], %[[BB1]] ]
; CHECK-NEXT:    [[FMUL:%.*]] = fmul float 0.000000e+00, 0.000000e+00
; CHECK-NEXT:    [[TMP2:%.*]] = insertelement <4 x float> [[TMP1]], float [[FMUL]], i32 2
; CHECK-NEXT:    [[TMP3:%.*]] = shufflevector <4 x float> [[TMP2]], <4 x float> poison, <4 x i32> <i32 0, i32 1, i32 2, i32 2>
; CHECK-NEXT:    [[TMP4:%.*]] = fadd <4 x float> [[TMP0]], [[TMP3]]
; CHECK-NEXT:    [[TMP5:%.*]] = fadd <4 x float> [[TMP4]], zeroinitializer
; CHECK-NEXT:    [[TMP6:%.*]] = fcmp ogt <4 x float> [[TMP5]], zeroinitializer
; CHECK-NEXT:    [[TMP7:%.*]] = select <4 x i1> [[TMP6]], <4 x i32> zeroinitializer, <4 x i32> zeroinitializer
; CHECK-NEXT:    [[TMP8:%.*]] = select <4 x i1> zeroinitializer, <4 x i32> [[TMP7]], <4 x i32> zeroinitializer
; CHECK-NEXT:    [[TMP9]] = call i32 @llvm.vector.reduce.or.v4i32(<4 x i32> [[TMP8]])
; CHECK-NEXT:    br label %[[BB1]]
;
bb:
  br label %bb1

bb1:
  %phi = phi i32 [ 0, %bb ], [ %or21, %bb1 ]
  %sitofp = sitofp i32 0 to float
  %fadd = fadd float %sitofp, %sitofp
  %fadd2 = fadd float %fadd, 0.000000e+00
  %fcmp = fcmp ogt float %fadd2, 0.000000e+00
  %select = select i1 %fcmp, i32 0, i32 0
  %select3 = select i1 false, i32 %select, i32 0
  %fadd4 = fadd float %sitofp, 0.000000e+00
  %fadd5 = fadd float %fadd4, 0.000000e+00
  %fcmp6 = fcmp ogt float %fadd5, 0.000000e+00
  %select7 = select i1 %fcmp6, i32 0, i32 0
  %select8 = select i1 false, i32 %select7, i32 0
  %or = or i32 %select3, %select8
  %sitofp9 = sitofp i32 0 to float
  %fmul = fmul float 0.000000e+00, 0.000000e+00
  %fadd10 = fadd float %sitofp9, %fmul
  %fadd11 = fadd float %fadd10, 0.000000e+00
  %fcmp12 = fcmp ogt float %fadd11, 0.000000e+00
  %select13 = select i1 %fcmp12, i32 0, i32 0
  %select14 = select i1 false, i32 %select13, i32 0
  %or15 = or i32 %select14, %or
  %fadd16 = fadd float %fmul, 0.000000e+00
  %fadd17 = fadd float %fadd16, 0.000000e+00
  %fcmp18 = fcmp ogt float %fadd17, 0.000000e+00
  %select19 = select i1 %fcmp18, i32 0, i32 0
  %select20 = select i1 false, i32 %select19, i32 0
  %or21 = or i32 %or15, %select20
  br label %bb1
}
