; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown-linux < %s | FileCheck %s

define i32 @test() {
; CHECK-LABEL: define i32 @test() {
; CHECK-NEXT:  [[ENTRY:.*:]]
; CHECK-NEXT:    [[TMP0:%.*]] = call i32 @llvm.vector.reduce.or.v4i32(<4 x i32> zeroinitializer)
; CHECK-NEXT:    ret i32 [[TMP0]]
;
entry:
  %cond = zext i1 false to i32
  %cond258 = zext i1 false to i32
  %cond283 = zext i1 false to i32
  %cond308 = zext i1 false to i32
  %conv685 = or i32 %cond308, %cond
  %conv710 = or i32 %conv685, %cond258
  %conv735 = or i32 %conv710, %cond283
  %conv791 = or i32 %conv735, %cond
  ret i32 %conv791
}
