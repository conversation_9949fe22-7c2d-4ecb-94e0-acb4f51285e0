; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -passes=slp-vectorizer -S -mtriple=x86_64-unknown-linux-gnu -mcpu=bdver2 < %s | FileCheck %s

; Function Attrs: nounwind uwtable
define void @get_block(i32 %y_pos, i1 %arg) local_unnamed_addr #0 {
; CHECK-LABEL: @get_block(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    br label [[LAND_LHS_TRUE:%.*]]
; CHECK:       land.lhs.true:
; CHECK-NEXT:    br i1 %arg, label [[IF_THEN:%.*]], label [[IF_END:%.*]]
; CHECK:       if.then:
; CHECK-NEXT:    unreachable
; CHECK:       if.end:
; CHECK-NEXT:    [[SUB14:%.*]] = sub nsw i32 [[Y_POS:%.*]], undef
; CHECK-NEXT:    [[SHR15:%.*]] = ashr i32 [[SUB14]], 2
; CHECK-NEXT:    [[CMP_I_I:%.*]] = icmp sgt i32 [[SHR15]], 0
; CHECK-NEXT:    [[COND_I_I:%.*]] = select i1 [[CMP_I_I]], i32 [[SHR15]], i32 0
; CHECK-NEXT:    [[CMP_I4_I:%.*]] = icmp slt i32 [[COND_I_I]], undef
; CHECK-NEXT:    [[COND_I5_I:%.*]] = select i1 [[CMP_I4_I]], i32 [[COND_I_I]], i32 undef
; CHECK-NEXT:    [[IDXPROM30:%.*]] = sext i32 [[COND_I5_I]] to i64
; CHECK-NEXT:    [[ARRAYIDX31:%.*]] = getelementptr inbounds ptr, ptr undef, i64 [[IDXPROM30]]
; CHECK-NEXT:    [[CMP_I_I_1:%.*]] = icmp sgt i32 [[SUB14]], -1
; CHECK-NEXT:    [[COND_I_I_1:%.*]] = select i1 [[CMP_I_I_1]], i32 undef, i32 0
; CHECK-NEXT:    [[CMP_I4_I_1:%.*]] = icmp slt i32 [[COND_I_I_1]], undef
; CHECK-NEXT:    [[COND_I5_I_1:%.*]] = select i1 [[CMP_I4_I_1]], i32 [[COND_I_I_1]], i32 undef
; CHECK-NEXT:    [[IDXPROM30_1:%.*]] = sext i32 [[COND_I5_I_1]] to i64
; CHECK-NEXT:    [[ARRAYIDX31_1:%.*]] = getelementptr inbounds ptr, ptr undef, i64 [[IDXPROM30_1]]
; CHECK-NEXT:    [[CMP_I_I_2:%.*]] = icmp sgt i32 [[SUB14]], -5
; CHECK-NEXT:    [[COND_I_I_2:%.*]] = select i1 [[CMP_I_I_2]], i32 undef, i32 0
; CHECK-NEXT:    [[CMP_I4_I_2:%.*]] = icmp slt i32 [[COND_I_I_2]], undef
; CHECK-NEXT:    [[COND_I5_I_2:%.*]] = select i1 [[CMP_I4_I_2]], i32 [[COND_I_I_2]], i32 undef
; CHECK-NEXT:    [[IDXPROM30_2:%.*]] = sext i32 [[COND_I5_I_2]] to i64
; CHECK-NEXT:    [[ARRAYIDX31_2:%.*]] = getelementptr inbounds ptr, ptr undef, i64 [[IDXPROM30_2]]
; CHECK-NEXT:    [[CMP_I_I_3:%.*]] = icmp sgt i32 [[SUB14]], -9
; CHECK-NEXT:    [[COND_I_I_3:%.*]] = select i1 [[CMP_I_I_3]], i32 undef, i32 0
; CHECK-NEXT:    [[CMP_I4_I_3:%.*]] = icmp slt i32 [[COND_I_I_3]], undef
; CHECK-NEXT:    [[COND_I5_I_3:%.*]] = select i1 [[CMP_I4_I_3]], i32 [[COND_I_I_3]], i32 undef
; CHECK-NEXT:    [[IDXPROM30_3:%.*]] = sext i32 [[COND_I5_I_3]] to i64
; CHECK-NEXT:    [[ARRAYIDX31_3:%.*]] = getelementptr inbounds ptr, ptr undef, i64 [[IDXPROM30_3]]
; CHECK-NEXT:    ret void
;
entry:
  br label %land.lhs.true

land.lhs.true:                                    ; preds = %entry
  br i1 %arg, label %if.then, label %if.end

if.then:                                          ; preds = %land.lhs.true
  unreachable

if.end:                                           ; preds = %land.lhs.true
  %sub14 = sub nsw i32 %y_pos, undef
  %shr15 = ashr i32 %sub14, 2
  %cmp.i.i = icmp sgt i32 %shr15, 0
  %cond.i.i = select i1 %cmp.i.i, i32 %shr15, i32 0
  %cmp.i4.i = icmp slt i32 %cond.i.i, undef
  %cond.i5.i = select i1 %cmp.i4.i, i32 %cond.i.i, i32 undef
  %idxprom30 = sext i32 %cond.i5.i to i64
  %arrayidx31 = getelementptr inbounds ptr, ptr undef, i64 %idxprom30
  %cmp.i.i.1 = icmp sgt i32 %sub14, -1
  %cond.i.i.1 = select i1 %cmp.i.i.1, i32 undef, i32 0
  %cmp.i4.i.1 = icmp slt i32 %cond.i.i.1, undef
  %cond.i5.i.1 = select i1 %cmp.i4.i.1, i32 %cond.i.i.1, i32 undef
  %idxprom30.1 = sext i32 %cond.i5.i.1 to i64
  %arrayidx31.1 = getelementptr inbounds ptr, ptr undef, i64 %idxprom30.1
  %cmp.i.i.2 = icmp sgt i32 %sub14, -5
  %cond.i.i.2 = select i1 %cmp.i.i.2, i32 undef, i32 0
  %cmp.i4.i.2 = icmp slt i32 %cond.i.i.2, undef
  %cond.i5.i.2 = select i1 %cmp.i4.i.2, i32 %cond.i.i.2, i32 undef
  %idxprom30.2 = sext i32 %cond.i5.i.2 to i64
  %arrayidx31.2 = getelementptr inbounds ptr, ptr undef, i64 %idxprom30.2
  %cmp.i.i.3 = icmp sgt i32 %sub14, -9
  %cond.i.i.3 = select i1 %cmp.i.i.3, i32 undef, i32 0
  %cmp.i4.i.3 = icmp slt i32 %cond.i.i.3, undef
  %cond.i5.i.3 = select i1 %cmp.i4.i.3, i32 %cond.i.i.3, i32 undef
  %idxprom30.3 = sext i32 %cond.i5.i.3 to i64
  %arrayidx31.3 = getelementptr inbounds ptr, ptr undef, i64 %idxprom30.3
  ret void
}
