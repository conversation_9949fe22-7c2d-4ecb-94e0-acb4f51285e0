; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt < %s -mtriple=x86_64-unknown -passes=slp-vectorizer -S | FileCheck %s --check-prefixes=CHECK,SSE
; RUN: opt < %s -mtriple=x86_64-unknown -mcpu=corei7 -passes=slp-vectorizer -S | FileCheck %s --check-prefixes=CHECK,SSE
; RUN: opt < %s -mtriple=x86_64-unknown -mcpu=corei7-avx -passes=slp-vectorizer -S | FileCheck %s --check-prefixes=CHECK,AVX,AVX1
; RUN: opt < %s -mtriple=x86_64-unknown -mcpu=core-avx2 -passes=slp-vectorizer -S | FileCheck %s --check-prefixes=CHECK,AVX,AVX2
; RUN: opt < %s -mtriple=x86_64-unknown -mcpu=icelake-server -passes=slp-vectorizer -S | FileCheck %s --check-prefixes=CHECK,AVX,AVX512

target datalayout = "e-m:e-i64:64-f80:128-n8:16:32:64-S128"

@src64 = common global [4 x i64] zeroinitializer, align 32
@dst64 = common global [4 x i64] zeroinitializer, align 32
@src32 = common global [8 x i32] zeroinitializer, align 32
@dst32 = common global [8 x i32] zeroinitializer, align 32
@src16 = common global [16 x i16] zeroinitializer, align 32
@dst16 = common global [16 x i16] zeroinitializer, align 32
@src8  = common global [32 x i8] zeroinitializer, align 32
@dst8  = common global [32 x i8] zeroinitializer, align 32

declare i64 @llvm.cttz.i64(i64, i1)
declare i32 @llvm.cttz.i32(i32, i1)
declare i16 @llvm.cttz.i16(i16, i1)
declare  i8 @llvm.cttz.i8(i8, i1)

;
; CTTZ
;

define void @cttz_2i64() #0 {
; SSE-LABEL: @cttz_2i64(
; SSE-NEXT:    [[LD0:%.*]] = load i64, ptr @src64, align 8
; SSE-NEXT:    [[LD1:%.*]] = load i64, ptr getelementptr inbounds ([4 x i64], ptr @src64, i32 0, i64 1), align 8
; SSE-NEXT:    [[CTTZ0:%.*]] = call i64 @llvm.cttz.i64(i64 [[LD0]], i1 false)
; SSE-NEXT:    [[CTTZ1:%.*]] = call i64 @llvm.cttz.i64(i64 [[LD1]], i1 false)
; SSE-NEXT:    store i64 [[CTTZ0]], ptr @dst64, align 8
; SSE-NEXT:    store i64 [[CTTZ1]], ptr getelementptr inbounds ([4 x i64], ptr @dst64, i32 0, i64 1), align 8
; SSE-NEXT:    ret void
;
; AVX1-LABEL: @cttz_2i64(
; AVX1-NEXT:    [[LD0:%.*]] = load i64, ptr @src64, align 8
; AVX1-NEXT:    [[LD1:%.*]] = load i64, ptr getelementptr inbounds ([4 x i64], ptr @src64, i32 0, i64 1), align 8
; AVX1-NEXT:    [[CTTZ0:%.*]] = call i64 @llvm.cttz.i64(i64 [[LD0]], i1 false)
; AVX1-NEXT:    [[CTTZ1:%.*]] = call i64 @llvm.cttz.i64(i64 [[LD1]], i1 false)
; AVX1-NEXT:    store i64 [[CTTZ0]], ptr @dst64, align 8
; AVX1-NEXT:    store i64 [[CTTZ1]], ptr getelementptr inbounds ([4 x i64], ptr @dst64, i32 0, i64 1), align 8
; AVX1-NEXT:    ret void
;
; AVX2-LABEL: @cttz_2i64(
; AVX2-NEXT:    [[LD0:%.*]] = load i64, ptr @src64, align 8
; AVX2-NEXT:    [[LD1:%.*]] = load i64, ptr getelementptr inbounds ([4 x i64], ptr @src64, i32 0, i64 1), align 8
; AVX2-NEXT:    [[CTTZ0:%.*]] = call i64 @llvm.cttz.i64(i64 [[LD0]], i1 false)
; AVX2-NEXT:    [[CTTZ1:%.*]] = call i64 @llvm.cttz.i64(i64 [[LD1]], i1 false)
; AVX2-NEXT:    store i64 [[CTTZ0]], ptr @dst64, align 8
; AVX2-NEXT:    store i64 [[CTTZ1]], ptr getelementptr inbounds ([4 x i64], ptr @dst64, i32 0, i64 1), align 8
; AVX2-NEXT:    ret void
;
; AVX512-LABEL: @cttz_2i64(
; AVX512-NEXT:    [[TMP1:%.*]] = load <2 x i64>, ptr @src64, align 8
; AVX512-NEXT:    [[TMP2:%.*]] = call <2 x i64> @llvm.cttz.v2i64(<2 x i64> [[TMP1]], i1 false)
; AVX512-NEXT:    store <2 x i64> [[TMP2]], ptr @dst64, align 8
; AVX512-NEXT:    ret void
;
  %ld0 = load i64, ptr @src64, align 8
  %ld1 = load i64, ptr getelementptr inbounds ([4 x i64], ptr @src64, i32 0, i64 1), align 8
  %cttz0 = call i64 @llvm.cttz.i64(i64 %ld0, i1 0)
  %cttz1 = call i64 @llvm.cttz.i64(i64 %ld1, i1 0)
  store i64 %cttz0, ptr @dst64, align 8
  store i64 %cttz1, ptr getelementptr inbounds ([4 x i64], ptr @dst64, i32 0, i64 1), align 8
  ret void
}

define void @cttz_4i64() #0 {
; SSE-LABEL: @cttz_4i64(
; SSE-NEXT:    [[LD0:%.*]] = load i64, ptr @src64, align 4
; SSE-NEXT:    [[LD1:%.*]] = load i64, ptr getelementptr inbounds ([4 x i64], ptr @src64, i64 0, i64 1), align 4
; SSE-NEXT:    [[LD2:%.*]] = load i64, ptr getelementptr inbounds ([4 x i64], ptr @src64, i64 0, i64 2), align 4
; SSE-NEXT:    [[LD3:%.*]] = load i64, ptr getelementptr inbounds ([4 x i64], ptr @src64, i64 0, i64 3), align 4
; SSE-NEXT:    [[CTTZ0:%.*]] = call i64 @llvm.cttz.i64(i64 [[LD0]], i1 false)
; SSE-NEXT:    [[CTTZ1:%.*]] = call i64 @llvm.cttz.i64(i64 [[LD1]], i1 false)
; SSE-NEXT:    [[CTTZ2:%.*]] = call i64 @llvm.cttz.i64(i64 [[LD2]], i1 false)
; SSE-NEXT:    [[CTTZ3:%.*]] = call i64 @llvm.cttz.i64(i64 [[LD3]], i1 false)
; SSE-NEXT:    store i64 [[CTTZ0]], ptr @dst64, align 4
; SSE-NEXT:    store i64 [[CTTZ1]], ptr getelementptr inbounds ([4 x i64], ptr @dst64, i64 0, i64 1), align 4
; SSE-NEXT:    store i64 [[CTTZ2]], ptr getelementptr inbounds ([4 x i64], ptr @dst64, i64 0, i64 2), align 4
; SSE-NEXT:    store i64 [[CTTZ3]], ptr getelementptr inbounds ([4 x i64], ptr @dst64, i64 0, i64 3), align 4
; SSE-NEXT:    ret void
;
; AVX1-LABEL: @cttz_4i64(
; AVX1-NEXT:    [[LD0:%.*]] = load i64, ptr @src64, align 4
; AVX1-NEXT:    [[LD1:%.*]] = load i64, ptr getelementptr inbounds ([4 x i64], ptr @src64, i64 0, i64 1), align 4
; AVX1-NEXT:    [[LD2:%.*]] = load i64, ptr getelementptr inbounds ([4 x i64], ptr @src64, i64 0, i64 2), align 4
; AVX1-NEXT:    [[LD3:%.*]] = load i64, ptr getelementptr inbounds ([4 x i64], ptr @src64, i64 0, i64 3), align 4
; AVX1-NEXT:    [[CTTZ0:%.*]] = call i64 @llvm.cttz.i64(i64 [[LD0]], i1 false)
; AVX1-NEXT:    [[CTTZ1:%.*]] = call i64 @llvm.cttz.i64(i64 [[LD1]], i1 false)
; AVX1-NEXT:    [[CTTZ2:%.*]] = call i64 @llvm.cttz.i64(i64 [[LD2]], i1 false)
; AVX1-NEXT:    [[CTTZ3:%.*]] = call i64 @llvm.cttz.i64(i64 [[LD3]], i1 false)
; AVX1-NEXT:    store i64 [[CTTZ0]], ptr @dst64, align 4
; AVX1-NEXT:    store i64 [[CTTZ1]], ptr getelementptr inbounds ([4 x i64], ptr @dst64, i64 0, i64 1), align 4
; AVX1-NEXT:    store i64 [[CTTZ2]], ptr getelementptr inbounds ([4 x i64], ptr @dst64, i64 0, i64 2), align 4
; AVX1-NEXT:    store i64 [[CTTZ3]], ptr getelementptr inbounds ([4 x i64], ptr @dst64, i64 0, i64 3), align 4
; AVX1-NEXT:    ret void
;
; AVX2-LABEL: @cttz_4i64(
; AVX2-NEXT:    [[TMP1:%.*]] = load <4 x i64>, ptr @src64, align 4
; AVX2-NEXT:    [[TMP2:%.*]] = call <4 x i64> @llvm.cttz.v4i64(<4 x i64> [[TMP1]], i1 false)
; AVX2-NEXT:    store <4 x i64> [[TMP2]], ptr @dst64, align 4
; AVX2-NEXT:    ret void
;
; AVX512-LABEL: @cttz_4i64(
; AVX512-NEXT:    [[TMP1:%.*]] = load <4 x i64>, ptr @src64, align 4
; AVX512-NEXT:    [[TMP2:%.*]] = call <4 x i64> @llvm.cttz.v4i64(<4 x i64> [[TMP1]], i1 false)
; AVX512-NEXT:    store <4 x i64> [[TMP2]], ptr @dst64, align 4
; AVX512-NEXT:    ret void
;
  %ld0 = load i64, ptr @src64, align 4
  %ld1 = load i64, ptr getelementptr inbounds ([4 x i64], ptr @src64, i64 0, i64 1), align 4
  %ld2 = load i64, ptr getelementptr inbounds ([4 x i64], ptr @src64, i64 0, i64 2), align 4
  %ld3 = load i64, ptr getelementptr inbounds ([4 x i64], ptr @src64, i64 0, i64 3), align 4
  %cttz0 = call i64 @llvm.cttz.i64(i64 %ld0, i1 0)
  %cttz1 = call i64 @llvm.cttz.i64(i64 %ld1, i1 0)
  %cttz2 = call i64 @llvm.cttz.i64(i64 %ld2, i1 0)
  %cttz3 = call i64 @llvm.cttz.i64(i64 %ld3, i1 0)
  store i64 %cttz0, ptr @dst64, align 4
  store i64 %cttz1, ptr getelementptr inbounds ([4 x i64], ptr @dst64, i64 0, i64 1), align 4
  store i64 %cttz2, ptr getelementptr inbounds ([4 x i64], ptr @dst64, i64 0, i64 2), align 4
  store i64 %cttz3, ptr getelementptr inbounds ([4 x i64], ptr @dst64, i64 0, i64 3), align 4
  ret void
}

define void @cttz_4i32() #0 {
; SSE-LABEL: @cttz_4i32(
; SSE-NEXT:    [[LD0:%.*]] = load i32, ptr @src32, align 4
; SSE-NEXT:    [[LD1:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 1), align 4
; SSE-NEXT:    [[LD2:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 2), align 4
; SSE-NEXT:    [[LD3:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 3), align 4
; SSE-NEXT:    [[CTTZ0:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD0]], i1 false)
; SSE-NEXT:    [[CTTZ1:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD1]], i1 false)
; SSE-NEXT:    [[CTTZ2:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD2]], i1 false)
; SSE-NEXT:    [[CTTZ3:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD3]], i1 false)
; SSE-NEXT:    store i32 [[CTTZ0]], ptr @dst32, align 4
; SSE-NEXT:    store i32 [[CTTZ1]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 1), align 4
; SSE-NEXT:    store i32 [[CTTZ2]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 2), align 4
; SSE-NEXT:    store i32 [[CTTZ3]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 3), align 4
; SSE-NEXT:    ret void
;
; AVX1-LABEL: @cttz_4i32(
; AVX1-NEXT:    [[LD0:%.*]] = load i32, ptr @src32, align 4
; AVX1-NEXT:    [[LD1:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 1), align 4
; AVX1-NEXT:    [[LD2:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 2), align 4
; AVX1-NEXT:    [[LD3:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 3), align 4
; AVX1-NEXT:    [[CTTZ0:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD0]], i1 false)
; AVX1-NEXT:    [[CTTZ1:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD1]], i1 false)
; AVX1-NEXT:    [[CTTZ2:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD2]], i1 false)
; AVX1-NEXT:    [[CTTZ3:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD3]], i1 false)
; AVX1-NEXT:    store i32 [[CTTZ0]], ptr @dst32, align 4
; AVX1-NEXT:    store i32 [[CTTZ1]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 1), align 4
; AVX1-NEXT:    store i32 [[CTTZ2]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 2), align 4
; AVX1-NEXT:    store i32 [[CTTZ3]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 3), align 4
; AVX1-NEXT:    ret void
;
; AVX2-LABEL: @cttz_4i32(
; AVX2-NEXT:    [[TMP1:%.*]] = load <4 x i32>, ptr @src32, align 4
; AVX2-NEXT:    [[TMP2:%.*]] = call <4 x i32> @llvm.cttz.v4i32(<4 x i32> [[TMP1]], i1 false)
; AVX2-NEXT:    store <4 x i32> [[TMP2]], ptr @dst32, align 4
; AVX2-NEXT:    ret void
;
; AVX512-LABEL: @cttz_4i32(
; AVX512-NEXT:    [[TMP1:%.*]] = load <4 x i32>, ptr @src32, align 4
; AVX512-NEXT:    [[TMP2:%.*]] = call <4 x i32> @llvm.cttz.v4i32(<4 x i32> [[TMP1]], i1 false)
; AVX512-NEXT:    store <4 x i32> [[TMP2]], ptr @dst32, align 4
; AVX512-NEXT:    ret void
;
  %ld0 = load i32, ptr @src32, align 4
  %ld1 = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 1), align 4
  %ld2 = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 2), align 4
  %ld3 = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 3), align 4
  %cttz0 = call i32 @llvm.cttz.i32(i32 %ld0, i1 0)
  %cttz1 = call i32 @llvm.cttz.i32(i32 %ld1, i1 0)
  %cttz2 = call i32 @llvm.cttz.i32(i32 %ld2, i1 0)
  %cttz3 = call i32 @llvm.cttz.i32(i32 %ld3, i1 0)
  store i32 %cttz0, ptr @dst32, align 4
  store i32 %cttz1, ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 1), align 4
  store i32 %cttz2, ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 2), align 4
  store i32 %cttz3, ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 3), align 4
  ret void
}

define void @cttz_8i32() #0 {
; SSE-LABEL: @cttz_8i32(
; SSE-NEXT:    [[LD0:%.*]] = load i32, ptr @src32, align 2
; SSE-NEXT:    [[LD1:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 1), align 2
; SSE-NEXT:    [[LD2:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 2), align 2
; SSE-NEXT:    [[LD3:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 3), align 2
; SSE-NEXT:    [[LD4:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 4), align 2
; SSE-NEXT:    [[LD5:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 5), align 2
; SSE-NEXT:    [[LD6:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 6), align 2
; SSE-NEXT:    [[LD7:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 7), align 2
; SSE-NEXT:    [[CTTZ0:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD0]], i1 false)
; SSE-NEXT:    [[CTTZ1:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD1]], i1 false)
; SSE-NEXT:    [[CTTZ2:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD2]], i1 false)
; SSE-NEXT:    [[CTTZ3:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD3]], i1 false)
; SSE-NEXT:    [[CTTZ4:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD4]], i1 false)
; SSE-NEXT:    [[CTTZ5:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD5]], i1 false)
; SSE-NEXT:    [[CTTZ6:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD6]], i1 false)
; SSE-NEXT:    [[CTTZ7:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD7]], i1 false)
; SSE-NEXT:    store i32 [[CTTZ0]], ptr @dst32, align 2
; SSE-NEXT:    store i32 [[CTTZ1]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 1), align 2
; SSE-NEXT:    store i32 [[CTTZ2]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 2), align 2
; SSE-NEXT:    store i32 [[CTTZ3]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 3), align 2
; SSE-NEXT:    store i32 [[CTTZ4]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 4), align 2
; SSE-NEXT:    store i32 [[CTTZ5]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 5), align 2
; SSE-NEXT:    store i32 [[CTTZ6]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 6), align 2
; SSE-NEXT:    store i32 [[CTTZ7]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 7), align 2
; SSE-NEXT:    ret void
;
; AVX1-LABEL: @cttz_8i32(
; AVX1-NEXT:    [[LD0:%.*]] = load i32, ptr @src32, align 2
; AVX1-NEXT:    [[LD1:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 1), align 2
; AVX1-NEXT:    [[LD2:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 2), align 2
; AVX1-NEXT:    [[LD3:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 3), align 2
; AVX1-NEXT:    [[LD4:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 4), align 2
; AVX1-NEXT:    [[LD5:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 5), align 2
; AVX1-NEXT:    [[LD6:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 6), align 2
; AVX1-NEXT:    [[LD7:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 7), align 2
; AVX1-NEXT:    [[CTTZ0:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD0]], i1 false)
; AVX1-NEXT:    [[CTTZ1:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD1]], i1 false)
; AVX1-NEXT:    [[CTTZ2:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD2]], i1 false)
; AVX1-NEXT:    [[CTTZ3:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD3]], i1 false)
; AVX1-NEXT:    [[CTTZ4:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD4]], i1 false)
; AVX1-NEXT:    [[CTTZ5:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD5]], i1 false)
; AVX1-NEXT:    [[CTTZ6:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD6]], i1 false)
; AVX1-NEXT:    [[CTTZ7:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD7]], i1 false)
; AVX1-NEXT:    store i32 [[CTTZ0]], ptr @dst32, align 2
; AVX1-NEXT:    store i32 [[CTTZ1]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 1), align 2
; AVX1-NEXT:    store i32 [[CTTZ2]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 2), align 2
; AVX1-NEXT:    store i32 [[CTTZ3]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 3), align 2
; AVX1-NEXT:    store i32 [[CTTZ4]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 4), align 2
; AVX1-NEXT:    store i32 [[CTTZ5]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 5), align 2
; AVX1-NEXT:    store i32 [[CTTZ6]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 6), align 2
; AVX1-NEXT:    store i32 [[CTTZ7]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 7), align 2
; AVX1-NEXT:    ret void
;
; AVX2-LABEL: @cttz_8i32(
; AVX2-NEXT:    [[TMP1:%.*]] = load <8 x i32>, ptr @src32, align 2
; AVX2-NEXT:    [[TMP2:%.*]] = call <8 x i32> @llvm.cttz.v8i32(<8 x i32> [[TMP1]], i1 false)
; AVX2-NEXT:    store <8 x i32> [[TMP2]], ptr @dst32, align 2
; AVX2-NEXT:    ret void
;
; AVX512-LABEL: @cttz_8i32(
; AVX512-NEXT:    [[TMP1:%.*]] = load <8 x i32>, ptr @src32, align 2
; AVX512-NEXT:    [[TMP2:%.*]] = call <8 x i32> @llvm.cttz.v8i32(<8 x i32> [[TMP1]], i1 false)
; AVX512-NEXT:    store <8 x i32> [[TMP2]], ptr @dst32, align 2
; AVX512-NEXT:    ret void
;
  %ld0 = load i32, ptr @src32, align 2
  %ld1 = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 1), align 2
  %ld2 = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 2), align 2
  %ld3 = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 3), align 2
  %ld4 = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 4), align 2
  %ld5 = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 5), align 2
  %ld6 = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 6), align 2
  %ld7 = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 7), align 2
  %cttz0 = call i32 @llvm.cttz.i32(i32 %ld0, i1 0)
  %cttz1 = call i32 @llvm.cttz.i32(i32 %ld1, i1 0)
  %cttz2 = call i32 @llvm.cttz.i32(i32 %ld2, i1 0)
  %cttz3 = call i32 @llvm.cttz.i32(i32 %ld3, i1 0)
  %cttz4 = call i32 @llvm.cttz.i32(i32 %ld4, i1 0)
  %cttz5 = call i32 @llvm.cttz.i32(i32 %ld5, i1 0)
  %cttz6 = call i32 @llvm.cttz.i32(i32 %ld6, i1 0)
  %cttz7 = call i32 @llvm.cttz.i32(i32 %ld7, i1 0)
  store i32 %cttz0, ptr @dst32, align 2
  store i32 %cttz1, ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 1), align 2
  store i32 %cttz2, ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 2), align 2
  store i32 %cttz3, ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 3), align 2
  store i32 %cttz4, ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 4), align 2
  store i32 %cttz5, ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 5), align 2
  store i32 %cttz6, ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 6), align 2
  store i32 %cttz7, ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 7), align 2
  ret void
}

define void @cttz_8i16() #0 {
; CHECK-LABEL: @cttz_8i16(
; CHECK-NEXT:    [[TMP1:%.*]] = load <8 x i16>, ptr @src16, align 2
; CHECK-NEXT:    [[TMP2:%.*]] = call <8 x i16> @llvm.cttz.v8i16(<8 x i16> [[TMP1]], i1 false)
; CHECK-NEXT:    store <8 x i16> [[TMP2]], ptr @dst16, align 2
; CHECK-NEXT:    ret void
;
  %ld0 = load i16, ptr @src16, align 2
  %ld1 = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64 1), align 2
  %ld2 = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64 2), align 2
  %ld3 = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64 3), align 2
  %ld4 = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64 4), align 2
  %ld5 = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64 5), align 2
  %ld6 = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64 6), align 2
  %ld7 = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64 7), align 2
  %cttz0 = call i16 @llvm.cttz.i16(i16 %ld0, i1 0)
  %cttz1 = call i16 @llvm.cttz.i16(i16 %ld1, i1 0)
  %cttz2 = call i16 @llvm.cttz.i16(i16 %ld2, i1 0)
  %cttz3 = call i16 @llvm.cttz.i16(i16 %ld3, i1 0)
  %cttz4 = call i16 @llvm.cttz.i16(i16 %ld4, i1 0)
  %cttz5 = call i16 @llvm.cttz.i16(i16 %ld5, i1 0)
  %cttz6 = call i16 @llvm.cttz.i16(i16 %ld6, i1 0)
  %cttz7 = call i16 @llvm.cttz.i16(i16 %ld7, i1 0)
  store i16 %cttz0, ptr @dst16, align 2
  store i16 %cttz1, ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64 1), align 2
  store i16 %cttz2, ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64 2), align 2
  store i16 %cttz3, ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64 3), align 2
  store i16 %cttz4, ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64 4), align 2
  store i16 %cttz5, ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64 5), align 2
  store i16 %cttz6, ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64 6), align 2
  store i16 %cttz7, ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64 7), align 2
  ret void
}

define void @cttz_16i16() #0 {
; SSE-LABEL: @cttz_16i16(
; SSE-NEXT:    [[TMP1:%.*]] = load <8 x i16>, ptr @src16, align 2
; SSE-NEXT:    [[TMP2:%.*]] = call <8 x i16> @llvm.cttz.v8i16(<8 x i16> [[TMP1]], i1 false)
; SSE-NEXT:    store <8 x i16> [[TMP2]], ptr @dst16, align 2
; SSE-NEXT:    [[TMP3:%.*]] = load <8 x i16>, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64 8), align 2
; SSE-NEXT:    [[TMP4:%.*]] = call <8 x i16> @llvm.cttz.v8i16(<8 x i16> [[TMP3]], i1 false)
; SSE-NEXT:    store <8 x i16> [[TMP4]], ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64 8), align 2
; SSE-NEXT:    ret void
;
; AVX-LABEL: @cttz_16i16(
; AVX-NEXT:    [[TMP1:%.*]] = load <16 x i16>, ptr @src16, align 2
; AVX-NEXT:    [[TMP2:%.*]] = call <16 x i16> @llvm.cttz.v16i16(<16 x i16> [[TMP1]], i1 false)
; AVX-NEXT:    store <16 x i16> [[TMP2]], ptr @dst16, align 2
; AVX-NEXT:    ret void
;
  %ld0  = load i16, ptr @src16, align 2
  %ld1  = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64  1), align 2
  %ld2  = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64  2), align 2
  %ld3  = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64  3), align 2
  %ld4  = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64  4), align 2
  %ld5  = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64  5), align 2
  %ld6  = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64  6), align 2
  %ld7  = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64  7), align 2
  %ld8  = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64  8), align 2
  %ld9  = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64  9), align 2
  %ld10 = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64 10), align 2
  %ld11 = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64 11), align 2
  %ld12 = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64 12), align 2
  %ld13 = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64 13), align 2
  %ld14 = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64 14), align 2
  %ld15 = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64 15), align 2
  %cttz0  = call i16 @llvm.cttz.i16(i16 %ld0, i1 0)
  %cttz1  = call i16 @llvm.cttz.i16(i16 %ld1, i1 0)
  %cttz2  = call i16 @llvm.cttz.i16(i16 %ld2, i1 0)
  %cttz3  = call i16 @llvm.cttz.i16(i16 %ld3, i1 0)
  %cttz4  = call i16 @llvm.cttz.i16(i16 %ld4, i1 0)
  %cttz5  = call i16 @llvm.cttz.i16(i16 %ld5, i1 0)
  %cttz6  = call i16 @llvm.cttz.i16(i16 %ld6, i1 0)
  %cttz7  = call i16 @llvm.cttz.i16(i16 %ld7, i1 0)
  %cttz8  = call i16 @llvm.cttz.i16(i16 %ld8, i1 0)
  %cttz9  = call i16 @llvm.cttz.i16(i16 %ld9, i1 0)
  %cttz10 = call i16 @llvm.cttz.i16(i16 %ld10, i1 0)
  %cttz11 = call i16 @llvm.cttz.i16(i16 %ld11, i1 0)
  %cttz12 = call i16 @llvm.cttz.i16(i16 %ld12, i1 0)
  %cttz13 = call i16 @llvm.cttz.i16(i16 %ld13, i1 0)
  %cttz14 = call i16 @llvm.cttz.i16(i16 %ld14, i1 0)
  %cttz15 = call i16 @llvm.cttz.i16(i16 %ld15, i1 0)
  store i16 %cttz0 , ptr @dst16, align 2
  store i16 %cttz1 , ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64  1), align 2
  store i16 %cttz2 , ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64  2), align 2
  store i16 %cttz3 , ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64  3), align 2
  store i16 %cttz4 , ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64  4), align 2
  store i16 %cttz5 , ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64  5), align 2
  store i16 %cttz6 , ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64  6), align 2
  store i16 %cttz7 , ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64  7), align 2
  store i16 %cttz8 , ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64  8), align 2
  store i16 %cttz9 , ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64  9), align 2
  store i16 %cttz10, ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64 10), align 2
  store i16 %cttz11, ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64 11), align 2
  store i16 %cttz12, ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64 12), align 2
  store i16 %cttz13, ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64 13), align 2
  store i16 %cttz14, ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64 14), align 2
  store i16 %cttz15, ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64 15), align 2
  ret void
}

define void @cttz_16i8() #0 {
; CHECK-LABEL: @cttz_16i8(
; CHECK-NEXT:    [[TMP1:%.*]] = load <16 x i8>, ptr @src8, align 1
; CHECK-NEXT:    [[TMP2:%.*]] = call <16 x i8> @llvm.cttz.v16i8(<16 x i8> [[TMP1]], i1 false)
; CHECK-NEXT:    store <16 x i8> [[TMP2]], ptr @dst8, align 1
; CHECK-NEXT:    ret void
;
  %ld0  = load i8, ptr @src8, align 1
  %ld1  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  1), align 1
  %ld2  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  2), align 1
  %ld3  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  3), align 1
  %ld4  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  4), align 1
  %ld5  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  5), align 1
  %ld6  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  6), align 1
  %ld7  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  7), align 1
  %ld8  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  8), align 1
  %ld9  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  9), align 1
  %ld10 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 10), align 1
  %ld11 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 11), align 1
  %ld12 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 12), align 1
  %ld13 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 13), align 1
  %ld14 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 14), align 1
  %ld15 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 15), align 1
  %cttz0  = call i8 @llvm.cttz.i8(i8 %ld0, i1 0)
  %cttz1  = call i8 @llvm.cttz.i8(i8 %ld1, i1 0)
  %cttz2  = call i8 @llvm.cttz.i8(i8 %ld2, i1 0)
  %cttz3  = call i8 @llvm.cttz.i8(i8 %ld3, i1 0)
  %cttz4  = call i8 @llvm.cttz.i8(i8 %ld4, i1 0)
  %cttz5  = call i8 @llvm.cttz.i8(i8 %ld5, i1 0)
  %cttz6  = call i8 @llvm.cttz.i8(i8 %ld6, i1 0)
  %cttz7  = call i8 @llvm.cttz.i8(i8 %ld7, i1 0)
  %cttz8  = call i8 @llvm.cttz.i8(i8 %ld8, i1 0)
  %cttz9  = call i8 @llvm.cttz.i8(i8 %ld9, i1 0)
  %cttz10 = call i8 @llvm.cttz.i8(i8 %ld10, i1 0)
  %cttz11 = call i8 @llvm.cttz.i8(i8 %ld11, i1 0)
  %cttz12 = call i8 @llvm.cttz.i8(i8 %ld12, i1 0)
  %cttz13 = call i8 @llvm.cttz.i8(i8 %ld13, i1 0)
  %cttz14 = call i8 @llvm.cttz.i8(i8 %ld14, i1 0)
  %cttz15 = call i8 @llvm.cttz.i8(i8 %ld15, i1 0)
  store i8 %cttz0 , ptr @dst8, align 1
  store i8 %cttz1 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  1), align 1
  store i8 %cttz2 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  2), align 1
  store i8 %cttz3 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  3), align 1
  store i8 %cttz4 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  4), align 1
  store i8 %cttz5 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  5), align 1
  store i8 %cttz6 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  6), align 1
  store i8 %cttz7 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  7), align 1
  store i8 %cttz8 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  8), align 1
  store i8 %cttz9 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  9), align 1
  store i8 %cttz10, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 10), align 1
  store i8 %cttz11, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 11), align 1
  store i8 %cttz12, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 12), align 1
  store i8 %cttz13, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 13), align 1
  store i8 %cttz14, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 14), align 1
  store i8 %cttz15, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 15), align 1
  ret void
}

define void @cttz_32i8() #0 {
; SSE-LABEL: @cttz_32i8(
; SSE-NEXT:    [[TMP1:%.*]] = load <16 x i8>, ptr @src8, align 1
; SSE-NEXT:    [[TMP2:%.*]] = call <16 x i8> @llvm.cttz.v16i8(<16 x i8> [[TMP1]], i1 false)
; SSE-NEXT:    store <16 x i8> [[TMP2]], ptr @dst8, align 1
; SSE-NEXT:    [[TMP3:%.*]] = load <16 x i8>, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 16), align 1
; SSE-NEXT:    [[TMP4:%.*]] = call <16 x i8> @llvm.cttz.v16i8(<16 x i8> [[TMP3]], i1 false)
; SSE-NEXT:    store <16 x i8> [[TMP4]], ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 16), align 1
; SSE-NEXT:    ret void
;
; AVX-LABEL: @cttz_32i8(
; AVX-NEXT:    [[TMP1:%.*]] = load <32 x i8>, ptr @src8, align 1
; AVX-NEXT:    [[TMP2:%.*]] = call <32 x i8> @llvm.cttz.v32i8(<32 x i8> [[TMP1]], i1 false)
; AVX-NEXT:    store <32 x i8> [[TMP2]], ptr @dst8, align 1
; AVX-NEXT:    ret void
;
  %ld0  = load i8, ptr @src8, align 1
  %ld1  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  1), align 1
  %ld2  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  2), align 1
  %ld3  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  3), align 1
  %ld4  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  4), align 1
  %ld5  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  5), align 1
  %ld6  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  6), align 1
  %ld7  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  7), align 1
  %ld8  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  8), align 1
  %ld9  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  9), align 1
  %ld10 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 10), align 1
  %ld11 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 11), align 1
  %ld12 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 12), align 1
  %ld13 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 13), align 1
  %ld14 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 14), align 1
  %ld15 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 15), align 1
  %ld16 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 16), align 1
  %ld17 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 17), align 1
  %ld18 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 18), align 1
  %ld19 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 19), align 1
  %ld20 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 20), align 1
  %ld21 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 21), align 1
  %ld22 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 22), align 1
  %ld23 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 23), align 1
  %ld24 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 24), align 1
  %ld25 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 25), align 1
  %ld26 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 26), align 1
  %ld27 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 27), align 1
  %ld28 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 28), align 1
  %ld29 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 29), align 1
  %ld30 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 30), align 1
  %ld31 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 31), align 1
  %cttz0  = call i8 @llvm.cttz.i8(i8 %ld0, i1 0)
  %cttz1  = call i8 @llvm.cttz.i8(i8 %ld1, i1 0)
  %cttz2  = call i8 @llvm.cttz.i8(i8 %ld2, i1 0)
  %cttz3  = call i8 @llvm.cttz.i8(i8 %ld3, i1 0)
  %cttz4  = call i8 @llvm.cttz.i8(i8 %ld4, i1 0)
  %cttz5  = call i8 @llvm.cttz.i8(i8 %ld5, i1 0)
  %cttz6  = call i8 @llvm.cttz.i8(i8 %ld6, i1 0)
  %cttz7  = call i8 @llvm.cttz.i8(i8 %ld7, i1 0)
  %cttz8  = call i8 @llvm.cttz.i8(i8 %ld8, i1 0)
  %cttz9  = call i8 @llvm.cttz.i8(i8 %ld9, i1 0)
  %cttz10 = call i8 @llvm.cttz.i8(i8 %ld10, i1 0)
  %cttz11 = call i8 @llvm.cttz.i8(i8 %ld11, i1 0)
  %cttz12 = call i8 @llvm.cttz.i8(i8 %ld12, i1 0)
  %cttz13 = call i8 @llvm.cttz.i8(i8 %ld13, i1 0)
  %cttz14 = call i8 @llvm.cttz.i8(i8 %ld14, i1 0)
  %cttz15 = call i8 @llvm.cttz.i8(i8 %ld15, i1 0)
  %cttz16 = call i8 @llvm.cttz.i8(i8 %ld16, i1 0)
  %cttz17 = call i8 @llvm.cttz.i8(i8 %ld17, i1 0)
  %cttz18 = call i8 @llvm.cttz.i8(i8 %ld18, i1 0)
  %cttz19 = call i8 @llvm.cttz.i8(i8 %ld19, i1 0)
  %cttz20 = call i8 @llvm.cttz.i8(i8 %ld20, i1 0)
  %cttz21 = call i8 @llvm.cttz.i8(i8 %ld21, i1 0)
  %cttz22 = call i8 @llvm.cttz.i8(i8 %ld22, i1 0)
  %cttz23 = call i8 @llvm.cttz.i8(i8 %ld23, i1 0)
  %cttz24 = call i8 @llvm.cttz.i8(i8 %ld24, i1 0)
  %cttz25 = call i8 @llvm.cttz.i8(i8 %ld25, i1 0)
  %cttz26 = call i8 @llvm.cttz.i8(i8 %ld26, i1 0)
  %cttz27 = call i8 @llvm.cttz.i8(i8 %ld27, i1 0)
  %cttz28 = call i8 @llvm.cttz.i8(i8 %ld28, i1 0)
  %cttz29 = call i8 @llvm.cttz.i8(i8 %ld29, i1 0)
  %cttz30 = call i8 @llvm.cttz.i8(i8 %ld30, i1 0)
  %cttz31 = call i8 @llvm.cttz.i8(i8 %ld31, i1 0)
  store i8 %cttz0 , ptr @dst8, align 1
  store i8 %cttz1 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  1), align 1
  store i8 %cttz2 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  2), align 1
  store i8 %cttz3 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  3), align 1
  store i8 %cttz4 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  4), align 1
  store i8 %cttz5 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  5), align 1
  store i8 %cttz6 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  6), align 1
  store i8 %cttz7 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  7), align 1
  store i8 %cttz8 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  8), align 1
  store i8 %cttz9 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  9), align 1
  store i8 %cttz10, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 10), align 1
  store i8 %cttz11, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 11), align 1
  store i8 %cttz12, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 12), align 1
  store i8 %cttz13, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 13), align 1
  store i8 %cttz14, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 14), align 1
  store i8 %cttz15, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 15), align 1
  store i8 %cttz16, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 16), align 1
  store i8 %cttz17, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 17), align 1
  store i8 %cttz18, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 18), align 1
  store i8 %cttz19, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 19), align 1
  store i8 %cttz20, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 20), align 1
  store i8 %cttz21, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 21), align 1
  store i8 %cttz22, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 22), align 1
  store i8 %cttz23, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 23), align 1
  store i8 %cttz24, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 24), align 1
  store i8 %cttz25, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 25), align 1
  store i8 %cttz26, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 26), align 1
  store i8 %cttz27, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 27), align 1
  store i8 %cttz28, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 28), align 1
  store i8 %cttz29, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 29), align 1
  store i8 %cttz30, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 30), align 1
  store i8 %cttz31, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 31), align 1
  ret void
}

;
; CTTZ_ZERO_UNDEF
;

define void @cttz_undef_2i64() #0 {
; SSE-LABEL: @cttz_undef_2i64(
; SSE-NEXT:    [[LD0:%.*]] = load i64, ptr @src64, align 8
; SSE-NEXT:    [[LD1:%.*]] = load i64, ptr getelementptr inbounds ([4 x i64], ptr @src64, i32 0, i64 1), align 8
; SSE-NEXT:    [[CTTZ0:%.*]] = call i64 @llvm.cttz.i64(i64 [[LD0]], i1 true)
; SSE-NEXT:    [[CTTZ1:%.*]] = call i64 @llvm.cttz.i64(i64 [[LD1]], i1 true)
; SSE-NEXT:    store i64 [[CTTZ0]], ptr @dst64, align 8
; SSE-NEXT:    store i64 [[CTTZ1]], ptr getelementptr inbounds ([4 x i64], ptr @dst64, i32 0, i64 1), align 8
; SSE-NEXT:    ret void
;
; AVX1-LABEL: @cttz_undef_2i64(
; AVX1-NEXT:    [[LD0:%.*]] = load i64, ptr @src64, align 8
; AVX1-NEXT:    [[LD1:%.*]] = load i64, ptr getelementptr inbounds ([4 x i64], ptr @src64, i32 0, i64 1), align 8
; AVX1-NEXT:    [[CTTZ0:%.*]] = call i64 @llvm.cttz.i64(i64 [[LD0]], i1 true)
; AVX1-NEXT:    [[CTTZ1:%.*]] = call i64 @llvm.cttz.i64(i64 [[LD1]], i1 true)
; AVX1-NEXT:    store i64 [[CTTZ0]], ptr @dst64, align 8
; AVX1-NEXT:    store i64 [[CTTZ1]], ptr getelementptr inbounds ([4 x i64], ptr @dst64, i32 0, i64 1), align 8
; AVX1-NEXT:    ret void
;
; AVX2-LABEL: @cttz_undef_2i64(
; AVX2-NEXT:    [[LD0:%.*]] = load i64, ptr @src64, align 8
; AVX2-NEXT:    [[LD1:%.*]] = load i64, ptr getelementptr inbounds ([4 x i64], ptr @src64, i32 0, i64 1), align 8
; AVX2-NEXT:    [[CTTZ0:%.*]] = call i64 @llvm.cttz.i64(i64 [[LD0]], i1 true)
; AVX2-NEXT:    [[CTTZ1:%.*]] = call i64 @llvm.cttz.i64(i64 [[LD1]], i1 true)
; AVX2-NEXT:    store i64 [[CTTZ0]], ptr @dst64, align 8
; AVX2-NEXT:    store i64 [[CTTZ1]], ptr getelementptr inbounds ([4 x i64], ptr @dst64, i32 0, i64 1), align 8
; AVX2-NEXT:    ret void
;
; AVX512-LABEL: @cttz_undef_2i64(
; AVX512-NEXT:    [[TMP1:%.*]] = load <2 x i64>, ptr @src64, align 8
; AVX512-NEXT:    [[TMP2:%.*]] = call <2 x i64> @llvm.cttz.v2i64(<2 x i64> [[TMP1]], i1 true)
; AVX512-NEXT:    store <2 x i64> [[TMP2]], ptr @dst64, align 8
; AVX512-NEXT:    ret void
;
  %ld0 = load i64, ptr @src64, align 8
  %ld1 = load i64, ptr getelementptr inbounds ([4 x i64], ptr @src64, i32 0, i64 1), align 8
  %cttz0 = call i64 @llvm.cttz.i64(i64 %ld0, i1 -1)
  %cttz1 = call i64 @llvm.cttz.i64(i64 %ld1, i1 -1)
  store i64 %cttz0, ptr @dst64, align 8
  store i64 %cttz1, ptr getelementptr inbounds ([4 x i64], ptr @dst64, i32 0, i64 1), align 8
  ret void
}

define void @cttz_undef_4i64() #0 {
; SSE-LABEL: @cttz_undef_4i64(
; SSE-NEXT:    [[LD0:%.*]] = load i64, ptr @src64, align 4
; SSE-NEXT:    [[LD1:%.*]] = load i64, ptr getelementptr inbounds ([4 x i64], ptr @src64, i64 0, i64 1), align 4
; SSE-NEXT:    [[LD2:%.*]] = load i64, ptr getelementptr inbounds ([4 x i64], ptr @src64, i64 0, i64 2), align 4
; SSE-NEXT:    [[LD3:%.*]] = load i64, ptr getelementptr inbounds ([4 x i64], ptr @src64, i64 0, i64 3), align 4
; SSE-NEXT:    [[CTTZ0:%.*]] = call i64 @llvm.cttz.i64(i64 [[LD0]], i1 true)
; SSE-NEXT:    [[CTTZ1:%.*]] = call i64 @llvm.cttz.i64(i64 [[LD1]], i1 true)
; SSE-NEXT:    [[CTTZ2:%.*]] = call i64 @llvm.cttz.i64(i64 [[LD2]], i1 true)
; SSE-NEXT:    [[CTTZ3:%.*]] = call i64 @llvm.cttz.i64(i64 [[LD3]], i1 true)
; SSE-NEXT:    store i64 [[CTTZ0]], ptr @dst64, align 4
; SSE-NEXT:    store i64 [[CTTZ1]], ptr getelementptr inbounds ([4 x i64], ptr @dst64, i64 0, i64 1), align 4
; SSE-NEXT:    store i64 [[CTTZ2]], ptr getelementptr inbounds ([4 x i64], ptr @dst64, i64 0, i64 2), align 4
; SSE-NEXT:    store i64 [[CTTZ3]], ptr getelementptr inbounds ([4 x i64], ptr @dst64, i64 0, i64 3), align 4
; SSE-NEXT:    ret void
;
; AVX1-LABEL: @cttz_undef_4i64(
; AVX1-NEXT:    [[LD0:%.*]] = load i64, ptr @src64, align 4
; AVX1-NEXT:    [[LD1:%.*]] = load i64, ptr getelementptr inbounds ([4 x i64], ptr @src64, i64 0, i64 1), align 4
; AVX1-NEXT:    [[LD2:%.*]] = load i64, ptr getelementptr inbounds ([4 x i64], ptr @src64, i64 0, i64 2), align 4
; AVX1-NEXT:    [[LD3:%.*]] = load i64, ptr getelementptr inbounds ([4 x i64], ptr @src64, i64 0, i64 3), align 4
; AVX1-NEXT:    [[CTTZ0:%.*]] = call i64 @llvm.cttz.i64(i64 [[LD0]], i1 true)
; AVX1-NEXT:    [[CTTZ1:%.*]] = call i64 @llvm.cttz.i64(i64 [[LD1]], i1 true)
; AVX1-NEXT:    [[CTTZ2:%.*]] = call i64 @llvm.cttz.i64(i64 [[LD2]], i1 true)
; AVX1-NEXT:    [[CTTZ3:%.*]] = call i64 @llvm.cttz.i64(i64 [[LD3]], i1 true)
; AVX1-NEXT:    store i64 [[CTTZ0]], ptr @dst64, align 4
; AVX1-NEXT:    store i64 [[CTTZ1]], ptr getelementptr inbounds ([4 x i64], ptr @dst64, i64 0, i64 1), align 4
; AVX1-NEXT:    store i64 [[CTTZ2]], ptr getelementptr inbounds ([4 x i64], ptr @dst64, i64 0, i64 2), align 4
; AVX1-NEXT:    store i64 [[CTTZ3]], ptr getelementptr inbounds ([4 x i64], ptr @dst64, i64 0, i64 3), align 4
; AVX1-NEXT:    ret void
;
; AVX2-LABEL: @cttz_undef_4i64(
; AVX2-NEXT:    [[TMP1:%.*]] = load <4 x i64>, ptr @src64, align 4
; AVX2-NEXT:    [[TMP2:%.*]] = call <4 x i64> @llvm.cttz.v4i64(<4 x i64> [[TMP1]], i1 true)
; AVX2-NEXT:    store <4 x i64> [[TMP2]], ptr @dst64, align 4
; AVX2-NEXT:    ret void
;
; AVX512-LABEL: @cttz_undef_4i64(
; AVX512-NEXT:    [[TMP1:%.*]] = load <4 x i64>, ptr @src64, align 4
; AVX512-NEXT:    [[TMP2:%.*]] = call <4 x i64> @llvm.cttz.v4i64(<4 x i64> [[TMP1]], i1 true)
; AVX512-NEXT:    store <4 x i64> [[TMP2]], ptr @dst64, align 4
; AVX512-NEXT:    ret void
;
  %ld0 = load i64, ptr @src64, align 4
  %ld1 = load i64, ptr getelementptr inbounds ([4 x i64], ptr @src64, i64 0, i64 1), align 4
  %ld2 = load i64, ptr getelementptr inbounds ([4 x i64], ptr @src64, i64 0, i64 2), align 4
  %ld3 = load i64, ptr getelementptr inbounds ([4 x i64], ptr @src64, i64 0, i64 3), align 4
  %cttz0 = call i64 @llvm.cttz.i64(i64 %ld0, i1 -1)
  %cttz1 = call i64 @llvm.cttz.i64(i64 %ld1, i1 -1)
  %cttz2 = call i64 @llvm.cttz.i64(i64 %ld2, i1 -1)
  %cttz3 = call i64 @llvm.cttz.i64(i64 %ld3, i1 -1)
  store i64 %cttz0, ptr @dst64, align 4
  store i64 %cttz1, ptr getelementptr inbounds ([4 x i64], ptr @dst64, i64 0, i64 1), align 4
  store i64 %cttz2, ptr getelementptr inbounds ([4 x i64], ptr @dst64, i64 0, i64 2), align 4
  store i64 %cttz3, ptr getelementptr inbounds ([4 x i64], ptr @dst64, i64 0, i64 3), align 4
  ret void
}

define void @cttz_undef_4i32() #0 {
; SSE-LABEL: @cttz_undef_4i32(
; SSE-NEXT:    [[LD0:%.*]] = load i32, ptr @src32, align 4
; SSE-NEXT:    [[LD1:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 1), align 4
; SSE-NEXT:    [[LD2:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 2), align 4
; SSE-NEXT:    [[LD3:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 3), align 4
; SSE-NEXT:    [[CTTZ0:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD0]], i1 true)
; SSE-NEXT:    [[CTTZ1:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD1]], i1 true)
; SSE-NEXT:    [[CTTZ2:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD2]], i1 true)
; SSE-NEXT:    [[CTTZ3:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD3]], i1 true)
; SSE-NEXT:    store i32 [[CTTZ0]], ptr @dst32, align 4
; SSE-NEXT:    store i32 [[CTTZ1]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 1), align 4
; SSE-NEXT:    store i32 [[CTTZ2]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 2), align 4
; SSE-NEXT:    store i32 [[CTTZ3]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 3), align 4
; SSE-NEXT:    ret void
;
; AVX1-LABEL: @cttz_undef_4i32(
; AVX1-NEXT:    [[LD0:%.*]] = load i32, ptr @src32, align 4
; AVX1-NEXT:    [[LD1:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 1), align 4
; AVX1-NEXT:    [[LD2:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 2), align 4
; AVX1-NEXT:    [[LD3:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 3), align 4
; AVX1-NEXT:    [[CTTZ0:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD0]], i1 true)
; AVX1-NEXT:    [[CTTZ1:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD1]], i1 true)
; AVX1-NEXT:    [[CTTZ2:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD2]], i1 true)
; AVX1-NEXT:    [[CTTZ3:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD3]], i1 true)
; AVX1-NEXT:    store i32 [[CTTZ0]], ptr @dst32, align 4
; AVX1-NEXT:    store i32 [[CTTZ1]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 1), align 4
; AVX1-NEXT:    store i32 [[CTTZ2]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 2), align 4
; AVX1-NEXT:    store i32 [[CTTZ3]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 3), align 4
; AVX1-NEXT:    ret void
;
; AVX2-LABEL: @cttz_undef_4i32(
; AVX2-NEXT:    [[TMP1:%.*]] = load <4 x i32>, ptr @src32, align 4
; AVX2-NEXT:    [[TMP2:%.*]] = call <4 x i32> @llvm.cttz.v4i32(<4 x i32> [[TMP1]], i1 true)
; AVX2-NEXT:    store <4 x i32> [[TMP2]], ptr @dst32, align 4
; AVX2-NEXT:    ret void
;
; AVX512-LABEL: @cttz_undef_4i32(
; AVX512-NEXT:    [[TMP1:%.*]] = load <4 x i32>, ptr @src32, align 4
; AVX512-NEXT:    [[TMP2:%.*]] = call <4 x i32> @llvm.cttz.v4i32(<4 x i32> [[TMP1]], i1 true)
; AVX512-NEXT:    store <4 x i32> [[TMP2]], ptr @dst32, align 4
; AVX512-NEXT:    ret void
;
  %ld0 = load i32, ptr @src32, align 4
  %ld1 = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 1), align 4
  %ld2 = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 2), align 4
  %ld3 = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 3), align 4
  %cttz0 = call i32 @llvm.cttz.i32(i32 %ld0, i1 -1)
  %cttz1 = call i32 @llvm.cttz.i32(i32 %ld1, i1 -1)
  %cttz2 = call i32 @llvm.cttz.i32(i32 %ld2, i1 -1)
  %cttz3 = call i32 @llvm.cttz.i32(i32 %ld3, i1 -1)
  store i32 %cttz0, ptr @dst32, align 4
  store i32 %cttz1, ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 1), align 4
  store i32 %cttz2, ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 2), align 4
  store i32 %cttz3, ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 3), align 4
  ret void
}

define void @cttz_undef_8i32() #0 {
; SSE-LABEL: @cttz_undef_8i32(
; SSE-NEXT:    [[LD0:%.*]] = load i32, ptr @src32, align 2
; SSE-NEXT:    [[LD1:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 1), align 2
; SSE-NEXT:    [[LD2:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 2), align 2
; SSE-NEXT:    [[LD3:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 3), align 2
; SSE-NEXT:    [[LD4:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 4), align 2
; SSE-NEXT:    [[LD5:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 5), align 2
; SSE-NEXT:    [[LD6:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 6), align 2
; SSE-NEXT:    [[LD7:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 7), align 2
; SSE-NEXT:    [[CTTZ0:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD0]], i1 true)
; SSE-NEXT:    [[CTTZ1:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD1]], i1 true)
; SSE-NEXT:    [[CTTZ2:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD2]], i1 true)
; SSE-NEXT:    [[CTTZ3:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD3]], i1 true)
; SSE-NEXT:    [[CTTZ4:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD4]], i1 true)
; SSE-NEXT:    [[CTTZ5:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD5]], i1 true)
; SSE-NEXT:    [[CTTZ6:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD6]], i1 true)
; SSE-NEXT:    [[CTTZ7:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD7]], i1 true)
; SSE-NEXT:    store i32 [[CTTZ0]], ptr @dst32, align 2
; SSE-NEXT:    store i32 [[CTTZ1]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 1), align 2
; SSE-NEXT:    store i32 [[CTTZ2]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 2), align 2
; SSE-NEXT:    store i32 [[CTTZ3]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 3), align 2
; SSE-NEXT:    store i32 [[CTTZ4]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 4), align 2
; SSE-NEXT:    store i32 [[CTTZ5]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 5), align 2
; SSE-NEXT:    store i32 [[CTTZ6]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 6), align 2
; SSE-NEXT:    store i32 [[CTTZ7]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 7), align 2
; SSE-NEXT:    ret void
;
; AVX1-LABEL: @cttz_undef_8i32(
; AVX1-NEXT:    [[LD0:%.*]] = load i32, ptr @src32, align 2
; AVX1-NEXT:    [[LD1:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 1), align 2
; AVX1-NEXT:    [[LD2:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 2), align 2
; AVX1-NEXT:    [[LD3:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 3), align 2
; AVX1-NEXT:    [[LD4:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 4), align 2
; AVX1-NEXT:    [[LD5:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 5), align 2
; AVX1-NEXT:    [[LD6:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 6), align 2
; AVX1-NEXT:    [[LD7:%.*]] = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 7), align 2
; AVX1-NEXT:    [[CTTZ0:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD0]], i1 true)
; AVX1-NEXT:    [[CTTZ1:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD1]], i1 true)
; AVX1-NEXT:    [[CTTZ2:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD2]], i1 true)
; AVX1-NEXT:    [[CTTZ3:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD3]], i1 true)
; AVX1-NEXT:    [[CTTZ4:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD4]], i1 true)
; AVX1-NEXT:    [[CTTZ5:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD5]], i1 true)
; AVX1-NEXT:    [[CTTZ6:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD6]], i1 true)
; AVX1-NEXT:    [[CTTZ7:%.*]] = call i32 @llvm.cttz.i32(i32 [[LD7]], i1 true)
; AVX1-NEXT:    store i32 [[CTTZ0]], ptr @dst32, align 2
; AVX1-NEXT:    store i32 [[CTTZ1]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 1), align 2
; AVX1-NEXT:    store i32 [[CTTZ2]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 2), align 2
; AVX1-NEXT:    store i32 [[CTTZ3]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 3), align 2
; AVX1-NEXT:    store i32 [[CTTZ4]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 4), align 2
; AVX1-NEXT:    store i32 [[CTTZ5]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 5), align 2
; AVX1-NEXT:    store i32 [[CTTZ6]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 6), align 2
; AVX1-NEXT:    store i32 [[CTTZ7]], ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 7), align 2
; AVX1-NEXT:    ret void
;
; AVX2-LABEL: @cttz_undef_8i32(
; AVX2-NEXT:    [[TMP1:%.*]] = load <8 x i32>, ptr @src32, align 2
; AVX2-NEXT:    [[TMP2:%.*]] = call <8 x i32> @llvm.cttz.v8i32(<8 x i32> [[TMP1]], i1 true)
; AVX2-NEXT:    store <8 x i32> [[TMP2]], ptr @dst32, align 2
; AVX2-NEXT:    ret void
;
; AVX512-LABEL: @cttz_undef_8i32(
; AVX512-NEXT:    [[TMP1:%.*]] = load <8 x i32>, ptr @src32, align 2
; AVX512-NEXT:    [[TMP2:%.*]] = call <8 x i32> @llvm.cttz.v8i32(<8 x i32> [[TMP1]], i1 true)
; AVX512-NEXT:    store <8 x i32> [[TMP2]], ptr @dst32, align 2
; AVX512-NEXT:    ret void
;
  %ld0 = load i32, ptr @src32, align 2
  %ld1 = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 1), align 2
  %ld2 = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 2), align 2
  %ld3 = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 3), align 2
  %ld4 = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 4), align 2
  %ld5 = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 5), align 2
  %ld6 = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 6), align 2
  %ld7 = load i32, ptr getelementptr inbounds ([8 x i32], ptr @src32, i32 0, i64 7), align 2
  %cttz0 = call i32 @llvm.cttz.i32(i32 %ld0, i1 -1)
  %cttz1 = call i32 @llvm.cttz.i32(i32 %ld1, i1 -1)
  %cttz2 = call i32 @llvm.cttz.i32(i32 %ld2, i1 -1)
  %cttz3 = call i32 @llvm.cttz.i32(i32 %ld3, i1 -1)
  %cttz4 = call i32 @llvm.cttz.i32(i32 %ld4, i1 -1)
  %cttz5 = call i32 @llvm.cttz.i32(i32 %ld5, i1 -1)
  %cttz6 = call i32 @llvm.cttz.i32(i32 %ld6, i1 -1)
  %cttz7 = call i32 @llvm.cttz.i32(i32 %ld7, i1 -1)
  store i32 %cttz0, ptr @dst32, align 2
  store i32 %cttz1, ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 1), align 2
  store i32 %cttz2, ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 2), align 2
  store i32 %cttz3, ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 3), align 2
  store i32 %cttz4, ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 4), align 2
  store i32 %cttz5, ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 5), align 2
  store i32 %cttz6, ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 6), align 2
  store i32 %cttz7, ptr getelementptr inbounds ([8 x i32], ptr @dst32, i32 0, i64 7), align 2
  ret void
}

define void @cttz_undef_8i16() #0 {
; CHECK-LABEL: @cttz_undef_8i16(
; CHECK-NEXT:    [[TMP1:%.*]] = load <8 x i16>, ptr @src16, align 2
; CHECK-NEXT:    [[TMP2:%.*]] = call <8 x i16> @llvm.cttz.v8i16(<8 x i16> [[TMP1]], i1 true)
; CHECK-NEXT:    store <8 x i16> [[TMP2]], ptr @dst16, align 2
; CHECK-NEXT:    ret void
;
  %ld0 = load i16, ptr @src16, align 2
  %ld1 = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64 1), align 2
  %ld2 = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64 2), align 2
  %ld3 = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64 3), align 2
  %ld4 = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64 4), align 2
  %ld5 = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64 5), align 2
  %ld6 = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64 6), align 2
  %ld7 = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64 7), align 2
  %cttz0 = call i16 @llvm.cttz.i16(i16 %ld0, i1 -1)
  %cttz1 = call i16 @llvm.cttz.i16(i16 %ld1, i1 -1)
  %cttz2 = call i16 @llvm.cttz.i16(i16 %ld2, i1 -1)
  %cttz3 = call i16 @llvm.cttz.i16(i16 %ld3, i1 -1)
  %cttz4 = call i16 @llvm.cttz.i16(i16 %ld4, i1 -1)
  %cttz5 = call i16 @llvm.cttz.i16(i16 %ld5, i1 -1)
  %cttz6 = call i16 @llvm.cttz.i16(i16 %ld6, i1 -1)
  %cttz7 = call i16 @llvm.cttz.i16(i16 %ld7, i1 -1)
  store i16 %cttz0, ptr @dst16, align 2
  store i16 %cttz1, ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64 1), align 2
  store i16 %cttz2, ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64 2), align 2
  store i16 %cttz3, ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64 3), align 2
  store i16 %cttz4, ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64 4), align 2
  store i16 %cttz5, ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64 5), align 2
  store i16 %cttz6, ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64 6), align 2
  store i16 %cttz7, ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64 7), align 2
  ret void
}

define void @cttz_undef_16i16() #0 {
; SSE-LABEL: @cttz_undef_16i16(
; SSE-NEXT:    [[TMP1:%.*]] = load <8 x i16>, ptr @src16, align 2
; SSE-NEXT:    [[TMP2:%.*]] = call <8 x i16> @llvm.cttz.v8i16(<8 x i16> [[TMP1]], i1 true)
; SSE-NEXT:    store <8 x i16> [[TMP2]], ptr @dst16, align 2
; SSE-NEXT:    [[TMP3:%.*]] = load <8 x i16>, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64 8), align 2
; SSE-NEXT:    [[TMP4:%.*]] = call <8 x i16> @llvm.cttz.v8i16(<8 x i16> [[TMP3]], i1 true)
; SSE-NEXT:    store <8 x i16> [[TMP4]], ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64 8), align 2
; SSE-NEXT:    ret void
;
; AVX-LABEL: @cttz_undef_16i16(
; AVX-NEXT:    [[TMP1:%.*]] = load <16 x i16>, ptr @src16, align 2
; AVX-NEXT:    [[TMP2:%.*]] = call <16 x i16> @llvm.cttz.v16i16(<16 x i16> [[TMP1]], i1 true)
; AVX-NEXT:    store <16 x i16> [[TMP2]], ptr @dst16, align 2
; AVX-NEXT:    ret void
;
  %ld0  = load i16, ptr @src16, align 2
  %ld1  = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64  1), align 2
  %ld2  = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64  2), align 2
  %ld3  = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64  3), align 2
  %ld4  = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64  4), align 2
  %ld5  = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64  5), align 2
  %ld6  = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64  6), align 2
  %ld7  = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64  7), align 2
  %ld8  = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64  8), align 2
  %ld9  = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64  9), align 2
  %ld10 = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64 10), align 2
  %ld11 = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64 11), align 2
  %ld12 = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64 12), align 2
  %ld13 = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64 13), align 2
  %ld14 = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64 14), align 2
  %ld15 = load i16, ptr getelementptr inbounds ([16 x i16], ptr @src16, i16 0, i64 15), align 2
  %cttz0  = call i16 @llvm.cttz.i16(i16 %ld0, i1 -1)
  %cttz1  = call i16 @llvm.cttz.i16(i16 %ld1, i1 -1)
  %cttz2  = call i16 @llvm.cttz.i16(i16 %ld2, i1 -1)
  %cttz3  = call i16 @llvm.cttz.i16(i16 %ld3, i1 -1)
  %cttz4  = call i16 @llvm.cttz.i16(i16 %ld4, i1 -1)
  %cttz5  = call i16 @llvm.cttz.i16(i16 %ld5, i1 -1)
  %cttz6  = call i16 @llvm.cttz.i16(i16 %ld6, i1 -1)
  %cttz7  = call i16 @llvm.cttz.i16(i16 %ld7, i1 -1)
  %cttz8  = call i16 @llvm.cttz.i16(i16 %ld8, i1 -1)
  %cttz9  = call i16 @llvm.cttz.i16(i16 %ld9, i1 -1)
  %cttz10 = call i16 @llvm.cttz.i16(i16 %ld10, i1 -1)
  %cttz11 = call i16 @llvm.cttz.i16(i16 %ld11, i1 -1)
  %cttz12 = call i16 @llvm.cttz.i16(i16 %ld12, i1 -1)
  %cttz13 = call i16 @llvm.cttz.i16(i16 %ld13, i1 -1)
  %cttz14 = call i16 @llvm.cttz.i16(i16 %ld14, i1 -1)
  %cttz15 = call i16 @llvm.cttz.i16(i16 %ld15, i1 -1)
  store i16 %cttz0 , ptr @dst16, align 2
  store i16 %cttz1 , ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64  1), align 2
  store i16 %cttz2 , ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64  2), align 2
  store i16 %cttz3 , ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64  3), align 2
  store i16 %cttz4 , ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64  4), align 2
  store i16 %cttz5 , ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64  5), align 2
  store i16 %cttz6 , ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64  6), align 2
  store i16 %cttz7 , ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64  7), align 2
  store i16 %cttz8 , ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64  8), align 2
  store i16 %cttz9 , ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64  9), align 2
  store i16 %cttz10, ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64 10), align 2
  store i16 %cttz11, ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64 11), align 2
  store i16 %cttz12, ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64 12), align 2
  store i16 %cttz13, ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64 13), align 2
  store i16 %cttz14, ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64 14), align 2
  store i16 %cttz15, ptr getelementptr inbounds ([16 x i16], ptr @dst16, i16 0, i64 15), align 2
  ret void
}

define void @cttz_undef_16i8() #0 {
; CHECK-LABEL: @cttz_undef_16i8(
; CHECK-NEXT:    [[TMP1:%.*]] = load <16 x i8>, ptr @src8, align 1
; CHECK-NEXT:    [[TMP2:%.*]] = call <16 x i8> @llvm.cttz.v16i8(<16 x i8> [[TMP1]], i1 true)
; CHECK-NEXT:    store <16 x i8> [[TMP2]], ptr @dst8, align 1
; CHECK-NEXT:    ret void
;
  %ld0  = load i8, ptr @src8, align 1
  %ld1  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  1), align 1
  %ld2  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  2), align 1
  %ld3  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  3), align 1
  %ld4  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  4), align 1
  %ld5  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  5), align 1
  %ld6  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  6), align 1
  %ld7  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  7), align 1
  %ld8  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  8), align 1
  %ld9  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  9), align 1
  %ld10 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 10), align 1
  %ld11 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 11), align 1
  %ld12 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 12), align 1
  %ld13 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 13), align 1
  %ld14 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 14), align 1
  %ld15 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 15), align 1
  %cttz0  = call i8 @llvm.cttz.i8(i8 %ld0, i1 -1)
  %cttz1  = call i8 @llvm.cttz.i8(i8 %ld1, i1 -1)
  %cttz2  = call i8 @llvm.cttz.i8(i8 %ld2, i1 -1)
  %cttz3  = call i8 @llvm.cttz.i8(i8 %ld3, i1 -1)
  %cttz4  = call i8 @llvm.cttz.i8(i8 %ld4, i1 -1)
  %cttz5  = call i8 @llvm.cttz.i8(i8 %ld5, i1 -1)
  %cttz6  = call i8 @llvm.cttz.i8(i8 %ld6, i1 -1)
  %cttz7  = call i8 @llvm.cttz.i8(i8 %ld7, i1 -1)
  %cttz8  = call i8 @llvm.cttz.i8(i8 %ld8, i1 -1)
  %cttz9  = call i8 @llvm.cttz.i8(i8 %ld9, i1 -1)
  %cttz10 = call i8 @llvm.cttz.i8(i8 %ld10, i1 -1)
  %cttz11 = call i8 @llvm.cttz.i8(i8 %ld11, i1 -1)
  %cttz12 = call i8 @llvm.cttz.i8(i8 %ld12, i1 -1)
  %cttz13 = call i8 @llvm.cttz.i8(i8 %ld13, i1 -1)
  %cttz14 = call i8 @llvm.cttz.i8(i8 %ld14, i1 -1)
  %cttz15 = call i8 @llvm.cttz.i8(i8 %ld15, i1 -1)
  store i8 %cttz0 , ptr @dst8, align 1
  store i8 %cttz1 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  1), align 1
  store i8 %cttz2 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  2), align 1
  store i8 %cttz3 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  3), align 1
  store i8 %cttz4 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  4), align 1
  store i8 %cttz5 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  5), align 1
  store i8 %cttz6 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  6), align 1
  store i8 %cttz7 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  7), align 1
  store i8 %cttz8 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  8), align 1
  store i8 %cttz9 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  9), align 1
  store i8 %cttz10, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 10), align 1
  store i8 %cttz11, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 11), align 1
  store i8 %cttz12, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 12), align 1
  store i8 %cttz13, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 13), align 1
  store i8 %cttz14, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 14), align 1
  store i8 %cttz15, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 15), align 1
  ret void
}

define void @cttz_undef_32i8() #0 {
; SSE-LABEL: @cttz_undef_32i8(
; SSE-NEXT:    [[TMP1:%.*]] = load <16 x i8>, ptr @src8, align 1
; SSE-NEXT:    [[TMP2:%.*]] = call <16 x i8> @llvm.cttz.v16i8(<16 x i8> [[TMP1]], i1 true)
; SSE-NEXT:    store <16 x i8> [[TMP2]], ptr @dst8, align 1
; SSE-NEXT:    [[TMP3:%.*]] = load <16 x i8>, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 16), align 1
; SSE-NEXT:    [[TMP4:%.*]] = call <16 x i8> @llvm.cttz.v16i8(<16 x i8> [[TMP3]], i1 true)
; SSE-NEXT:    store <16 x i8> [[TMP4]], ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 16), align 1
; SSE-NEXT:    ret void
;
; AVX-LABEL: @cttz_undef_32i8(
; AVX-NEXT:    [[TMP1:%.*]] = load <32 x i8>, ptr @src8, align 1
; AVX-NEXT:    [[TMP2:%.*]] = call <32 x i8> @llvm.cttz.v32i8(<32 x i8> [[TMP1]], i1 true)
; AVX-NEXT:    store <32 x i8> [[TMP2]], ptr @dst8, align 1
; AVX-NEXT:    ret void
;
  %ld0  = load i8, ptr @src8, align 1
  %ld1  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  1), align 1
  %ld2  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  2), align 1
  %ld3  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  3), align 1
  %ld4  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  4), align 1
  %ld5  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  5), align 1
  %ld6  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  6), align 1
  %ld7  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  7), align 1
  %ld8  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  8), align 1
  %ld9  = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64  9), align 1
  %ld10 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 10), align 1
  %ld11 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 11), align 1
  %ld12 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 12), align 1
  %ld13 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 13), align 1
  %ld14 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 14), align 1
  %ld15 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 15), align 1
  %ld16 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 16), align 1
  %ld17 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 17), align 1
  %ld18 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 18), align 1
  %ld19 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 19), align 1
  %ld20 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 20), align 1
  %ld21 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 21), align 1
  %ld22 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 22), align 1
  %ld23 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 23), align 1
  %ld24 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 24), align 1
  %ld25 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 25), align 1
  %ld26 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 26), align 1
  %ld27 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 27), align 1
  %ld28 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 28), align 1
  %ld29 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 29), align 1
  %ld30 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 30), align 1
  %ld31 = load i8, ptr getelementptr inbounds ([32 x i8], ptr @src8, i8 0, i64 31), align 1
  %cttz0  = call i8 @llvm.cttz.i8(i8 %ld0, i1 -1)
  %cttz1  = call i8 @llvm.cttz.i8(i8 %ld1, i1 -1)
  %cttz2  = call i8 @llvm.cttz.i8(i8 %ld2, i1 -1)
  %cttz3  = call i8 @llvm.cttz.i8(i8 %ld3, i1 -1)
  %cttz4  = call i8 @llvm.cttz.i8(i8 %ld4, i1 -1)
  %cttz5  = call i8 @llvm.cttz.i8(i8 %ld5, i1 -1)
  %cttz6  = call i8 @llvm.cttz.i8(i8 %ld6, i1 -1)
  %cttz7  = call i8 @llvm.cttz.i8(i8 %ld7, i1 -1)
  %cttz8  = call i8 @llvm.cttz.i8(i8 %ld8, i1 -1)
  %cttz9  = call i8 @llvm.cttz.i8(i8 %ld9, i1 -1)
  %cttz10 = call i8 @llvm.cttz.i8(i8 %ld10, i1 -1)
  %cttz11 = call i8 @llvm.cttz.i8(i8 %ld11, i1 -1)
  %cttz12 = call i8 @llvm.cttz.i8(i8 %ld12, i1 -1)
  %cttz13 = call i8 @llvm.cttz.i8(i8 %ld13, i1 -1)
  %cttz14 = call i8 @llvm.cttz.i8(i8 %ld14, i1 -1)
  %cttz15 = call i8 @llvm.cttz.i8(i8 %ld15, i1 -1)
  %cttz16 = call i8 @llvm.cttz.i8(i8 %ld16, i1 -1)
  %cttz17 = call i8 @llvm.cttz.i8(i8 %ld17, i1 -1)
  %cttz18 = call i8 @llvm.cttz.i8(i8 %ld18, i1 -1)
  %cttz19 = call i8 @llvm.cttz.i8(i8 %ld19, i1 -1)
  %cttz20 = call i8 @llvm.cttz.i8(i8 %ld20, i1 -1)
  %cttz21 = call i8 @llvm.cttz.i8(i8 %ld21, i1 -1)
  %cttz22 = call i8 @llvm.cttz.i8(i8 %ld22, i1 -1)
  %cttz23 = call i8 @llvm.cttz.i8(i8 %ld23, i1 -1)
  %cttz24 = call i8 @llvm.cttz.i8(i8 %ld24, i1 -1)
  %cttz25 = call i8 @llvm.cttz.i8(i8 %ld25, i1 -1)
  %cttz26 = call i8 @llvm.cttz.i8(i8 %ld26, i1 -1)
  %cttz27 = call i8 @llvm.cttz.i8(i8 %ld27, i1 -1)
  %cttz28 = call i8 @llvm.cttz.i8(i8 %ld28, i1 -1)
  %cttz29 = call i8 @llvm.cttz.i8(i8 %ld29, i1 -1)
  %cttz30 = call i8 @llvm.cttz.i8(i8 %ld30, i1 -1)
  %cttz31 = call i8 @llvm.cttz.i8(i8 %ld31, i1 -1)
  store i8 %cttz0 , ptr @dst8, align 1
  store i8 %cttz1 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  1), align 1
  store i8 %cttz2 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  2), align 1
  store i8 %cttz3 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  3), align 1
  store i8 %cttz4 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  4), align 1
  store i8 %cttz5 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  5), align 1
  store i8 %cttz6 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  6), align 1
  store i8 %cttz7 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  7), align 1
  store i8 %cttz8 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  8), align 1
  store i8 %cttz9 , ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64  9), align 1
  store i8 %cttz10, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 10), align 1
  store i8 %cttz11, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 11), align 1
  store i8 %cttz12, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 12), align 1
  store i8 %cttz13, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 13), align 1
  store i8 %cttz14, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 14), align 1
  store i8 %cttz15, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 15), align 1
  store i8 %cttz16, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 16), align 1
  store i8 %cttz17, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 17), align 1
  store i8 %cttz18, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 18), align 1
  store i8 %cttz19, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 19), align 1
  store i8 %cttz20, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 20), align 1
  store i8 %cttz21, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 21), align 1
  store i8 %cttz22, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 22), align 1
  store i8 %cttz23, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 23), align 1
  store i8 %cttz24, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 24), align 1
  store i8 %cttz25, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 25), align 1
  store i8 %cttz26, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 26), align 1
  store i8 %cttz27, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 27), align 1
  store i8 %cttz28, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 28), align 1
  store i8 %cttz29, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 29), align 1
  store i8 %cttz30, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 30), align 1
  store i8 %cttz31, ptr getelementptr inbounds ([32 x i8], ptr @dst8, i8 0, i64 31), align 1
  ret void
}

attributes #0 = { nounwind }
