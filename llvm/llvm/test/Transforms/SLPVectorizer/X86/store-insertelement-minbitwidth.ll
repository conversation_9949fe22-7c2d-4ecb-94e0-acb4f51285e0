; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt < %s -S -mtriple=x86_64-unknown -passes=slp-vectorizer -mattr=+avx -pass-remarks-output=%t | FileCheck %s
; RUN: FileCheck --input-file=%t --check-prefix=YAML %s

; YAML-LABEL: --- !Passed
; YAML-NEXT:  Pass:            slp-vectorizer
; YAML-NEXT:  Name:            StoresVectorized
; YAML-NEXT:  Function:        stores
; YAML-NEXT:  Args:
; YAML-NEXT:    - String:          'Stores SLP vectorized with cost '
; YAML-NEXT:    - Cost:            '-7'
; YAML-NEXT:    - String:          ' and with tree size '
; YAML-NEXT:    - TreeSize:        '6'
define void @stores(ptr noalias %in, ptr noalias %inn, ptr noalias %out) {
; CHECK-LABEL: @stores(
; CHECK-NEXT:    [[TMP1:%.*]] = load <4 x i8>, ptr [[IN:%.*]], align 1
; CHECK-NEXT:    [[TMP2:%.*]] = load <4 x i8>, ptr [[INN:%.*]], align 1
; CHECK-NEXT:    [[TMP3:%.*]] = zext <4 x i8> [[TMP1]] to <4 x i16>
; CHECK-NEXT:    [[TMP4:%.*]] = zext <4 x i8> [[TMP2]] to <4 x i16>
; CHECK-NEXT:    [[TMP5:%.*]] = add <4 x i16> [[TMP3]], [[TMP4]]
; CHECK-NEXT:    [[TMP6:%.*]] = zext <4 x i16> [[TMP5]] to <4 x i64>
; CHECK-NEXT:    store <4 x i64> [[TMP6]], ptr [[OUT:%.*]], align 4
; CHECK-NEXT:    ret void
;
  %load.1 = load i8, ptr %in, align 1
  %gep.1 = getelementptr inbounds i8, ptr %in, i64 1
  %load.2 = load i8, ptr %gep.1, align 1
  %gep.2 = getelementptr inbounds i8, ptr %in, i64 2
  %load.3 = load i8, ptr %gep.2, align 1
  %gep.3 = getelementptr inbounds i8, ptr %in, i64 3
  %load.4 = load i8, ptr %gep.3, align 1
  %load.5 = load i8, ptr %inn, align 1
  %gep.4 = getelementptr inbounds i8, ptr %inn, i64 1
  %load.6 = load i8, ptr %gep.4, align 1
  %gep.5 = getelementptr inbounds i8, ptr %inn, i64 2
  %load.7 = load i8, ptr %gep.5, align 1
  %gep.6 = getelementptr inbounds i8, ptr %inn, i64 3
  %load.8 = load i8, ptr %gep.6, align 1
  %z1 = zext i8 %load.1 to i64
  %z2 = zext i8 %load.2 to i64
  %z3 = zext i8 %load.3 to i64
  %z4 = zext i8 %load.4 to i64
  %z5 = zext i8 %load.5 to i64
  %z6 = zext i8 %load.6 to i64
  %z7 = zext i8 %load.7 to i64
  %z8 = zext i8 %load.8 to i64
  %add1 = add i64 %z1, %z5
  %add2 = add i64 %z2, %z6
  %add3 = add i64 %z3, %z7
  %add4 = add i64 %z4, %z8
  %gep.8 = getelementptr inbounds i64, ptr %out, i64 1
  %gep.9 = getelementptr inbounds i64, ptr %out, i64 2
  %gep.10 = getelementptr inbounds i64, ptr %out, i64 3
  store i64 %add1, ptr %out, align 4
  store i64 %add2, ptr %gep.8, align 4
  store i64 %add3, ptr %gep.9, align 4
  store i64 %add4, ptr %gep.10, align 4
  ret void
}

; YAML-LABEL: --- !Passed
; YAML-NEXT:  Pass:            slp-vectorizer
; YAML-NEXT:  Name:            VectorizedList
; YAML-NEXT:  Function:        insertelems
; YAML-NEXT:  Args:
; YAML-NEXT:    - String:          'SLP vectorized with cost '
; YAML-NEXT:    - Cost:            '-9'
; YAML-NEXT:    - String:          ' and with tree size '
; YAML-NEXT:    - TreeSize:        '6'
define <4 x i64> @insertelems(ptr noalias %in, ptr noalias %inn) {
; CHECK-LABEL: @insertelems(
; CHECK-NEXT:    [[TMP1:%.*]] = load <4 x i8>, ptr [[IN:%.*]], align 1
; CHECK-NEXT:    [[TMP2:%.*]] = load <4 x i8>, ptr [[INN:%.*]], align 1
; CHECK-NEXT:    [[TMP3:%.*]] = zext <4 x i8> [[TMP1]] to <4 x i16>
; CHECK-NEXT:    [[TMP4:%.*]] = zext <4 x i8> [[TMP2]] to <4 x i16>
; CHECK-NEXT:    [[TMP5:%.*]] = add <4 x i16> [[TMP3]], [[TMP4]]
; CHECK-NEXT:    [[TMP6:%.*]] = zext <4 x i16> [[TMP5]] to <4 x i64>
; CHECK-NEXT:    ret <4 x i64> [[TMP6]]
;
  %load.1 = load i8, ptr %in, align 1
  %gep.1 = getelementptr inbounds i8, ptr %in, i64 1
  %load.2 = load i8, ptr %gep.1, align 1
  %gep.2 = getelementptr inbounds i8, ptr %in, i64 2
  %load.3 = load i8, ptr %gep.2, align 1
  %gep.3 = getelementptr inbounds i8, ptr %in, i64 3
  %load.4 = load i8, ptr %gep.3, align 1
  %load.5 = load i8, ptr %inn, align 1
  %gep.4 = getelementptr inbounds i8, ptr %inn, i64 1
  %load.6 = load i8, ptr %gep.4, align 1
  %gep.5 = getelementptr inbounds i8, ptr %inn, i64 2
  %load.7 = load i8, ptr %gep.5, align 1
  %gep.6 = getelementptr inbounds i8, ptr %inn, i64 3
  %load.8 = load i8, ptr %gep.6, align 1
  %z1 = zext i8 %load.1 to i64
  %z2 = zext i8 %load.2 to i64
  %z3 = zext i8 %load.3 to i64
  %z4 = zext i8 %load.4 to i64
  %z5 = zext i8 %load.5 to i64
  %z6 = zext i8 %load.6 to i64
  %z7 = zext i8 %load.7 to i64
  %z8 = zext i8 %load.8 to i64
  %add1 = add i64 %z1, %z5
  %add2 = add i64 %z2, %z6
  %add3 = add i64 %z3, %z7
  %add4 = add i64 %z4, %z8
  %ins1 = insertelement <4 x i64> poison, i64 %add1, i32 0
  %ins2 = insertelement <4 x i64> %ins1, i64 %add2, i32 1
  %ins3 = insertelement <4 x i64> %ins2, i64 %add3, i32 2
  %ins4 = insertelement <4 x i64> %ins3, i64 %add4, i32 3
  ret <4 x i64> %ins4
}
