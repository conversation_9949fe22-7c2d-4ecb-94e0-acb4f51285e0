; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown-linux-gnu -mattr=+avx2 -pass-remarks-output=%t < %s | FileCheck %s
; RUN: FileCheck --input-file=%t --check-prefix=YAML %s

; YAML-LABEL: --- !Passed
; YAML-NEXT: Pass:            slp-vectorizer
; YAML-NEXT: Name:            VectorizedHorizontalReduction
; YAML-NEXT: Function:        test
; YAML-NEXT: Args:
; YAML-NEXT:   - String:          'Vectorized horizontal reduction with cost '
; YAML-NEXT:   - Cost:            '-41'
; YAML-NEXT:   - String:          ' and with tree size '
; YAML-NEXT:   - TreeSize:        '7'
; YAML-NEXT: ...

define i64 @test() {
; CHECK-LABEL: define i64 @test(
; CHECK-SAME: ) #[[ATTR0:[0-9]+]] {
; CHECK-NEXT:  [[ENTRY:.*:]]
; CHECK-NEXT:    [[TMP0:%.*]] = insertelement <32 x i32> <i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 undef, i32 0, i32 undef, i32 undef, i32 undef, i32 undef, i32 undef, i32 undef, i32 undef, i32 undef, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 undef, i32 undef, i32 undef, i32 undef, i32 0, i32 0, i32 1, i32 0>, i32 0, i32 6
; CHECK-NEXT:    [[TMP1:%.*]] = call <32 x i32> @llvm.vector.insert.v32i32.v8i32(<32 x i32> [[TMP0]], <8 x i32> zeroinitializer, i64 8)
; CHECK-NEXT:    [[TMP2:%.*]] = call <32 x i32> @llvm.vector.insert.v32i32.v4i32(<32 x i32> [[TMP1]], <4 x i32> <i32 0, i32 0, i32 0, i32 1>, i64 24)
; CHECK-NEXT:    [[TMP3:%.*]] = sub <32 x i32> zeroinitializer, [[TMP2]]
; CHECK-NEXT:    [[TMP4:%.*]] = call i32 @llvm.vector.reduce.add.v32i32(<32 x i32> [[TMP3]])
; CHECK-NEXT:    [[OP_RDX:%.*]] = add i32 [[TMP4]], 0
; CHECK-NEXT:    [[RES:%.*]] = sext i32 [[OP_RDX]] to i64
; CHECK-NEXT:    ret i64 [[RES]]
;
entry:
  %.neg15 = sub i32 0, 0
  %0 = trunc i64 1 to i32
  %.neg.1 = sub i32 0, 0
  %.neg15.1 = sub i32 0, %0
  %.neg16.1 = add i32 %.neg.1, %.neg15.1
  %1 = add i32 %.neg16.1, %.neg15
  %2 = trunc i64 0 to i32
  %.neg.2 = sub i32 0, %0
  %.neg15.2 = sub i32 0, %2
  %.neg16.2 = add i32 %.neg.2, %.neg15.2
  %3 = add i32 %.neg16.2, %1
  %4 = trunc i64 0 to i32
  %.neg.3 = sub i32 0, %2
  %.neg15.3 = sub i32 0, %4
  %.neg16.3 = add i32 %.neg.3, %.neg15.3
  %5 = add i32 %.neg16.3, %3
  %6 = trunc i64 0 to i32
  %.neg.4 = sub i32 0, %4
  %.neg15.4 = sub i32 0, %6
  %.neg16.4 = add i32 %.neg.4, %.neg15.4
  %7 = add i32 %.neg16.4, %5
  %.neg.5 = sub i32 0, %6
  %.neg15.5 = sub i32 0, 0
  %.neg16.5 = add i32 %.neg.5, %.neg15.5
  %8 = add i32 %.neg16.5, %7
  %.neg15.6 = sub i32 0, 0
  %.neg16.6 = add i32 0, %.neg15.6
  %9 = add i32 %.neg16.6, %8
  %.neg.7 = sub i32 0, 0
  %.neg15.7 = sub i32 0, 0
  %.neg16.7 = add i32 %.neg.7, %.neg15.7
  %10 = add i32 %.neg16.7, %9
  %11 = trunc i64 0 to i32
  %.neg.8 = sub i32 0, 0
  %.neg15.8 = sub i32 0, %11
  %.neg16.8 = add i32 %.neg.8, %.neg15.8
  %12 = add i32 %.neg16.8, %10
  %13 = trunc i64 0 to i32
  %.neg.9 = sub i32 0, %11
  %.neg15.9 = sub i32 0, %13
  %.neg16.9 = add i32 %.neg.9, %.neg15.9
  %14 = add i32 %.neg16.9, %12
  %15 = trunc i64 0 to i32
  %.neg.10 = sub i32 0, %13
  %.neg15.10 = sub i32 0, %15
  %.neg16.10 = add i32 %.neg.10, %.neg15.10
  %16 = add i32 %.neg16.10, %14
  %17 = trunc i64 0 to i32
  %.neg.11 = sub i32 0, %15
  %.neg15.11 = sub i32 0, %17
  %.neg16.11 = add i32 %.neg.11, %.neg15.11
  %18 = add i32 %.neg16.11, %16
  %19 = trunc i64 0 to i32
  %.neg.12 = sub i32 0, %17
  %.neg15.12 = sub i32 0, %19
  %.neg16.12 = add i32 %.neg.12, %.neg15.12
  %20 = add i32 %.neg16.12, %18
  %.neg.13 = sub i32 0, %19
  %.neg15.13 = sub i32 0, 0
  %.neg16.13 = add i32 %.neg.13, %.neg15.13
  %21 = add i32 %.neg16.13, %20
  %.neg.14 = sub i32 0, 0
  %.neg15.14 = sub i32 0, 0
  %.neg16.14 = add i32 %.neg.14, %.neg15.14
  %22 = add i32 %.neg16.14, %21
  %.neg.15 = sub i32 0, 0
  %.neg15.15 = sub i32 0, 0
  %.neg16.15 = add i32 %.neg.15, %.neg15.15
  %23 = add i32 %.neg16.15, %22
  %.neg.16 = sub i32 0, 0
  %.neg15.16 = sub i32 0, 0
  %.neg16.16 = add i32 %.neg.16, %.neg15.16
  %24 = add i32 %.neg16.16, %23
  %res = sext i32 %24 to i64
  ret i64 %res
}
