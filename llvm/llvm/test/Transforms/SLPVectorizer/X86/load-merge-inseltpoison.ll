; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -passes=slp-vectorizer -slp-vectorize-hor -slp-vectorize-hor-store -S < %s -mtriple=x86_64-apple-macosx -mcpu=haswell | FileCheck %s --check-prefixes=CHECK,AVX2
; RUN: opt -passes=slp-vectorizer -slp-vectorize-hor -slp-vectorize-hor-store -S < %s -mtriple=x86_64-apple-macosx -mcpu=x86-64-v4 | FileCheck %s --check-prefixes=CHECK,AVX512

;unsigned load_le32(unsigned char *data) {
;    unsigned le32 = (data[0]<<0) | (data[1]<<8) | (data[2]<<16) | (data[3]<<24);
;    return le32;
;}

define i32 @_Z9load_le32Ph(ptr nocapture readonly %data) {
; CHECK-LABEL: @_Z9load_le32Ph(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP0:%.*]] = load i8, ptr [[DATA:%.*]], align 1
; CHECK-NEXT:    [[CONV:%.*]] = zext i8 [[TMP0]] to i32
; CHECK-NEXT:    [[ARRAYIDX1:%.*]] = getelementptr inbounds i8, ptr [[DATA]], i64 1
; CHECK-NEXT:    [[TMP1:%.*]] = load i8, ptr [[ARRAYIDX1]], align 1
; CHECK-NEXT:    [[CONV2:%.*]] = zext i8 [[TMP1]] to i32
; CHECK-NEXT:    [[SHL3:%.*]] = shl nuw nsw i32 [[CONV2]], 8
; CHECK-NEXT:    [[OR:%.*]] = or i32 [[SHL3]], [[CONV]]
; CHECK-NEXT:    [[ARRAYIDX4:%.*]] = getelementptr inbounds i8, ptr [[DATA]], i64 2
; CHECK-NEXT:    [[TMP2:%.*]] = load i8, ptr [[ARRAYIDX4]], align 1
; CHECK-NEXT:    [[CONV5:%.*]] = zext i8 [[TMP2]] to i32
; CHECK-NEXT:    [[SHL6:%.*]] = shl nuw nsw i32 [[CONV5]], 16
; CHECK-NEXT:    [[OR7:%.*]] = or i32 [[OR]], [[SHL6]]
; CHECK-NEXT:    [[ARRAYIDX8:%.*]] = getelementptr inbounds i8, ptr [[DATA]], i64 3
; CHECK-NEXT:    [[TMP3:%.*]] = load i8, ptr [[ARRAYIDX8]], align 1
; CHECK-NEXT:    [[CONV9:%.*]] = zext i8 [[TMP3]] to i32
; CHECK-NEXT:    [[SHL10:%.*]] = shl nuw i32 [[CONV9]], 24
; CHECK-NEXT:    [[OR11:%.*]] = or i32 [[OR7]], [[SHL10]]
; CHECK-NEXT:    ret i32 [[OR11]]
;
entry:
  %0 = load i8, ptr %data, align 1
  %conv = zext i8 %0 to i32
  %arrayidx1 = getelementptr inbounds i8, ptr %data, i64 1
  %1 = load i8, ptr %arrayidx1, align 1
  %conv2 = zext i8 %1 to i32
  %shl3 = shl nuw nsw i32 %conv2, 8
  %or = or i32 %shl3, %conv
  %arrayidx4 = getelementptr inbounds i8, ptr %data, i64 2
  %2 = load i8, ptr %arrayidx4, align 1
  %conv5 = zext i8 %2 to i32
  %shl6 = shl nuw nsw i32 %conv5, 16
  %or7 = or i32 %or, %shl6
  %arrayidx8 = getelementptr inbounds i8, ptr %data, i64 3
  %3 = load i8, ptr %arrayidx8, align 1
  %conv9 = zext i8 %3 to i32
  %shl10 = shl nuw i32 %conv9, 24
  %or11 = or i32 %or7, %shl10
  ret i32 %or11
}

define <4 x float> @PR16739_byref(ptr nocapture readonly dereferenceable(16) %x) {
; AVX2-LABEL: @PR16739_byref(
; AVX2-NEXT:    [[GEP2:%.*]] = getelementptr inbounds <4 x float>, ptr [[X:%.*]], i64 0, i64 2
; AVX2-NEXT:    [[TMP1:%.*]] = load <2 x float>, ptr [[X]], align 4
; AVX2-NEXT:    [[X2:%.*]] = load float, ptr [[GEP2]], align 4
; AVX2-NEXT:    [[TMP2:%.*]] = shufflevector <2 x float> [[TMP1]], <2 x float> poison, <4 x i32> <i32 0, i32 1, i32 poison, i32 poison>
; AVX2-NEXT:    [[I2:%.*]] = insertelement <4 x float> [[TMP2]], float [[X2]], i32 2
; AVX2-NEXT:    [[I3:%.*]] = insertelement <4 x float> [[I2]], float [[X2]], i32 3
; AVX2-NEXT:    ret <4 x float> [[I3]]
;
; AVX512-LABEL: @PR16739_byref(
; AVX512-NEXT:    [[GEP1:%.*]] = getelementptr inbounds <4 x float>, ptr [[X:%.*]], i64 0, i64 1
; AVX512-NEXT:    [[X0:%.*]] = load float, ptr [[X]], align 4
; AVX512-NEXT:    [[TMP1:%.*]] = load <2 x float>, ptr [[GEP1]], align 4
; AVX512-NEXT:    [[I0:%.*]] = insertelement <4 x float> poison, float [[X0]], i32 0
; AVX512-NEXT:    [[TMP2:%.*]] = shufflevector <2 x float> [[TMP1]], <2 x float> poison, <4 x i32> <i32 0, i32 1, i32 poison, i32 poison>
; AVX512-NEXT:    [[I21:%.*]] = shufflevector <4 x float> [[I0]], <4 x float> [[TMP2]], <4 x i32> <i32 0, i32 4, i32 5, i32 poison>
; AVX512-NEXT:    [[TMP3:%.*]] = shufflevector <4 x float> [[I21]], <4 x float> [[TMP2]], <4 x i32> <i32 0, i32 1, i32 2, i32 5>
; AVX512-NEXT:    ret <4 x float> [[TMP3]]
;
  %gep1 = getelementptr inbounds <4 x float>, ptr %x, i64 0, i64 1
  %gep2 = getelementptr inbounds <4 x float>, ptr %x, i64 0, i64 2
  %x0 = load float, ptr %x
  %x1 = load float, ptr %gep1
  %x2 = load float, ptr %gep2
  %i0 = insertelement <4 x float> poison, float %x0, i32 0
  %i1 = insertelement <4 x float> %i0, float %x1, i32 1
  %i2 = insertelement <4 x float> %i1, float %x2, i32 2
  %i3 = insertelement <4 x float> %i2, float %x2, i32 3
  ret <4 x float> %i3
}

define <4 x float> @PR16739_byref_alt(ptr nocapture readonly dereferenceable(16) %x) {
; CHECK-LABEL: @PR16739_byref_alt(
; CHECK-NEXT:    [[TMP1:%.*]] = load <2 x float>, ptr [[X:%.*]], align 4
; CHECK-NEXT:    [[TMP2:%.*]] = shufflevector <2 x float> [[TMP1]], <2 x float> poison, <4 x i32> <i32 0, i32 0, i32 1, i32 1>
; CHECK-NEXT:    ret <4 x float> [[TMP2]]
;
  %gep1 = getelementptr inbounds <4 x float>, ptr %x, i64 0, i64 1
  %x0 = load float, ptr %x
  %x1 = load float, ptr %gep1
  %i0 = insertelement <4 x float> poison, float %x0, i32 0
  %i1 = insertelement <4 x float> %i0, float %x0, i32 1
  %i2 = insertelement <4 x float> %i1, float %x1, i32 2
  %i3 = insertelement <4 x float> %i2, float %x1, i32 3
  ret <4 x float> %i3
}

define <4 x float> @PR16739_byval(ptr nocapture readonly dereferenceable(16) %x) {
; CHECK-LABEL: @PR16739_byval(
; CHECK-NEXT:    [[TMP1:%.*]] = load <2 x i64>, ptr [[X:%.*]], align 16
; CHECK-NEXT:    [[T1:%.*]] = load i64, ptr [[X]], align 16
; CHECK-NEXT:    [[T8:%.*]] = lshr i64 [[T1]], 32
; CHECK-NEXT:    [[TMP2:%.*]] = shufflevector <2 x i64> [[TMP1]], <2 x i64> poison, <4 x i32> <i32 0, i32 poison, i32 1, i32 1>
; CHECK-NEXT:    [[TMP3:%.*]] = insertelement <4 x i64> [[TMP2]], i64 [[T8]], i32 1
; CHECK-NEXT:    [[TMP4:%.*]] = trunc <4 x i64> [[TMP3]] to <4 x i32>
; CHECK-NEXT:    [[TMP5:%.*]] = bitcast <4 x i32> [[TMP4]] to <4 x float>
; CHECK-NEXT:    ret <4 x float> [[TMP5]]
;
  %t1 = load i64, ptr %x, align 16
  %t2 = getelementptr inbounds <4 x float>, ptr %x, i64 0, i64 2
  %t4 = load i64, ptr %t2, align 8
  %t5 = trunc i64 %t1 to i32
  %t6 = bitcast i32 %t5 to float
  %t7 = insertelement <4 x float> poison, float %t6, i32 0
  %t8 = lshr i64 %t1, 32
  %t9 = trunc i64 %t8 to i32
  %t10 = bitcast i32 %t9 to float
  %t11 = insertelement <4 x float> %t7, float %t10, i32 1
  %t12 = trunc i64 %t4 to i32
  %t13 = bitcast i32 %t12 to float
  %t14 = insertelement <4 x float> %t11, float %t13, i32 2
  %t15 = insertelement <4 x float> %t14, float %t13, i32 3
  ret <4 x float> %t15
}

define void @PR43578_prefer128(ptr %r, ptr %p, ptr %q) #0 {
; CHECK-LABEL: @PR43578_prefer128(
; CHECK-NEXT:    [[P2:%.*]] = getelementptr inbounds i64, ptr [[P:%.*]], i64 2
; CHECK-NEXT:    [[Q2:%.*]] = getelementptr inbounds i64, ptr [[Q:%.*]], i64 2
; CHECK-NEXT:    [[TMP1:%.*]] = load <2 x i64>, ptr [[P]], align 2
; CHECK-NEXT:    [[TMP2:%.*]] = load <2 x i64>, ptr [[Q]], align 2
; CHECK-NEXT:    [[TMP3:%.*]] = sub nsw <2 x i64> [[TMP1]], [[TMP2]]
; CHECK-NEXT:    [[TMP4:%.*]] = load <2 x i64>, ptr [[P2]], align 2
; CHECK-NEXT:    [[TMP5:%.*]] = load <2 x i64>, ptr [[Q2]], align 2
; CHECK-NEXT:    [[TMP6:%.*]] = sub nsw <2 x i64> [[TMP4]], [[TMP5]]
; CHECK-NEXT:    [[TMP7:%.*]] = extractelement <2 x i64> [[TMP3]], i32 0
; CHECK-NEXT:    [[G0:%.*]] = getelementptr inbounds i32, ptr [[R:%.*]], i64 [[TMP7]]
; CHECK-NEXT:    [[TMP8:%.*]] = extractelement <2 x i64> [[TMP3]], i32 1
; CHECK-NEXT:    [[G1:%.*]] = getelementptr inbounds i32, ptr [[R]], i64 [[TMP8]]
; CHECK-NEXT:    [[TMP9:%.*]] = extractelement <2 x i64> [[TMP6]], i32 0
; CHECK-NEXT:    [[G2:%.*]] = getelementptr inbounds i32, ptr [[R]], i64 [[TMP9]]
; CHECK-NEXT:    [[TMP10:%.*]] = extractelement <2 x i64> [[TMP6]], i32 1
; CHECK-NEXT:    [[G3:%.*]] = getelementptr inbounds i32, ptr [[R]], i64 [[TMP10]]
; CHECK-NEXT:    ret void
;
  %p1 = getelementptr inbounds i64, ptr %p, i64 1
  %p2 = getelementptr inbounds i64, ptr %p, i64 2
  %p3 = getelementptr inbounds i64, ptr %p, i64 3

  %q1 = getelementptr inbounds i64, ptr %q, i64 1
  %q2 = getelementptr inbounds i64, ptr %q, i64 2
  %q3 = getelementptr inbounds i64, ptr %q, i64 3

  %x0 = load i64, ptr %p, align 2
  %x1 = load i64, ptr %p1, align 2
  %x2 = load i64, ptr %p2, align 2
  %x3 = load i64, ptr %p3, align 2

  %y0 = load i64, ptr %q, align 2
  %y1 = load i64, ptr %q1, align 2
  %y2 = load i64, ptr %q2, align 2
  %y3 = load i64, ptr %q3, align 2

  %sub0 = sub nsw i64 %x0, %y0
  %sub1 = sub nsw i64 %x1, %y1
  %sub2 = sub nsw i64 %x2, %y2
  %sub3 = sub nsw i64 %x3, %y3

  %g0 = getelementptr inbounds i32, ptr %r, i64 %sub0
  %g1 = getelementptr inbounds i32, ptr %r, i64 %sub1
  %g2 = getelementptr inbounds i32, ptr %r, i64 %sub2
  %g3 = getelementptr inbounds i32, ptr %r, i64 %sub3
  ret void
}

attributes #0 = { "prefer-vector-width"="128" }
