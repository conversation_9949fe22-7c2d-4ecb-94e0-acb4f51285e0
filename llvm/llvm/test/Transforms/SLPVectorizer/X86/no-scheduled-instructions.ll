; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 2
; RUN: opt -S -passes=slp-vectorizer -mattr=+avx -mtriple=x86_64 < %s | FileCheck %s

define void @test(i1 %arg) {
; CHECK-LABEL: define void @test
; CHECK-SAME: (i1 %arg) #[[ATTR0:[0-9]+]] {
; CHECK-NEXT:    br i1 %arg, label [[BB1:%.*]], label [[BB2:%.*]]
; CHECK:       bb1:
; CHECK-NEXT:    [[TMP1:%.*]] = call i32 @llvm.vector.reduce.mul.v8i32(<8 x i32> <i32 0, i32 0, i32 0, i32 0, i32 undef, i32 undef, i32 undef, i32 undef>)
; CHECK-NEXT:    call void @f(i32 noundef [[TMP1]])
; CHECK-NEXT:    br label [[BB2]]
; CHECK:       bb2:
; CHECK-NEXT:    ret void
;
  %i27 = extractelement <4 x i32> poison, i64 0
  %i28 = extractelement <4 x i32> poison, i64 1
  %i29 = extractelement <4 x i32> poison, i64 2
  %i30 = extractelement <4 x i32> poison, i64 3
  %i31 = extractelement <4 x i32> zeroinitializer, i64 0
  %i32 = extractelement <4 x i32> zeroinitializer, i64 1
  %i33 = extractelement <4 x i32> zeroinitializer, i64 2
  %i34 = extractelement <4 x i32> zeroinitializer, i64 3
  br i1 %arg, label %bb1, label %bb2

bb1:
  %i11 = mul nsw i32 %i28, %i27
  %i12 = mul nsw i32 %i11, %i29
  %i13 = mul nsw i32 %i12, %i30
  %i14 = mul nsw i32 %i13, %i31
  %i15 = mul nsw i32 %i14, %i32
  %i16 = mul nsw i32 %i15, %i33
  %i17 = mul nsw i32 %i16, %i34
  call void @f(i32 noundef %i17)
  br label %bb2

bb2:
  ret void
}

declare void @f(i32)
