; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown-linux-gnu < %s | FileCheck %s

define void @test(ptr %a, i32 %0, i32 %1, i1 %cmp1) {
; CHECK-LABEL: define void @test(
; CHECK-SAME: ptr [[A:%.*]], i32 [[TMP0:%.*]], i32 [[TMP1:%.*]], i1 [[CMP1:%.*]]) {
; CHECK-NEXT:  [[ENTRY:.*:]]
; CHECK-NEXT:    [[TMP2:%.*]] = load i32, ptr [[A]], align 4
; CHECK-NEXT:    [[TOBOOL10_NOT:%.*]] = icmp eq i32 [[TMP0]], 0
; CHECK-NEXT:    [[CMP4_3:%.*]] = icmp ne i32 [[TMP1]], 0
; CHECK-NEXT:    [[TMP3:%.*]] = and i1 [[CMP4_3]], [[TOBOOL10_NOT]]
; CHECK-NEXT:    [[CMP2_2:%.*]] = xor i1 [[TOBOOL10_NOT]], true
; CHECK-NEXT:    [[CONV3_2:%.*]] = zext i1 [[CMP2_2]] to i32
; CHECK-NEXT:    [[CMP4_2:%.*]] = icmp ne i32 [[TMP2]], [[CONV3_2]]
; CHECK-NEXT:    [[CMP2_1:%.*]] = xor i1 [[CMP1]], true
; CHECK-NEXT:    [[CONV3_1:%.*]] = zext i1 [[CMP2_1]] to i32
; CHECK-NEXT:    [[CMP4_1:%.*]] = icmp ne i32 [[TMP2]], [[CONV3_1]]
; CHECK-NEXT:    [[TMP4:%.*]] = select i1 [[TMP3]], i1 [[CMP4_2]], i1 false
; CHECK-NEXT:    [[TMP5:%.*]] = select i1 [[TMP4]], i1 [[CMP4_1]], i1 false
; CHECK-NEXT:    [[AND_3:%.*]] = zext i1 [[TMP5]] to i32
; CHECK-NEXT:    store i32 [[AND_3]], ptr [[A]], align 4
; CHECK-NEXT:    ret void
;
entry:
  %2 = load i32, ptr %a, align 4
  %tobool10.not = icmp eq i32 %0, 0
  %cmp4.3 = icmp ne i32 %1, 0
  %3 = and i1 %cmp4.3, %tobool10.not
  %cmp2.2 = xor i1 %tobool10.not, true
  %conv3.2 = zext i1 %cmp2.2 to i32
  %cmp4.2 = icmp ne i32 %2, %conv3.2
  %cmp2.1 = xor i1 %cmp1, true
  %conv3.1 = zext i1 %cmp2.1 to i32
  %cmp4.1 = icmp ne i32 %2, %conv3.1
  %4 = select i1 %3, i1 %cmp4.2, i1 false
  %5 = select i1 %4, i1 %cmp4.1, i1 false
  %and.3 = zext i1 %5 to i32
  store i32 %and.3, ptr %a, align 4
  ret void
}
