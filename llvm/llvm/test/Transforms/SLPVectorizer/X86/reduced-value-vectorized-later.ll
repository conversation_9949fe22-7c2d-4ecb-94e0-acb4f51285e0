; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S -passes=slp-vectorizer -mtriple=x86_64-unknown-linux-gnu < %s | FileCheck %s

define i16 @test() {
; CHECK-LABEL: define i16 @test() {
; CHECK-NEXT:  [[ENTRY:.*:]]
; CHECK-NEXT:    [[TMP0:%.*]] = call i16 @llvm.vector.reduce.or.v8i16(<8 x i16> zeroinitializer)
; CHECK-NEXT:    [[TMP1:%.*]] = call i16 @llvm.vector.reduce.or.v4i16(<4 x i16> zeroinitializer)
; CHECK-NEXT:    [[OP_RDX:%.*]] = or i16 [[TMP0]], [[TMP1]]
; CHECK-NEXT:    [[OP_RDX1:%.*]] = or i16 [[OP_RDX]], 0
; CHECK-NEXT:    ret i16 [[OP_RDX1]]
;
entry:
  %subi = add i16 0, 0
  %sub40.i = add i16 %subi, 0
  %sub41.i = add i16 %subi, 0
  %sub42.i = add i16 %subi, 0
  %sub43.i = add i16 %subi, 0
  %sub44.i = add i16 %subi, 0
  %sub45.i = add i16 %subi, 0
  %sub46.i = add i16 0, 0
  %sub47.i = add i16 0, 0
  %sub48.i = add i16 0, 0
  %sub49.i = add i16 0, 0
  %or40.i = or i16 %sub40.i, %sub41.i
  %or41.i = or i16 %or40.i, %sub42.i
  %or42.i = or i16 %or41.i, %sub43.i
  %or43.i = or i16 %or42.i, %sub44.i
  %or44.i = or i16 %or43.i, %sub45.i
  %or45.i = or i16 %or44.i, %sub46.i
  %or46.i = or i16 %or45.i, %sub47.i
  %or47.i = or i16 %or46.i, %sub48.i
  %or48.i = or i16 %or47.i, %sub49.i
  %or50.i = or i16 %or48.i, %subi
  %subii = add i16 0, 0
  %subi16.i = add i16 %subii, 0
  %subi17.i = add i16 %subii, 0
  %0 = or i16 %subi16.i, %subi17.i
  %1 = or i16 %0, %or50.i
  ret i16 %1
}
