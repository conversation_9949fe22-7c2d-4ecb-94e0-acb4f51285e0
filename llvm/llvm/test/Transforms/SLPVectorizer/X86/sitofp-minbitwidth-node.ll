; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 4
; RUN: opt -passes=slp-vectorizer -mtriple=x86_64 -mcpu=k8 -mattr=+sse4.1 -S < %s | FileCheck %s

define void @foo(ptr %ptr) {
; CHECK-LABEL: define void @foo(
; CHECK-SAME: ptr [[PTR:%.*]]) #[[ATTR0:[0-9]+]] {
; CHECK-NEXT:    [[GEP0:%.*]] = getelementptr inbounds i8, ptr [[PTR]], i64 328
; CHECK-NEXT:    [[GEP3:%.*]] = getelementptr inbounds i8, ptr [[PTR]], i64 334
; CHECK-NEXT:    [[TMP1:%.*]] = load <2 x i16>, ptr [[GEP0]], align 8
; CHECK-NEXT:    [[TMP3:%.*]] = xor <2 x i16> [[TMP1]], splat (i16 -1)
; CHECK-NEXT:    [[TMP4:%.*]] = uitofp <2 x i16> [[TMP3]] to <2 x double>
; CHECK-NEXT:    [[TMP5:%.*]] = load <2 x i16>, ptr [[GEP3]], align 2
; CHECK-NEXT:    [[TMP6:%.*]] = zext <2 x i16> [[TMP5]] to <2 x i32>
; CHECK-NEXT:    [[TMP2:%.*]] = zext <2 x i16> [[TMP1]] to <2 x i32>
; CHECK-NEXT:    [[TMP7:%.*]] = sub nsw <2 x i32> [[TMP6]], [[TMP2]]
; CHECK-NEXT:    [[TMP8:%.*]] = sitofp <2 x i32> [[TMP7]] to <2 x double>
; CHECK-NEXT:    [[TMP9:%.*]] = fdiv <2 x double> [[TMP4]], [[TMP8]]
; CHECK-NEXT:    [[TMP10:%.*]] = extractelement <2 x double> [[TMP9]], i32 0
; CHECK-NEXT:    [[TMP11:%.*]] = extractelement <2 x double> [[TMP9]], i32 1
; CHECK-NEXT:    [[FCMP:%.*]] = fcmp olt double [[TMP11]], [[TMP10]]
; CHECK-NEXT:    ret void
;
  %gep0 = getelementptr inbounds i8, ptr %ptr, i64 328
  %gep1 = getelementptr inbounds i8, ptr %ptr, i64 330

  %gep3 = getelementptr inbounds i8, ptr %ptr, i64 334
  %gep4 = getelementptr inbounds i8, ptr %ptr, i64 336

  %ld0 = load i16, ptr %gep0, align 8
  %ld1 = load i16, ptr %gep1, align 2

  %zext0 = zext i16 %ld0 to i32
  %zext1 = zext i16 %ld1 to i32

  %xor0 = xor i32 %zext0, 65535
  %xor1 = xor i32 %zext1, 65535

  %sitofp0 = sitofp i32 %xor0 to double
  %sitofp1 = sitofp i32 %xor1 to double

  %ld3 = load i16, ptr %gep3, align 2
  %ld4 = load i16, ptr %gep4, align 8

  %zext3 = zext i16 %ld3 to i32
  %zext4 = zext i16 %ld4 to i32

  %sub30 = sub nsw i32 %zext3, %zext0
  %sub41 = sub nsw i32 %zext4, %zext1

  %sitofp30 = sitofp i32 %sub30 to double
  %sitofp41 = sitofp i32 %sub41 to double

  %fdiv030 = fdiv double %sitofp0, %sitofp30
  %fdiv141 = fdiv double %sitofp1, %sitofp41
  %fcmp = fcmp olt double %fdiv141, %fdiv030
  ret void
}
