; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown-linux-gnu < %s | FileCheck %s

define void @test(double %v) {
; CHECK-LABEL: define void @test(
; CHECK-SAME: double [[V:%.*]]) {
; CHECK-NEXT:  [[ENTRY:.*]]:
; CHECK-NEXT:    [[TMP0:%.*]] = insertelement <2 x double> <double 0.000000e+00, double poison>, double [[V]], i32 1
; CHECK-NEXT:    [[TMP1:%.*]] = fmul <2 x double> zeroinitializer, [[TMP0]]
; CHECK-NEXT:    [[TMP2:%.*]] = shufflevector <2 x double> [[TMP1]], <2 x double> poison, <2 x i32> <i32 1, i32 0>
; CHECK-NEXT:    br label %[[LOOP:.*]]
; CHECK:       [[LOOP]]:
; CHECK-NEXT:    [[T50_02:%.*]] = phi double [ 0.000000e+00, %[[ENTRY]] ], [ [[TMP8:%.*]], %[[LOOP]] ]
; CHECK-NEXT:    [[TMP3:%.*]] = phi <2 x double> [ zeroinitializer, %[[ENTRY]] ], [ [[TMP10:%.*]], %[[LOOP]] ]
; CHECK-NEXT:    [[TMP4:%.*]] = fmul <2 x double> [[TMP3]], [[TMP2]]
; CHECK-NEXT:    [[TMP5:%.*]] = extractelement <2 x double> [[TMP4]], i32 0
; CHECK-NEXT:    [[TMP6:%.*]] = extractelement <2 x double> [[TMP4]], i32 1
; CHECK-NEXT:    [[TMP7:%.*]] = fadd double [[TMP6]], [[TMP5]]
; CHECK-NEXT:    [[TMP8]] = fadd double [[TMP7]], [[V]]
; CHECK-NEXT:    [[TMP9:%.*]] = fmul <2 x double> zeroinitializer, [[TMP1]]
; CHECK-NEXT:    [[TMP10]] = fadd <2 x double> zeroinitializer, [[TMP9]]
; CHECK-NEXT:    br label %[[LOOP]]
;
entry:
  %mul.3 = fmul double 0.000000e+00, %v
  %mul.4 = fmul double 0.000000e+00, 0.000000e+00
  br label %loop

loop:
  %t48.0 = phi double [ 0.000000e+00, %entry ], [ %5, %loop ]
  %t50.02 = phi double [ 0.000000e+00, %entry ], [ %3, %loop ]
  %t52.0 = phi double [ 0.000000e+00, %entry ], [ %7, %loop ]
  %0 = fmul double %t52.0, %mul.3
  %1 = fmul double %t48.0, %mul.4
  %2 = fadd double %1, %0
  %3 = fadd double %2, %v
  %4 = fmul double 0.000000e+00, %mul.3
  %5 = fadd double 0.000000e+00, %4
  %6 = fmul double 0.000000e+00, %mul.4
  %7 = fadd double 0.000000e+00, %6
  br label %loop
}
