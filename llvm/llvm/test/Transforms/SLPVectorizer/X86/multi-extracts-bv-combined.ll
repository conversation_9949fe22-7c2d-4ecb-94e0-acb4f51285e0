; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown-linux-gnu -mcpu=cascadelake < %s | FileCheck %s

define i32 @foo() {
; CHECK-LABEL: define i32 @foo(
; CHECK-SAME: ) #[[ATTR0:[0-9]+]] {
; CHECK-NEXT:  [[ENTRY:.*:]]
; CHECK-NEXT:    [[D:%.*]] = load i32, ptr null, align 4
; CHECK-NEXT:    [[TMP0:%.*]] = insertelement <4 x i32> <i32 0, i32 undef, i32 1, i32 0>, i32 [[D]], i32 1
; CHECK-NEXT:    [[TMP1:%.*]] = shufflevector <4 x i32> [[TMP0]], <4 x i32> poison, <8 x i32> <i32 0, i32 1, i32 1, i32 2, i32 3, i32 1, i32 1, i32 1>
; CHECK-NEXT:    [[TMP2:%.*]] = or <8 x i32> zeroinitializer, [[TMP1]]
; CHECK-NEXT:    [[TMP3:%.*]] = add <8 x i32> zeroinitializer, [[TMP1]]
; CHECK-NEXT:    [[TMP4:%.*]] = shufflevector <8 x i32> [[TMP2]], <8 x i32> [[TMP3]], <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 12, i32 5, i32 6, i32 7>
; CHECK-NEXT:    store <8 x i32> [[TMP4]], ptr getelementptr inbounds ([64 x i32], ptr null, i64 0, i64 15), align 4
; CHECK-NEXT:    ret i32 0
;
entry:
  %d = load i32, ptr null, align 4
  %0 = extractelement <8 x i32> <i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1, i32 1>, i32 0
  %1 = extractelement <8 x i32> zeroinitializer, i32 0
  %2 = or i32 0, %d
  %3 = or i32 0, %d
  %4 = or i32 0, %d
  %5 = add i32 0, 0
  %6 = or i32 0, %0
  %7 = or i32 0, %d
  %8 = or i32 0, %d
  %9 = or i32 0, %1
  store i32 %2, ptr getelementptr inbounds ([64 x i32], ptr null, i64 0, i64 22), align 8
  store i32 %3, ptr getelementptr inbounds ([64 x i32], ptr null, i64 0, i64 21), align 4
  store i32 %4, ptr getelementptr inbounds ([64 x i32], ptr null, i64 0, i64 20), align 16
  store i32 %5, ptr getelementptr inbounds ([64 x i32], ptr null, i64 0, i64 19), align 4
  store i32 %6, ptr getelementptr inbounds ([64 x i32], ptr null, i64 0, i64 18), align 8
  store i32 %7, ptr getelementptr inbounds ([64 x i32], ptr null, i64 0, i64 17), align 4
  store i32 %8, ptr getelementptr inbounds ([64 x i32], ptr null, i64 0, i64 16), align 16
  store i32 %9, ptr getelementptr inbounds ([64 x i32], ptr null, i64 0, i64 15), align 4
  ret i32 0
}
