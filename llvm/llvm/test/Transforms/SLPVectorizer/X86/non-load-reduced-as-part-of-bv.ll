; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown-linux-gnu < %s | FileCheck %s

define i1 @foo() {
; CHECK-LABEL: define i1 @foo() {
; CHECK-NEXT:  [[ENTRY:.*:]]
; CHECK-NEXT:    [[TOBOOL_NOT_NOT509_I_2329_I_I:%.*]] = icmp ne i32 0, 0
; CHECK-NEXT:    [[STOREMERGE_2333_I_I:%.*]] = select i1 [[TOBOOL_NOT_NOT509_I_2329_I_I]], i32 0, i32 0
; CHECK-NEXT:    [[TOBOOL_NOT_NOT509_I_1_2_I_I:%.*]] = icmp ne i32 [[STOREMERGE_2333_I_I]], 0
; CHECK-NEXT:    [[TMP0:%.*]] = insertelement <8 x i1> poison, i1 [[TOBOOL_NOT_NOT509_I_1_2_I_I]], i32 4
; CHECK-NEXT:    [[TMP1:%.*]] = insertelement <8 x i1> [[TMP0]], i1 [[TOBOOL_NOT_NOT509_I_2329_I_I]], i32 5
; CHECK-NEXT:    [[TMP2:%.*]] = call <8 x i1> @llvm.vector.insert.v8i1.v4i1(<8 x i1> [[TMP1]], <4 x i1> zeroinitializer, i64 0)
; CHECK-NEXT:    [[TMP3:%.*]] = call <8 x i1> @llvm.vector.insert.v8i1.v2i1(<8 x i1> [[TMP2]], <2 x i1> zeroinitializer, i64 6)
; CHECK-NEXT:    [[TMP4:%.*]] = freeze <8 x i1> [[TMP3]]
; CHECK-NEXT:    [[TMP5:%.*]] = call i1 @llvm.vector.reduce.and.v8i1(<8 x i1> [[TMP4]])
; CHECK-NEXT:    [[OP_RDX:%.*]] = select i1 false, i1 [[TMP5]], i1 false
; CHECK-NEXT:    ret i1 [[OP_RDX]]
;
entry:
  %tobool.not.not509.i.1311.i.i = icmp ne i32 0, 0
  %cmp80.1319.i.i189 = icmp sgt i32 0, 0
  %tobool.not.not509.i.1.1.i.i = icmp ne i32 0, 0
  %cmp80.1.1.i.i190 = icmp sgt i32 0, 0
  %tobool.not.not509.i.2329.i.i = icmp ne i32 0, 0
  %storemerge.2333.i.i = select i1 %tobool.not.not509.i.2329.i.i, i32 0, i32 0
  %cmp80.2337.i.i192 = icmp sgt i32 0, 0
  %tobool.not.not509.i.1.2.i.i = icmp ne i32 %storemerge.2333.i.i, 0
  %cmp80.1.2.i.i193 = icmp sgt i32 0, 0
  %cmp80.1.2.i.i = select i1 %tobool.not.not509.i.1.2.i.i, i1 %cmp80.1.2.i.i193, i1 false
  %0 = select i1 %cmp80.1.2.i.i, i1 %tobool.not.not509.i.2329.i.i, i1 false
  %1 = select i1 %0, i1 %cmp80.2337.i.i192, i1 false
  %2 = select i1 %1, i1 false, i1 false
  %3 = select i1 %2, i1 %tobool.not.not509.i.1.1.i.i, i1 false
  %4 = select i1 %3, i1 %cmp80.1.1.i.i190, i1 false
  %5 = select i1 %4, i1 %tobool.not.not509.i.1311.i.i, i1 false
  %6 = select i1 %5, i1 %cmp80.1319.i.i189, i1 false
  ret i1 %6
}
