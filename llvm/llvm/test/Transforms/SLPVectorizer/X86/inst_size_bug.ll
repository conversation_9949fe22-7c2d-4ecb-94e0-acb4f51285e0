; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt < %s -passes=slp-vectorizer -S -mtriple=x86_64-unknown-linux-gnu -mattr=+avx2 -slp-max-reg-size=128 | FileCheck %s

define void @inst_size(ptr %a, <2 x i64> %b) {
; CHECK-LABEL: @inst_size(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMPL1:%.*]] = load i64, ptr [[A:%.*]], align 4
; CHECK-NEXT:    [[PTR2:%.*]] = getelementptr inbounds i64, ptr [[A]], i64 1
; CHECK-NEXT:    [[TMP0:%.*]] = load <2 x i64>, ptr [[PTR2]], align 4
; CHECK-NEXT:    [[PTR4:%.*]] = getelementptr inbounds i64, ptr [[A]], i64 3
; CHECK-NEXT:    [[TMPL4:%.*]] = load i64, ptr [[PTR4]], align 4
; CHECK-NEXT:    [[TMP1:%.*]] = shufflevector <2 x i64> [[B:%.*]], <2 x i64> poison, <4 x i32> <i32 0, i32 poison, i32 poison, i32 poison>
; CHECK-NEXT:    [[TMP2:%.*]] = insertelement <4 x i64> [[TMP1]], i64 [[TMPL1]], i32 1
; CHECK-NEXT:    [[TMP3:%.*]] = call <4 x i64> @llvm.vector.insert.v4i64.v2i64(<4 x i64> [[TMP2]], <2 x i64> [[TMP0]], i64 2)
; CHECK-NEXT:    [[TMP4:%.*]] = icmp sgt <4 x i64> zeroinitializer, [[TMP3]]
; CHECK-NEXT:    [[T45:%.*]] = icmp sgt i64 0, [[TMPL4]]
; CHECK-NEXT:    br label [[BLOCK:%.*]]
; CHECK:       block:
; CHECK-NEXT:    [[PHI5:%.*]] = phi i1 [ [[T45]], [[ENTRY:%.*]] ]
; CHECK-NEXT:    [[TMP5:%.*]] = phi <4 x i1> [ [[TMP4]], [[ENTRY]] ]
; CHECK-NEXT:    ret void
;
entry:
  %val = extractelement <2 x i64> %b, i32 0
  %tmpl1 = load i64, ptr %a, align 4
  %ptr2 = getelementptr inbounds i64, ptr %a, i64 1
  %tmpl2 = load i64, ptr %ptr2, align 4
  %ptr3 = getelementptr inbounds i64, ptr %a, i64 2
  %tmpl3 = load i64, ptr %ptr3, align 4
  %ptr4 = getelementptr inbounds i64, ptr %a, i64 3
  %tmpl4 = load i64, ptr %ptr4, align 4
  %t41 = icmp sgt i64 0, %val
  %t42 = icmp sgt i64 0, %tmpl1
  %t43 = icmp sgt i64 0, %tmpl2
  %t44 = icmp sgt i64 0, %tmpl3
  %t45 = icmp sgt i64 0, %tmpl4
  br label %block
block:
  %phi1 = phi i1 [ %t41, %entry]
  %phi2 = phi i1 [ %t42, %entry]
  %phi3 = phi i1 [ %t43, %entry]
  %phi4 = phi i1 [ %t44, %entry]
  %phi5 = phi i1 [ %t45, %entry]
  ret void
}
