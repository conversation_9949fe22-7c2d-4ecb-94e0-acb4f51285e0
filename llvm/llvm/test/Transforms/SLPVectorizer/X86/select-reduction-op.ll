; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown-linux < %s | FileCheck %s

define i1 @src(i1 %cmp4.118.i) {
; CHECK-LABEL: define i1 @src(
; CHECK-SAME: i1 [[CMP4_118_I:%.*]]) {
; CHECK-NEXT:    [[CMP4_118_I_NOT:%.*]] = xor i1 [[CMP4_118_I]], true
; CHECK-NEXT:    [[TMP1:%.*]] = freeze <4 x i1> poison
; CHECK-NEXT:    [[TMP2:%.*]] = call i1 @llvm.vector.reduce.or.v4i1(<4 x i1> [[TMP1]])
; CHECK-NEXT:    [[OP_RDX:%.*]] = select i1 [[CMP4_118_I_NOT]], i1 true, i1 [[TMP2]]
; CHECK-NEXT:    [[TMP3:%.*]] = freeze i1 [[OP_RDX]]
; CHECK-NEXT:    [[OP_RDX1:%.*]] = select i1 [[TMP3]], i1 true, i1 poison
; CHECK-NEXT:    ret i1 [[OP_RDX1]]
;
  %cmp4.118.i.not = xor i1 %cmp4.118.i, true
  %brmerge = select i1 %cmp4.118.i.not, i1 true, i1 poison
  %.not = xor i1 poison, true
  %brmerge2 = select i1 %brmerge, i1 true, i1 %.not
  %.not3 = xor i1 poison, true
  %brmerge4 = select i1 %brmerge2, i1 true, i1 %.not3
  %.not5 = xor i1 poison, true
  %brmerge6 = select i1 %brmerge4, i1 true, i1 %.not5
  %.not7 = xor i1 poison, true
  %brmerge8 = select i1 %brmerge6, i1 true, i1 %.not7
  ret i1 %brmerge8
}
