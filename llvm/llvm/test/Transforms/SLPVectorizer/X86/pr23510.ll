; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; PR23510
; RUN: opt < %s -passes=slp-vectorizer -S | FileCheck %s

target datalayout = "e-m:e-i64:64-f80:128-n8:16:32:64-S128"
target triple = "x86_64-unknown-linux-gnu"

@total = global i64 0, align 8

define void @_Z3fooPml(ptr nocapture %a, i64 %i) {
; CHECK-LABEL: @_Z3fooPml(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP1:%.*]] = load <2 x i64>, ptr [[A:%.*]], align 8
; CHECK-NEXT:    [[TMP2:%.*]] = lshr <2 x i64> [[TMP1]], splat (i64 4)
; CHECK-NEXT:    store <2 x i64> [[TMP2]], ptr [[A]], align 8
; CHECK-NEXT:    [[ARRAYIDX3:%.*]] = getelementptr inbounds i64, ptr [[A]], i64 [[I:%.*]]
; CHECK-NEXT:    [[TMP5:%.*]] = load i64, ptr [[ARRAYIDX3]], align 8
; CHECK-NEXT:    [[TMP3:%.*]] = load i64, ptr @total, align 8
; CHECK-NEXT:    [[ADD:%.*]] = add i64 [[TMP3]], [[TMP5]]
; CHECK-NEXT:    store i64 [[ADD]], ptr @total, align 8
; CHECK-NEXT:    [[TMP5:%.*]] = load <2 x i64>, ptr [[A]], align 8
; CHECK-NEXT:    [[TMP3:%.*]] = lshr <2 x i64> [[TMP5]], splat (i64 4)
; CHECK-NEXT:    store <2 x i64> [[TMP3]], ptr [[A]], align 8
; CHECK-NEXT:    [[TMP6:%.*]] = load i64, ptr [[ARRAYIDX3]], align 8
; CHECK-NEXT:    [[TMP7:%.*]] = load i64, ptr @total, align 8
; CHECK-NEXT:    [[ADD9:%.*]] = add i64 [[TMP7]], [[TMP6]]
; CHECK-NEXT:    store i64 [[ADD9]], ptr @total, align 8
; CHECK-NEXT:    ret void
;
entry:
  %tmp = load i64, ptr %a, align 8
  %shr = lshr i64 %tmp, 4
  store i64 %shr, ptr %a, align 8
  %arrayidx1 = getelementptr inbounds i64, ptr %a, i64 1
  %tmp1 = load i64, ptr %arrayidx1, align 8
  %shr2 = lshr i64 %tmp1, 4
  store i64 %shr2, ptr %arrayidx1, align 8
  %arrayidx3 = getelementptr inbounds i64, ptr %a, i64 %i
  %tmp2 = load i64, ptr %arrayidx3, align 8
  %tmp3 = load i64, ptr @total, align 8
  %add = add i64 %tmp3, %tmp2
  store i64 %add, ptr @total, align 8
  %tmp4 = load i64, ptr %a, align 8
  %shr5 = lshr i64 %tmp4, 4
  store i64 %shr5, ptr %a, align 8
  %tmp5 = load i64, ptr %arrayidx1, align 8
  %shr7 = lshr i64 %tmp5, 4
  store i64 %shr7, ptr %arrayidx1, align 8
  %tmp6 = load i64, ptr %arrayidx3, align 8
  %tmp7 = load i64, ptr @total, align 8
  %add9 = add i64 %tmp7, %tmp6
  store i64 %add9, ptr @total, align 8
  ret void
}
