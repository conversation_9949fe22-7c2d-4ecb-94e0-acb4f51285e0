; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -passes=slp-vectorizer -S -mtriple=x86_64-unknown-linux-gnu -mcpu=bdver2 < %s | FileCheck %s

define void @rftbsub(ptr %a) {
; CHECK-LABEL: @rftbsub(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[ARRAYIDX6:%.*]] = getelementptr inbounds double, ptr [[A:%.*]], i64 2
; CHECK-NEXT:    [[TMP0:%.*]] = or disjoint i64 2, 1
; CHECK-NEXT:    [[ARRAYIDX12:%.*]] = getelementptr inbounds double, ptr [[A]], i64 [[TMP0]]
; CHECK-NEXT:    [[SUB22:%.*]] = fsub double undef, undef
; CHECK-NEXT:    [[TMP1:%.*]] = load double, ptr [[ARRAYIDX12]], align 8
; CHECK-NEXT:    [[TMP2:%.*]] = load <2 x double>, ptr [[ARRAYIDX6]], align 8
; CHECK-NEXT:    [[ADD16:%.*]] = fadd double [[TMP1]], undef
; CHECK-NEXT:    [[MUL18:%.*]] = fmul double undef, [[ADD16]]
; CHECK-NEXT:    [[ADD19:%.*]] = fadd double undef, [[MUL18]]
; CHECK-NEXT:    [[TMP3:%.*]] = insertelement <2 x double> poison, double [[ADD19]], i32 0
; CHECK-NEXT:    [[TMP4:%.*]] = insertelement <2 x double> [[TMP3]], double [[SUB22]], i32 1
; CHECK-NEXT:    [[TMP5:%.*]] = fsub <2 x double> [[TMP2]], [[TMP4]]
; CHECK-NEXT:    store <2 x double> [[TMP5]], ptr [[ARRAYIDX6]], align 8
; CHECK-NEXT:    ret void
;
entry:
  %arrayidx6 = getelementptr inbounds double, ptr %a, i64 2
  %0 = load double, ptr %arrayidx6, align 8
  %1 = or disjoint i64 2, 1
  %arrayidx12 = getelementptr inbounds double, ptr %a, i64 %1
  %2 = load double, ptr %arrayidx12, align 8
  %add16 = fadd double %2, undef
  %mul18 = fmul double undef, %add16
  %add19 = fadd double undef, %mul18
  %sub22 = fsub double undef, undef
  %sub25 = fsub double %0, %add19
  store double %sub25, ptr %arrayidx6, align 8
  %sub29 = fsub double %2, %sub22
  store double %sub29, ptr %arrayidx12, align 8
  ret void
}
