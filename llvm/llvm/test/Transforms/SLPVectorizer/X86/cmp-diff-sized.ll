; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt < %s -passes=slp-vectorizer -S -mtriple=x86_64--- | FileCheck %s

define void @test(ptr noalias %a, ptr %b) {
; CHECK-LABEL: @test(
; CHECK-NEXT:    [[PA1:%.*]] = getelementptr inbounds i64, ptr [[A:%.*]], i32 64
; CHECK-NEXT:    [[A1:%.*]] = load i64, ptr [[PA1]], align 8
; CHECK-NEXT:    [[PB1:%.*]] = getelementptr inbounds i64, ptr [[B:%.*]], i32 64
; CHECK-NEXT:    [[B1:%.*]] = load i64, ptr [[PB1]], align 8
; CHECK-NEXT:    [[TMP1:%.*]] = load <2 x i32>, ptr [[A]], align 4
; CHECK-NEXT:    [[TMP2:%.*]] = load <2 x i32>, ptr [[B]], align 4
; CHECK-NEXT:    [[C1:%.*]] = icmp eq i64 [[B1]], [[A1]]
; CHECK-NEXT:    [[TMP3:%.*]] = icmp eq <2 x i32> [[TMP1]], [[TMP2]]
; CHECK-NEXT:    ret void
;
  %pa1 = getelementptr inbounds i64, ptr %a, i32 64
  %pa2 = getelementptr inbounds i32, ptr %a, i32 1
  %a0 = load i32, ptr %a, align 4
  %a1 = load i64, ptr %pa1, align 8
  %a2 = load i32, ptr %pa2, align 4
  %pb1 = getelementptr inbounds i64, ptr %b, i32 64
  %pb2 = getelementptr inbounds i32, ptr %b, i32 1
  %b0 = load i32, ptr %b, align 4
  %b1 = load i64, ptr %pb1, align 8
  %b2 = load i32, ptr %pb2, align 4
  %c0 = icmp eq i32 %a0, %b0
  %c1 = icmp eq i64 %b1, %a1
  %c2 = icmp eq i32 %b2, %a2
  ret void
}
