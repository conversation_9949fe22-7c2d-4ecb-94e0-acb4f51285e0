; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S -mtriple=x86_64-unknown-linux --passes=slp-vectorizer < %s | FileCheck %s

define <6 x double> @test(ptr %a) {
; CHECK-LABEL: define <6 x double> @test(
; CHECK-SAME: ptr [[A:%.*]]) {
; CHECK-NEXT:  [[ENTRY:.*:]]
; CHECK-NEXT:    [[TMP5:%.*]] = load <6 x double>, ptr [[A]], align 8
; CHECK-NEXT:    ret <6 x double> [[TMP5]]
;
entry:
  %1 = load double, ptr %a, align 8
  %2 = getelementptr double, ptr %a, i16 1
  %3 = load double, ptr %2, align 8
  %4 = getelementptr double, ptr %a, i16 2
  %5 = load double, ptr %4, align 8
  %6 = getelementptr double, ptr %a, i16 3
  %7 = load double, ptr %6, align 8
  %8 = getelementptr double, ptr %a, i16 4
  %9 = load double, ptr %8, align 8
  %10 = getelementptr double, ptr %a, i16 5
  %11 = load double, ptr %10, align 8
  %12 = insertelement <6 x double> poison, double %1, i32 0
  %13 = insertelement <6 x double> %12, double %3, i32 1
  %14 = insertelement <6 x double> %13, double %5, i32 2
  %15 = insertelement <6 x double> %14, double %7, i32 3
  %16 = insertelement <6 x double> %15, double %9, i32 4
  %17 = insertelement <6 x double> %16, double %11, i32 5
  ret <6 x double> %17
}
