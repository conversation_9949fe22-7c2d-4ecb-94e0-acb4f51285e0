; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -mtriple x86_64-unknown-linux-gnu -passes=slp-vectorizer -S %s | FileCheck %s

define void @e() {
; CHECK-LABEL: @e(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    store <2 x i64> zeroinitializer, ptr null, align 8
; CHECK-NEXT:    ret void
;
entry:
  %0 = extractelement <1 x i64> zeroinitializer, i64 0
  %bf.value = and i64 %0, 0
  %bf.set = or i64 0, %bf.value
  store i64 %bf.set, ptr getelementptr inbounds (i8, ptr null, i64 8), align 8
  %bf.value2 = and i64 0, 0
  %bf.set4 = or i64 0, %bf.value2
  store i64 %bf.set4, ptr null, align 8
  ret void
}
