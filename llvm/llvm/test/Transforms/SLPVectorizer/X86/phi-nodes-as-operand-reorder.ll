; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown-linux-gnu -slp-threshold=-99999 < %s | FileCheck %s

define void @test() {
; CHECK-LABEL: define void @test() {
; CHECK-NEXT:  [[BB:.*]]:
; CHECK-NEXT:    br label %[[BB1:.*]]
; CHECK:       [[BB1]]:
; CHECK-NEXT:    [[TMP0:%.*]] = phi <2 x i32> [ zeroinitializer, %[[BB]] ], [ [[TMP3:%.*]], %[[BB3:.*]] ]
; CHECK-NEXT:    br i1 false, label %[[BB6:.*]], label %[[BB3]]
; CHECK:       [[BB3]]:
; CHECK-NEXT:    [[TMP1:%.*]] = shufflevector <2 x i32> [[TMP0]], <2 x i32> <i32 0, i32 poison>, <2 x i32> <i32 2, i32 1>
; CHECK-NEXT:    [[TMP2:%.*]] = add <2 x i32> zeroinitializer, [[TMP1]]
; CHECK-NEXT:    [[TMP3]] = add <2 x i32> zeroinitializer, [[TMP1]]
; CHECK-NEXT:    br i1 false, label %[[BB6]], label %[[BB1]]
; CHECK:       [[BB6]]:
; CHECK-NEXT:    [[TMP4:%.*]] = phi <2 x i32> [ [[TMP0]], %[[BB1]] ], [ [[TMP2]], %[[BB3]] ]
; CHECK-NEXT:    ret void
;
bb:
  br label %bb1

bb1:
  %phi = phi i32 [ 0, %bb ], [ %add5, %bb3 ]
  %phi2 = phi i32 [ 0, %bb ], [ %add, %bb3 ]
  br i1 false, label %bb6, label %bb3

bb3:
  %add = add i32 0, 0
  %add4 = add i32 0, 0
  %add5 = add i32 %phi, 0
  br i1 false, label %bb6, label %bb1

bb6:
  %phi7 = phi i32 [ %phi2, %bb1 ], [ %add4, %bb3 ]
  %phi8 = phi i32 [ %phi, %bb1 ], [ %add5, %bb3 ]
  ret void
}
