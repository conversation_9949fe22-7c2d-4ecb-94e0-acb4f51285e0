; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown-linux-gnu < %s | FileCheck %s

define void @test(ptr noalias %0, ptr noalias %1) {
; CHECK-LABEL: define void @test(
; CHECK-SAME: ptr noalias [[TMP0:%.*]], ptr noalias [[TMP1:%.*]]) {
; CHECK-NEXT:    [[TMP5:%.*]] = getelementptr i8, ptr [[TMP1]], i64 8
; CHECK-NEXT:    [[TMP9:%.*]] = getelementptr i8, ptr [[TMP0]], i64 48
; CHECK-NEXT:    [[TMP11:%.*]] = getelementptr i8, ptr [[TMP0]], i64 8
; CHECK-NEXT:    [[TMP6:%.*]] = load <2 x double>, ptr [[TMP9]], align 16
; CHECK-NEXT:    [[TMP7:%.*]] = load <4 x double>, ptr [[TMP11]], align 8
; CHECK-NEXT:    [[TMP8:%.*]] = shufflevector <2 x double> [[TMP6]], <2 x double> poison, <4 x i32> <i32 0, i32 1, i32 poison, i32 poison>
; CHECK-NEXT:    [[TMP12:%.*]] = shufflevector <4 x double> [[TMP7]], <4 x double> [[TMP8]], <6 x i32> <i32 2, i32 4, i32 0, i32 3, i32 poison, i32 poison>
; CHECK-NEXT:    [[TMP10:%.*]] = shufflevector <4 x double> [[TMP8]], <4 x double> [[TMP7]], <6 x i32> <i32 poison, i32 poison, i32 poison, i32 poison, i32 1, i32 5>
; CHECK-NEXT:    [[TMP13:%.*]] = shufflevector <6 x double> [[TMP12]], <6 x double> [[TMP10]], <6 x i32> <i32 0, i32 1, i32 2, i32 3, i32 10, i32 11>
; CHECK-NEXT:    store <6 x double> [[TMP13]], ptr [[TMP5]], align 8
; CHECK-NEXT:    [[TMP21:%.*]] = getelementptr i8, ptr [[TMP0]], i64 40
; CHECK-NEXT:    [[TMP22:%.*]] = load double, ptr [[TMP21]], align 8
; CHECK-NEXT:    [[TMP23:%.*]] = getelementptr i8, ptr [[TMP1]], i64 56
; CHECK-NEXT:    store double [[TMP22]], ptr [[TMP23]], align 8
; CHECK-NEXT:    ret void
;
  %3 = getelementptr i8, ptr %1, i64 24
  %4 = getelementptr i8, ptr %1, i64 48
  %5 = getelementptr i8, ptr %1, i64 8
  %6 = getelementptr i8, ptr %1, i64 16
  %8 = getelementptr i8, ptr %0, i64 24
  %9 = load double, ptr %8, align 8
  store double %9, ptr %5, align 8
  %10 = getelementptr i8, ptr %0, i64 48
  %11 = load double, ptr %10, align 16
  store double %11, ptr %6, align 16
  %12 = getelementptr i8, ptr %0, i64 8
  %13 = load double, ptr %12, align 8
  store double %13, ptr %3, align 8
  %14 = getelementptr i8, ptr %0, i64 32
  %15 = load double, ptr %14, align 16
  %16 = getelementptr i8, ptr %1, i64 32
  store double %15, ptr %16, align 16
  %17 = getelementptr i8, ptr %0, i64 56
  %18 = load double, ptr %17, align 8
  %19 = getelementptr i8, ptr %1, i64 40
  store double %18, ptr %19, align 8
  %20 = getelementptr i8, ptr %0, i64 16
  %21 = load double, ptr %20, align 16
  store double %21, ptr %4, align 16
  %22 = getelementptr i8, ptr %0, i64 40
  %23 = load double, ptr %22, align 8
  %24 = getelementptr i8, ptr %1, i64 56
  store double %23, ptr %24, align 8
  ret void
}
