; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -S -mtriple=x86_64-pc-linux-gnu -mcpu=generic -passes=slp-vectorizer -pass-remarks-output=%t < %s | FileCheck %s
; RUN: FileCheck --input-file=%t --check-prefix=YAML %s

define i32 @foo(ptr %diff) #0 {
; CHECK-LABEL: @foo(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[M2:%.*]] = alloca [8 x [8 x i32]], align 16
; CHECK-NEXT:    br label [[FOR_BODY:%.*]]
; CHECK:       for.body:
; CHECK-NEXT:    [[INDVARS_IV:%.*]] = phi i64 [ 0, [[ENTRY:%.*]] ], [ [[INDVARS_IV_NEXT:%.*]], [[FOR_BODY]] ]
; CHECK-NEXT:    [[A_088:%.*]] = phi i32 [ 0, [[ENTRY]] ], [ [[OP_RDX:%.*]], [[FOR_BODY]] ]
; CHECK-NEXT:    [[TMP0:%.*]] = shl i64 [[INDVARS_IV]], 3
; CHECK-NEXT:    [[ARRAYIDX:%.*]] = getelementptr inbounds i32, ptr [[DIFF:%.*]], i64 [[TMP0]]
; CHECK-NEXT:    [[TMP1:%.*]] = or disjoint i64 [[TMP0]], 4
; CHECK-NEXT:    [[ARRAYIDX2:%.*]] = getelementptr inbounds i32, ptr [[DIFF]], i64 [[TMP1]]
; CHECK-NEXT:    [[ARRAYIDX6:%.*]] = getelementptr inbounds [8 x [8 x i32]], ptr [[M2]], i64 0, i64 [[INDVARS_IV]], i64 0
; CHECK-NEXT:    [[TMP2:%.*]] = load <4 x i32>, ptr [[ARRAYIDX]], align 4
; CHECK-NEXT:    [[TMP3:%.*]] = load <4 x i32>, ptr [[ARRAYIDX2]], align 4
; CHECK-NEXT:    [[TMP4:%.*]] = add nsw <4 x i32> [[TMP3]], [[TMP2]]
; CHECK-NEXT:    store <4 x i32> [[TMP4]], ptr [[ARRAYIDX6]], align 16
; CHECK-NEXT:    [[TMP5:%.*]] = call i32 @llvm.vector.reduce.add.v4i32(<4 x i32> [[TMP4]])
; CHECK-NEXT:    [[OP_RDX]] = add i32 [[TMP5]], [[A_088]]
; CHECK-NEXT:    [[INDVARS_IV_NEXT]] = add nuw nsw i64 [[INDVARS_IV]], 1
; CHECK-NEXT:    [[EXITCOND:%.*]] = icmp eq i64 [[INDVARS_IV_NEXT]], 8
; CHECK-NEXT:    br i1 [[EXITCOND]], label [[FOR_END:%.*]], label [[FOR_BODY]]
; CHECK:       for.end:
; CHECK-NEXT:    ret i32 [[OP_RDX]]
;
entry:
  %m2 = alloca [8 x [8 x i32]], align 16
  br label %for.body

for.body:                                         ; preds = %for.body, %entry
  %indvars.iv = phi i64 [ 0, %entry ], [ %indvars.iv.next, %for.body ]
  %a.088 = phi i32 [ 0, %entry ], [ %add52, %for.body ]
  %0 = shl i64 %indvars.iv, 3
  %arrayidx = getelementptr inbounds i32, ptr %diff, i64 %0
  %1 = load i32, ptr %arrayidx, align 4
  %2 = or disjoint i64 %0, 4
  %arrayidx2 = getelementptr inbounds i32, ptr %diff, i64 %2
  %3 = load i32, ptr %arrayidx2, align 4
  %add3 = add nsw i32 %3, %1
  %arrayidx6 = getelementptr inbounds [8 x [8 x i32]], ptr %m2, i64 0, i64 %indvars.iv, i64 0
  store i32 %add3, ptr %arrayidx6, align 16

  %add10 = add nsw i32 %add3, %a.088
  %4 = or disjoint i64 %0, 1
  %arrayidx13 = getelementptr inbounds i32, ptr %diff, i64 %4
  %5 = load i32, ptr %arrayidx13, align 4
  %6 = or disjoint i64 %0, 5
  %arrayidx16 = getelementptr inbounds i32, ptr %diff, i64 %6
  %7 = load i32, ptr %arrayidx16, align 4
  %add17 = add nsw i32 %7, %5
  %arrayidx20 = getelementptr inbounds [8 x [8 x i32]], ptr %m2, i64 0, i64 %indvars.iv, i64 1
  store i32 %add17, ptr %arrayidx20, align 4

  %add24 = add nsw i32 %add10, %add17
  %8 = or disjoint i64 %0, 2
  %arrayidx27 = getelementptr inbounds i32, ptr %diff, i64 %8
  %9 = load i32, ptr %arrayidx27, align 4
  %10 = or disjoint i64 %0, 6
  %arrayidx30 = getelementptr inbounds i32, ptr %diff, i64 %10
  %11 = load i32, ptr %arrayidx30, align 4
  %add31 = add nsw i32 %11, %9
  %arrayidx34 = getelementptr inbounds [8 x [8 x i32]], ptr %m2, i64 0, i64 %indvars.iv, i64 2
  store i32 %add31, ptr %arrayidx34, align 8

  %add38 = add nsw i32 %add24, %add31
  %12 = or disjoint i64 %0, 3
  %arrayidx41 = getelementptr inbounds i32, ptr %diff, i64 %12
  %13 = load i32, ptr %arrayidx41, align 4
  %14 = or disjoint i64 %0, 7
  %arrayidx44 = getelementptr inbounds i32, ptr %diff, i64 %14
  %15 = load i32, ptr %arrayidx44, align 4

  %add45 = add nsw i32 %15, %13
  %arrayidx48 = getelementptr inbounds [8 x [8 x i32]], ptr %m2, i64 0, i64 %indvars.iv, i64 3
  store i32 %add45, ptr %arrayidx48, align 4

  %add52 = add nsw i32 %add38, %add45

  ; YAML:      --- !Passed
  ; YAML-NEXT: Pass:            slp-vectorizer
  ; YAML-NEXT: Name:            StoresVectorized
  ; YAML-NEXT: Function:        foo
  ; YAML-NEXT: Args:
  ; YAML-NEXT:   - String:          'Stores SLP vectorized with cost '
  ; YAML-NEXT:   - Cost:            '-5'
  ; YAML-NEXT:   - String:          ' and with tree size '
  ; YAML-NEXT:   - TreeSize:        '4'

  ; YAML:      --- !Passed
  ; YAML-NEXT: Pass:            slp-vectorizer
  ; YAML-NEXT: Name:            VectorizedHorizontalReduction
  ; YAML-NEXT: Function:        foo
  ; YAML-NEXT: Args:
  ; YAML-NEXT:   - String:          'Vectorized horizontal reduction with cost '
  ; YAML-NEXT:   - Cost:            '-7'
  ; YAML-NEXT:   - String:          ' and with tree size '
  ; YAML-NEXT:   - TreeSize:        '1'

  %indvars.iv.next = add nuw nsw i64 %indvars.iv, 1
  %exitcond = icmp eq i64 %indvars.iv.next, 8
  br i1 %exitcond, label %for.end, label %for.body

for.end:                                          ; preds = %for.body
  ret i32 %add52
}
