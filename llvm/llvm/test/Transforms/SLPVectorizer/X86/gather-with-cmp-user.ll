; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown-linux-gnu < %s | FileCheck %s

define i1 @test(i32 %g, i16 %d) {
; CHECK-LABEL: define i1 @test(
; CHECK-SAME: i32 [[G:%.*]], i16 [[D:%.*]]) {
; CHECK-NEXT:  [[ENTRY:.*:]]
; CHECK-NEXT:    [[TMP0:%.*]] = and i16 [[D]], 1
; CHECK-NEXT:    [[XOR_I_I:%.*]] = xor i32 [[G]], 1
; CHECK-NEXT:    [[TMP1:%.*]] = insertelement <2 x i32> poison, i32 [[G]], i32 0
; CHECK-NEXT:    [[TMP9:%.*]] = insertelement <2 x i32> [[TMP1]], i32 [[XOR_I_I]], i32 1
; CHECK-NEXT:    [[TMP4:%.*]] = trunc <2 x i32> [[TMP9]] to <2 x i8>
; CHECK-NEXT:    [[TMP5:%.*]] = shufflevector <2 x i8> [[TMP4]], <2 x i8> poison, <4 x i32> <i32 0, i32 1, i32 0, i32 1>
; CHECK-NEXT:    [[TMP6:%.*]] = add <4 x i8> [[TMP5]], <i8 -9, i8 -9, i8 -1, i8 -1>
; CHECK-NEXT:    [[TMP7:%.*]] = icmp sgt <4 x i8> [[TMP6]], splat (i8 -3)
; CHECK-NEXT:    [[TMP8:%.*]] = zext <4 x i1> [[TMP7]] to <4 x i8>
; CHECK-NEXT:    [[TMP10:%.*]] = shufflevector <2 x i32> [[TMP9]], <2 x i32> poison, <4 x i32> <i32 0, i32 1, i32 0, i32 1>
; CHECK-NEXT:    [[TMP11:%.*]] = zext <4 x i8> [[TMP8]] to <4 x i32>
; CHECK-NEXT:    [[TMP12:%.*]] = icmp sgt <4 x i32> [[TMP10]], [[TMP11]]
; CHECK-NEXT:    [[TMP13:%.*]] = call i1 @llvm.vector.reduce.and.v4i1(<4 x i1> [[TMP12]])
; CHECK-NEXT:    ret i1 [[TMP13]]
;
entry:
  %0 = and i16 %d, 1
  %xor.i.i = xor i32 %g, 1
  %conv1.i.i = trunc i32 %xor.i.i to i8
  %notsub.i = add i8 %conv1.i.i, -1
  %cmp.i.i = icmp sgt i8 %notsub.i, -3
  %conv3.i.i = zext i1 %cmp.i.i to i32
  %cmp4.i.i = icmp sgt i32 %xor.i.i, %conv3.i.i
  %conv1.1.i.i = trunc i32 %g to i8
  %notsub25.i = add i8 %conv1.1.i.i, -1
  %cmp.1.i.i = icmp sgt i8 %notsub25.i, -3
  %conv3.1.i.i = zext i1 %cmp.1.i.i to i32
  %cmp4.1.i.i = icmp sgt i32 %g, %conv3.1.i.i
  %notsub26.i = add i8 %conv1.1.i.i, -9
  %cmp.i17.i = icmp sgt i8 %notsub26.i, -3
  %conv3.i18.i = zext i1 %cmp.i17.i to i32
  %cmp4.i19.i = icmp sgt i32 %g, %conv3.i18.i
  %notsub27.i = add i8 %conv1.i.i, -9
  %cmp.1.i22.i = icmp sgt i8 %notsub27.i, -3
  %conv3.1.i23.i = zext i1 %cmp.1.i22.i to i32
  %cmp4.1.i24.i = icmp sgt i32 %xor.i.i, %conv3.1.i23.i
  %1 = and i1 %cmp4.i19.i, %cmp4.1.i24.i
  %2 = and i1 %cmp4.i.i, %1
  %3 = and i1 %cmp4.1.i.i, %2
  ret i1 %3
}
