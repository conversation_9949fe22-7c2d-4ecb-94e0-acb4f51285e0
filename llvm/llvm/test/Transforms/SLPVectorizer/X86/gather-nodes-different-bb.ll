; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -S -passes=slp-vectorizer -mtriple=x86_64-unknown-linux -mattr="-avx512pf,+avx512f,+avx512bw" -slp-threshold=-100 < %s | FileCheck %s

define i1 @foo(i32 %a) {
; CHECK-LABEL: @foo(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP0:%.*]] = sub nsw i32 0, [[A:%.*]]
; CHECK-NEXT:    br label [[BB4:%.*]]
; CHECK:       bb1:
; CHECK-NEXT:    [[LOCAL:%.*]] = sub nsw i32 0, 0
; CHECK-NEXT:    [[INS1:%.*]] = insertelement <2 x i32> poison, i32 [[TMP0]], i32 0
; CHECK-NEXT:    [[ADD:%.*]] = icmp eq i32 [[TMP0]], [[LOCAL]]
; CHECK-NEXT:    ret i1 [[ADD]]
;
entry:
  %0 = sub nsw i32 0, %a
  br label %bb1

bb1:
  %local = sub nsw i32 0, 0
  %ins1 = insertelement <2 x i32> poison, i32 %0, i32 0
  %add = icmp eq i32 %0, %local
  ret i1 %add
}
