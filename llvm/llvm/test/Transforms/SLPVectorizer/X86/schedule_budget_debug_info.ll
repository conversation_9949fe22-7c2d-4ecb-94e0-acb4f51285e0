; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt < %s -passes=slp-vectorizer -S -slp-schedule-budget=3 -mtriple=x86_64-apple-macosx10.8.0 -mcpu=corei7-avx | FileCheck %s -check-prefix VECTOR_DBG
; RUN: opt < %s -strip-debug -passes=slp-vectorizer -S -slp-schedule-budget=3 -mtriple=x86_64-apple-macosx10.8.0 -mcpu=corei7-avx | FileCheck %s -check-prefix VECTOR_NODBG

target datalayout = "e-m:o-i64:64-f80:128-n8:16:32:64-S128"
target triple = "x86_64-apple-macosx10.9.0"

; Verify that we get vectorization with -slp-schedule-budget=3. We should
; get vectorization even if there happens to be some dbg.value calls since they
; should be ignored, to not let debug information affect the code we get.

declare void @unknown()

define void @test(ptr %a, ptr %b, ptr %c, ptr %d) {
; VECTOR_DBG-LABEL: @test(
; VECTOR_DBG-NEXT:  entry:
; VECTOR_DBG-NEXT:    [[L0:%.*]] = load float, ptr [[A:%.*]], align 4
; VECTOR_DBG-NEXT:    [[A1:%.*]] = getelementptr inbounds float, ptr [[A]], i64 1
; VECTOR_DBG-NEXT:    [[L1:%.*]] = load float, ptr [[A1]], align 4
; VECTOR_DBG-NEXT:    [[A2:%.*]] = getelementptr inbounds float, ptr [[A]], i64 2
; VECTOR_DBG-NEXT:      #dbg_value(i16 1, [[META3:![0-9]+]], !DIExpression(), [[META5:![0-9]+]])
; VECTOR_DBG-NEXT:      #dbg_value(i16 1, [[META3]], !DIExpression(), [[META5]])
; VECTOR_DBG-NEXT:      #dbg_value(i16 1, [[META3]], !DIExpression(), [[META5]])
; VECTOR_DBG-NEXT:      #dbg_value(i16 1, [[META3]], !DIExpression(), [[META5]])
; VECTOR_DBG-NEXT:      #dbg_value(i16 1, [[META3]], !DIExpression(), [[META5]])
; VECTOR_DBG-NEXT:      #dbg_value(i16 1, [[META3]], !DIExpression(), [[META5]])
; VECTOR_DBG-NEXT:      #dbg_value(i16 1, [[META3]], !DIExpression(), [[META5]])
; VECTOR_DBG-NEXT:      #dbg_value(i16 1, [[META3]], !DIExpression(), [[META5]])
; VECTOR_DBG-NEXT:    [[B1:%.*]] = getelementptr inbounds float, ptr [[B:%.*]], i64 1
; VECTOR_DBG-NEXT:    [[B2:%.*]] = getelementptr inbounds float, ptr [[B]], i64 2
; VECTOR_DBG-NEXT:    [[TMP0:%.*]] = load <2 x float>, ptr [[A2]], align 4
; VECTOR_DBG-NEXT:    call void @unknown()
; VECTOR_DBG-NEXT:    call void @unknown()
; VECTOR_DBG-NEXT:    call void @unknown()
; VECTOR_DBG-NEXT:    call void @unknown()
; VECTOR_DBG-NEXT:    call void @unknown()
; VECTOR_DBG-NEXT:    call void @unknown()
; VECTOR_DBG-NEXT:    call void @unknown()
; VECTOR_DBG-NEXT:    call void @unknown()
; VECTOR_DBG-NEXT:    call void @unknown()
; VECTOR_DBG-NEXT:    call void @unknown()
; VECTOR_DBG-NEXT:    call void @unknown()
; VECTOR_DBG-NEXT:    call void @unknown()
; VECTOR_DBG-NEXT:    call void @unknown()
; VECTOR_DBG-NEXT:    call void @unknown()
; VECTOR_DBG-NEXT:    call void @unknown()
; VECTOR_DBG-NEXT:    call void @unknown()
; VECTOR_DBG-NEXT:    call void @unknown()
; VECTOR_DBG-NEXT:    call void @unknown()
; VECTOR_DBG-NEXT:    call void @unknown()
; VECTOR_DBG-NEXT:    call void @unknown()
; VECTOR_DBG-NEXT:    call void @unknown()
; VECTOR_DBG-NEXT:    call void @unknown()
; VECTOR_DBG-NEXT:    call void @unknown()
; VECTOR_DBG-NEXT:    call void @unknown()
; VECTOR_DBG-NEXT:    call void @unknown()
; VECTOR_DBG-NEXT:    call void @unknown()
; VECTOR_DBG-NEXT:    call void @unknown()
; VECTOR_DBG-NEXT:    call void @unknown()
; VECTOR_DBG-NEXT:    store float [[L0]], ptr [[B]], align 4
; VECTOR_DBG-NEXT:    store float [[L1]], ptr [[B1]], align 4
; VECTOR_DBG-NEXT:    store <2 x float> [[TMP0]], ptr [[B2]], align 4
; VECTOR_DBG-NEXT:    [[TMP1:%.*]] = load <4 x float>, ptr [[C:%.*]], align 4
; VECTOR_DBG-NEXT:    store <4 x float> [[TMP1]], ptr [[D:%.*]], align 4
; VECTOR_DBG-NEXT:    ret void
;
; VECTOR_NODBG-LABEL: @test(
; VECTOR_NODBG-NEXT:  entry:
; VECTOR_NODBG-NEXT:    [[L0:%.*]] = load float, ptr [[A:%.*]], align 4
; VECTOR_NODBG-NEXT:    [[A1:%.*]] = getelementptr inbounds float, ptr [[A]], i64 1
; VECTOR_NODBG-NEXT:    [[L1:%.*]] = load float, ptr [[A1]], align 4
; VECTOR_NODBG-NEXT:    [[A2:%.*]] = getelementptr inbounds float, ptr [[A]], i64 2
; VECTOR_NODBG-NEXT:    [[B1:%.*]] = getelementptr inbounds float, ptr [[B:%.*]], i64 1
; VECTOR_NODBG-NEXT:    [[B2:%.*]] = getelementptr inbounds float, ptr [[B]], i64 2
; VECTOR_NODBG-NEXT:    [[TMP0:%.*]] = load <2 x float>, ptr [[A2]], align 4
; VECTOR_NODBG-NEXT:    call void @unknown()
; VECTOR_NODBG-NEXT:    call void @unknown()
; VECTOR_NODBG-NEXT:    call void @unknown()
; VECTOR_NODBG-NEXT:    call void @unknown()
; VECTOR_NODBG-NEXT:    call void @unknown()
; VECTOR_NODBG-NEXT:    call void @unknown()
; VECTOR_NODBG-NEXT:    call void @unknown()
; VECTOR_NODBG-NEXT:    call void @unknown()
; VECTOR_NODBG-NEXT:    call void @unknown()
; VECTOR_NODBG-NEXT:    call void @unknown()
; VECTOR_NODBG-NEXT:    call void @unknown()
; VECTOR_NODBG-NEXT:    call void @unknown()
; VECTOR_NODBG-NEXT:    call void @unknown()
; VECTOR_NODBG-NEXT:    call void @unknown()
; VECTOR_NODBG-NEXT:    call void @unknown()
; VECTOR_NODBG-NEXT:    call void @unknown()
; VECTOR_NODBG-NEXT:    call void @unknown()
; VECTOR_NODBG-NEXT:    call void @unknown()
; VECTOR_NODBG-NEXT:    call void @unknown()
; VECTOR_NODBG-NEXT:    call void @unknown()
; VECTOR_NODBG-NEXT:    call void @unknown()
; VECTOR_NODBG-NEXT:    call void @unknown()
; VECTOR_NODBG-NEXT:    call void @unknown()
; VECTOR_NODBG-NEXT:    call void @unknown()
; VECTOR_NODBG-NEXT:    call void @unknown()
; VECTOR_NODBG-NEXT:    call void @unknown()
; VECTOR_NODBG-NEXT:    call void @unknown()
; VECTOR_NODBG-NEXT:    call void @unknown()
; VECTOR_NODBG-NEXT:    store float [[L0]], ptr [[B]], align 4
; VECTOR_NODBG-NEXT:    store float [[L1]], ptr [[B1]], align 4
; VECTOR_NODBG-NEXT:    store <2 x float> [[TMP0]], ptr [[B2]], align 4
; VECTOR_NODBG-NEXT:    [[TMP1:%.*]] = load <4 x float>, ptr [[C:%.*]], align 4
; VECTOR_NODBG-NEXT:    store <4 x float> [[TMP1]], ptr [[D:%.*]], align 4
; VECTOR_NODBG-NEXT:    ret void
;
entry:
  %l0 = load float, ptr %a
  %a1 = getelementptr inbounds float, ptr %a, i64 1
  %l1 = load float, ptr %a1
  %a2 = getelementptr inbounds float, ptr %a, i64 2
  %l2 = load float, ptr %a2
  %a3 = getelementptr inbounds float, ptr %a, i64 3
  %l3 = load float, ptr %a3

  ; some unrelated instructions inbetween to enlarge the scheduling region
  call void @unknown()
  call void @unknown()
  call void @unknown()
  call void @unknown()
  call void @unknown()
  call void @unknown()
  call void @unknown()
  call void @unknown()
  call void @unknown()
  call void @unknown()
  call void @unknown()
  call void @unknown()
  call void @unknown()
  call void @unknown()
  call void @unknown()
  call void @unknown()
  call void @unknown()
  call void @unknown()
  call void @unknown()
  call void @unknown()
  call void @unknown()
  call void @unknown()
  call void @unknown()
  call void @unknown()
  call void @unknown()
  call void @unknown()
  call void @unknown()
  call void @unknown()

  ; The dbg.values should not affect vectorization.
  call void @llvm.dbg.value(metadata i16 1, metadata !3, metadata !DIExpression()), !dbg !5
  call void @llvm.dbg.value(metadata i16 1, metadata !3, metadata !DIExpression()), !dbg !5
  call void @llvm.dbg.value(metadata i16 1, metadata !3, metadata !DIExpression()), !dbg !5
  call void @llvm.dbg.value(metadata i16 1, metadata !3, metadata !DIExpression()), !dbg !5

  store float %l0, ptr %b

  ; The dbg.values should not affect vectorization.
  call void @llvm.dbg.value(metadata i16 1, metadata !3, metadata !DIExpression()), !dbg !5
  call void @llvm.dbg.value(metadata i16 1, metadata !3, metadata !DIExpression()), !dbg !5
  call void @llvm.dbg.value(metadata i16 1, metadata !3, metadata !DIExpression()), !dbg !5
  call void @llvm.dbg.value(metadata i16 1, metadata !3, metadata !DIExpression()), !dbg !5
  %b1 = getelementptr inbounds float, ptr %b, i64 1
  store float %l1, ptr %b1
  %b2 = getelementptr inbounds float, ptr %b, i64 2
  store float %l2, ptr %b2
  %b3 = getelementptr inbounds float, ptr %b, i64 3
  store float %l3, ptr %b3

  %l4 = load float, ptr %c
  %c1 = getelementptr inbounds float, ptr %c, i64 1
  %l5 = load float, ptr %c1
  %c2 = getelementptr inbounds float, ptr %c, i64 2
  %l6 = load float, ptr %c2
  %c3 = getelementptr inbounds float, ptr %c, i64 3
  %l7 = load float, ptr %c3

  store float %l4, ptr %d
  %d1 = getelementptr inbounds float, ptr %d, i64 1
  store float %l5, ptr %d1
  %d2 = getelementptr inbounds float, ptr %d, i64 2
  store float %l6, ptr %d2
  %d3 = getelementptr inbounds float, ptr %d, i64 3
  store float %l7, ptr %d3

  ret void
}

declare void @llvm.dbg.value(metadata, metadata, metadata)

!llvm.dbg.cu = !{!0}
!llvm.module.flags = !{!2}

!0 = distinct !DICompileUnit(language: DW_LANG_C11, file: !1)
!1 = !DIFile(filename: "foo.c", directory: "/")
!2 = !{i32 2, !"Debug Info Version", i32 3}
!3 = !DILocalVariable(scope: !4)
!4 = distinct !DISubprogram(unit: !0)
!5 = !DILocation(scope: !4)
