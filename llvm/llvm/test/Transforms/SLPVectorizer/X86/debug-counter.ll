; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 2
; RUN: opt -S -passes=slp-vectorizer -mtriple=x86_64-unknown-linux -debug-counter=slp-vectorized=0 -slp-threshold=-99999 < %s | FileCheck %s --check-prefix=COUNT0
; RUN: opt -S -passes=slp-vectorizer -mtriple=x86_64-unknown-linux -debug-counter=slp-vectorized=1 -slp-threshold=-99999 < %s | FileCheck %s --check-prefix=COUNT1
; RUN: opt -S -passes=slp-vectorizer -mtriple=x86_64-unknown-linux -debug-counter=slp-vectorized=2 -slp-threshold=-99999 < %s | FileCheck %s --check-prefix=COUNT2
; RUN: opt -S -passes=slp-vectorizer -mtriple=x86_64-unknown-linux -debug-counter=slp-vectorized=0-1 -slp-threshold=-99999 < %s | FileCheck %s --check-prefix=COUNT-1
; REQUIRES: asserts

define void @blam(ptr %arg, double %load2, i1 %fcmp3) {
; COUNT0-LABEL: define void @blam
; COUNT0-SAME: (ptr [[ARG:%.*]], double [[LOAD2:%.*]], i1 [[FCMP3:%.*]]) {
; COUNT0-NEXT:  bb:
; COUNT0-NEXT:    [[GETELEMENTPTR13:%.*]] = getelementptr double, ptr [[ARG]], i64 3
; COUNT0-NEXT:    [[TMP0:%.*]] = load <2 x double>, ptr [[ARG]], align 8
; COUNT0-NEXT:    [[TMP1:%.*]] = insertelement <2 x i1> poison, i1 [[FCMP3]], i32 0
; COUNT0-NEXT:    [[TMP2:%.*]] = shufflevector <2 x i1> [[TMP1]], <2 x i1> poison, <2 x i32> zeroinitializer
; COUNT0-NEXT:    [[TMP3:%.*]] = select <2 x i1> [[TMP2]], <2 x double> zeroinitializer, <2 x double> [[TMP0]]
; COUNT0-NEXT:    [[TMP4:%.*]] = insertelement <2 x double> [[TMP0]], double [[LOAD2]], i32 0
; COUNT0-NEXT:    [[TMP5:%.*]] = fcmp olt <2 x double> [[TMP4]], zeroinitializer
; COUNT0-NEXT:    [[TMP6:%.*]] = select <2 x i1> [[TMP5]], <2 x double> zeroinitializer, <2 x double> [[TMP0]]
; COUNT0-NEXT:    [[TMP7:%.*]] = fcmp olt <2 x double> [[TMP3]], zeroinitializer
; COUNT0-NEXT:    [[TMP8:%.*]] = select <2 x i1> [[TMP7]], <2 x double> <double 0.000000e+00, double 1.000000e+00>, <2 x double> <double 1.000000e+00, double 0.000000e+00>
; COUNT0-NEXT:    [[TMP9:%.*]] = shufflevector <2 x double> [[TMP8]], <2 x double> poison, <2 x i32> <i32 1, i32 0>
; COUNT0-NEXT:    [[TMP10:%.*]] = fcmp olt <2 x double> [[TMP9]], [[TMP6]]
; COUNT0-NEXT:    [[TMP11:%.*]] = shufflevector <2 x double> [[TMP4]], <2 x double> <double poison, double 0.000000e+00>, <2 x i32> <i32 0, i32 3>
; COUNT0-NEXT:    [[TMP12:%.*]] = shufflevector <2 x double> [[TMP4]], <2 x double> <double 0.000000e+00, double poison>, <2 x i32> <i32 2, i32 0>
; COUNT0-NEXT:    [[TMP13:%.*]] = select <2 x i1> [[TMP10]], <2 x double> [[TMP11]], <2 x double> [[TMP12]]
; COUNT0-NEXT:    [[TMP14:%.*]] = fcmp olt <2 x double> [[TMP13]], zeroinitializer
; COUNT0-NEXT:    [[TMP15:%.*]] = select <2 x i1> [[TMP14]], <2 x double> zeroinitializer, <2 x double> splat (double 1.000000e+00)
; COUNT0-NEXT:    [[TMP16:%.*]] = fcmp ogt <2 x double> [[TMP15]], zeroinitializer
; COUNT0-NEXT:    [[TMP17:%.*]] = shufflevector <2 x double> [[TMP4]], <2 x double> poison, <2 x i32> zeroinitializer
; COUNT0-NEXT:    [[TMP18:%.*]] = select <2 x i1> [[TMP16]], <2 x double> zeroinitializer, <2 x double> [[TMP17]]
; COUNT0-NEXT:    [[TMP19:%.*]] = fcmp olt <2 x double> [[TMP18]], zeroinitializer
; COUNT0-NEXT:    [[TMP20:%.*]] = select <2 x i1> [[TMP19]], <2 x double> splat (double 1.000000e+00), <2 x double> zeroinitializer
; COUNT0-NEXT:    store <2 x double> [[TMP20]], ptr [[GETELEMENTPTR13]], align 8
; COUNT0-NEXT:    ret void
;
; COUNT1-LABEL: define void @blam
; COUNT1-SAME: (ptr [[ARG:%.*]], double [[LOAD2:%.*]], i1 [[FCMP3:%.*]]) {
; COUNT1-NEXT:  bb:
; COUNT1-NEXT:    [[GETELEMENTPTR:%.*]] = getelementptr double, ptr [[ARG]], i64 1
; COUNT1-NEXT:    [[LOAD:%.*]] = load double, ptr [[GETELEMENTPTR]], align 8
; COUNT1-NEXT:    [[FCMP:%.*]] = fcmp olt double [[LOAD]], 0.000000e+00
; COUNT1-NEXT:    [[SELECT3:%.*]] = select i1 [[FCMP]], double 0.000000e+00, double [[LOAD]]
; COUNT1-NEXT:    [[SELECT4:%.*]] = select i1 [[FCMP3]], double 0.000000e+00, double [[LOAD]]
; COUNT1-NEXT:    [[LOAD7:%.*]] = load double, ptr [[ARG]], align 8
; COUNT1-NEXT:    [[SELECT10:%.*]] = select i1 [[FCMP3]], double 0.000000e+00, double [[LOAD7]]
; COUNT1-NEXT:    [[GETELEMENTPTR13:%.*]] = getelementptr double, ptr [[ARG]], i64 3
; COUNT1-NEXT:    [[GETELEMENTPTR21:%.*]] = getelementptr double, ptr [[ARG]], i64 4
; COUNT1-NEXT:    [[FCMP23:%.*]] = fcmp olt double [[SELECT10]], 0.000000e+00
; COUNT1-NEXT:    [[SELECT24:%.*]] = select i1 [[FCMP23]], double 0.000000e+00, double 1.000000e+00
; COUNT1-NEXT:    [[TMP0:%.*]] = insertelement <2 x double> poison, double [[LOAD2]], i32 1
; COUNT1-NEXT:    [[TMP1:%.*]] = insertelement <2 x double> [[TMP0]], double [[SELECT4]], i32 0
; COUNT1-NEXT:    [[TMP2:%.*]] = fcmp olt <2 x double> [[TMP1]], zeroinitializer
; COUNT1-NEXT:    [[TMP3:%.*]] = insertelement <2 x double> <double 0.000000e+00, double poison>, double [[LOAD7]], i32 1
; COUNT1-NEXT:    [[TMP4:%.*]] = select <2 x i1> [[TMP2]], <2 x double> <double 1.000000e+00, double 0.000000e+00>, <2 x double> [[TMP3]]
; COUNT1-NEXT:    [[FCMP33:%.*]] = fcmp olt double [[SELECT24]], [[SELECT3]]
; COUNT1-NEXT:    [[SELECT34:%.*]] = select i1 [[FCMP33]], double 0.000000e+00, double [[LOAD2]]
; COUNT1-NEXT:    [[TMP5:%.*]] = extractelement <2 x double> [[TMP4]], i32 0
; COUNT1-NEXT:    [[TMP6:%.*]] = extractelement <2 x double> [[TMP4]], i32 1
; COUNT1-NEXT:    [[FCMP39:%.*]] = fcmp olt double [[TMP5]], [[TMP6]]
; COUNT1-NEXT:    [[SELECT40:%.*]] = select i1 [[FCMP39]], double [[LOAD2]], double 0.000000e+00
; COUNT1-NEXT:    [[FCMP62:%.*]] = fcmp olt double [[SELECT34]], 0.000000e+00
; COUNT1-NEXT:    [[SELECT639:%.*]] = select i1 [[FCMP62]], double 0.000000e+00, double 1.000000e+00
; COUNT1-NEXT:    [[FCMP76:%.*]] = fcmp olt double [[SELECT40]], 0.000000e+00
; COUNT1-NEXT:    [[SELECT77:%.*]] = select i1 [[FCMP76]], double 0.000000e+00, double 1.000000e+00
; COUNT1-NEXT:    [[FCMP90:%.*]] = fcmp ogt double [[SELECT639]], 0.000000e+00
; COUNT1-NEXT:    [[SELECT91:%.*]] = select i1 [[FCMP90]], double 0.000000e+00, double [[LOAD2]]
; COUNT1-NEXT:    [[FCMP92:%.*]] = fcmp ogt double [[SELECT77]], 0.000000e+00
; COUNT1-NEXT:    [[SELECT93:%.*]] = select i1 [[FCMP92]], double 0.000000e+00, double [[LOAD2]]
; COUNT1-NEXT:    [[FCMP108:%.*]] = fcmp olt double [[SELECT93]], 0.000000e+00
; COUNT1-NEXT:    [[SELECT109:%.*]] = select i1 [[FCMP108]], double 1.000000e+00, double 0.000000e+00
; COUNT1-NEXT:    [[FCMP110:%.*]] = fcmp olt double [[SELECT91]], 0.000000e+00
; COUNT1-NEXT:    [[SELECT111:%.*]] = select i1 [[FCMP110]], double 1.000000e+00, double 0.000000e+00
; COUNT1-NEXT:    store double [[SELECT111]], ptr [[GETELEMENTPTR21]], align 8
; COUNT1-NEXT:    store double [[SELECT109]], ptr [[GETELEMENTPTR13]], align 8
; COUNT1-NEXT:    ret void
;
; COUNT2-LABEL: define void @blam
; COUNT2-SAME: (ptr [[ARG:%.*]], double [[LOAD2:%.*]], i1 [[FCMP3:%.*]]) {
; COUNT2-NEXT:  bb:
; COUNT2-NEXT:    [[GETELEMENTPTR:%.*]] = getelementptr double, ptr [[ARG]], i64 1
; COUNT2-NEXT:    [[LOAD:%.*]] = load double, ptr [[GETELEMENTPTR]], align 8
; COUNT2-NEXT:    [[SELECT4:%.*]] = select i1 [[FCMP3]], double 0.000000e+00, double [[LOAD]]
; COUNT2-NEXT:    [[LOAD7:%.*]] = load double, ptr [[ARG]], align 8
; COUNT2-NEXT:    [[SELECT10:%.*]] = select i1 [[FCMP3]], double 0.000000e+00, double [[LOAD7]]
; COUNT2-NEXT:    [[FCMP11:%.*]] = fcmp olt double [[LOAD2]], 0.000000e+00
; COUNT2-NEXT:    [[SELECT128:%.*]] = select i1 [[FCMP11]], double 0.000000e+00, double [[LOAD7]]
; COUNT2-NEXT:    [[GETELEMENTPTR13:%.*]] = getelementptr double, ptr [[ARG]], i64 3
; COUNT2-NEXT:    [[GETELEMENTPTR21:%.*]] = getelementptr double, ptr [[ARG]], i64 4
; COUNT2-NEXT:    [[TMP0:%.*]] = insertelement <2 x double> poison, double [[SELECT10]], i32 0
; COUNT2-NEXT:    [[TMP1:%.*]] = insertelement <2 x double> [[TMP0]], double [[LOAD]], i32 1
; COUNT2-NEXT:    [[TMP2:%.*]] = fcmp olt <2 x double> [[TMP1]], zeroinitializer
; COUNT2-NEXT:    [[TMP3:%.*]] = shufflevector <2 x double> [[TMP1]], <2 x double> <double 1.000000e+00, double poison>, <2 x i32> <i32 2, i32 1>
; COUNT2-NEXT:    [[TMP4:%.*]] = select <2 x i1> [[TMP2]], <2 x double> zeroinitializer, <2 x double> [[TMP3]]
; COUNT2-NEXT:    [[FCMP29:%.*]] = fcmp olt double [[SELECT4]], 0.000000e+00
; COUNT2-NEXT:    [[SELECT30:%.*]] = select i1 [[FCMP29]], double 1.000000e+00, double 0.000000e+00
; COUNT2-NEXT:    [[TMP5:%.*]] = extractelement <2 x double> [[TMP4]], i32 0
; COUNT2-NEXT:    [[TMP6:%.*]] = extractelement <2 x double> [[TMP4]], i32 1
; COUNT2-NEXT:    [[FCMP33:%.*]] = fcmp olt double [[TMP5]], [[TMP6]]
; COUNT2-NEXT:    [[SELECT34:%.*]] = select i1 [[FCMP33]], double 0.000000e+00, double [[LOAD2]]
; COUNT2-NEXT:    [[FCMP39:%.*]] = fcmp olt double [[SELECT30]], [[SELECT128]]
; COUNT2-NEXT:    [[SELECT40:%.*]] = select i1 [[FCMP39]], double [[LOAD2]], double 0.000000e+00
; COUNT2-NEXT:    [[FCMP62:%.*]] = fcmp olt double [[SELECT34]], 0.000000e+00
; COUNT2-NEXT:    [[SELECT639:%.*]] = select i1 [[FCMP62]], double 0.000000e+00, double 1.000000e+00
; COUNT2-NEXT:    [[FCMP76:%.*]] = fcmp olt double [[SELECT40]], 0.000000e+00
; COUNT2-NEXT:    [[SELECT77:%.*]] = select i1 [[FCMP76]], double 0.000000e+00, double 1.000000e+00
; COUNT2-NEXT:    [[FCMP90:%.*]] = fcmp ogt double [[SELECT639]], 0.000000e+00
; COUNT2-NEXT:    [[SELECT91:%.*]] = select i1 [[FCMP90]], double 0.000000e+00, double [[LOAD2]]
; COUNT2-NEXT:    [[FCMP92:%.*]] = fcmp ogt double [[SELECT77]], 0.000000e+00
; COUNT2-NEXT:    [[SELECT93:%.*]] = select i1 [[FCMP92]], double 0.000000e+00, double [[LOAD2]]
; COUNT2-NEXT:    [[FCMP108:%.*]] = fcmp olt double [[SELECT93]], 0.000000e+00
; COUNT2-NEXT:    [[SELECT109:%.*]] = select i1 [[FCMP108]], double 1.000000e+00, double 0.000000e+00
; COUNT2-NEXT:    [[FCMP110:%.*]] = fcmp olt double [[SELECT91]], 0.000000e+00
; COUNT2-NEXT:    [[SELECT111:%.*]] = select i1 [[FCMP110]], double 1.000000e+00, double 0.000000e+00
; COUNT2-NEXT:    store double [[SELECT111]], ptr [[GETELEMENTPTR21]], align 8
; COUNT2-NEXT:    store double [[SELECT109]], ptr [[GETELEMENTPTR13]], align 8
; COUNT2-NEXT:    ret void
;
; COUNT-1-LABEL: define void @blam
; COUNT-1-SAME: (ptr [[ARG:%.*]], double [[LOAD2:%.*]], i1 [[FCMP3:%.*]]) {
; COUNT-1-NEXT:  bb:
; COUNT-1-NEXT:    [[GETELEMENTPTR13:%.*]] = getelementptr double, ptr [[ARG]], i64 3
; COUNT-1-NEXT:    [[TMP0:%.*]] = load <2 x double>, ptr [[ARG]], align 8
; COUNT-1-NEXT:    [[TMP1:%.*]] = insertelement <2 x i1> poison, i1 [[FCMP3]], i32 0
; COUNT-1-NEXT:    [[TMP2:%.*]] = shufflevector <2 x i1> [[TMP1]], <2 x i1> poison, <2 x i32> zeroinitializer
; COUNT-1-NEXT:    [[TMP3:%.*]] = select <2 x i1> [[TMP2]], <2 x double> zeroinitializer, <2 x double> [[TMP0]]
; COUNT-1-NEXT:    [[TMP4:%.*]] = insertelement <2 x double> [[TMP0]], double [[LOAD2]], i32 0
; COUNT-1-NEXT:    [[TMP5:%.*]] = fcmp olt <2 x double> [[TMP4]], zeroinitializer
; COUNT-1-NEXT:    [[TMP6:%.*]] = select <2 x i1> [[TMP5]], <2 x double> zeroinitializer, <2 x double> [[TMP0]]
; COUNT-1-NEXT:    [[TMP7:%.*]] = fcmp olt <2 x double> [[TMP3]], zeroinitializer
; COUNT-1-NEXT:    [[TMP8:%.*]] = select <2 x i1> [[TMP7]], <2 x double> <double 0.000000e+00, double 1.000000e+00>, <2 x double> <double 1.000000e+00, double 0.000000e+00>
; COUNT-1-NEXT:    [[TMP9:%.*]] = shufflevector <2 x double> [[TMP8]], <2 x double> poison, <2 x i32> <i32 1, i32 0>
; COUNT-1-NEXT:    [[TMP10:%.*]] = fcmp olt <2 x double> [[TMP9]], [[TMP6]]
; COUNT-1-NEXT:    [[TMP11:%.*]] = shufflevector <2 x double> [[TMP4]], <2 x double> <double poison, double 0.000000e+00>, <2 x i32> <i32 0, i32 3>
; COUNT-1-NEXT:    [[TMP12:%.*]] = shufflevector <2 x double> [[TMP4]], <2 x double> <double 0.000000e+00, double poison>, <2 x i32> <i32 2, i32 0>
; COUNT-1-NEXT:    [[TMP13:%.*]] = select <2 x i1> [[TMP10]], <2 x double> [[TMP11]], <2 x double> [[TMP12]]
; COUNT-1-NEXT:    [[TMP14:%.*]] = fcmp olt <2 x double> [[TMP13]], zeroinitializer
; COUNT-1-NEXT:    [[TMP15:%.*]] = select <2 x i1> [[TMP14]], <2 x double> zeroinitializer, <2 x double> splat (double 1.000000e+00)
; COUNT-1-NEXT:    [[TMP16:%.*]] = fcmp ogt <2 x double> [[TMP15]], zeroinitializer
; COUNT-1-NEXT:    [[TMP17:%.*]] = shufflevector <2 x double> [[TMP4]], <2 x double> poison, <2 x i32> zeroinitializer
; COUNT-1-NEXT:    [[TMP18:%.*]] = select <2 x i1> [[TMP16]], <2 x double> zeroinitializer, <2 x double> [[TMP17]]
; COUNT-1-NEXT:    [[TMP19:%.*]] = fcmp olt <2 x double> [[TMP18]], zeroinitializer
; COUNT-1-NEXT:    [[TMP20:%.*]] = select <2 x i1> [[TMP19]], <2 x double> splat (double 1.000000e+00), <2 x double> zeroinitializer
; COUNT-1-NEXT:    store <2 x double> [[TMP20]], ptr [[GETELEMENTPTR13]], align 8
; COUNT-1-NEXT:    ret void
;
bb:
  %getelementptr = getelementptr double, ptr %arg, i64 1
  %load = load double, ptr %getelementptr, align 8
  %fcmp = fcmp olt double %load, 0.000000e+00
  %select3 = select i1 %fcmp, double 0.000000e+00, double %load
  %select4 = select i1 %fcmp3, double 0.000000e+00, double %load
  %load7 = load double, ptr %arg, align 8
  %select10 = select i1 %fcmp3, double 0.000000e+00, double %load7
  %fcmp11 = fcmp olt double %load2, 0.000000e+00
  %select128 = select i1 %fcmp11, double 0.000000e+00, double %load7
  %getelementptr13 = getelementptr double, ptr %arg, i64 3
  %getelementptr21 = getelementptr double, ptr %arg, i64 4
  %fcmp23 = fcmp olt double %select10, 0.000000e+00
  %select24 = select i1 %fcmp23, double 0.000000e+00, double 1.000000e+00
  %fcmp29 = fcmp olt double %select4, 0.000000e+00
  %select30 = select i1 %fcmp29, double 1.000000e+00, double 0.000000e+00
  %fcmp33 = fcmp olt double %select24, %select3
  %select34 = select i1 %fcmp33, double 0.000000e+00, double %load2
  %fcmp39 = fcmp olt double %select30, %select128
  %select40 = select i1 %fcmp39, double %load2, double 0.000000e+00
  %fcmp62 = fcmp olt double %select34, 0.000000e+00
  %select639 = select i1 %fcmp62, double 0.000000e+00, double 1.000000e+00
  %fcmp76 = fcmp olt double %select40, 0.000000e+00
  %select77 = select i1 %fcmp76, double 0.000000e+00, double 1.000000e+00
  %fcmp90 = fcmp ogt double %select639, 0.000000e+00
  %select91 = select i1 %fcmp90, double 0.000000e+00, double %load2
  %fcmp92 = fcmp ogt double %select77, 0.000000e+00
  %select93 = select i1 %fcmp92, double 0.000000e+00, double %load2
  %fcmp108 = fcmp olt double %select93, 0.000000e+00
  %select109 = select i1 %fcmp108, double 1.000000e+00, double 0.000000e+00
  %fcmp110 = fcmp olt double %select91, 0.000000e+00
  %select111 = select i1 %fcmp110, double 1.000000e+00, double 0.000000e+00
  store double %select111, ptr %getelementptr21, align 8
  store double %select109, ptr %getelementptr13, align 8
  ret void
}
