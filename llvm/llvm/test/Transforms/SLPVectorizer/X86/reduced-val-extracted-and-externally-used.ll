; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S --passes=slp-vectorizer -mtriple=x86_64-unknown-linux-gnu < %s | FileCheck %s

define void @test(i32 %arg) {
; CHECK-LABEL: define void @test(
; CHECK-SAME: i32 [[ARG:%.*]]) {
; CHECK-NEXT:  [[BB:.*]]:
; CHECK-NEXT:    [[TMP0:%.*]] = insertelement <2 x i32> <i32 poison, i32 0>, i32 [[ARG]], i32 0
; CHECK-NEXT:    br label %[[BB1:.*]]
; CHECK:       [[BB1]]:
; CHECK-NEXT:    [[PHI2:%.*]] = phi i32 [ 0, %[[BB]] ], [ [[TMP5:%.*]], %[[BB1]] ]
; CHECK-NEXT:    [[PHI:%.*]] = phi i32 [ 0, %[[BB]] ], [ [[TMP6:%.*]], %[[BB1]] ]
; CHECK-NEXT:    [[PHI3:%.*]] = phi i32 [ 0, %[[BB]] ], [ [[OP_RDX4:%.*]], %[[BB1]] ]
; CHECK-NEXT:    [[TMP1:%.*]] = phi <2 x i32> [ zeroinitializer, %[[BB]] ], [ [[TMP4:%.*]], %[[BB1]] ]
; CHECK-NEXT:    [[TMP2:%.*]] = shufflevector <2 x i32> [[TMP1]], <2 x i32> poison, <8 x i32> <i32 0, i32 0, i32 1, i32 0, i32 0, i32 1, i32 0, i32 0>
; CHECK-NEXT:    [[ADD17:%.*]] = add i32 [[PHI]], 0
; CHECK-NEXT:    [[ADD4:%.*]] = add i32 [[PHI]], 0
; CHECK-NEXT:    [[ADD19:%.*]] = add i32 [[PHI2]], 0
; CHECK-NEXT:    [[ADD6:%.*]] = add i32 [[PHI]], 0
; CHECK-NEXT:    [[TMP3:%.*]] = add <8 x i32> [[TMP2]], zeroinitializer
; CHECK-NEXT:    [[TMP4]] = add <2 x i32> [[TMP0]], <i32 0, i32 1>
; CHECK-NEXT:    [[TMP5]] = extractelement <2 x i32> [[TMP4]], i32 1
; CHECK-NEXT:    [[TMP6]] = extractelement <2 x i32> [[TMP4]], i32 0
; CHECK-NEXT:    [[TMP7:%.*]] = call i32 @llvm.vector.reduce.xor.v8i32(<8 x i32> [[TMP3]])
; CHECK-NEXT:    [[OP_RDX:%.*]] = xor i32 [[TMP7]], [[ADD17]]
; CHECK-NEXT:    [[OP_RDX1:%.*]] = xor i32 [[ADD4]], [[ADD6]]
; CHECK-NEXT:    [[OP_RDX2:%.*]] = xor i32 [[ADD19]], [[TMP6]]
; CHECK-NEXT:    [[OP_RDX3:%.*]] = xor i32 [[OP_RDX]], [[OP_RDX1]]
; CHECK-NEXT:    [[OP_RDX4]] = xor i32 [[OP_RDX3]], [[OP_RDX2]]
; CHECK-NEXT:    [[ICMP:%.*]] = icmp ult i32 [[TMP5]], 0
; CHECK-NEXT:    br label %[[BB1]]
;
bb:
  br label %bb1

bb1:
  %phi = phi i32 [ 0, %bb ], [ %add27, %bb1 ]
  %phi2 = phi i32 [ 0, %bb ], [ %add24, %bb1 ]
  %phi3 = phi i32 [ 0, %bb ], [ %xor26, %bb1 ]
  %add = add i32 %phi2, 0
  %add4 = add i32 %phi2, 0
  %xor = xor i32 %add, %add4
  %add5 = add i32 %phi, 0
  %add6 = add i32 %phi2, 0
  %add7 = add i32 %phi2, 0
  %xor8 = xor i32 %add6, %xor
  %xor9 = xor i32 %xor8, %add5
  %xor10 = xor i32 %xor9, %add7
  %add11 = add i32 %phi, 0
  %add12 = add i32 %phi2, 0
  %add13 = add i32 %phi2, 0
  %xor14 = xor i32 %add12, %xor10
  %xor15 = xor i32 %xor14, %add11
  %xor16 = xor i32 %xor15, %add13
  %add17 = add i32 %phi, 0
  %add18 = add i32 %phi2, 0
  %add19 = add i32 %phi2, 0
  %xor20 = xor i32 %add18, %xor16
  %xor21 = xor i32 %xor20, %add17
  %xor22 = xor i32 %xor21, %add19
  %add23 = add i32 %phi2, 0
  %add24 = add i32 %arg, 0
  %xor25 = xor i32 %add23, %xor22
  %xor26 = xor i32 %xor25, %add24
  %add27 = add i32 1, 0
  %icmp = icmp ult i32 %add27, 0
  br label %bb1
}
