; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -S -passes=slp-vectorizer -mtriple=x86_64-unknown-linux -mattr="+avx512f,+avx512bw" -slp-threshold=-100 -slp-min-tree-size=0 < %s | FileCheck %s

define i32 @foo(i32 %a) {
; CHECK-LABEL: @foo(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP0:%.*]] = sub nsw i32 0, [[A:%.*]]
; CHECK-NEXT:    [[LOCAL:%.*]] = sub nsw i32 0, 0
; CHECK-NEXT:    br i1 false, label [[BB5:%.*]], label [[BB1:%.*]]
; CHECK:       bb1:
; CHECK-NEXT:    [[TMP1:%.*]] = mul i32 [[LOCAL]], 3
; CHECK-NEXT:    [[OP_RDX2:%.*]] = add i32 [[TMP1]], [[TMP0]]
; CHECK-NEXT:    [[OP_RDX3:%.*]] = add i32 [[OP_RDX2]], 0
; CHECK-NEXT:    br label [[BB3:%.*]]
; CHECK:       bb2:
; CHECK-NEXT:    br label [[BB3]]
; CHECK:       bb3:
; CHECK-NEXT:    [[P1:%.*]] = phi i32 [ [[OP_RDX3]], [[BB1]] ], [ 0, [[BB2:%.*]] ]
; CHECK-NEXT:    ret i32 0
; CHECK:       bb4:
; CHECK-NEXT:    [[TMP2:%.*]] = mul i32 [[LOCAL]], 8
; CHECK-NEXT:    [[OP_RDX:%.*]] = add i32 [[TMP2]], [[TMP0]]
; CHECK-NEXT:    [[OP_RDX1:%.*]] = add i32 [[OP_RDX]], 0
; CHECK-NEXT:    ret i32 [[OP_RDX1]]
; CHECK:       bb5:
; CHECK-NEXT:    br label [[BB4:%.*]]
;
entry:
  %0 = sub nsw i32 0, %a
  %local = sub nsw i32 0, 0
  br i1 false, label %bb5, label %bb1

bb1:
  %1 = add i32 %0, %local
  %2 = add i32 %1, 0
  %3 = add i32 %2, %local
  %4 = add i32 %3, 0
  %5 = add i32 %4, %local
  br label %bb3

bb2:
  br label %bb3

bb3:
  %p1 = phi i32 [ %5, %bb1 ], [ 0, %bb2 ]
  ret i32 0

bb4:
  %6 = add i32 %0, %local
  %7 = add i32 %6, %local
  %8 = add i32 %7, 0
  %9 = add i32 %8, %local
  %10 = add i32 %9, 0
  %11 = add i32 %10, %local
  %12 = add i32 %11, 0
  %13 = add i32 %12, %local
  %14 = add i32 %13, 0
  %15 = add i32 %14, %local
  %16 = add i32 %15, 0
  %17 = add i32 %16, %local
  %18 = add i32 %17, 0
  %19 = add i32 %18, %local
  ret i32 %19

bb5:
  br label %bb4
}
