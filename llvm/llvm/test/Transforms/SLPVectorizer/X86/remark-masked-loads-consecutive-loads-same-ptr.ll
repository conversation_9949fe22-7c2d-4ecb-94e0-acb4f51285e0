; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt < %s -passes=slp-vectorizer -S -mtriple=x86_64-unknown-linux-gnu -mcpu=skylake-avx512 -pass-remarks-output=%t | FileCheck %s
; RUN: FileCheck --input-file=%t --check-prefix=YAML %s

; YAML-LABEL: --- !Passed
; YAML-NEXT:  Pass:            slp-vectorizer
; YAML-NEXT:  Name:            StoresVectorized
; YAML-NEXT:  Function:        test
; YAML-NEXT:  Args:
; YAML-NEXT:    - String:          'Stores SLP vectorized with cost '
; YAML-NEXT:    - Cost:            '-7'
; YAML-NEXT:    - String:          ' and with tree size '
; YAML-NEXT:    - TreeSize:        '5'

define void @test(ptr noalias %p, ptr noalias %p1) {
; CHECK-LABEL: @test(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[I:%.*]] = load i32, ptr [[P:%.*]], align 4
; CHECK-NEXT:    [[ARRAYIDX4:%.*]] = getelementptr i32, ptr [[P]], i64 32
; CHECK-NEXT:    [[I2:%.*]] = load i32, ptr [[ARRAYIDX4]], align 4
; CHECK-NEXT:    [[ARRAYIDX11:%.*]] = getelementptr i32, ptr [[P]], i64 33
; CHECK-NEXT:    [[TMP0:%.*]] = load <2 x i32>, ptr [[ARRAYIDX11]], align 4
; CHECK-NEXT:    [[TMP1:%.*]] = load <4 x i32>, ptr [[P]], align 4
; CHECK-NEXT:    [[TMP2:%.*]] = insertelement <4 x i32> poison, i32 [[I]], i32 0
; CHECK-NEXT:    [[TMP3:%.*]] = insertelement <4 x i32> [[TMP2]], i32 [[I2]], i32 1
; CHECK-NEXT:    [[TMP4:%.*]] = call <4 x i32> @llvm.vector.insert.v4i32.v2i32(<4 x i32> [[TMP3]], <2 x i32> [[TMP0]], i64 2)
; CHECK-NEXT:    [[TMP5:%.*]] = add nsw <4 x i32> [[TMP4]], [[TMP1]]
; CHECK-NEXT:    store <4 x i32> [[TMP5]], ptr [[P1:%.*]], align 4
; CHECK-NEXT:    ret void
;
entry:
  %i = load i32, ptr %p, align 4
  %i1 = load i32, ptr %p, align 4
  %add = add nsw i32 %i, %i1
  store i32 %add, ptr %p1, align 4
  %arrayidx4 = getelementptr i32, ptr %p, i64 32
  %i2 = load i32, ptr %arrayidx4, align 4
  %arrayidx6 = getelementptr i32, ptr %p, i64 1
  %i3 = load i32, ptr %arrayidx6, align 4
  %add7 = add nsw i32 %i2, %i3
  %arrayidx9 = getelementptr i32, ptr %p1, i64 1
  store i32 %add7, ptr %arrayidx9, align 4
  %arrayidx11 = getelementptr i32, ptr %p, i64 33
  %i4 = load i32, ptr %arrayidx11, align 4
  %arrayidx13 = getelementptr i32, ptr %p, i64 2
  %i5 = load i32, ptr %arrayidx13, align 4
  %add14 = add nsw i32 %i4, %i5
  %arrayidx16 = getelementptr i32, ptr %p1, i64 2
  store i32 %add14, ptr %arrayidx16, align 4
  %arrayidx18 = getelementptr i32, ptr %p, i64 34
  %i6 = load i32, ptr %arrayidx18, align 4
  %arrayidx19 = getelementptr i32, ptr %p, i64 3
  %i7 = load i32, ptr %arrayidx19, align 4
  %add21 = add nsw i32 %i6, %i7
  %arrayidx23 = getelementptr i32, ptr %p1, i64 3
  store i32 %add21, ptr %arrayidx23, align 4
  ret void
}
