; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S --passes=slp-vectorizer -mtriple=x86_64-grtev4-linux-gnu -mattr="+aes,+avx,+cmov,+crc32,+cx16,+cx8,+fxsr,+mmx,+pclmul,+popcnt,+prfchw,+sse,+sse2,+sse3,+sse4.1,+sse4.2,+ssse3,+x87,+xsave" < %s | FileCheck %s

define void @test(i64 %v) {
; CHECK-LABEL: define void @test(
; CHECK-SAME: i64 [[V:%.*]]) #[[ATTR0:[0-9]+]] {
; CHECK-NEXT:  [[BB:.*:]]
; CHECK-NEXT:    [[TMP0:%.*]] = insertelement <2 x i64> <i64 0, i64 poison>, i64 [[V]], i32 1
; CHECK-NEXT:    [[TMP1:%.*]] = or <2 x i64> zeroinitializer, [[TMP0]]
; CHECK-NEXT:    [[TMP2:%.*]] = extractelement <2 x i64> [[TMP1]], i32 1
; CHECK-NEXT:    [[TMP3:%.*]] = icmp eq i64 0, [[TMP2]]
; CHECK-NEXT:    [[TMP4:%.*]] = icmp eq i64 0, 0
; CHECK-NEXT:    [[TMP5:%.*]] = and i1 [[TMP3]], [[TMP4]]
; CHECK-NEXT:    [[TMP6:%.*]] = icmp eq i64 0, 0
; CHECK-NEXT:    [[TMP7:%.*]] = and i1 [[TMP5]], [[TMP6]]
; CHECK-NEXT:    [[TMP8:%.*]] = icmp eq i64 0, 0
; CHECK-NEXT:    [[TMP9:%.*]] = and i1 [[TMP7]], [[TMP8]]
; CHECK-NEXT:    [[TMP10:%.*]] = and i1 [[TMP9]], false
; CHECK-NEXT:    [[TMP11:%.*]] = icmp eq i64 0, [[TMP2]]
; CHECK-NEXT:    [[TMP12:%.*]] = and i1 [[TMP10]], [[TMP11]]
; CHECK-NEXT:    [[TMP13:%.*]] = icmp eq i64 0, 0
; CHECK-NEXT:    [[TMP14:%.*]] = and i1 [[TMP12]], [[TMP13]]
; CHECK-NEXT:    [[TMP15:%.*]] = icmp eq i64 0, 0
; CHECK-NEXT:    [[TMP16:%.*]] = and i1 [[TMP14]], [[TMP15]]
; CHECK-NEXT:    [[TMP17:%.*]] = icmp eq i64 0, 0
; CHECK-NEXT:    [[TMP18:%.*]] = and i1 [[TMP16]], [[TMP17]]
; CHECK-NEXT:    [[TMP19:%.*]] = icmp ult i64 0, 0
; CHECK-NEXT:    [[TMP20:%.*]] = select i1 [[TMP19]], i1 [[TMP18]], i1 false
; CHECK-NEXT:    br i1 [[TMP20]], label %[[BB_I107_PREHEADER:.*]], label %[[BB_I27_I_PREHEADER:.*]]
; CHECK:       [[BB_I107_PREHEADER]]:
; CHECK-NEXT:    [[TMP21:%.*]] = extractelement <2 x i64> [[TMP1]], i32 0
; CHECK-NEXT:    [[DOTSROA_1278_10_EXTRACT_SHIFT83_I1622_1:%.*]] = xor i64 0, [[TMP21]]
; CHECK-NEXT:    [[TMP22:%.*]] = xor <2 x i64> zeroinitializer, [[TMP1]]
; CHECK-NEXT:    [[TMP23:%.*]] = or <2 x i64> [[TMP22]], zeroinitializer
; CHECK-NEXT:    [[TMP24:%.*]] = or <2 x i64> splat (i64 1), [[TMP23]]
; CHECK-NEXT:    [[TMP25:%.*]] = and <2 x i64> [[TMP24]], zeroinitializer
; CHECK-NEXT:    [[TMP26:%.*]] = icmp eq <2 x i64> [[TMP25]], zeroinitializer
; CHECK-NEXT:    ret void
; CHECK:       [[BB_I27_I_PREHEADER]]:
; CHECK-NEXT:    unreachable
;
bb:
  %.sroa.82529.14.insert.insert = or i64 0, 0
  %.sroa.02528.sroa.0.0.insert.insert = or i64 %v, 0
  %0 = icmp eq i64 0, %.sroa.02528.sroa.0.0.insert.insert
  %1 = icmp eq i64 0, 0
  %2 = and i1 %0, %1
  %3 = icmp eq i64 0, 0
  %4 = and i1 %2, %3
  %5 = icmp eq i64 0, 0
  %6 = and i1 %4, %5
  %7 = and i1 %6, false
  %8 = icmp eq i64 0, %.sroa.02528.sroa.0.0.insert.insert
  %9 = and i1 %7, %8
  %10 = icmp eq i64 0, 0
  %11 = and i1 %9, %10
  %12 = icmp eq i64 0, 0
  %13 = and i1 %11, %12
  %14 = icmp eq i64 0, 0
  %15 = and i1 %13, %14
  %16 = icmp ult i64 0, 0
  %17 = select i1 %16, i1 %15, i1 false
  br i1 %17, label %bb.i107.preheader, label %bb.i27.i.preheader

bb.i107.preheader: ; preds = %bb
  %.sroa.1278.10.extract.shift83.i1622.1 = xor i64 0, %.sroa.82529.14.insert.insert
  %.sroa.076.2.extract.shift80.i1619.4 = xor i64 0, %.sroa.02528.sroa.0.0.insert.insert
  %.sroa.071.2.extract.shift86.i1625.4 = or i64 %.sroa.076.2.extract.shift80.i1619.4, 0
  %.sroa.1278.10.extract.shift83.i1622.7 = xor i64 0, %.sroa.82529.14.insert.insert
  %.sroa.12.10.extract.shift89.i1634.7 = or i64 %.sroa.1278.10.extract.shift83.i1622.7, 0
  %.sroa.02756.2.extract.shift6530 = or i64 %.sroa.071.2.extract.shift86.i1625.4, 1
  %18 = and i64 %.sroa.02756.2.extract.shift6530, 0
  %19 = icmp eq i64 %18, 0
  %20 = or i64 1, %.sroa.12.10.extract.shift89.i1634.7
  %21 = and i64 %20, 0
  %22 = icmp eq i64 %21, 0
  ret void

bb.i27.i.preheader: ; preds = %bb
  unreachable
}

