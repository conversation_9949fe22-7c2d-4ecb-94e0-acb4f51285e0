; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -passes=slp-vectorizer -mcpu=skx -mtriple=x86_64-unknown-linux-gnu -S < %s | FileCheck %s

define void @main(ptr %0) {
; CHECK-LABEL: @main(
; CHECK-NEXT:    [[TMP2:%.*]] = load <2 x double>, ptr [[TMP0:%.*]], align 8
; CHECK-NEXT:    [[TMP3:%.*]] = fadd <2 x double> zeroinitializer, [[TMP2]]
; CHECK-NEXT:    [[TMP4:%.*]] = fsub <2 x double> zeroinitializer, [[TMP2]]
; CHECK-NEXT:    [[TMP5:%.*]] = shufflevector <2 x double> [[TMP3]], <2 x double> [[TMP4]], <4 x i32> <i32 0, i32 3, i32 0, i32 3>
; CHECK-NEXT:    [[TMP6:%.*]] = fmul <4 x double> [[TMP5]], zeroinitializer
; CHECK-NEXT:    [[TMP7:%.*]] = call <4 x double> @llvm.fabs.v4f64(<4 x double> [[TMP6]])
; CHECK-NEXT:    [[TMP8:%.*]] = fcmp oeq <4 x double> [[TMP7]], zeroinitializer
; CHECK-NEXT:    [[TMP9:%.*]] = call i1 @llvm.vector.reduce.or.v4i1(<4 x i1> [[TMP8]])
; CHECK-NEXT:    [[TMP10:%.*]] = select i1 [[TMP9]], double 0.000000e+00, double 0.000000e+00
; CHECK-NEXT:    store double [[TMP10]], ptr null, align 8
; CHECK-NEXT:    ret void
;
  %.unpack = load double, ptr %0, align 8
  %.elt1 = getelementptr { double, double }, ptr %0, i64 0, i32 1
  %.unpack2 = load double, ptr %.elt1, align 8
  %2 = fadd double %.unpack, 0.000000e+00
  %3 = fsub double 0.000000e+00, %.unpack2
  %4 = fmul double %2, 0.000000e+00
  %5 = call double @llvm.fabs.f64(double %4)
  %6 = fmul double %3, 0.000000e+00
  %7 = call double @llvm.fabs.f64(double %6)
  %8 = fmul double %3, 0.000000e+00
  %9 = call double @llvm.fabs.f64(double %8)
  %10 = fmul double %2, 0.000000e+00
  %11 = call double @llvm.fabs.f64(double %10)
  %12 = fcmp oeq double %5, 0.000000e+00
  %13 = fcmp oeq double %7, 0.000000e+00
  %14 = or i1 %12, %13
  %15 = fcmp oeq double %11, 0.000000e+00
  %16 = or i1 %14, %15
  %17 = fcmp oeq double %9, 0.000000e+00
  %18 = or i1 %16, %17
  %19 = select i1 %18, double 0.000000e+00, double 0.000000e+00
  store double %19, ptr null, align 8
  ret void
}

declare double @llvm.fabs.f64(double)
