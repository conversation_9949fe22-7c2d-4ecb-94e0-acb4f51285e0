; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -passes=slp-vectorizer -slp-vectorize-non-power-of-2 -mtriple=x86_64-apple-macosx -S %s | FileCheck --check-prefixes=CHECK,NON-POW2 %s
; RUN: opt -passes=slp-vectorizer -slp-vectorize-non-power-of-2=false -mtriple=x86_64-apple-macosx -S %s | FileCheck --check-prefixes=CHECK,POW2-ONLY %s

define void @v3_load_i32_mul_by_constant_store(ptr %src, ptr %dst) {
; CHECK-LABEL: @v3_load_i32_mul_by_constant_store(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[GEP_SRC_0:%.*]] = getelementptr inbounds i32, ptr [[SRC:%.*]], i32 0
; CHECK-NEXT:    [[L_SRC_0:%.*]] = load i32, ptr [[GEP_SRC_0]], align 4
; CHECK-NEXT:    [[MUL_0:%.*]] = mul nsw i32 [[L_SRC_0]], 10
; CHECK-NEXT:    [[GEP_SRC_1:%.*]] = getelementptr inbounds i32, ptr [[SRC]], i32 1
; CHECK-NEXT:    [[L_SRC_1:%.*]] = load i32, ptr [[GEP_SRC_1]], align 4
; CHECK-NEXT:    [[MUL_1:%.*]] = mul nsw i32 [[L_SRC_1]], 10
; CHECK-NEXT:    [[GEP_SRC_2:%.*]] = getelementptr inbounds i32, ptr [[SRC]], i32 2
; CHECK-NEXT:    [[L_SRC_2:%.*]] = load i32, ptr [[GEP_SRC_2]], align 4
; CHECK-NEXT:    [[MUL_2:%.*]] = mul nsw i32 [[L_SRC_2]], 10
; CHECK-NEXT:    store i32 [[MUL_0]], ptr [[DST:%.*]], align 4
; CHECK-NEXT:    [[DST_1:%.*]] = getelementptr i32, ptr [[DST]], i32 1
; CHECK-NEXT:    store i32 [[MUL_1]], ptr [[DST_1]], align 4
; CHECK-NEXT:    [[DST_2:%.*]] = getelementptr i32, ptr [[DST]], i32 2
; CHECK-NEXT:    store i32 [[MUL_2]], ptr [[DST_2]], align 4
; CHECK-NEXT:    ret void
;
entry:
  %gep.src.0 = getelementptr inbounds i32, ptr %src, i32 0
  %l.src.0 = load i32, ptr %gep.src.0, align 4
  %mul.0 = mul nsw i32 %l.src.0, 10

  %gep.src.1 = getelementptr inbounds i32, ptr %src, i32 1
  %l.src.1 = load i32, ptr %gep.src.1, align 4
  %mul.1 = mul nsw i32 %l.src.1, 10

  %gep.src.2 = getelementptr inbounds i32, ptr %src, i32 2
  %l.src.2 = load i32, ptr %gep.src.2, align 4
  %mul.2 = mul nsw i32 %l.src.2, 10

  store i32 %mul.0, ptr %dst

  %dst.1 = getelementptr i32, ptr %dst, i32 1
  store i32 %mul.1, ptr %dst.1

  %dst.2 = getelementptr i32, ptr %dst, i32 2
  store i32 %mul.2, ptr %dst.2

  ret void
}

define void @v3_load_i32_mul_store(ptr %src.1, ptr %src.2, ptr %dst) {
; CHECK-LABEL: @v3_load_i32_mul_store(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[GEP_SRC_1_0:%.*]] = getelementptr inbounds i32, ptr [[SRC_1:%.*]], i32 0
; CHECK-NEXT:    [[L_SRC_1_0:%.*]] = load i32, ptr [[GEP_SRC_1_0]], align 4
; CHECK-NEXT:    [[GEP_SRC_2_0:%.*]] = getelementptr inbounds i32, ptr [[SRC_2:%.*]], i32 0
; CHECK-NEXT:    [[L_SRC_2_0:%.*]] = load i32, ptr [[GEP_SRC_2_0]], align 4
; CHECK-NEXT:    [[MUL_0:%.*]] = mul nsw i32 [[L_SRC_1_0]], [[L_SRC_2_0]]
; CHECK-NEXT:    [[GEP_SRC_1_1:%.*]] = getelementptr inbounds i32, ptr [[SRC_1]], i32 1
; CHECK-NEXT:    [[L_SRC_1_1:%.*]] = load i32, ptr [[GEP_SRC_1_1]], align 4
; CHECK-NEXT:    [[GEP_SRC_2_1:%.*]] = getelementptr inbounds i32, ptr [[SRC_2]], i32 1
; CHECK-NEXT:    [[L_SRC_2_1:%.*]] = load i32, ptr [[GEP_SRC_2_1]], align 4
; CHECK-NEXT:    [[MUL_1:%.*]] = mul nsw i32 [[L_SRC_1_1]], [[L_SRC_2_1]]
; CHECK-NEXT:    [[GEP_SRC_1_2:%.*]] = getelementptr inbounds i32, ptr [[SRC_1]], i32 2
; CHECK-NEXT:    [[L_SRC_1_2:%.*]] = load i32, ptr [[GEP_SRC_1_2]], align 4
; CHECK-NEXT:    [[GEP_SRC_2_2:%.*]] = getelementptr inbounds i32, ptr [[SRC_2]], i32 2
; CHECK-NEXT:    [[L_SRC_2_2:%.*]] = load i32, ptr [[GEP_SRC_2_2]], align 4
; CHECK-NEXT:    [[MUL_2:%.*]] = mul nsw i32 [[L_SRC_1_2]], [[L_SRC_2_2]]
; CHECK-NEXT:    store i32 [[MUL_0]], ptr [[DST:%.*]], align 4
; CHECK-NEXT:    [[DST_1:%.*]] = getelementptr i32, ptr [[DST]], i32 1
; CHECK-NEXT:    store i32 [[MUL_1]], ptr [[DST_1]], align 4
; CHECK-NEXT:    [[DST_2:%.*]] = getelementptr i32, ptr [[DST]], i32 2
; CHECK-NEXT:    store i32 [[MUL_2]], ptr [[DST_2]], align 4
; CHECK-NEXT:    ret void
;
entry:
  %gep.src.1.0 = getelementptr inbounds i32, ptr %src.1, i32 0
  %l.src.1.0 = load i32, ptr %gep.src.1.0, align 4
  %gep.src.2.0 = getelementptr inbounds i32, ptr %src.2, i32 0
  %l.src.2.0 = load i32, ptr %gep.src.2.0, align 4
  %mul.0 = mul nsw i32 %l.src.1.0, %l.src.2.0

  %gep.src.1.1 = getelementptr inbounds i32, ptr %src.1, i32 1
  %l.src.1.1 = load i32, ptr %gep.src.1.1, align 4
  %gep.src.2.1 = getelementptr inbounds i32, ptr %src.2, i32 1
  %l.src.2.1 = load i32, ptr %gep.src.2.1, align 4
  %mul.1 = mul nsw i32 %l.src.1.1, %l.src.2.1

  %gep.src.1.2 = getelementptr inbounds i32, ptr %src.1, i32 2
  %l.src.1.2 = load i32, ptr %gep.src.1.2, align 4
  %gep.src.2.2 = getelementptr inbounds i32, ptr %src.2, i32 2
  %l.src.2.2 = load i32, ptr %gep.src.2.2, align 4
  %mul.2 = mul nsw i32 %l.src.1.2, %l.src.2.2

  store i32 %mul.0, ptr %dst

  %dst.1 = getelementptr i32, ptr %dst, i32 1
  store i32 %mul.1, ptr %dst.1

  %dst.2 = getelementptr i32, ptr %dst, i32 2
  store i32 %mul.2, ptr %dst.2

  ret void
}

define void @v3_load_i32_mul_add_const_store(ptr %src.1, ptr %src.2, ptr %dst) {
; CHECK-LABEL: @v3_load_i32_mul_add_const_store(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[GEP_SRC_1_0:%.*]] = getelementptr inbounds i32, ptr [[SRC_1:%.*]], i32 0
; CHECK-NEXT:    [[L_SRC_1_0:%.*]] = load i32, ptr [[GEP_SRC_1_0]], align 4
; CHECK-NEXT:    [[GEP_SRC_2_0:%.*]] = getelementptr inbounds i32, ptr [[SRC_2:%.*]], i32 0
; CHECK-NEXT:    [[L_SRC_2_0:%.*]] = load i32, ptr [[GEP_SRC_2_0]], align 4
; CHECK-NEXT:    [[MUL_0:%.*]] = mul nsw i32 [[L_SRC_1_0]], [[L_SRC_2_0]]
; CHECK-NEXT:    [[ADD_0:%.*]] = add i32 [[MUL_0]], 9
; CHECK-NEXT:    [[GEP_SRC_1_1:%.*]] = getelementptr inbounds i32, ptr [[SRC_1]], i32 1
; CHECK-NEXT:    [[L_SRC_1_1:%.*]] = load i32, ptr [[GEP_SRC_1_1]], align 4
; CHECK-NEXT:    [[GEP_SRC_2_1:%.*]] = getelementptr inbounds i32, ptr [[SRC_2]], i32 1
; CHECK-NEXT:    [[L_SRC_2_1:%.*]] = load i32, ptr [[GEP_SRC_2_1]], align 4
; CHECK-NEXT:    [[MUL_1:%.*]] = mul nsw i32 [[L_SRC_1_1]], [[L_SRC_2_1]]
; CHECK-NEXT:    [[ADD_1:%.*]] = add i32 [[MUL_1]], 9
; CHECK-NEXT:    [[GEP_SRC_1_2:%.*]] = getelementptr inbounds i32, ptr [[SRC_1]], i32 2
; CHECK-NEXT:    [[L_SRC_1_2:%.*]] = load i32, ptr [[GEP_SRC_1_2]], align 4
; CHECK-NEXT:    [[GEP_SRC_2_2:%.*]] = getelementptr inbounds i32, ptr [[SRC_2]], i32 2
; CHECK-NEXT:    [[L_SRC_2_2:%.*]] = load i32, ptr [[GEP_SRC_2_2]], align 4
; CHECK-NEXT:    [[MUL_2:%.*]] = mul nsw i32 [[L_SRC_1_2]], [[L_SRC_2_2]]
; CHECK-NEXT:    [[ADD_2:%.*]] = add i32 [[MUL_2]], 9
; CHECK-NEXT:    store i32 [[ADD_0]], ptr [[DST:%.*]], align 4
; CHECK-NEXT:    [[DST_1:%.*]] = getelementptr i32, ptr [[DST]], i32 1
; CHECK-NEXT:    store i32 [[ADD_1]], ptr [[DST_1]], align 4
; CHECK-NEXT:    [[DST_2:%.*]] = getelementptr i32, ptr [[DST]], i32 2
; CHECK-NEXT:    store i32 [[ADD_2]], ptr [[DST_2]], align 4
; CHECK-NEXT:    ret void
;
entry:
  %gep.src.1.0 = getelementptr inbounds i32, ptr %src.1, i32 0
  %l.src.1.0 = load i32, ptr %gep.src.1.0, align 4
  %gep.src.2.0 = getelementptr inbounds i32, ptr %src.2, i32 0
  %l.src.2.0 = load i32, ptr %gep.src.2.0, align 4
  %mul.0 = mul nsw i32 %l.src.1.0, %l.src.2.0
  %add.0 = add i32 %mul.0, 9

  %gep.src.1.1 = getelementptr inbounds i32, ptr %src.1, i32 1
  %l.src.1.1 = load i32, ptr %gep.src.1.1, align 4
  %gep.src.2.1 = getelementptr inbounds i32, ptr %src.2, i32 1
  %l.src.2.1 = load i32, ptr %gep.src.2.1, align 4
  %mul.1 = mul nsw i32 %l.src.1.1, %l.src.2.1
  %add.1 = add i32 %mul.1, 9

  %gep.src.1.2 = getelementptr inbounds i32, ptr %src.1, i32 2
  %l.src.1.2 = load i32, ptr %gep.src.1.2, align 4
  %gep.src.2.2 = getelementptr inbounds i32, ptr %src.2, i32 2
  %l.src.2.2 = load i32, ptr %gep.src.2.2, align 4
  %mul.2 = mul nsw i32 %l.src.1.2, %l.src.2.2
  %add.2 = add i32 %mul.2, 9

  store i32 %add.0, ptr %dst

  %dst.1 = getelementptr i32, ptr %dst, i32 1
  store i32 %add.1, ptr %dst.1

  %dst.2 = getelementptr i32, ptr %dst, i32 2
  store i32 %add.2, ptr %dst.2

  ret void
}

define void @v3_load_f32_fadd_fadd_by_constant_store(ptr %src, ptr %dst) {
; NON-POW2-LABEL: @v3_load_f32_fadd_fadd_by_constant_store(
; NON-POW2-NEXT:  entry:
; NON-POW2-NEXT:    [[GEP_SRC_0:%.*]] = getelementptr inbounds float, ptr [[SRC:%.*]], i32 0
; NON-POW2-NEXT:    [[TMP0:%.*]] = load <3 x float>, ptr [[GEP_SRC_0]], align 4
; NON-POW2-NEXT:    [[TMP1:%.*]] = fadd <3 x float> [[TMP0]], splat (float 1.000000e+01)
; NON-POW2-NEXT:    store <3 x float> [[TMP1]], ptr [[DST:%.*]], align 4
; NON-POW2-NEXT:    ret void
;
; POW2-ONLY-LABEL: @v3_load_f32_fadd_fadd_by_constant_store(
; POW2-ONLY-NEXT:  entry:
; POW2-ONLY-NEXT:    [[GEP_SRC_0:%.*]] = getelementptr inbounds float, ptr [[SRC:%.*]], i32 0
; POW2-ONLY-NEXT:    [[GEP_SRC_2:%.*]] = getelementptr inbounds float, ptr [[SRC]], i32 2
; POW2-ONLY-NEXT:    [[L_SRC_2:%.*]] = load float, ptr [[GEP_SRC_2]], align 4
; POW2-ONLY-NEXT:    [[FADD_2:%.*]] = fadd float [[L_SRC_2]], 1.000000e+01
; POW2-ONLY-NEXT:    [[TMP0:%.*]] = load <2 x float>, ptr [[GEP_SRC_0]], align 4
; POW2-ONLY-NEXT:    [[TMP1:%.*]] = fadd <2 x float> [[TMP0]], splat (float 1.000000e+01)
; POW2-ONLY-NEXT:    store <2 x float> [[TMP1]], ptr [[DST:%.*]], align 4
; POW2-ONLY-NEXT:    [[DST_2:%.*]] = getelementptr float, ptr [[DST]], i32 2
; POW2-ONLY-NEXT:    store float [[FADD_2]], ptr [[DST_2]], align 4
; POW2-ONLY-NEXT:    ret void
;
entry:
  %gep.src.0 = getelementptr inbounds float, ptr %src, i32 0
  %l.src.0 = load float , ptr %gep.src.0, align 4
  %fadd.0 = fadd float %l.src.0, 10.0

  %gep.src.1 = getelementptr inbounds float , ptr %src, i32 1
  %l.src.1 = load float, ptr %gep.src.1, align 4
  %fadd.1 = fadd float %l.src.1, 10.0

  %gep.src.2 = getelementptr inbounds float, ptr %src, i32 2
  %l.src.2 = load float, ptr %gep.src.2, align 4
  %fadd.2 = fadd float %l.src.2, 10.0

  store float %fadd.0, ptr %dst

  %dst.1 = getelementptr float, ptr %dst, i32 1
  store float %fadd.1, ptr %dst.1

  %dst.2 = getelementptr float, ptr %dst, i32 2
  store float %fadd.2, ptr %dst.2

  ret void
}

define void @phi_store3(ptr %dst) {
; CHECK-LABEL: @phi_store3(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    br label [[EXIT:%.*]]
; CHECK:       invoke.cont8.loopexit:
; CHECK-NEXT:    br label [[EXIT]]
; CHECK:       exit:
; CHECK-NEXT:    [[P_2:%.*]] = phi i32 [ 3, [[ENTRY:%.*]] ], [ 0, [[INVOKE_CONT8_LOOPEXIT:%.*]] ]
; CHECK-NEXT:    [[TMP0:%.*]] = phi <2 x i32> [ <i32 1, i32 2>, [[ENTRY]] ], [ poison, [[INVOKE_CONT8_LOOPEXIT]] ]
; CHECK-NEXT:    [[DST_2:%.*]] = getelementptr i32, ptr [[DST:%.*]], i32 2
; CHECK-NEXT:    store <2 x i32> [[TMP0]], ptr [[DST]], align 4
; CHECK-NEXT:    store i32 [[P_2]], ptr [[DST_2]], align 4
; CHECK-NEXT:    ret void
;
entry:
  br label %exit

invoke.cont8.loopexit:                            ; No predecessors!
  br label %exit

exit:
  %p.0 = phi i32 [ 1, %entry ], [ 0, %invoke.cont8.loopexit ]
  %p.1 = phi i32 [ 2, %entry ], [ 0, %invoke.cont8.loopexit ]
  %p.2 = phi i32 [ 3, %entry ], [ 0, %invoke.cont8.loopexit ]

  %dst.1 = getelementptr i32, ptr %dst, i32 1
  %dst.2 = getelementptr i32, ptr %dst, i32 2

  store i32 %p.0, ptr %dst, align 4
  store i32 %p.1, ptr %dst.1, align 4
  store i32 %p.2, ptr %dst.2, align 4
  ret void
}

define void @store_try_reorder(ptr %dst) {
; CHECK-LABEL: @store_try_reorder(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[ADD:%.*]] = add i32 0, 0
; CHECK-NEXT:    store i32 [[ADD]], ptr [[DST:%.*]], align 4
; CHECK-NEXT:    [[ARRAYIDX_I1887:%.*]] = getelementptr i32, ptr [[DST]], i64 1
; CHECK-NEXT:    store <2 x i32> zeroinitializer, ptr [[ARRAYIDX_I1887]], align 4
; CHECK-NEXT:    ret void
;
entry:
  %add = add i32 0, 0
  store i32 %add, ptr %dst, align 4
  %add207 = sub i32 0, 0
  %arrayidx.i1887 = getelementptr i32, ptr %dst, i64 1
  store i32 %add207, ptr %arrayidx.i1887, align 4
  %add216 = sub i32 0, 0
  %arrayidx.i1891 = getelementptr i32, ptr %dst, i64 2
  store i32 %add216, ptr %arrayidx.i1891, align 4
  ret void
}

define void @vec3_fpext_cost(ptr %Colour, float %0) {
; CHECK-LABEL: @vec3_fpext_cost(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[ARRAYIDX80:%.*]] = getelementptr float, ptr [[COLOUR:%.*]], i64 2
; CHECK-NEXT:    [[TMP1:%.*]] = insertelement <2 x float> poison, float [[TMP0:%.*]], i32 0
; CHECK-NEXT:    [[TMP2:%.*]] = shufflevector <2 x float> [[TMP1]], <2 x float> poison, <2 x i32> zeroinitializer
; CHECK-NEXT:    [[TMP3:%.*]] = fpext <2 x float> [[TMP2]] to <2 x double>
; CHECK-NEXT:    [[TMP4:%.*]] = call <2 x double> @llvm.fmuladd.v2f64(<2 x double> [[TMP3]], <2 x double> zeroinitializer, <2 x double> zeroinitializer)
; CHECK-NEXT:    [[TMP5:%.*]] = fptrunc <2 x double> [[TMP4]] to <2 x float>
; CHECK-NEXT:    store <2 x float> [[TMP5]], ptr [[COLOUR]], align 4
; CHECK-NEXT:    [[CONV78:%.*]] = fpext float [[TMP0]] to double
; CHECK-NEXT:    [[TMP6:%.*]] = call double @llvm.fmuladd.f64(double [[CONV78]], double 0.000000e+00, double 0.000000e+00)
; CHECK-NEXT:    [[CONV82:%.*]] = fptrunc double [[TMP6]] to float
; CHECK-NEXT:    store float [[CONV82]], ptr [[ARRAYIDX80]], align 4
; CHECK-NEXT:    ret void
;
entry:
  %arrayidx72 = getelementptr float, ptr %Colour, i64 1
  %arrayidx80 = getelementptr float, ptr %Colour, i64 2
  %conv62 = fpext float %0 to double
  %1 = call double @llvm.fmuladd.f64(double %conv62, double 0.000000e+00, double 0.000000e+00)
  %conv66 = fptrunc double %1 to float
  store float %conv66, ptr %Colour, align 4
  %conv70 = fpext float %0 to double
  %2 = call double @llvm.fmuladd.f64(double %conv70, double 0.000000e+00, double 0.000000e+00)
  %conv74 = fptrunc double %2 to float
  store float %conv74, ptr %arrayidx72, align 4
  %conv78 = fpext float %0 to double
  %3 = call double @llvm.fmuladd.f64(double %conv78, double 0.000000e+00, double 0.000000e+00)
  %conv82 = fptrunc double %3 to float
  store float %conv82, ptr %arrayidx80, align 4
  ret void
}

define void @fpext_gather(ptr %dst, double %conv) {
; CHECK-LABEL: @fpext_gather(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP0:%.*]] = insertelement <2 x double> poison, double [[CONV:%.*]], i32 0
; CHECK-NEXT:    [[TMP1:%.*]] = shufflevector <2 x double> [[TMP0]], <2 x double> poison, <2 x i32> zeroinitializer
; CHECK-NEXT:    [[TMP2:%.*]] = fptrunc <2 x double> [[TMP1]] to <2 x float>
; CHECK-NEXT:    [[LENGTHS:%.*]] = getelementptr float, ptr [[DST:%.*]], i64 0
; CHECK-NEXT:    [[TMP3:%.*]] = extractelement <2 x float> [[TMP2]], i32 0
; CHECK-NEXT:    store float [[TMP3]], ptr [[LENGTHS]], align 4
; CHECK-NEXT:    [[ARRAYIDX32:%.*]] = getelementptr float, ptr [[DST]], i64 1
; CHECK-NEXT:    store <2 x float> [[TMP2]], ptr [[ARRAYIDX32]], align 4
; CHECK-NEXT:    ret void
;
entry:
  %conv25 = fptrunc double %conv to float
  %Lengths = getelementptr float, ptr %dst, i64 0
  store float %conv25, ptr %Lengths, align 4
  %arrayidx32 = getelementptr float, ptr %dst, i64 1
  store float %conv25, ptr %arrayidx32, align 4
  %conv34 = fptrunc double %conv to float
  %arrayidx37 = getelementptr float, ptr %dst, i64 2
  store float %conv34, ptr %arrayidx37, align 4
  ret void
}

declare float @llvm.fmuladd.f32(float, float, float)

declare double @llvm.fmuladd.f64(double, double, double)
