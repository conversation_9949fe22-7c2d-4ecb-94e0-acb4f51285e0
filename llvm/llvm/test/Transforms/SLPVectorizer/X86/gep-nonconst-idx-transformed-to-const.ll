; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 4
; RUN: opt -passes=slp-vectorizer -mtriple=x86_64-unknown-linux-gnu -S < %s | FileCheck %s

define void @foo(i64 %0) {
; CHECK-LABEL: define void @foo(
; CHECK-SAME: i64 [[TMP0:%.*]]) {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP1:%.*]] = insertelement <8 x i64> zeroinitializer, i64 0, i64 0
; CHECK-NEXT:    [[TMP2:%.*]] = getelementptr i32, ptr addrspace(1) null, i64 0
; CHECK-NEXT:    [[TMP3:%.*]] = getelementptr i32, ptr addrspace(1) null, i64 [[TMP0]]
; CHECK-NEXT:    ret void
;
entry:
  %1 = or i64 0, 0
  %2 = insertelement <8 x i64> zeroinitializer, i64 %1, i64 0
  %3 = getelementptr i32, ptr addrspace(1) null, i64 %1
  %4 = getelementptr i32, ptr addrspace(1) null, i64 %0
  ret void
}
