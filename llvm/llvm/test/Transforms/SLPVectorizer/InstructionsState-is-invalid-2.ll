; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -passes=slp-vectorizer -S %s | FileCheck %s

define i32 @test(i32 %minlib) {
; CHECK-LABEL: @test(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[MUL2_I306:%.*]] = mul i32 [[MINLIB:%.*]], [[MINLIB]]
; CHECK-NEXT:    [[MUL3_I307:%.*]] = mul i32 [[MUL2_I306]], [[MINLIB]]
; CHECK-NEXT:    [[CMP183:%.*]] = icmp sgt i32 [[MUL3_I307]], 0
; CHECK-NEXT:    ret i32 0
;
entry:
  %mul2.i306 = mul i32 %minlib, %minlib
  %mul3.i307 = mul i32 %mul2.i306, %minlib
  %cmp183 = icmp sgt i32 %mul3.i307, 0
  ret i32 0
}
