; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S --passes=slp-vectorizer < %s | FileCheck %s

define i1 @test(<4 x i32> %x) {
; CHECK-LABEL: define i1 @test(
; CHECK-SAME: <4 x i32> [[X:%.*]]) {
; CHECK-NEXT:    [[X0:%.*]] = extractelement <4 x i32> [[X]], i32 0
; CHECK-NEXT:    [[X1:%.*]] = extractelement <4 x i32> [[X]], i32 -1
; CHECK-NEXT:    [[X2:%.*]] = extractelement <4 x i32> [[X]], i32 2
; CHECK-NEXT:    [[X3:%.*]] = extractelement <4 x i32> [[X]], i32 3
; CHECK-NEXT:    [[TMP1:%.*]] = icmp ugt i32 [[X0]], 0
; CHECK-NEXT:    [[C1:%.*]] = icmp slt i32 [[X1]], 0
; CHECK-NEXT:    [[C2:%.*]] = icmp sgt i32 [[X2]], 0
; CHECK-NEXT:    [[C3:%.*]] = icmp slt i32 [[X3]], 0
; CHECK-NEXT:    [[TMP2:%.*]] = freeze i1 [[C3]]
; CHECK-NEXT:    [[OP_RDX:%.*]] = select i1 [[TMP2]], i1 [[C1]], i1 false
; CHECK-NEXT:    [[OP_RDX1:%.*]] = select i1 [[TMP1]], i1 [[OP_RDX]], i1 false
; CHECK-NEXT:    ret i1 [[OP_RDX1]]
;
  %x0 = extractelement <4 x i32> %x, i32 0
  %x1 = extractelement <4 x i32> %x, i32 -1
  %x2 = extractelement <4 x i32> %x, i32 2
  %x3 = extractelement <4 x i32> %x, i32 3
  %2 = icmp ugt i32 %x0, 0
  %c1 = icmp slt i32 %x1, 0
  %c2 = icmp sgt i32 %x2, 0
  %c3 = icmp slt i32 %x3, 0
  %s1 = select i1 %2, i1 %c1, i1 false
  %s2 = select i1 %s1, i1 %c3, i1 false
  %s3 = select i1 %s2, i1 %c3, i1 false
  ret i1 %s3
}

