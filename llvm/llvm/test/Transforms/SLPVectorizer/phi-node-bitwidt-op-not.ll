; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 4
; RUN: %if x86-registered-target %{ opt -S -passes=slp-vectorizer -mtriple=x86_64-unknown-linux-gnu < %s | FileCheck %s %}
; RUN: %if aarch64-registered-target %{ opt -S -passes=slp-vectorizer -mtriple=aarch64-unknown-linux-gnu < %s | FileCheck %s %}

define i32 @test(ptr %b, ptr %c, i32 %0, ptr %a, i1 %tobool3.not) {
; CHECK-LABEL: define i32 @test(
; CHECK-SAME: ptr [[B:%.*]], ptr [[C:%.*]], i32 [[TMP0:%.*]], ptr [[A:%.*]], i1 [[TOBOOL3_NOT:%.*]]) {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    br i1 [[TOBOOL3_NOT]], label [[BB1:%.*]], label [[BB2:%.*]]
; CHECK:       bb1:
; CHECK-NEXT:    [[TMP1:%.*]] = insertelement <4 x i32> poison, i32 [[TMP0]], i32 0
; CHECK-NEXT:    [[TMP2:%.*]] = shufflevector <4 x i32> [[TMP1]], <4 x i32> poison, <4 x i32> zeroinitializer
; CHECK-NEXT:    [[TMP3:%.*]] = ashr <4 x i32> [[TMP2]], splat (i32 16)
; CHECK-NEXT:    [[TMP4:%.*]] = icmp slt <4 x i32> [[TMP3]], [[TMP2]]
; CHECK-NEXT:    [[TMP5:%.*]] = zext <4 x i1> [[TMP4]] to <4 x i16>
; CHECK-NEXT:    br label [[BB3:%.*]]
; CHECK:       bb2:
; CHECK-NEXT:    [[TMP6:%.*]] = insertelement <4 x i32> poison, i32 [[TMP0]], i32 0
; CHECK-NEXT:    [[TMP7:%.*]] = shufflevector <4 x i32> [[TMP6]], <4 x i32> poison, <4 x i32> zeroinitializer
; CHECK-NEXT:    [[TMP8:%.*]] = icmp sgt <4 x i32> [[TMP7]], zeroinitializer
; CHECK-NEXT:    [[TMP9:%.*]] = zext <4 x i1> [[TMP8]] to <4 x i32>
; CHECK-NEXT:    [[TMP10:%.*]] = insertelement <4 x i1> poison, i1 [[TOBOOL3_NOT]], i32 0
; CHECK-NEXT:    [[TMP11:%.*]] = shufflevector <4 x i1> [[TMP10]], <4 x i1> poison, <4 x i32> zeroinitializer
; CHECK-NEXT:    [[TMP12:%.*]] = select <4 x i1> [[TMP11]], <4 x i32> [[TMP7]], <4 x i32> [[TMP9]]
; CHECK-NEXT:    [[TMP13:%.*]] = shl <4 x i32> [[TMP12]], splat (i32 16)
; CHECK-NEXT:    [[TMP14:%.*]] = ashr <4 x i32> [[TMP13]], splat (i32 16)
; CHECK-NEXT:    [[TMP15:%.*]] = trunc <4 x i32> [[TMP14]] to <4 x i16>
; CHECK-NEXT:    br i1 true, label [[BB3]], label [[BB2]]
; CHECK:       bb3:
; CHECK-NEXT:    [[TMP16:%.*]] = phi <4 x i16> [ [[TMP5]], [[BB1]] ], [ [[TMP15]], [[BB2]] ]
; CHECK-NEXT:    [[TMP17:%.*]] = extractelement <4 x i16> [[TMP16]], i32 0
; CHECK-NEXT:    [[TMP18:%.*]] = sext i16 [[TMP17]] to i32
; CHECK-NEXT:    store i32 [[TMP18]], ptr [[B]], align 16
; CHECK-NEXT:    [[TMP19:%.*]] = extractelement <4 x i16> [[TMP16]], i32 1
; CHECK-NEXT:    [[TMP20:%.*]] = sext i16 [[TMP19]] to i32
; CHECK-NEXT:    store i32 [[TMP20]], ptr [[A]], align 8
; CHECK-NEXT:    [[TMP21:%.*]] = extractelement <4 x i16> [[TMP16]], i32 2
; CHECK-NEXT:    [[TMP22:%.*]] = sext i16 [[TMP21]] to i32
; CHECK-NEXT:    store i32 [[TMP22]], ptr [[C]], align 16
; CHECK-NEXT:    [[TMP23:%.*]] = extractelement <4 x i16> [[TMP16]], i32 3
; CHECK-NEXT:    [[TMP24:%.*]] = sext i16 [[TMP23]] to i32
; CHECK-NEXT:    store i32 [[TMP24]], ptr [[B]], align 8
; CHECK-NEXT:    ret i32 0
;
entry:
  br i1 %tobool3.not, label %bb1, label %bb2

bb1:
  %conv1.i.us = ashr i32 %0, 16
  %cmp2.i.us = icmp slt i32 %conv1.i.us, %0
  %sext26.us = zext i1 %cmp2.i.us to i32
  %conv1.i.us.5 = ashr i32 %0, 16
  %cmp2.i.us.5 = icmp slt i32 %conv1.i.us.5, %0
  %sext26.us.5 = zext i1 %cmp2.i.us.5 to i32
  %conv1.i.us.6 = ashr i32 %0, 16
  %cmp2.i.us.6 = icmp slt i32 %conv1.i.us.6, %0
  %sext26.us.6 = zext i1 %cmp2.i.us.6 to i32
  %conv1.i.us.7 = ashr i32 %0, 16
  %cmp2.i.us.7 = icmp slt i32 %conv1.i.us.7, %0
  %sext26.us.7 = zext i1 %cmp2.i.us.7 to i32
  br label %bb3

bb2:
  %cmp2.i = icmp sgt i32 %0, 0
  %1 = zext i1 %cmp2.i to i32
  %cond.i = select i1 %tobool3.not, i32 %0, i32 %1
  %sext26 = shl i32 %cond.i, 16
  %conv13 = ashr i32 %sext26, 16
  %cmp2.i.5 = icmp sgt i32 %0, 0
  %2 = zext i1 %cmp2.i.5 to i32
  %cond.i.5 = select i1 %tobool3.not, i32 %0, i32 %2
  %sext26.5 = shl i32 %cond.i.5, 16
  %conv13.5 = ashr i32 %sext26.5, 16
  %cmp2.i.6 = icmp sgt i32 %0, 0
  %3 = zext i1 %cmp2.i.6 to i32
  %cond.i.6 = select i1 %tobool3.not, i32 %0, i32 %3
  %sext26.6 = shl i32 %cond.i.6, 16
  %conv13.6 = ashr i32 %sext26.6, 16
  %cmp2.i.7 = icmp sgt i32 %0, 0
  %4 = zext i1 %cmp2.i.7 to i32
  %cond.i.7 = select i1 %tobool3.not, i32 %0, i32 %4
  %sext26.7 = shl i32 %cond.i.7, 16
  %conv13.7 = ashr i32 %sext26.7, 16
  br i1 true, label %bb3, label %bb2

bb3:
  %conv13p = phi i32 [ %sext26.us, %bb1 ], [ %conv13, %bb2 ]
  %conv13.5p = phi i32 [ %sext26.us.5, %bb1 ], [ %conv13.5, %bb2 ]
  %conv13.6p = phi i32 [ %sext26.us.6, %bb1 ], [ %conv13.6, %bb2 ]
  %conv13.7p = phi i32 [ %sext26.us.7, %bb1 ], [ %conv13.7, %bb2 ]
  store i32 %conv13p, ptr %b, align 16
  store i32 %conv13.5p, ptr %a, align 8
  store i32 %conv13.6p, ptr %c, align 16
  store i32 %conv13.7p, ptr %b, align 8
  ret i32 0
}
