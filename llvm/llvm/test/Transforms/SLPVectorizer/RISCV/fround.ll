; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 3
; RUN: opt < %s -passes=slp-vectorizer -S -mtriple=riscv32 -mattr=+m,+v | FileCheck %s
; RUN: opt < %s -passes=slp-vectorizer -S -mtriple=riscv64 -mattr=+m,+v | FileCheck %s
; RUN: opt < %s -passes=slp-vectorizer -S -mtriple=riscv32 -mattr=+v,+zvbb | FileCheck %s
; RUN: opt < %s -passes=slp-vectorizer -S -mtriple=riscv64 -mattr=+v,+zvbb | FileCheck %s

define <4 x float> @rint_v4f32(ptr %a) {
; CHECK-LABEL: define <4 x float> @rint_v4f32(
; CHECK-SAME: ptr [[A:%.*]]) #[[ATTR0:[0-9]+]] {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP0:%.*]] = load <4 x float>, ptr [[A]], align 16
; CHECK-NEXT:    [[TMP1:%.*]] = call <4 x float> @llvm.rint.v4f32(<4 x float> [[TMP0]])
; CHECK-NEXT:    ret <4 x float> [[TMP1]]
;
entry:
  %0 = load <4 x float>, ptr %a
  %vecext = extractelement <4 x float> %0, i64 0
  %1 = call float @llvm.rint.f32(float %vecext)
  %vecins = insertelement <4 x float> undef, float %1, i64 0
  %vecext.1 = extractelement <4 x float> %0, i64 1
  %2 = call float @llvm.rint.f32(float %vecext.1)
  %vecins.1 = insertelement <4 x float> %vecins, float %2, i64 1
  %vecext.2 = extractelement <4 x float> %0, i64 2
  %3 = call float @llvm.rint.f32(float %vecext.2)
  %vecins.2 = insertelement <4 x float> %vecins.1, float %3, i64 2
  %vecext.3 = extractelement <4 x float> %0, i64 3
  %4 = call float @llvm.rint.f32(float %vecext.3)
  %vecins.3 = insertelement <4 x float> %vecins.2, float %4, i64 3
  ret <4 x float> %vecins.3
}

define <2 x i32> @lrint_v2i32f32(ptr %a) {
; CHECK-LABEL: define <2 x i32> @lrint_v2i32f32(
; CHECK-SAME: ptr [[A:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP0:%.*]] = load <2 x float>, ptr [[A]], align 8
; CHECK-NEXT:    [[TMP1:%.*]] = call <2 x i32> @llvm.lrint.v2i32.v2f32(<2 x float> [[TMP0]])
; CHECK-NEXT:    ret <2 x i32> [[TMP1]]
;
entry:
  %0 = load <2 x float>, ptr %a
  %vecext = extractelement <2 x float> %0, i32 0
  %1 = call i32 @llvm.lrint.i32.f32(float %vecext)
  %vecins = insertelement <2 x i32> undef, i32 %1, i32 0
  %vecext.1 = extractelement <2 x float> %0, i32 1
  %2 = call i32 @llvm.lrint.i32.f32(float %vecext.1)
  %vecins.1 = insertelement <2 x i32> %vecins, i32 %2, i32 1
  ret <2 x i32> %vecins.1
}

define <4 x i32> @lrint_v4i32f32(ptr %a) {
; CHECK-LABEL: define <4 x i32> @lrint_v4i32f32(
; CHECK-SAME: ptr [[A:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP0:%.*]] = load <4 x float>, ptr [[A]], align 16
; CHECK-NEXT:    [[TMP1:%.*]] = call <4 x i32> @llvm.lrint.v4i32.v4f32(<4 x float> [[TMP0]])
; CHECK-NEXT:    ret <4 x i32> [[TMP1]]
;
entry:
  %0 = load <4 x float>, ptr %a
  %vecext = extractelement <4 x float> %0, i32 0
  %1 = call i32 @llvm.lrint.i32.f32(float %vecext)
  %vecins = insertelement <4 x i32> undef, i32 %1, i32 0
  %vecext.1 = extractelement <4 x float> %0, i32 1
  %2 = call i32 @llvm.lrint.i32.f32(float %vecext.1)
  %vecins.1 = insertelement <4 x i32> %vecins, i32 %2, i32 1
  %vecext.2 = extractelement <4 x float> %0, i32 2
  %3 = call i32 @llvm.lrint.i32.f32(float %vecext.2)
  %vecins.2 = insertelement <4 x i32> %vecins.1, i32 %3, i32 2
  %vecext.3 = extractelement <4 x float> %0, i32 3
  %4 = call i32 @llvm.lrint.i32.f32(float %vecext.3)
  %vecins.3 = insertelement <4 x i32> %vecins.2, i32 %4, i32 3
  ret <4 x i32> %vecins.3
}

define <8 x i32> @lrint_v8i32f32(ptr %a) {
; CHECK-LABEL: define <8 x i32> @lrint_v8i32f32(
; CHECK-SAME: ptr [[A:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP0:%.*]] = load <8 x float>, ptr [[A]], align 32
; CHECK-NEXT:    [[TMP1:%.*]] = call <8 x i32> @llvm.lrint.v8i32.v8f32(<8 x float> [[TMP0]])
; CHECK-NEXT:    ret <8 x i32> [[TMP1]]
;
entry:
  %0 = load <8 x float>, ptr %a
  %vecext = extractelement <8 x float> %0, i32 0
  %1 = call i32 @llvm.lrint.i32.f32(float %vecext)
  %vecins = insertelement <8 x i32> undef, i32 %1, i32 0
  %vecext.1 = extractelement <8 x float> %0, i32 1
  %2 = call i32 @llvm.lrint.i32.f32(float %vecext.1)
  %vecins.1 = insertelement <8 x i32> %vecins, i32 %2, i32 1
  %vecext.2 = extractelement <8 x float> %0, i32 2
  %3 = call i32 @llvm.lrint.i32.f32(float %vecext.2)
  %vecins.2 = insertelement <8 x i32> %vecins.1, i32 %3, i32 2
  %vecext.3 = extractelement <8 x float> %0, i32 3
  %4 = call i32 @llvm.lrint.i32.f32(float %vecext.3)
  %vecins.3 = insertelement <8 x i32> %vecins.2, i32 %4, i32 3
  %vecext.4 = extractelement <8 x float> %0, i32 4
  %5 = call i32 @llvm.lrint.i32.f32(float %vecext.4)
  %vecins.4 = insertelement <8 x i32> %vecins.3, i32 %5, i32 4
  %vecext.5 = extractelement <8 x float> %0, i32 5
  %6 = call i32 @llvm.lrint.i32.f32(float %vecext.5)
  %vecins.5 = insertelement <8 x i32> %vecins.4, i32 %6, i32 5
  %vecext.6 = extractelement <8 x float> %0, i32 6
  %7 = call i32 @llvm.lrint.i32.f32(float %vecext.6)
  %vecins.6 = insertelement <8 x i32> %vecins.5, i32 %7, i32 6
  %vecext.7 = extractelement <8 x float> %0, i32 7
  %8 = call i32 @llvm.lrint.i32.f32(float %vecext.7)
  %vecins.7 = insertelement <8 x i32> %vecins.6, i32 %8, i32 7
  ret <8 x i32> %vecins.7
}

define <2 x i64> @lrint_v2i64f32(ptr %a) {
; CHECK-LABEL: define <2 x i64> @lrint_v2i64f32(
; CHECK-SAME: ptr [[A:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP0:%.*]] = load <2 x float>, ptr [[A]], align 8
; CHECK-NEXT:    [[TMP1:%.*]] = call <2 x i64> @llvm.lrint.v2i64.v2f32(<2 x float> [[TMP0]])
; CHECK-NEXT:    ret <2 x i64> [[TMP1]]
;
entry:
  %0 = load <2 x float>, ptr %a
  %vecext = extractelement <2 x float> %0, i64 0
  %1 = call i64 @llvm.lrint.i64.f32(float %vecext)
  %vecins = insertelement <2 x i64> undef, i64 %1, i64 0
  %vecext.1 = extractelement <2 x float> %0, i64 1
  %2 = call i64 @llvm.lrint.i64.f32(float %vecext.1)
  %vecins.1 = insertelement <2 x i64> %vecins, i64 %2, i64 1
  ret <2 x i64> %vecins.1
}

define <4 x i64> @lrint_v4i64f32(ptr %a) {
; CHECK-LABEL: define <4 x i64> @lrint_v4i64f32(
; CHECK-SAME: ptr [[A:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP0:%.*]] = load <4 x float>, ptr [[A]], align 16
; CHECK-NEXT:    [[TMP1:%.*]] = call <4 x i64> @llvm.lrint.v4i64.v4f32(<4 x float> [[TMP0]])
; CHECK-NEXT:    ret <4 x i64> [[TMP1]]
;
entry:
  %0 = load <4 x float>, ptr %a
  %vecext = extractelement <4 x float> %0, i64 0
  %1 = call i64 @llvm.lrint.i64.f32(float %vecext)
  %vecins = insertelement <4 x i64> undef, i64 %1, i64 0
  %vecext.1 = extractelement <4 x float> %0, i64 1
  %2 = call i64 @llvm.lrint.i64.f32(float %vecext.1)
  %vecins.1 = insertelement <4 x i64> %vecins, i64 %2, i64 1
  %vecext.2 = extractelement <4 x float> %0, i64 2
  %3 = call i64 @llvm.lrint.i64.f32(float %vecext.2)
  %vecins.2 = insertelement <4 x i64> %vecins.1, i64 %3, i64 2
  %vecext.3 = extractelement <4 x float> %0, i64 3
  %4 = call i64 @llvm.lrint.i64.f32(float %vecext.3)
  %vecins.3 = insertelement <4 x i64> %vecins.2, i64 %4, i64 3
  ret <4 x i64> %vecins.3
}

define <8 x i64> @lrint_v8i64f32(ptr %a) {
; CHECK-LABEL: define <8 x i64> @lrint_v8i64f32(
; CHECK-SAME: ptr [[A:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP0:%.*]] = load <8 x float>, ptr [[A]], align 32
; CHECK-NEXT:    [[TMP1:%.*]] = shufflevector <8 x float> [[TMP0]], <8 x float> poison, <4 x i32> <i32 0, i32 1, i32 2, i32 3>
; CHECK-NEXT:    [[TMP2:%.*]] = call <4 x i64> @llvm.lrint.v4i64.v4f32(<4 x float> [[TMP1]])
; CHECK-NEXT:    [[TMP3:%.*]] = shufflevector <4 x i64> [[TMP2]], <4 x i64> poison, <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 poison, i32 poison, i32 poison, i32 poison>
; CHECK-NEXT:    [[TMP4:%.*]] = shufflevector <8 x float> [[TMP0]], <8 x float> poison, <4 x i32> <i32 4, i32 5, i32 6, i32 7>
; CHECK-NEXT:    [[TMP5:%.*]] = call <4 x i64> @llvm.lrint.v4i64.v4f32(<4 x float> [[TMP4]])
; CHECK-NEXT:    [[TMP6:%.*]] = shufflevector <4 x i64> [[TMP5]], <4 x i64> poison, <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 poison, i32 poison, i32 poison, i32 poison>
; CHECK-NEXT:    [[VECINS_71:%.*]] = shufflevector <8 x i64> [[TMP3]], <8 x i64> [[TMP6]], <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 8, i32 9, i32 10, i32 11>
; CHECK-NEXT:    ret <8 x i64> [[VECINS_71]]
;
entry:
  %0 = load <8 x float>, ptr %a
  %vecext = extractelement <8 x float> %0, i64 0
  %1 = call i64 @llvm.lrint.i64.f32(float %vecext)
  %vecins = insertelement <8 x i64> undef, i64 %1, i64 0
  %vecext.1 = extractelement <8 x float> %0, i64 1
  %2 = call i64 @llvm.lrint.i64.f32(float %vecext.1)
  %vecins.1 = insertelement <8 x i64> %vecins, i64 %2, i64 1
  %vecext.2 = extractelement <8 x float> %0, i64 2
  %3 = call i64 @llvm.lrint.i64.f32(float %vecext.2)
  %vecins.2 = insertelement <8 x i64> %vecins.1, i64 %3, i64 2
  %vecext.3 = extractelement <8 x float> %0, i64 3
  %4 = call i64 @llvm.lrint.i64.f32(float %vecext.3)
  %vecins.3 = insertelement <8 x i64> %vecins.2, i64 %4, i64 3
  %vecext.4 = extractelement <8 x float> %0, i64 4
  %5 = call i64 @llvm.lrint.i64.f32(float %vecext.4)
  %vecins.4 = insertelement <8 x i64> %vecins.3, i64 %5, i64 4
  %vecext.5 = extractelement <8 x float> %0, i64 5
  %6 = call i64 @llvm.lrint.i64.f32(float %vecext.5)
  %vecins.5 = insertelement <8 x i64> %vecins.4, i64 %6, i64 5
  %vecext.6 = extractelement <8 x float> %0, i64 6
  %7 = call i64 @llvm.lrint.i64.f32(float %vecext.6)
  %vecins.6 = insertelement <8 x i64> %vecins.5, i64 %7, i64 6
  %vecext.7 = extractelement <8 x float> %0, i64 7
  %8 = call i64 @llvm.lrint.i64.f32(float %vecext.7)
  %vecins.7 = insertelement <8 x i64> %vecins.6, i64 %8, i64 7
  ret <8 x i64> %vecins.7
}

define <2 x i64> @llrint_v2i64f32(ptr %a) {
; CHECK-LABEL: define <2 x i64> @llrint_v2i64f32(
; CHECK-SAME: ptr [[A:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP0:%.*]] = load <2 x float>, ptr [[A]], align 8
; CHECK-NEXT:    [[TMP1:%.*]] = call <2 x i64> @llvm.llrint.v2i64.v2f32(<2 x float> [[TMP0]])
; CHECK-NEXT:    ret <2 x i64> [[TMP1]]
;
entry:
  %0 = load <2 x float>, ptr %a
  %vecext = extractelement <2 x float> %0, i64 0
  %1 = call i64 @llvm.llrint.i64.f32(float %vecext)
  %vecins = insertelement <2 x i64> undef, i64 %1, i64 0
  %vecext.1 = extractelement <2 x float> %0, i64 1
  %2 = call i64 @llvm.llrint.i64.f32(float %vecext.1)
  %vecins.1 = insertelement <2 x i64> %vecins, i64 %2, i64 1
  ret <2 x i64> %vecins.1
}

define <4 x i64> @llrint_v4i64f32(ptr %a) {
; CHECK-LABEL: define <4 x i64> @llrint_v4i64f32(
; CHECK-SAME: ptr [[A:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP0:%.*]] = load <4 x float>, ptr [[A]], align 16
; CHECK-NEXT:    [[TMP1:%.*]] = call <4 x i64> @llvm.llrint.v4i64.v4f32(<4 x float> [[TMP0]])
; CHECK-NEXT:    ret <4 x i64> [[TMP1]]
;
entry:
  %0 = load <4 x float>, ptr %a
  %vecext = extractelement <4 x float> %0, i64 0
  %1 = call i64 @llvm.llrint.i64.f32(float %vecext)
  %vecins = insertelement <4 x i64> undef, i64 %1, i64 0
  %vecext.1 = extractelement <4 x float> %0, i64 1
  %2 = call i64 @llvm.llrint.i64.f32(float %vecext.1)
  %vecins.1 = insertelement <4 x i64> %vecins, i64 %2, i64 1
  %vecext.2 = extractelement <4 x float> %0, i64 2
  %3 = call i64 @llvm.llrint.i64.f32(float %vecext.2)
  %vecins.2 = insertelement <4 x i64> %vecins.1, i64 %3, i64 2
  %vecext.3 = extractelement <4 x float> %0, i64 3
  %4 = call i64 @llvm.llrint.i64.f32(float %vecext.3)
  %vecins.3 = insertelement <4 x i64> %vecins.2, i64 %4, i64 3
  ret <4 x i64> %vecins.3
}

define <8 x i64> @llrint_v8i64f32(ptr %a) {
; CHECK-LABEL: define <8 x i64> @llrint_v8i64f32(
; CHECK-SAME: ptr [[A:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP0:%.*]] = load <8 x float>, ptr [[A]], align 32
; CHECK-NEXT:    [[TMP1:%.*]] = shufflevector <8 x float> [[TMP0]], <8 x float> poison, <4 x i32> <i32 0, i32 1, i32 2, i32 3>
; CHECK-NEXT:    [[TMP2:%.*]] = call <4 x i64> @llvm.llrint.v4i64.v4f32(<4 x float> [[TMP1]])
; CHECK-NEXT:    [[TMP3:%.*]] = shufflevector <4 x i64> [[TMP2]], <4 x i64> poison, <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 poison, i32 poison, i32 poison, i32 poison>
; CHECK-NEXT:    [[TMP4:%.*]] = shufflevector <8 x float> [[TMP0]], <8 x float> poison, <4 x i32> <i32 4, i32 5, i32 6, i32 7>
; CHECK-NEXT:    [[TMP5:%.*]] = call <4 x i64> @llvm.llrint.v4i64.v4f32(<4 x float> [[TMP4]])
; CHECK-NEXT:    [[TMP6:%.*]] = shufflevector <4 x i64> [[TMP5]], <4 x i64> poison, <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 poison, i32 poison, i32 poison, i32 poison>
; CHECK-NEXT:    [[VECINS_71:%.*]] = shufflevector <8 x i64> [[TMP3]], <8 x i64> [[TMP6]], <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 8, i32 9, i32 10, i32 11>
; CHECK-NEXT:    ret <8 x i64> [[VECINS_71]]
;
entry:
  %0 = load <8 x float>, ptr %a
  %vecext = extractelement <8 x float> %0, i64 0
  %1 = call i64 @llvm.llrint.i64.f32(float %vecext)
  %vecins = insertelement <8 x i64> undef, i64 %1, i64 0
  %vecext.1 = extractelement <8 x float> %0, i64 1
  %2 = call i64 @llvm.llrint.i64.f32(float %vecext.1)
  %vecins.1 = insertelement <8 x i64> %vecins, i64 %2, i64 1
  %vecext.2 = extractelement <8 x float> %0, i64 2
  %3 = call i64 @llvm.llrint.i64.f32(float %vecext.2)
  %vecins.2 = insertelement <8 x i64> %vecins.1, i64 %3, i64 2
  %vecext.3 = extractelement <8 x float> %0, i64 3
  %4 = call i64 @llvm.llrint.i64.f32(float %vecext.3)
  %vecins.3 = insertelement <8 x i64> %vecins.2, i64 %4, i64 3
  %vecext.4 = extractelement <8 x float> %0, i64 4
  %5 = call i64 @llvm.llrint.i64.f32(float %vecext.4)
  %vecins.4 = insertelement <8 x i64> %vecins.3, i64 %5, i64 4
  %vecext.5 = extractelement <8 x float> %0, i64 5
  %6 = call i64 @llvm.llrint.i64.f32(float %vecext.5)
  %vecins.5 = insertelement <8 x i64> %vecins.4, i64 %6, i64 5
  %vecext.6 = extractelement <8 x float> %0, i64 6
  %7 = call i64 @llvm.llrint.i64.f32(float %vecext.6)
  %vecins.6 = insertelement <8 x i64> %vecins.5, i64 %7, i64 6
  %vecext.7 = extractelement <8 x float> %0, i64 7
  %8 = call i64 @llvm.llrint.i64.f32(float %vecext.7)
  %vecins.7 = insertelement <8 x i64> %vecins.6, i64 %8, i64 7
  ret <8 x i64> %vecins.7
}

declare float @llvm.rint.f32(float)
declare i32 @llvm.lrint.i32.f32(float)
declare i64 @llvm.lrint.i64.f32(float)
declare i64 @llvm.llrint.i64.f32(float)
