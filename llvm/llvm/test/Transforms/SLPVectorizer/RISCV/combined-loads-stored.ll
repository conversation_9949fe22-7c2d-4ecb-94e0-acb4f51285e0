; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 4
; RUN: opt -S --passes=slp-vectorizer -mtriple=riscv64-unknown-linux -mattr=+v < %s | FileCheck %s

define void @test(ptr noalias %p, ptr %p1) {
; CHECK-LABEL: define void @test(
; CHECK-SAME: ptr noalias [[P:%.*]], ptr [[P1:%.*]]) #[[ATTR0:[0-9]+]] {
; CHECK-NEXT:    [[GEP2:%.*]] = getelementptr inbounds i8, ptr [[P]], i64 16
; CHECK-NEXT:    [[TMP1:%.*]] = load <2 x i16>, ptr [[P]], align 2
; CHECK-NEXT:    [[TMP2:%.*]] = load <2 x i16>, ptr [[GEP2]], align 2
; CHECK-NEXT:    [[TMP3:%.*]] = call <4 x i16> @llvm.vector.insert.v4i16.v2i16(<4 x i16> poison, <2 x i16> [[TMP1]], i64 0)
; CHECK-NEXT:    [[TMP5:%.*]] = call <4 x i16> @llvm.vector.insert.v4i16.v2i16(<4 x i16> [[TMP3]], <2 x i16> [[TMP2]], i64 2)
; CHECK-NEXT:    store <4 x i16> [[TMP5]], ptr [[P1]], align 2
; CHECK-NEXT:    ret void
;
  %l1 = load i16, ptr %p, align 2
  %gep1 = getelementptr inbounds i8, ptr %p, i64 2
  %l2 = load i16, ptr %gep1, align 2
  %gep2 = getelementptr inbounds i8, ptr %p, i64 16
  %l3 = load i16, ptr %gep2, align 2
  %gep3 = getelementptr inbounds i8, ptr %p, i64 18
  %l4 = load i16, ptr %gep3, align 2
  store i16 %l1, ptr %p1, align 2
  %geps1 = getelementptr inbounds i8, ptr %p1, i64 2
  store i16 %l2, ptr %geps1, align 2
  %geps2 = getelementptr inbounds i8, ptr %p1, i64 4
  store i16 %l3, ptr %geps2, align 2
  %geps3 = getelementptr inbounds i8, ptr %p1, i64 6
  store i16 %l4, ptr %geps3, align 2
  ret void
}

