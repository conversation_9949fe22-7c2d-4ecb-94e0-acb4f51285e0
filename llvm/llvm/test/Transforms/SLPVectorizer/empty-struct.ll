; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S --passes=slp-vectorizer < %s | FileCheck %s

define { { {}, {}, {}, {}, {}, {}, {} } } @test({ {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, { {}, {}, {}, {}, {}, {}, {} }, { {} } } %0) {
; CHECK-LABEL: define { { {}, {}, {}, {}, {}, {}, {} } } @test(
; CHECK-SAME: { {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, { {}, {}, {}, {}, {}, {}, {} }, { {} } } [[TMP0:%.*]]) {
; CHECK-NEXT:    [[TMP2:%.*]] = extractvalue { {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, { {}, {}, {}, {}, {}, {}, {} }, { {} } } [[TMP0]], 18
; CHECK-NEXT:    [[TMP3:%.*]] = insertvalue { { {}, {}, {}, {}, {}, {}, {} } } undef, { {}, {}, {}, {}, {}, {}, {} } [[TMP2]], 0
; CHECK-NEXT:    ret { { {}, {}, {}, {}, {}, {}, {} } } [[TMP3]]
;
  %2 = extractvalue { {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, { {}, {}, {}, {}, {}, {}, {} }, { {} } } %0, 18
  %3 = insertvalue { { {}, {}, {}, {}, {}, {}, {} } } undef, { {}, {}, {}, {}, {}, {}, {} } %2, 0
  ret { { {}, {}, {}, {}, {}, {}, {} } } %3
}
