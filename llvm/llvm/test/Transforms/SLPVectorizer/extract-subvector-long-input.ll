; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 4
; RUN: %if x86-registered-target %{ opt -passes=slp-vectorizer -S -slp-threshold=-99999 -mtriple=x86_64-unknown-linux-gnu < %s | FileCheck %s %}
; RUN: %if aarch64-registered-target %{ opt -passes=slp-vectorizer -S -slp-threshold=-99999 -mtriple=aarch64-unknown-linux-gnu < %s | FileCheck %s %}

define void @test() {
; CHECK-LABEL: define void @test() {
; CHECK-NEXT:  bb:
; CHECK-NEXT:    br label [[BB1:%.*]]
; CHECK:       bb1:
; CHECK-NEXT:    [[PHI7:%.*]] = phi i32 [ 0, [[BB10:%.*]] ], [ 0, [[BB:%.*]] ]
; CHECK-NEXT:    [[TMP0:%.*]] = phi <8 x i32> [ poison, [[BB10]] ], [ zeroinitializer, [[BB]] ]
; CHECK-NEXT:    [[TMP1:%.*]] = insertelement <2 x i32> <i32 poison, i32 undef>, i32 [[PHI7]], i32 0
; CHECK-NEXT:    switch i32 0, label [[BB16:%.*]] [
; CHECK-NEXT:      i32 0, label [[BB14:%.*]]
; CHECK-NEXT:      i32 1, label [[BB11:%.*]]
; CHECK-NEXT:    ]
; CHECK:       bb9:
; CHECK-NEXT:    br label [[BB11]]
; CHECK:       bb10:
; CHECK-NEXT:    br label [[BB1]]
; CHECK:       bb11:
; CHECK-NEXT:    [[TMP2:%.*]] = phi <2 x i32> [ poison, [[BB9:%.*]] ], [ [[TMP1]], [[BB1]] ]
; CHECK-NEXT:    ret void
; CHECK:       bb14:
; CHECK-NEXT:    ret void
; CHECK:       bb15:
; CHECK-NEXT:    ret void
; CHECK:       bb16:
; CHECK-NEXT:    [[TMP3:%.*]] = phi <8 x i32> [ [[TMP0]], [[BB1]] ], [ poison, [[BB25:%.*]] ]
; CHECK-NEXT:    ret void
; CHECK:       bb25:
; CHECK-NEXT:    switch i32 0, label [[BB16]] [
; CHECK-NEXT:      i32 0, label [[BB14]]
; CHECK-NEXT:      i32 1, label [[BB15:%.*]]
; CHECK-NEXT:    ]
;
bb:
  br label %bb1

bb1:
  %phi = phi i32 [ 0, %bb10 ], [ 0, %bb ]
  %phi2 = phi i32 [ 0, %bb10 ], [ 0, %bb ]
  %phi3 = phi i32 [ 0, %bb10 ], [ 0, %bb ]
  %phi4 = phi i32 [ 0, %bb10 ], [ 0, %bb ]
  %phi5 = phi i32 [ 0, %bb10 ], [ 0, %bb ]
  %phi6 = phi i32 [ 0, %bb10 ], [ 0, %bb ]
  %phi7 = phi i32 [ 0, %bb10 ], [ 0, %bb ]
  %phi8 = phi i32 [ 0, %bb10 ], [ 0, %bb ]
  switch i32 0, label %bb16 [
  i32 0, label %bb14
  i32 1, label %bb11
  ]

bb9:
  br label %bb11

bb10:
  br label %bb1

bb11:
  %phi12 = phi i32 [ 0, %bb9 ], [ %phi7, %bb1 ]
  %phi13 = phi i32 [ 0, %bb9 ], [ undef, %bb1 ]
  ret void

bb14:
  ret void

bb15:
  ret void

bb16:
  %phi17 = phi i32 [ %phi, %bb1 ], [ 0, %bb25 ]
  %phi18 = phi i32 [ %phi2, %bb1 ], [ 0, %bb25 ]
  %phi19 = phi i32 [ %phi3, %bb1 ], [ 0, %bb25 ]
  %phi20 = phi i32 [ %phi4, %bb1 ], [ 0, %bb25 ]
  %phi21 = phi i32 [ %phi5, %bb1 ], [ 0, %bb25 ]
  %phi22 = phi i32 [ %phi6, %bb1 ], [ 0, %bb25 ]
  %phi23 = phi i32 [ %phi7, %bb1 ], [ 0, %bb25 ]
  %phi24 = phi i32 [ %phi8, %bb1 ], [ 0, %bb25 ]
  ret void

bb25:
  switch i32 0, label %bb16 [
  i32 0, label %bb14
  i32 1, label %bb15
  ]
}
