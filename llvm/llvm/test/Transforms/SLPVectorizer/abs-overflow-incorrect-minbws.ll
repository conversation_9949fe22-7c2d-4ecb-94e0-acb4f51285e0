; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S --passes=slp-vectorizer < %s | FileCheck %s

define i32 @test(i32 %n) {
; CHECK-LABEL: define i32 @test(
; CHECK-SAME: i32 [[N:%.*]]) {
; CHECK-NEXT:  [[ENTRY:.*:]]
; CHECK-NEXT:    [[TMP0:%.*]] = insertelement <2 x i32> poison, i32 [[N]], i32 0
; CHECK-NEXT:    [[TMP1:%.*]] = shufflevector <2 x i32> [[TMP0]], <2 x i32> poison, <2 x i32> zeroinitializer
; CHECK-NEXT:    [[TMP2:%.*]] = add <2 x i32> [[TMP1]], <i32 1, i32 2>
; CHECK-NEXT:    [[TMP3:%.*]] = zext <2 x i32> [[TMP2]] to <2 x i64>
; CHECK-NEXT:    [[TMP7:%.*]] = mul nuw nsw <2 x i64> [[TMP3]], splat (i64 273837369)
; CHECK-NEXT:    [[TMP8:%.*]] = call <2 x i64> @llvm.abs.v2i64(<2 x i64> [[TMP7]], i1 true)
; CHECK-NEXT:    [[TMP4:%.*]] = trunc <2 x i64> [[TMP8]] to <2 x i32>
; CHECK-NEXT:    [[TMP5:%.*]] = extractelement <2 x i32> [[TMP4]], i32 0
; CHECK-NEXT:    [[TMP6:%.*]] = extractelement <2 x i32> [[TMP4]], i32 1
; CHECK-NEXT:    [[RES1:%.*]] = add i32 [[TMP5]], [[TMP6]]
; CHECK-NEXT:    ret i32 [[RES1]]
;
entry:
  %n1 = add i32 %n, 1
  %zn1 = zext nneg i32 %n1 to i64
  %m1 = mul nuw nsw i64 %zn1, 273837369
  %a1 = call i64 @llvm.abs.i64(i64 %m1, i1 true)
  %t1 = trunc i64 %a1 to i32
  %n2 = add i32 %n, 2
  %zn2 = zext nneg i32 %n2 to i64
  %m2 = mul nuw nsw i64 %zn2, 273837369
  %a2 = call i64 @llvm.abs.i64(i64 %m2, i1 true)
  %t2 = trunc i64 %a2 to i32
  %res1 = add i32 %t1, %t2
  ret i32 %res1
}
