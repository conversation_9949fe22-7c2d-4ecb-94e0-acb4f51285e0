; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 4
;RUN: opt -S -S --passes=slp-vectorizer -slp-threshold=-99999 < %s | FileCheck %s

define double @test() {
; CHECK-LABEL: define double @test() {
; CHECK-NEXT:  bb:
; CHECK-NEXT:    br label [[BB7:%.*]]
; CHECK:       bb7:
; CHECK-NEXT:    [[TMP0:%.*]] = phi <2 x i32> [ poison, [[BB9:%.*]] ], [ zeroinitializer, [[BB:%.*]] ]
; CHECK-NEXT:    [[TMP1:%.*]] = phi <2 x i32> [ zeroinitializer, [[BB9]] ], [ zeroinitializer, [[BB]] ]
; CHECK-NEXT:    [[TMP2:%.*]] = zext <2 x i32> [[TMP0]] to <2 x i64>
; CHECK-NEXT:    [[TMP3:%.*]] = extractelement <2 x i32> [[TMP0]], i32 0
; CHECK-NEXT:    [[TMP4:%.*]] = zext i32 [[TMP3]] to i64
; CHECK-NEXT:    [[TMP5:%.*]] = extractelement <2 x i32> [[TMP0]], i32 1
; CHECK-NEXT:    [[TMP6:%.*]] = zext i32 [[TMP5]] to i64
; CHECK-NEXT:    [[ICMP:%.*]] = icmp ult i64 [[TMP6]], [[TMP4]]
; CHECK-NEXT:    [[TMP7:%.*]] = shufflevector <2 x i64> zeroinitializer, <2 x i64> [[TMP2]], <2 x i32> <i32 3, i32 1>
; CHECK-NEXT:    ret double 0.000000e+00
; CHECK:       bb9:
; CHECK-NEXT:    br label [[BB7]]
;
bb:
  br label %bb7

bb7:
  %0 = phi <2 x i32> [ poison, %bb9 ], [ zeroinitializer, %bb ]
  %1 = phi <2 x i32> [ zeroinitializer, %bb9 ], [ zeroinitializer, %bb ]
  %2 = extractelement <2 x i32> %0, i32 1
  %3 = extractelement <2 x i32> %0, i32 0
  %zext8 = zext i32 %3 to i64
  %zext = zext i32 %2 to i64
  %icmp = icmp ult i64 %zext, %zext8
  %4 = insertelement <2 x i64> zeroinitializer, i64 %zext, i32 0
  ret double 0.000000e+00

bb9:
  br label %bb7
}
