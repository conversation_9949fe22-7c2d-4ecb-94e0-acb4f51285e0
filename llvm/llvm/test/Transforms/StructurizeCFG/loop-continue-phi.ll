; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -S -o - -structurizecfg < %s | FileCheck %s

define void @test1(i1 %arg) {
; CHECK-LABEL: @test1(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    %arg.inv = xor i1 %arg, true
; CHECK-NEXT:    br label %loop
; CHECK:       Flow:
; CHECK-NEXT:    br label %Flow1
; CHECK:       loop:
; CHECK-NEXT:    %ctr = phi i32 [ 0, %entry ], [ %0, %Flow1 ]
; CHECK-NEXT:    %ctr.next = add i32 %ctr, 1
; CHECK-NEXT:    br i1 %arg.inv, label %loop.a, label %Flow1
; CHECK:       loop.a:
; CHECK-NEXT:    br i1 %arg.inv, label %loop.b, label %Flow
; CHECK:       loop.b:
; CHECK-NEXT:    br label %Flow
; CHECK:       Flow1:
; CHECK-NEXT:    %0 = phi i32 [ %ctr.next, %Flow ], [ undef, %loop ]
; CHECK-NEXT:    %1 = phi i1 [ false, %Flow ], [ true, %loop ]
; CHECK-NEXT:    br i1 %1, label %exit, label %loop
; CHECK:       exit:
; CHECK-NEXT:    ret void
;
entry:
  br label %loop

loop:
  %ctr = phi i32 [ 0, %entry ], [ %ctr.next, %loop.a ], [ %ctr.next, %loop.b ]
  %ctr.next = add i32 %ctr, 1
  br i1 %arg, label %exit, label %loop.a

loop.a:
  br i1 %arg, label %loop, label %loop.b

loop.b:
  br label %loop

exit:
  ret void
}
