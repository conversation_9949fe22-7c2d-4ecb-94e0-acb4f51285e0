; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt < %s -fix-irreducible -structurizecfg -S | FileCheck %s

; Both B1 and B4 are headers of an irreducible cycle. But in the
; structurized version, B1 dominates B4. The program is structurized
; correctly when the irreducible cycle is fixed.

define void @irreducible(i1 %PredEntry, i1 %PredB1, i1 %PredB2, i1 %PredB3, i1 %PredB4)
; CHECK-LABEL: @irreducible(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[PREDB2_INV:%.*]] = xor i1 [[PREDB2:%.*]], true
; CHECK-NEXT:    [[PREDENTRY_INV:%.*]] = xor i1 [[PREDENTRY:%.*]], true
; CHECK-NEXT:    br label [[IRR_GUARD:%.*]]
; CHECK:       Flow4:
; CHECK-NEXT:    [[TMP0:%.*]] = phi i1 [ true, [[B3:%.*]] ], [ undef, [[B2:%.*]] ]
; CHECK-NEXT:    [[TMP1:%.*]] = phi i1 [ [[PREDB3:%.*]], [[B3]] ], [ true, [[B2]] ]
; CHECK-NEXT:    [[TMP2:%.*]] = phi i1 [ [[PREDB3]], [[B3]] ], [ false, [[B2]] ]
; CHECK-NEXT:    br label [[FLOW3:%.*]]
; CHECK:       B1:
; CHECK-NEXT:    br label [[FLOW5:%.*]]
; CHECK:       Flow2:
; CHECK-NEXT:    [[TMP3:%.*]] = phi i1 [ [[TMP9:%.*]], [[FLOW5]] ], [ [[TMP11:%.*]], [[FLOW:%.*]] ]
; CHECK-NEXT:    [[TMP4:%.*]] = phi i1 [ [[TMP10:%.*]], [[FLOW5]] ], [ [[TMP12:%.*]], [[FLOW]] ]
; CHECK-NEXT:    [[TMP5:%.*]] = phi i1 [ [[TMP7:%.*]], [[FLOW5]] ], [ true, [[FLOW]] ]
; CHECK-NEXT:    br i1 true, label [[FLOW6:%.*]], label [[FLOW]]
; CHECK:       B2:
; CHECK-NEXT:    br i1 [[PREDB2_INV]], label [[B3]], label [[FLOW4:%.*]]
; CHECK:       Flow3:
; CHECK-NEXT:    [[TMP6:%.*]] = phi i1 [ [[TMP0]], [[FLOW4]] ], [ undef, [[IRR_GUARD1:%.*]] ]
; CHECK-NEXT:    [[TMP7]] = phi i1 [ [[TMP1]], [[FLOW4]] ], [ true, [[IRR_GUARD1]] ]
; CHECK-NEXT:    [[TMP8:%.*]] = phi i1 [ [[TMP2]], [[FLOW4]] ], [ true, [[IRR_GUARD1]] ]
; CHECK-NEXT:    br i1 [[TMP8]], label [[B1:%.*]], label [[FLOW5]]
; CHECK:       B3:
; CHECK-NEXT:    br label [[FLOW4]]
; CHECK:       B4:
; CHECK-NEXT:    br label [[FLOW]]
; CHECK:       Flow5:
; CHECK-NEXT:    [[TMP9]] = phi i1 [ undef, [[B1]] ], [ [[TMP6]], [[FLOW3]] ]
; CHECK-NEXT:    [[TMP10]] = phi i1 [ true, [[B1]] ], [ undef, [[FLOW3]] ]
; CHECK-NEXT:    br label [[FLOW2:%.*]]
; CHECK:       Flow6:
; CHECK-NEXT:    br i1 [[TMP5]], label [[EXIT:%.*]], label [[IRR_GUARD]]
; CHECK:       exit:
; CHECK-NEXT:    ret void
; CHECK:       irr.guard:
; CHECK-NEXT:    [[GUARD_B4:%.*]] = phi i1 [ [[PREDENTRY_INV]], [[ENTRY:%.*]] ], [ [[TMP3]], [[FLOW6]] ]
; CHECK-NEXT:    br i1 [[GUARD_B4]], label [[B4:%.*]], label [[FLOW]]
; CHECK:       Flow:
; CHECK-NEXT:    [[TMP11]] = phi i1 [ [[TMP3]], [[FLOW2]] ], [ undef, [[B4]] ], [ undef, [[IRR_GUARD]] ]
; CHECK-NEXT:    [[TMP12]] = phi i1 [ [[TMP4]], [[FLOW2]] ], [ true, [[B4]] ], [ false, [[IRR_GUARD]] ]
; CHECK-NEXT:    [[TMP13:%.*]] = phi i1 [ false, [[FLOW2]] ], [ [[PREDB4:%.*]], [[B4]] ], [ true, [[IRR_GUARD]] ]
; CHECK-NEXT:    br i1 [[TMP13]], label [[IRR_GUARD1]], label [[FLOW2]]
; CHECK:       irr.guard1:
; CHECK-NEXT:    br i1 [[TMP12]], label [[B2]], label [[FLOW3]]
;
{
entry:
  br i1 %PredEntry, label %B1, label %B4

B1:
  br i1 %PredB1, label %exit, label %B2

B2:
  br i1 %PredB2, label %exit, label %B3

B3:
  br i1 %PredB3, label %B1, label %B4

B4:
  br i1 %PredB4, label %B2, label %exit

exit:
  ret void
}
