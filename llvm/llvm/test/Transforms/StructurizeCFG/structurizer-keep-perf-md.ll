; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S -mtriple=amdgcn-- -passes=structurizecfg %s | FileCheck -check-prefix=OPT %s

define amdgpu_ps i32 @if_else(i32 %0) {
; OPT-LABEL: define amdgpu_ps i32 @if_else(
; OPT-SAME: i32 [[TMP0:%.*]]) {
; OPT-NEXT:    [[C:%.*]] = icmp ne i32 [[TMP0]], 0
; OPT-NEXT:    br i1 [[C]], label %[[FALSE:.*]], label %[[FLOW:.*]], !prof [[PROF0:![0-9]+]]
; OPT:       [[FLOW]]:
; OPT-NEXT:    [[TMP2:%.*]] = phi i32 [ 33, %[[FALSE]] ], [ undef, [[TMP1:%.*]] ]
; OPT-NEXT:    [[TMP3:%.*]] = phi i1 [ false, %[[FALSE]] ], [ true, [[TMP1]] ]
; OPT-NEXT:    br i1 [[TMP3]], label %[[TRUE:.*]], label %[[EXIT:.*]]
; OPT:       [[TRUE]]:
; OPT-NEXT:    br label %[[EXIT]]
; OPT:       [[FALSE]]:
; OPT-NEXT:    br label %[[FLOW]]
; OPT:       [[EXIT]]:
; OPT-NEXT:    [[RET:%.*]] = phi i32 [ [[TMP2]], %[[FLOW]] ], [ 42, %[[TRUE]] ]
; OPT-NEXT:    ret i32 [[RET]]
;
  %c = icmp eq i32 %0, 0
  br i1 %c, label %true, label %false, !prof !0

true:                                             ; preds = %1
  br label %exit

false:                                            ; preds = %1
  br label %exit

exit:                                             ; preds = %false, %true
  %ret = phi i32 [ 42, %true ], [ 33, %false ]
  ret i32 %ret
}

define amdgpu_ps void @loop_if_break(i32 %n) {
; OPT-LABEL: define amdgpu_ps void @loop_if_break(
; OPT-SAME: i32 [[N:%.*]]) {
; OPT-NEXT:  [[ENTRY:.*]]:
; OPT-NEXT:    br label %[[LOOP:.*]]
; OPT:       [[LOOP]]:
; OPT-NEXT:    [[I:%.*]] = phi i32 [ [[N]], %[[ENTRY]] ], [ [[TMP0:%.*]], %[[FLOW:.*]] ]
; OPT-NEXT:    [[C:%.*]] = icmp ugt i32 [[I]], 0
; OPT-NEXT:    br i1 [[C]], label %[[LOOP_BODY:.*]], label %[[FLOW]], !prof [[PROF1:![0-9]+]]
; OPT:       [[LOOP_BODY]]:
; OPT-NEXT:    [[I_NEXT:%.*]] = sub i32 [[I]], 1
; OPT-NEXT:    br label %[[FLOW]]
; OPT:       [[FLOW]]:
; OPT-NEXT:    [[TMP0]] = phi i32 [ [[I_NEXT]], %[[LOOP_BODY]] ], [ undef, %[[LOOP]] ]
; OPT-NEXT:    [[TMP1:%.*]] = phi i1 [ false, %[[LOOP_BODY]] ], [ true, %[[LOOP]] ]
; OPT-NEXT:    br i1 [[TMP1]], label %[[EXIT:.*]], label %[[LOOP]]
; OPT:       [[EXIT]]:
; OPT-NEXT:    ret void
;
entry:
  br label %loop

loop:                                             ; preds = %loop_body, %entry
  %i = phi i32 [ %n, %entry ], [ %i.next, %loop_body ]
  %c = icmp ugt i32 %i, 0
  br i1 %c, label %loop_body, label %exit, !prof !0

loop_body:                                        ; preds = %loop
  %i.next = sub i32 %i, 1
  br label %loop

exit:                                             ; preds = %loop
  ret void
}

attributes #0 = { nocallback nofree nosync nounwind speculatable willreturn memory(none) }

!0 = !{!"branch_weights", i32 1000, i32 1}
;.
; OPT: [[PROF0]] = !{!"branch_weights", i32 1, i32 1000}
; OPT: [[PROF1]] = !{!"branch_weights", i32 1000, i32 1}
;.
