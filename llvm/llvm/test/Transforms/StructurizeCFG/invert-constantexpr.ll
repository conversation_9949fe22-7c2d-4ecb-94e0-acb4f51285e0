; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -S -o - -structurizecfg < %s | FileCheck %s

@g = global i32 0

define void @invert_constantexpr_condition(i32 %arg, i32 %arg1) #0 {
; CHECK-LABEL: @invert_constantexpr_condition(
; CHECK-NEXT:  bb:
; CHECK-NEXT:    [[TMP:%.*]] = icmp ne i32 [[ARG:%.*]], 0
; CHECK-NEXT:    br i1 ptrtoint (ptr @g to i1), label [[BB2:%.*]], label [[FLOW:%.*]]
; CHECK:       bb2:
; CHECK-NEXT:    br label [[FLOW]]
; CHECK:       bb3:
; CHECK-NEXT:    [[TMP4:%.*]] = phi i1 [ undef, [[FLOW]] ], [ [[TMP7:%.*]], [[BB6:%.*]] ]
; CHECK-NEXT:    [[TMP5:%.*]] = or i1 [[TMP4]], ptrtoint (ptr @g to i1)
; CHECK-NEXT:    br label [[BB8:%.*]]
; CHECK:       Flow:
; CHECK-NEXT:    [[TMP0:%.*]] = phi i1 [ [[TMP]], [[BB2]] ], [ xor (i1 ptrtoint (ptr @g to i1), i1 true), [[BB:%.*]] ]
; CHECK-NEXT:    br i1 [[TMP0]], label [[BB6]], label [[BB3:%.*]]
; CHECK:       bb6:
; CHECK-NEXT:    [[TMP7]] = icmp slt i32 [[ARG]], [[ARG1:%.*]]
; CHECK-NEXT:    br label [[BB3]]
; CHECK:       bb8:
; CHECK-NEXT:    ret void
;
bb:
  %tmp = icmp eq i32 %arg, 0
  br i1 ptrtoint (ptr @g to i1), label %bb2, label %bb6

bb2:
  br i1 %tmp, label %bb3, label %bb6

bb3:
  %tmp4 = phi i1 [ %tmp7, %bb6 ], [ undef, %bb2 ]
  %tmp5 = or i1 %tmp4, ptrtoint (ptr @g to i1)
  br i1 %tmp5, label %bb8, label %bb8

bb6:
  %tmp7 = icmp slt i32 %arg, %arg1
  br label %bb3

bb8:
  ret void
}

declare i32 @llvm.amdgcn.workitem.id.x() #1

attributes #0 = { nounwind }
attributes #1 = { nounwind readnone }
