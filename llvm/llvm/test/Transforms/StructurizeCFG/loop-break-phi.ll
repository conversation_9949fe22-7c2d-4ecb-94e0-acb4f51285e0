; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S -passes='structurizecfg' %s -o - | FileCheck %s

define float @while_break(i32 %z, float %v, i32 %x, i32 %y) #0 {
; CHECK-LABEL: define float @while_break(
; CHECK-SAME: i32 [[Z:%.*]], float [[V:%.*]], i32 [[X:%.*]], i32 [[Y:%.*]]) #[[ATTR0:[0-9]+]] {
; CHECK-NEXT:  [[ENTRY:.*]]:
; CHECK-NEXT:    br label %[[HEADER:.*]]
; CHECK:       [[HEADER]]:
; CHECK-NEXT:    [[V_1:%.*]] = phi float [ [[V]], %[[ENTRY]] ], [ [[TMP8:%.*]], %[[FLOW2:.*]] ]
; CHECK-NEXT:    [[IND:%.*]] = phi i32 [ 0, %[[ENTRY]] ], [ [[TMP5:%.*]], %[[FLOW2]] ]
; CHECK-NEXT:    [[CC:%.*]] = icmp sge i32 [[IND]], [[X]]
; CHECK-NEXT:    br i1 [[CC]], label %[[ELSE:.*]], label %[[FLOW:.*]]
; CHECK:       [[FLOW]]:
; CHECK-NEXT:    [[TMP0:%.*]] = phi float [ [[V_1]], %[[ELSE]] ], [ undef, %[[HEADER]] ]
; CHECK-NEXT:    [[TMP1:%.*]] = phi i1 [ [[CC2:%.*]], %[[ELSE]] ], [ false, %[[HEADER]] ]
; CHECK-NEXT:    [[TMP2:%.*]] = phi i1 [ false, %[[ELSE]] ], [ true, %[[HEADER]] ]
; CHECK-NEXT:    br i1 [[TMP2]], label %[[IF:.*]], label %[[FLOW1:.*]]
; CHECK:       [[IF]]:
; CHECK-NEXT:    [[V_IF:%.*]] = fadd float [[V_1]], 1.000000e+00
; CHECK-NEXT:    br label %[[FLOW1]]
; CHECK:       [[ELSE]]:
; CHECK-NEXT:    [[CC2]] = icmp slt i32 [[IND]], [[Y]]
; CHECK-NEXT:    br label %[[FLOW]]
; CHECK:       [[FLOW1]]:
; CHECK-NEXT:    [[TMP8]] = phi float [ [[V_IF]], %[[IF]] ], [ [[TMP0]], %[[FLOW]] ]
; CHECK-NEXT:    [[TMP4:%.*]] = phi i1 [ true, %[[IF]] ], [ [[TMP1]], %[[FLOW]] ]
; CHECK-NEXT:    br i1 [[TMP4]], label %[[LATCH:.*]], label %[[FLOW2]]
; CHECK:       [[LATCH]]:
; CHECK-NEXT:    [[IND_INC:%.*]] = add i32 [[IND]], 1
; CHECK-NEXT:    [[CC3:%.*]] = icmp slt i32 [[IND]], [[Z]]
; CHECK-NEXT:    br label %[[FLOW2]]
; CHECK:       [[FLOW2]]:
; CHECK-NEXT:    [[TMP5]] = phi i32 [ [[IND_INC]], %[[LATCH]] ], [ undef, %[[FLOW1]] ]
; CHECK-NEXT:    [[TMP6:%.*]] = phi i1 [ [[CC3]], %[[LATCH]] ], [ true, %[[FLOW1]] ]
; CHECK-NEXT:    br i1 [[TMP6]], label %[[END:.*]], label %[[HEADER]]
; CHECK:       [[END]]:
; CHECK-NEXT:    ret float [[TMP8]]
;
entry:
  br label %header

header:
  %v.1 = phi float [ %v, %entry ], [ %v.2, %latch ]
  %ind = phi i32 [ 0, %entry], [ %ind.inc, %latch ]
  %cc = icmp slt i32 %ind, %x
  br i1 %cc, label %if, label %else

if:
  %v.if = fadd float %v.1, 1.0
  br label %latch

else:
  %cc2 = icmp slt i32 %ind, %y
  br i1 %cc2, label %latch, label %end

latch:
  %v.2 = phi float [ %v.if, %if ], [ %v.1, %else ]
  %ind.inc = add i32 %ind, 1
  %cc3 = icmp slt i32 %ind, %z
  br i1 %cc3, label %end, label %header

end:
  %r = phi float [ %v.2, %latch ], [ %v.1, %else ]
  ret float %r
}

; Just different dfs order from while_break.
define float @while_break2(i32 %z, float %v, i32 %x, i32 %y) #0 {
; CHECK-LABEL: define float @while_break2(
; CHECK-SAME: i32 [[Z:%.*]], float [[V:%.*]], i32 [[X:%.*]], i32 [[Y:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:  [[ENTRY:.*]]:
; CHECK-NEXT:    br label %[[HEADER:.*]]
; CHECK:       [[HEADER]]:
; CHECK-NEXT:    [[V_1:%.*]] = phi float [ [[V]], %[[ENTRY]] ], [ [[TMP8:%.*]], %[[FLOW2:.*]] ]
; CHECK-NEXT:    [[IND:%.*]] = phi i32 [ 0, %[[ENTRY]] ], [ [[TMP5:%.*]], %[[FLOW2]] ]
; CHECK-NEXT:    [[CC:%.*]] = icmp sge i32 [[IND]], [[X]]
; CHECK-NEXT:    br i1 [[CC]], label %[[IF:.*]], label %[[FLOW:.*]]
; CHECK:       [[IF]]:
; CHECK-NEXT:    [[V_IF:%.*]] = fadd float [[V_1]], 1.000000e+00
; CHECK-NEXT:    br label %[[FLOW]]
; CHECK:       [[FLOW]]:
; CHECK-NEXT:    [[TMP0:%.*]] = phi float [ [[V_IF]], %[[IF]] ], [ undef, %[[HEADER]] ]
; CHECK-NEXT:    [[TMP1:%.*]] = phi i1 [ true, %[[IF]] ], [ false, %[[HEADER]] ]
; CHECK-NEXT:    [[TMP2:%.*]] = phi i1 [ false, %[[IF]] ], [ true, %[[HEADER]] ]
; CHECK-NEXT:    br i1 [[TMP2]], label %[[ELSE:.*]], label %[[FLOW1:.*]]
; CHECK:       [[ELSE]]:
; CHECK-NEXT:    [[CC2:%.*]] = icmp slt i32 [[IND]], [[Y]]
; CHECK-NEXT:    br label %[[FLOW1]]
; CHECK:       [[FLOW1]]:
; CHECK-NEXT:    [[TMP8]] = phi float [ [[V_1]], %[[ELSE]] ], [ [[TMP0]], %[[FLOW]] ]
; CHECK-NEXT:    [[TMP4:%.*]] = phi i1 [ [[CC2]], %[[ELSE]] ], [ [[TMP1]], %[[FLOW]] ]
; CHECK-NEXT:    br i1 [[TMP4]], label %[[LATCH:.*]], label %[[FLOW2]]
; CHECK:       [[LATCH]]:
; CHECK-NEXT:    [[IND_INC:%.*]] = add i32 [[IND]], 1
; CHECK-NEXT:    [[CC3:%.*]] = icmp slt i32 [[IND]], [[Z]]
; CHECK-NEXT:    br label %[[FLOW2]]
; CHECK:       [[FLOW2]]:
; CHECK-NEXT:    [[TMP5]] = phi i32 [ [[IND_INC]], %[[LATCH]] ], [ undef, %[[FLOW1]] ]
; CHECK-NEXT:    [[TMP6:%.*]] = phi i1 [ [[CC3]], %[[LATCH]] ], [ true, %[[FLOW1]] ]
; CHECK-NEXT:    br i1 [[TMP6]], label %[[END:.*]], label %[[HEADER]]
; CHECK:       [[END]]:
; CHECK-NEXT:    ret float [[TMP8]]
;
entry:
  br label %header

header:
  %v.1 = phi float [ %v, %entry ], [ %v.2, %latch ]
  %ind = phi i32 [ 0, %entry], [ %ind.inc, %latch ]
  %cc = icmp slt i32 %ind, %x
  br i1 %cc, label %else, label %if

if:
  %v.if = fadd float %v.1, 1.0
  br label %latch

else:
  %cc2 = icmp slt i32 %ind, %y
  br i1 %cc2, label %latch, label %end

latch:
  %v.2 = phi float [ %v.if, %if ], [ %v.1, %else ]
  %ind.inc = add i32 %ind, 1
  %cc3 = icmp slt i32 %ind, %z
  br i1 %cc3, label %end, label %header

end:
  %r = phi float [ %v.2, %latch ], [ %v.1, %else ]
  ret float %r
}

; Two chains of phi network that have the same value from %if block.
define < 2 x float> @while_break_two_chains_of_phi(float %v, i32 %x, i32 %y, i32 %z, ptr addrspace(1) %p) #0 {
; CHECK-LABEL: define <2 x float> @while_break_two_chains_of_phi(
; CHECK-SAME: float [[V:%.*]], i32 [[X:%.*]], i32 [[Y:%.*]], i32 [[Z:%.*]], ptr addrspace(1) [[P:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:  [[ENTRY:.*]]:
; CHECK-NEXT:    br label %[[HEADER:.*]]
; CHECK:       [[HEADER]]:
; CHECK-NEXT:    [[V_1:%.*]] = phi float [ [[V]], %[[ENTRY]] ], [ [[TMP8:%.*]], %[[FLOW1:.*]] ]
; CHECK-NEXT:    [[V_COPY:%.*]] = phi float [ 0.000000e+00, %[[ENTRY]] ], [ [[TMP7:%.*]], %[[FLOW1]] ]
; CHECK-NEXT:    [[IND:%.*]] = phi i32 [ 0, %[[ENTRY]] ], [ [[TMP3:%.*]], %[[FLOW1]] ]
; CHECK-NEXT:    [[CC:%.*]] = icmp slt i32 [[IND]], [[X]]
; CHECK-NEXT:    [[CC_INV:%.*]] = xor i1 [[CC]], true
; CHECK-NEXT:    br i1 [[CC]], label %[[IF:.*]], label %[[FLOW:.*]]
; CHECK:       [[IF]]:
; CHECK-NEXT:    [[V_PTR:%.*]] = getelementptr float, ptr addrspace(1) [[P]], i32 [[IND]]
; CHECK-NEXT:    [[V_LOAD:%.*]] = load float, ptr addrspace(1) [[V_PTR]], align 4
; CHECK-NEXT:    [[V_IF:%.*]] = fadd float [[V_LOAD]], 1.000000e+00
; CHECK-NEXT:    [[CC2:%.*]] = icmp slt i32 [[IND]], [[Y]]
; CHECK-NEXT:    br label %[[FLOW]]
; CHECK:       [[FLOW]]:
; CHECK-NEXT:    [[TMP7]] = phi float [ [[V_IF]], %[[IF]] ], [ [[V_COPY]], %[[HEADER]] ]
; CHECK-NEXT:    [[TMP8]] = phi float [ [[V_IF]], %[[IF]] ], [ [[V_1]], %[[HEADER]] ]
; CHECK-NEXT:    [[TMP2:%.*]] = phi i1 [ [[CC2]], %[[IF]] ], [ [[CC_INV]], %[[HEADER]] ]
; CHECK-NEXT:    br i1 [[TMP2]], label %[[LATCH:.*]], label %[[FLOW1]]
; CHECK:       [[LATCH]]:
; CHECK-NEXT:    [[IND_INC:%.*]] = add i32 [[IND]], 1
; CHECK-NEXT:    [[CC3:%.*]] = icmp slt i32 [[IND]], [[Z]]
; CHECK-NEXT:    br label %[[FLOW1]]
; CHECK:       [[FLOW1]]:
; CHECK-NEXT:    [[TMP3]] = phi i32 [ [[IND_INC]], %[[LATCH]] ], [ undef, %[[FLOW]] ]
; CHECK-NEXT:    [[TMP4:%.*]] = phi i1 [ [[CC3]], %[[LATCH]] ], [ true, %[[FLOW]] ]
; CHECK-NEXT:    br i1 [[TMP4]], label %[[END:.*]], label %[[HEADER]]
; CHECK:       [[END]]:
; CHECK-NEXT:    [[PACKED0:%.*]] = insertelement <2 x float> poison, float [[TMP8]], i32 0
; CHECK-NEXT:    [[PACKED1:%.*]] = insertelement <2 x float> [[PACKED0]], float [[TMP7]], i32 1
; CHECK-NEXT:    ret <2 x float> [[PACKED1]]
;
entry:
  br label %header

header:
  %v.1 = phi float [ %v, %entry ], [ %v.2, %latch ]
  %v.copy = phi float [ 0.0, %entry ], [ %v.copy.2, %latch ]
  %ind = phi i32 [ 0, %entry], [ %ind.inc, %latch ]
  %cc = icmp slt i32 %ind, %x
  br i1 %cc, label %if, label %latch

if:
  %v.ptr = getelementptr float, ptr addrspace(1) %p, i32 %ind
  %v.load = load float, ptr addrspace(1) %v.ptr
  %v.if = fadd float %v.load, 1.0
  %cc2 = icmp slt i32 %ind, %y
  br i1 %cc2, label %latch, label %end

latch:
  %v.2 = phi float [ %v.1, %header ], [ %v.if, %if ]
  %v.copy.2 = phi float [ %v.copy, %header ], [ %v.if, %if ]
  %ind.inc = add i32 %ind, 1
  %cc3 = icmp slt i32 %ind, %z
  br i1 %cc3, label %end, label %header

end:
  %r = phi float [ %v.2, %latch ], [ %v.if, %if ]
  %r2 = phi float [ %v.copy.2, %latch ], [ %v.if, %if ]
  %packed0 = insertelement < 2 x float > poison, float %r, i32 0
  %packed1 = insertelement < 2 x float > %packed0, float %r2, i32 1
  ret < 2 x float> %packed1
}

attributes #0 = { nounwind }
