; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S %s -passes=scalarize-masked-mem-intrin -mtriple=amdgcn-amd-amdhsa | FileCheck %s

; COM: Test that, unlike on CPU targets, the mask doesn't get bitcast to a scalar,
; COM: since, on GPUs, each i1 takes up at least one register and so they should
; COM: be treated separately.

define void @scalarize_v2i32(ptr %p, <2 x i1> %mask, <2 x i32> %data) {
; CHECK-LABEL: define void @scalarize_v2i32(
; CHECK-SAME: ptr [[P:%.*]], <2 x i1> [[MASK:%.*]], <2 x i32> [[DATA:%.*]]) {
; CHECK-NEXT:    [[TMP1:%.*]] = extractelement <2 x i1> [[MASK]], i64 0
; CHECK-NEXT:    br i1 [[TMP1]], label %[[COND_STORE:.*]], label %[[ELSE:.*]]
; CHECK:       [[COND_STORE]]:
; CHECK-NEXT:    [[TMP3:%.*]] = extractelement <2 x i32> [[DATA]], i64 0
; CHECK-NEXT:    [[TMP4:%.*]] = getelementptr inbounds i32, ptr [[P]], i32 0
; CHECK-NEXT:    store i32 [[TMP3]], ptr [[TMP4]], align 4
; CHECK-NEXT:    br label %[[ELSE]]
; CHECK:       [[ELSE]]:
; CHECK-NEXT:    [[TMP5:%.*]] = extractelement <2 x i1> [[MASK]], i64 1
; CHECK-NEXT:    br i1 [[TMP5]], label %[[COND_STORE1:.*]], label %[[ELSE2:.*]]
; CHECK:       [[COND_STORE1]]:
; CHECK-NEXT:    [[TMP7:%.*]] = extractelement <2 x i32> [[DATA]], i64 1
; CHECK-NEXT:    [[TMP8:%.*]] = getelementptr inbounds i32, ptr [[P]], i32 1
; CHECK-NEXT:    store i32 [[TMP7]], ptr [[TMP8]], align 4
; CHECK-NEXT:    br label %[[ELSE2]]
; CHECK:       [[ELSE2]]:
; CHECK-NEXT:    ret void
;
  call void @llvm.masked.store.v2i32.p0(<2 x i32> %data, ptr %p, i32 128, <2 x i1> %mask)
  ret void
}

define void @scalarize_v2i32_splat_mask(ptr %p, <2 x i32> %data, i1 %mask) {
; CHECK-LABEL: define void @scalarize_v2i32_splat_mask(
; CHECK-SAME: ptr [[P:%.*]], <2 x i32> [[DATA:%.*]], i1 [[MASK:%.*]]) {
; CHECK-NEXT:    [[MASK_VEC:%.*]] = insertelement <2 x i1> poison, i1 [[MASK]], i32 0
; CHECK-NEXT:    [[MASK_SPLAT:%.*]] = shufflevector <2 x i1> [[MASK_VEC]], <2 x i1> poison, <2 x i32> zeroinitializer
; CHECK-NEXT:    [[MASK_SPLAT_FIRST:%.*]] = extractelement <2 x i1> [[MASK_SPLAT]], i64 0
; CHECK-NEXT:    br i1 [[MASK_SPLAT_FIRST]], label %[[COND_STORE:.*]], label %[[BB1:.*]]
; CHECK:       [[COND_STORE]]:
; CHECK-NEXT:    store <2 x i32> [[DATA]], ptr [[P]], align 8
; CHECK-NEXT:    br label %[[BB1]]
; CHECK:       [[BB1]]:
; CHECK-NEXT:    ret void
;
  %mask.vec = insertelement <2 x i1> poison, i1 %mask, i32 0
  %mask.splat = shufflevector <2 x i1> %mask.vec, <2 x i1> poison, <2 x i32> zeroinitializer
  call void @llvm.masked.store.v2i32.p0(<2 x i32> %data, ptr %p, i32 8, <2 x i1> %mask.splat)
  ret void
}

define void @scalarize_v2f16(ptr %p, <2 x i1> %mask, <2 x half> %data) {
; CHECK-LABEL: define void @scalarize_v2f16(
; CHECK-SAME: ptr [[P:%.*]], <2 x i1> [[MASK:%.*]], <2 x half> [[DATA:%.*]]) {
; CHECK-NEXT:    [[TMP1:%.*]] = extractelement <2 x i1> [[MASK]], i64 0
; CHECK-NEXT:    br i1 [[TMP1]], label %[[COND_STORE:.*]], label %[[ELSE:.*]]
; CHECK:       [[COND_STORE]]:
; CHECK-NEXT:    [[TMP3:%.*]] = extractelement <2 x half> [[DATA]], i64 0
; CHECK-NEXT:    [[TMP4:%.*]] = getelementptr inbounds half, ptr [[P]], i32 0
; CHECK-NEXT:    store half [[TMP3]], ptr [[TMP4]], align 2
; CHECK-NEXT:    br label %[[ELSE]]
; CHECK:       [[ELSE]]:
; CHECK-NEXT:    [[TMP5:%.*]] = extractelement <2 x i1> [[MASK]], i64 1
; CHECK-NEXT:    br i1 [[TMP5]], label %[[COND_STORE1:.*]], label %[[ELSE2:.*]]
; CHECK:       [[COND_STORE1]]:
; CHECK-NEXT:    [[TMP7:%.*]] = extractelement <2 x half> [[DATA]], i64 1
; CHECK-NEXT:    [[TMP8:%.*]] = getelementptr inbounds half, ptr [[P]], i32 1
; CHECK-NEXT:    store half [[TMP7]], ptr [[TMP8]], align 2
; CHECK-NEXT:    br label %[[ELSE2]]
; CHECK:       [[ELSE2]]:
; CHECK-NEXT:    ret void
;
  call void @llvm.masked.store.v2f16.p0(<2 x half> %data, ptr %p, i32 128, <2 x i1> %mask)
  ret void
}

define void @scalarize_v2i32_p3(ptr addrspace(3) %p, <2 x i1> %mask, <2 x i32> %data) {
; CHECK-LABEL: define void @scalarize_v2i32_p3(
; CHECK-SAME: ptr addrspace(3) [[P:%.*]], <2 x i1> [[MASK:%.*]], <2 x i32> [[DATA:%.*]]) {
; CHECK-NEXT:    [[TMP1:%.*]] = extractelement <2 x i1> [[MASK]], i64 0
; CHECK-NEXT:    br i1 [[TMP1]], label %[[COND_STORE:.*]], label %[[ELSE:.*]]
; CHECK:       [[COND_STORE]]:
; CHECK-NEXT:    [[TMP3:%.*]] = extractelement <2 x i32> [[DATA]], i64 0
; CHECK-NEXT:    [[TMP4:%.*]] = getelementptr inbounds i32, ptr addrspace(3) [[P]], i32 0
; CHECK-NEXT:    store i32 [[TMP3]], ptr addrspace(3) [[TMP4]], align 4
; CHECK-NEXT:    br label %[[ELSE]]
; CHECK:       [[ELSE]]:
; CHECK-NEXT:    [[TMP5:%.*]] = extractelement <2 x i1> [[MASK]], i64 1
; CHECK-NEXT:    br i1 [[TMP5]], label %[[COND_STORE1:.*]], label %[[ELSE2:.*]]
; CHECK:       [[COND_STORE1]]:
; CHECK-NEXT:    [[TMP7:%.*]] = extractelement <2 x i32> [[DATA]], i64 1
; CHECK-NEXT:    [[TMP8:%.*]] = getelementptr inbounds i32, ptr addrspace(3) [[P]], i32 1
; CHECK-NEXT:    store i32 [[TMP7]], ptr addrspace(3) [[TMP8]], align 4
; CHECK-NEXT:    br label %[[ELSE2]]
; CHECK:       [[ELSE2]]:
; CHECK-NEXT:    ret void
;
  call void @llvm.masked.store.v2i32.p3(<2 x i32> %data, ptr addrspace(3) %p, i32 128, <2 x i1> %mask)
  ret void
}

define void @scalarize_v2i32_lane_mask(ptr %p, <2 x i32> %data) {
; CHECK-LABEL: define void @scalarize_v2i32_lane_mask(
; CHECK-SAME: ptr [[P:%.*]], <2 x i32> [[DATA:%.*]]) {
; CHECK-NEXT:    [[ITEM_ID:%.*]] = call i32 @llvm.amdgcn.workitem.id.x()
; CHECK-NEXT:    [[MASK:%.*]] = icmp ult i32 [[ITEM_ID]], 16
; CHECK-NEXT:    [[MASK_VEC:%.*]] = insertelement <2 x i1> poison, i1 [[MASK]], i32 0
; CHECK-NEXT:    [[MASK_SPLAT:%.*]] = shufflevector <2 x i1> [[MASK_VEC]], <2 x i1> poison, <2 x i32> zeroinitializer
; CHECK-NEXT:    [[MASK_SPLAT_FIRST:%.*]] = extractelement <2 x i1> [[MASK_SPLAT]], i64 0
; CHECK-NEXT:    br i1 [[MASK_SPLAT_FIRST]], label %[[COND_STORE:.*]], label %[[BB1:.*]]
; CHECK:       [[COND_STORE]]:
; CHECK-NEXT:    store <2 x i32> [[DATA]], ptr [[P]], align 8
; CHECK-NEXT:    br label %[[BB1]]
; CHECK:       [[BB1]]:
; CHECK-NEXT:    ret void
;
  %item.id = call i32 @llvm.amdgcn.workitem.id.x()
  %mask = icmp ult i32 %item.id, 16
  %mask.vec = insertelement <2 x i1> poison, i1 %mask, i32 0
  %mask.splat = shufflevector <2 x i1> %mask.vec, <2 x i1> poison, <2 x i32> zeroinitializer
  call void @llvm.masked.store.v2i32.p0(<2 x i32> %data, ptr %p, i32 8, <2 x i1> %mask.splat)
  ret void
}

define void @scalarize_v2i32_group_mask(ptr %p, <2 x i32> %data) {
; CHECK-LABEL: define void @scalarize_v2i32_group_mask(
; CHECK-SAME: ptr [[P:%.*]], <2 x i32> [[DATA:%.*]]) {
; CHECK-NEXT:    [[ITEM_ID:%.*]] = call i32 @llvm.amdgcn.workgroup.id.x()
; CHECK-NEXT:    [[MASK:%.*]] = icmp ult i32 [[ITEM_ID]], 4
; CHECK-NEXT:    [[MASK_VEC:%.*]] = insertelement <2 x i1> poison, i1 [[MASK]], i32 0
; CHECK-NEXT:    [[MASK_SPLAT:%.*]] = shufflevector <2 x i1> [[MASK_VEC]], <2 x i1> poison, <2 x i32> zeroinitializer
; CHECK-NEXT:    [[MASK_SPLAT_FIRST:%.*]] = extractelement <2 x i1> [[MASK_SPLAT]], i64 0
; CHECK-NEXT:    br i1 [[MASK_SPLAT_FIRST]], label %[[COND_STORE:.*]], label %[[BB1:.*]]
; CHECK:       [[COND_STORE]]:
; CHECK-NEXT:    store <2 x i32> [[DATA]], ptr [[P]], align 8
; CHECK-NEXT:    br label %[[BB1]]
; CHECK:       [[BB1]]:
; CHECK-NEXT:    ret void
;
  %group.id = call i32 @llvm.amdgcn.workgroup.id.x()
  %mask = icmp ult i32 %group.id, 4
  %mask.vec = insertelement <2 x i1> poison, i1 %mask, i32 0
  %mask.splat = shufflevector <2 x i1> %mask.vec, <2 x i1> poison, <2 x i32> zeroinitializer
  call void @llvm.masked.store.v2i32.p0(<2 x i32> %data, ptr %p, i32 8, <2 x i1> %mask.splat)
  ret void
}

declare void @llvm.masked.store.v2i32.p0(<2 x i32>, ptr, i32, <2 x i1>)
declare void @llvm.masked.store.v2f16.p0(<2 x half>, ptr, i32, <2 x i1>)
declare void @llvm.masked.store.v2i32.p3(<2 x i32>, ptr addrspace(3), i32, <2 x i1>)
declare noundef i32 @llvm.amdgcn.workitem.id.x()
declare noundef i32 @llvm.amdgcn.workgroup.id.x()
