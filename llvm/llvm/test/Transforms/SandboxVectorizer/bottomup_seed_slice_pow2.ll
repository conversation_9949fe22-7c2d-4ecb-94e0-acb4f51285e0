; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -passes=sandbox-vectorizer -sbvec-vec-reg-bits=1024 -sbvec-allow-non-pow2=false -sbvec-passes="bottom-up-vec<>" %s -S | FileCheck %s --check-prefix=POW2
; RUN: opt -passes=sandbox-vectorizer -sbvec-vec-reg-bits=1024 -sbvec-allow-non-pow2=true -sbvec-passes="bottom-up-vec<>" %s -S | FileCheck %s --check-prefix=NON-POW2

define void @pow2(ptr %ptr, float %val) {
; POW2-LABEL: define void @pow2(
; POW2-SAME: ptr [[PTR:%.*]], float [[VAL:%.*]]) {
; POW2-NEXT:    [[PTR0:%.*]] = getelementptr float, ptr [[PTR]], i32 0
; POW2-NEXT:    [[PTR2:%.*]] = getelementptr float, ptr [[PTR]], i32 2
; POW2-NEXT:    [[VECL:%.*]] = load <2 x float>, ptr [[PTR0]], align 4
; POW2-NEXT:    [[LD2:%.*]] = load float, ptr [[PTR2]], align 4
; POW2-NEXT:    store <2 x float> [[VECL]], ptr [[PTR0]], align 4
; POW2-NEXT:    store float [[LD2]], ptr [[PTR2]], align 4
; POW2-NEXT:    ret void
;
; NON-POW2-LABEL: define void @pow2(
; NON-POW2-SAME: ptr [[PTR:%.*]], float [[VAL:%.*]]) {
; NON-POW2-NEXT:    [[PTR0:%.*]] = getelementptr float, ptr [[PTR]], i32 0
; NON-POW2-NEXT:    [[PACK2:%.*]] = load <3 x float>, ptr [[PTR0]], align 4
; NON-POW2-NEXT:    store <3 x float> [[PACK2]], ptr [[PTR0]], align 4
; NON-POW2-NEXT:    ret void
;
  %ptr0 = getelementptr float, ptr %ptr, i32 0
  %ptr1 = getelementptr float, ptr %ptr, i32 1
  %ptr2 = getelementptr float, ptr %ptr, i32 2

  %ld0 = load float, ptr %ptr0
  %ld1 = load float, ptr %ptr1
  %ld2 = load float, ptr %ptr2
  store float %ld0, ptr %ptr0
  store float %ld1, ptr %ptr1
  store float %ld2, ptr %ptr2
  ret void
}
