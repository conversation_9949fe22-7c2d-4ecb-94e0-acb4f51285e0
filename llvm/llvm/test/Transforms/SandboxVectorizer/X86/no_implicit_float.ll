; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -passes=sandbox-vectorizer -mtriple=x86_64-- -mattr=+avx %s -S | FileCheck %s

; Check that we don't vectorize a NoImplicitFloat function.
define void @no_implicit_float(ptr %ptr) noimplicitfloat {
; CHECK-LABEL: define void @no_implicit_float(
; CHECK-SAME: ptr [[PTR:%.*]]) #[[ATTR0:[0-9]+]] {
; CHECK-NEXT:    [[PTR0:%.*]] = getelementptr double, ptr [[PTR]], i32 0
; CHECK-NEXT:    [[PTR1:%.*]] = getelementptr double, ptr [[PTR]], i32 1
; CHECK-NEXT:    [[LD0:%.*]] = load double, ptr [[PTR0]], align 8
; CHECK-NEXT:    [[LD1:%.*]] = load double, ptr [[PTR1]], align 8
; CHECK-NEXT:    store double [[LD0]], ptr [[PTR0]], align 8
; CHECK-NEXT:    store double [[LD1]], ptr [[PTR1]], align 8
; CHECK-NEXT:    ret void
;
  %ptr0 = getelementptr double, ptr %ptr, i32 0
  %ptr1 = getelementptr double, ptr %ptr, i32 1
  %ld0 = load double, ptr %ptr0
  %ld1 = load double, ptr %ptr1
  store double %ld0, ptr %ptr0
  store double %ld1, ptr %ptr1
  ret void
}
