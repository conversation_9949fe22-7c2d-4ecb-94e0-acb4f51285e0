; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -passes=sandbox-vectorizer -sbvec-vec-reg-bits=1024 -sbvec-allow-non-pow2 -sbvec-passes="bottom-up-vec<>" %s -S | FileCheck %s

define void @pack_constants(ptr %ptr) {
; CHECK-LABEL: define void @pack_constants(
; CHECK-SAME: ptr [[PTR:%.*]]) {
; CHECK-NEXT:    [[PTR0:%.*]] = getelementptr i8, ptr [[PTR]], i32 0
; CHECK-NEXT:    store <2 x i8> <i8 0, i8 1>, ptr [[PTR0]], align 1
; CHECK-NEXT:    ret void
;
  %ptr0 = getelementptr i8, ptr %ptr, i32 0
  %ptr1 = getelementptr i8, ptr %ptr, i32 1
  store i8 0, ptr %ptr0
  store i8 1, ptr %ptr1
  ret void
}

; Make sure we don't generate bad IR when packing PHIs.
; NOTE: This test may become obsolete once we start vectorizing PHIs.
define void @packPHIs(ptr %ptr) {
; CHECK-LABEL: define void @packPHIs(
; CHECK-SAME: ptr [[PTR:%.*]]) {
; CHECK-NEXT:  [[ENTRY:.*]]:
; CHECK-NEXT:    br label %[[LOOP:.*]]
; CHECK:       [[LOOP]]:
; CHECK-NEXT:    [[PHI0:%.*]] = phi i8 [ 0, %[[ENTRY]] ], [ 1, %[[LOOP]] ]
; CHECK-NEXT:    [[PHI1:%.*]] = phi i8 [ 0, %[[ENTRY]] ], [ 1, %[[LOOP]] ]
; CHECK-NEXT:    [[PHI2:%.*]] = phi i8 [ 0, %[[ENTRY]] ], [ 1, %[[LOOP]] ]
; CHECK-NEXT:    [[PHI3:%.*]] = phi i8 [ 0, %[[ENTRY]] ], [ 1, %[[LOOP]] ]
; CHECK-NEXT:    [[PACK:%.*]] = insertelement <2 x i8> poison, i8 [[PHI0]], i32 0
; CHECK-NEXT:    [[PACK1:%.*]] = insertelement <2 x i8> [[PACK]], i8 [[PHI1]], i32 1
; CHECK-NEXT:    [[GEP0:%.*]] = getelementptr i8, ptr [[PTR]], i64 0
; CHECK-NEXT:    store <2 x i8> [[PACK1]], ptr [[GEP0]], align 1
; CHECK-NEXT:    br label %[[LOOP]]
; CHECK:       [[EXIT:.*:]]
; CHECK-NEXT:    ret void
;
entry:
  br label %loop

loop:
  %phi0 = phi i8 [0, %entry], [1, %loop]
  %phi1 = phi i8 [0, %entry], [1, %loop]
  %phi2 = phi i8 [0, %entry], [1, %loop]
  %phi3 = phi i8 [0, %entry], [1, %loop]
  %gep0 = getelementptr i8, ptr %ptr, i64 0
  %gep1 = getelementptr i8, ptr %ptr, i64 1
  store i8 %phi0, ptr %gep0
  store i8 %phi1, ptr %gep1
  br label %loop

exit:
  ret void
}

define void @packFromOtherBB(ptr %ptr, i8 %val) {
; CHECK-LABEL: define void @packFromOtherBB(
; CHECK-SAME: ptr [[PTR:%.*]], i8 [[VAL:%.*]]) {
; CHECK-NEXT:  [[ENTRY:.*]]:
; CHECK-NEXT:    [[ADD0:%.*]] = add i8 [[VAL]], 0
; CHECK-NEXT:    [[MUL1:%.*]] = mul i8 [[VAL]], 1
; CHECK-NEXT:    br label %[[LOOP:.*]]
; CHECK:       [[LOOP]]:
; CHECK-NEXT:    [[PHI0:%.*]] = phi i8 [ 0, %[[ENTRY]] ], [ 1, %[[LOOP]] ]
; CHECK-NEXT:    [[PHI1:%.*]] = phi i8 [ 0, %[[ENTRY]] ], [ 1, %[[LOOP]] ]
; CHECK-NEXT:    [[PACK:%.*]] = insertelement <2 x i8> poison, i8 [[ADD0]], i32 0
; CHECK-NEXT:    [[PACK1:%.*]] = insertelement <2 x i8> [[PACK]], i8 [[MUL1]], i32 1
; CHECK-NEXT:    [[GEP0:%.*]] = getelementptr i8, ptr [[PTR]], i64 0
; CHECK-NEXT:    store <2 x i8> [[PACK1]], ptr [[GEP0]], align 1
; CHECK-NEXT:    br label %[[LOOP]]
; CHECK:       [[EXIT:.*:]]
; CHECK-NEXT:    ret void
;
entry:
  %add0 = add i8 %val, 0
  %mul1 = mul i8 %val, 1
  br label %loop

loop:
  %phi0 = phi i8 [0, %entry], [1, %loop]
  %phi1 = phi i8 [0, %entry], [1, %loop]
  %gep0 = getelementptr i8, ptr %ptr, i64 0
  %gep1 = getelementptr i8, ptr %ptr, i64 1
  store i8 %add0, ptr %gep0
  store i8 %mul1, ptr %gep1
  br label %loop

exit:
  ret void
}

define void @packFromDiffBBs(ptr %ptr, i8 %v) {
; CHECK-LABEL: define void @packFromDiffBBs(
; CHECK-SAME: ptr [[PTR:%.*]], i8 [[V:%.*]]) {
; CHECK-NEXT:  [[ENTRY:.*:]]
; CHECK-NEXT:    [[ADD0:%.*]] = add i8 [[V]], 1
; CHECK-NEXT:    br label %[[BB:.*]]
; CHECK:       [[BB]]:
; CHECK-NEXT:    [[ADD1:%.*]] = add i8 [[V]], 2
; CHECK-NEXT:    [[PACK:%.*]] = insertelement <2 x i8> poison, i8 [[ADD0]], i32 0
; CHECK-NEXT:    [[PACK1:%.*]] = insertelement <2 x i8> [[PACK]], i8 [[ADD1]], i32 1
; CHECK-NEXT:    [[GEP0:%.*]] = getelementptr i8, ptr [[PTR]], i64 0
; CHECK-NEXT:    store <2 x i8> [[PACK1]], ptr [[GEP0]], align 1
; CHECK-NEXT:    ret void
;
entry:
  %add0 = add i8 %v, 1
  br label %bb

bb:
  %add1 = add i8 %v, 2
  %gep0 = getelementptr i8, ptr %ptr, i64 0
  %gep1 = getelementptr i8, ptr %ptr, i64 1
  store i8 %add0, ptr %gep0
  store i8 %add1, ptr %gep1
  ret void
}
