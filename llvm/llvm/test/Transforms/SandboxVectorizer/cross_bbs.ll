; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -passes=sandbox-vectorizer -sbvec-vec-reg-bits=1024 -sbvec-allow-non-pow2 -sbvec-passes="bottom-up-vec<>" %s -S | FileCheck %s

define void @cross_bbs(ptr %ptr) {
; CHECK-LABEL: define void @cross_bbs(
; CHECK-SAME: ptr [[PTR:%.*]]) {
; CHECK-NEXT:    [[PTR0:%.*]] = getelementptr i8, ptr [[PTR]], i32 0
; CHECK-NEXT:    [[PTR1:%.*]] = getelementptr i8, ptr [[PTR]], i32 1
; CHECK-NEXT:    [[L0:%.*]] = load i8, ptr [[PTR0]], align 1
; CHECK-NEXT:    [[L1:%.*]] = load i8, ptr [[PTR1]], align 1
; CHECK-NEXT:    br label %[[BB:.*]]
; CHECK:       [[BB]]:
; CHECK-NEXT:    [[PACK:%.*]] = insertelement <2 x i8> poison, i8 [[L0]], i32 0
; CHECK-NEXT:    [[PACK1:%.*]] = insertelement <2 x i8> [[PACK]], i8 [[L1]], i32 1
; CHECK-NEXT:    store <2 x i8> [[PACK1]], ptr [[PTR0]], align 1
; CHECK-NEXT:    ret void
;
  %ptr0 = getelementptr i8, ptr %ptr, i32 0
  %ptr1 = getelementptr i8, ptr %ptr, i32 1
  %l0 = load i8, ptr %ptr0
  %l1 = load i8, ptr %ptr1
  br label %bb

bb:
  store i8 %l0, ptr %ptr0
  store i8 %l1, ptr %ptr1
  ret void
}
