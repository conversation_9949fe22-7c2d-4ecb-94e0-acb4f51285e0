; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -passes=sandbox-vectorizer -sbvec-vec-reg-bits=1024 -sbvec-allow-non-pow2 -sbvec-passes="bottom-up-vec<>" %s -S | FileCheck %s


declare void @foo()
define void @slice_seeds(ptr %ptr, float %val) {
; CHECK-LABEL: define void @slice_seeds(
; CHECK-SAME: ptr [[PTR:%.*]], float [[VAL:%.*]]) {
; CHECK-NEXT:    [[PTR0:%.*]] = getelementptr float, ptr [[PTR]], i32 0
; CHECK-NEXT:    [[PTR2:%.*]] = getelementptr float, ptr [[PTR]], i32 2
; CHECK-NEXT:    [[LD2:%.*]] = load float, ptr [[PTR2]], align 4
; CHECK-NEXT:    store float [[LD2]], ptr [[PTR2]], align 4
; CHECK-NEXT:    call void @foo()
; CHECK-NEXT:    [[VECL:%.*]] = load <2 x float>, ptr [[PTR0]], align 4
; CHECK-NEXT:    store <2 x float> [[VECL]], ptr [[PTR0]], align 4
; CHECK-NEXT:    ret void
;
  %ptr0 = getelementptr float, ptr %ptr, i32 0
  %ptr1 = getelementptr float, ptr %ptr, i32 1
  %ptr2 = getelementptr float, ptr %ptr, i32 2

  %ld2 = load float, ptr %ptr2
  store float %ld2, ptr %ptr2
  ; This call blocks scheduling of all 3 stores.
  call void @foo()

  %ld0 = load float, ptr %ptr0
  %ld1 = load float, ptr %ptr1
  store float %ld0, ptr %ptr0
  store float %ld1, ptr %ptr1
  ret void
}
