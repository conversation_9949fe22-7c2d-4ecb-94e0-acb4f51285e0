; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt < %s -passes='sroa<preserve-cfg>' -S | FileCheck %s --check-prefixes=CHECK,CHECK-PRESERVE-CFG
; RUN: opt < %s -passes='sroa<modify-cfg>' -S | FileCheck %s --check-prefixes=CHECK,CHECK-MODIFY-CFG

%struct.Value = type { %union.anon }
%union.anon = type { <32 x i8> }

@A = dso_local global i64 0, align 8

; Make sure that sroa does not crash when dealing with an invalid vector
; element type.
define void @foo() {
; CHECK-LABEL: @foo(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[REF_TMP_I:%.*]] = alloca [[STRUCT_VALUE:%.*]], align 32
; CHECK-NEXT:    call void @value_create(ptr sret([[STRUCT_VALUE]]) align 32 [[REF_TMP_I]])
; CHECK-NEXT:    [[CALL_I:%.*]] = call align 32 ptr @value_set_type(ptr align 32 [[REF_TMP_I]])
; CHECK-NEXT:    [[TMP0:%.*]] = load <32 x i8>, ptr [[CALL_I]], align 32
; CHECK-NEXT:    [[REF_TMP_SROA_0_0_VEC_EXTRACT:%.*]] = shufflevector <32 x i8> [[TMP0]], <32 x i8> poison, <8 x i32> <i32 0, i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7>
; CHECK-NEXT:    [[TMP1:%.*]] = bitcast <8 x i8> [[REF_TMP_SROA_0_0_VEC_EXTRACT]] to <1 x i64>
; CHECK-NEXT:    [[TMP2:%.*]] = call <1 x i64> @llvm.x86.sse.pshuf.w(<1 x i64> [[TMP1]], i8 0)
; CHECK-NEXT:    store <1 x i64> [[TMP2]], ptr @A, align 8
; CHECK-NEXT:    ret void
;
entry:
  %ref.tmp.i = alloca %struct.Value, align 32
  %ref.tmp = alloca %struct.Value, align 32
  call void @value_create(ptr sret(%struct.Value) align 32 %ref.tmp.i)
  %call.i = call align 32 ptr @value_set_type(ptr align 32 %ref.tmp.i)
  %0 = load <32 x i8>, ptr %call.i, align 32
  store <32 x i8> %0, ptr %ref.tmp, align 32
  %1 = load <1 x i64>, ptr %ref.tmp, align 32
  %2 = call <1 x i64> @llvm.x86.sse.pshuf.w(<1 x i64> %1, i8 0)
  store <1 x i64> %2, ptr @A, align 8
  ret void
}

declare <1 x i64> @llvm.x86.sse.pshuf.w(<1 x i64>, i8 immarg)

declare dso_local void @value_create(ptr sret(%struct.Value) align 32)

declare dso_local align 32 ptr @value_set_type(ptr align 32)

;; NOTE: These prefixes are unused and the list is autogenerated. Do not add tests below this line:
; CHECK-MODIFY-CFG: {{.*}}
; CHECK-PRESERVE-CFG: {{.*}}
