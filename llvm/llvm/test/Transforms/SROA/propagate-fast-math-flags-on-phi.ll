; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 4
; RUN: opt < %s -passes='sroa' -S | FileCheck %s
define double @phi_with_nsz(double %x) "no-signed-zeros-fp-math"="true" {
; CHECK-LABEL: define double @phi_with_nsz(
; CHECK-SAME: double [[X:%.*]]) #[[ATTR0:[0-9]+]] {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[CMP:%.*]] = fcmp olt double [[X]], 0.000000e+00
; CHECK-NEXT:    br i1 [[CMP]], label [[IF_THEN:%.*]], label [[RETURN:%.*]]
; CHECK:       if.then:
; CHECK-NEXT:    [[FNEG:%.*]] = fneg double [[X]]
; CHECK-NEXT:    br label [[RETURN]]
; CHECK:       return:
; CHECK-NEXT:    [[X_ADDR_0:%.*]] = phi nsz double [ [[FNEG]], [[IF_THEN]] ], [ undef, [[ENTRY:%.*]] ]
; CHECK-NEXT:    ret double [[X_ADDR_0]]
entry:
  %x.addr = alloca double
  %cmp = fcmp olt double %x, 0.0
  br i1 %cmp, label %if.then, label %return

if.then:                         ; preds = %entry
  %fneg = fneg double %x
  store double %fneg, ptr %x.addr
  br label %return

return:                          ; preds = %entry,%if.then
  %retval = load double, ptr %x.addr
  ret double %retval
}

define <2 x double> @vector_phi_with_nsz(<2 x double> %x, i1 %cmp, <2 x double> %a, <2 x double> %b) "no-signed-zeros-fp-math"="true" {
; CHECK-LABEL: define <2 x double> @vector_phi_with_nsz(
; CHECK-SAME: <2 x double> [[X:%.*]], i1 [[CMP:%.*]], <2 x double> [[A:%.*]], <2 x double> [[B:%.*]]) #[[ATTR0]] {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    br i1 [[CMP]], label [[IF_THEN:%.*]], label [[RETURN:%.*]]
; CHECK:       if.then:
; CHECK-NEXT:    br label [[RETURN]]
; CHECK:       return:
; CHECK-NEXT:    [[X_ADDR_0:%.*]] = phi nsz <2 x double> [ [[B]], [[IF_THEN]] ], [ [[A]], [[ENTRY:%.*]] ]
; CHECK-NEXT:    ret <2 x double> [[X_ADDR_0]]
entry:
  %x.addr = alloca <2 x double>
  store <2 x double> %a, ptr %x.addr
  br i1 %cmp, label %if.then, label %return

if.then:                         ; preds = %entry
  store <2 x double> %b, ptr %x.addr
  br label %return

return:                          ; preds = %entry,%if.then
  %retval = load <2 x double>, ptr %x.addr
  ret <2 x double> %retval
}

define double @phi_without_nsz(double %x) "no-signed-zeros-fp-math"="false" {
; CHECK-LABEL: define double @phi_without_nsz(
; CHECK-SAME: double [[X:%.*]]) #[[ATTR1:[0-9]+]] {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[CMP:%.*]] = fcmp olt double [[X]], 0.000000e+00
; CHECK-NEXT:    br i1 [[CMP]], label [[IF_THEN:%.*]], label [[RETURN:%.*]]
; CHECK:       if.then:
; CHECK-NEXT:    [[FNEG:%.*]] = fneg double [[X]]
; CHECK-NEXT:    br label [[RETURN]]
; CHECK:       return:
; CHECK-NEXT:    [[X_ADDR_0:%.*]] = phi double [ [[FNEG]], [[IF_THEN]] ], [ undef, [[ENTRY:%.*]] ]
; CHECK-NEXT:    ret double [[X_ADDR_0]]
entry:
  %x.addr = alloca double
  %cmp = fcmp olt double %x, 0.0
  br i1 %cmp, label %if.then, label %return

if.then:                         ; preds = %entry
  %fneg = fneg double %x
  store double %fneg, ptr %x.addr
  br label %return

return:                          ; preds = %entry,%if.then
  %retval = load double, ptr %x.addr
  ret double %retval
}
