; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -S -passes='sroa<preserve-cfg>' < %s | FileCheck %s --check-prefixes=CHECK,CHECK-PRESERVE-CFG
; RUN: opt -S -passes='sroa<modify-cfg>' < %s | FileCheck %s --check-prefixes=CHECK,CHECK-MODIFY-CFG

%pair = type { i32, i32 }

define i32 @test_sroa_select_gep(i1 %cond) {
; CHECK-LABEL: @test_sroa_select_gep(
; CHECK-NEXT:  bb:
; CHECK-NEXT:    [[LOAD_SROA_SPECULATED:%.*]] = select i1 [[COND:%.*]], i32 1, i32 2
; CHECK-NEXT:    ret i32 [[LOAD_SROA_SPECULATED]]
;
bb:
  %a = alloca %pair, align 4
  %b = alloca %pair, align 4
  %gep_a = getelementptr inbounds %pair, ptr %a, i32 0, i32 1
  %gep_b = getelementptr inbounds %pair, ptr %b, i32 0, i32 1
  store i32 1, ptr %gep_a, align 4
  store i32 2, ptr %gep_b, align 4
  %select = select i1 %cond, ptr %a, ptr %b
  %gep = getelementptr inbounds %pair, ptr %select, i32 0, i32 1
  %load = load i32, ptr %gep, align 4
  ret i32 %load
}

define i32 @test_sroa_select_gep_non_inbound(i1 %cond) {
; CHECK-LABEL: @test_sroa_select_gep_non_inbound(
; CHECK-NEXT:  bb:
; CHECK-NEXT:    [[LOAD_SROA_SPECULATED:%.*]] = select i1 [[COND:%.*]], i32 1, i32 2
; CHECK-NEXT:    ret i32 [[LOAD_SROA_SPECULATED]]
;
bb:
  %a = alloca %pair, align 4
  %b = alloca %pair, align 4
  %gep_a = getelementptr %pair, ptr %a, i32 0, i32 1
  %gep_b = getelementptr %pair, ptr %b, i32 0, i32 1
  store i32 1, ptr %gep_a, align 4
  store i32 2, ptr %gep_b, align 4
  %select = select i1 %cond, ptr %a, ptr %b
  %gep = getelementptr %pair, ptr %select, i32 0, i32 1
  %load = load i32, ptr %gep, align 4
  ret i32 %load
}

define i32 @test_sroa_select_gep_volatile_load(i1 %cond) {
; CHECK-LABEL: @test_sroa_select_gep_volatile_load(
; CHECK-NEXT:  bb:
; CHECK-NEXT:    [[A_SROA_0:%.*]] = alloca i32, align 4
; CHECK-NEXT:    [[A_SROA_2:%.*]] = alloca i32, align 4
; CHECK-NEXT:    [[B_SROA_0:%.*]] = alloca i32, align 4
; CHECK-NEXT:    [[B_SROA_2:%.*]] = alloca i32, align 4
; CHECK-NEXT:    store i32 11, ptr [[A_SROA_0]], align 4
; CHECK-NEXT:    store i32 12, ptr [[B_SROA_0]], align 4
; CHECK-NEXT:    store i32 21, ptr [[A_SROA_2]], align 4
; CHECK-NEXT:    store i32 22, ptr [[B_SROA_2]], align 4
; CHECK-NEXT:    [[SELECT:%.*]] = select i1 [[COND:%.*]], ptr [[A_SROA_0]], ptr [[B_SROA_0]]
; CHECK-NEXT:    [[LOAD1:%.*]] = load volatile i32, ptr [[SELECT]], align 4
; CHECK-NEXT:    [[SELECT_SROA_SEL:%.*]] = select i1 [[COND]], ptr [[A_SROA_2]], ptr [[B_SROA_2]]
; CHECK-NEXT:    [[LOAD2:%.*]] = load volatile i32, ptr [[SELECT_SROA_SEL]], align 4
; CHECK-NEXT:    [[ADD:%.*]] = add i32 [[LOAD1]], [[LOAD2]]
; CHECK-NEXT:    ret i32 [[ADD]]
;
bb:
  %a = alloca %pair, align 4
  %b = alloca %pair, align 4
  store i32 11, ptr %a, align 4
  store i32 12, ptr %b, align 4
  %gep_a1 = getelementptr inbounds %pair, ptr %a, i32 0, i32 1
  %gep_b1 = getelementptr inbounds %pair, ptr %b, i32 0, i32 1
  store i32 21, ptr %gep_a1, align 4
  store i32 22, ptr %gep_b1, align 4
  %select = select i1 %cond, ptr %a, ptr %b
  %load1 = load volatile i32, ptr %select, align 4
  %gep2 = getelementptr inbounds %pair, ptr %select, i32 0, i32 1
  %load2 = load volatile i32, ptr %gep2, align 4
  %add = add i32 %load1, %load2
  ret i32 %add
}

define i32 @test_sroa_select_gep_poison(i1 %cond) {
; CHECK-PRESERVE-CFG-LABEL: @test_sroa_select_gep_poison(
; CHECK-PRESERVE-CFG-NEXT:  bb:
; CHECK-PRESERVE-CFG-NEXT:    [[A_SROA_0:%.*]] = alloca i32, align 4
; CHECK-PRESERVE-CFG-NEXT:    [[SELECT_SROA_SEL:%.*]] = select i1 [[COND:%.*]], ptr [[A_SROA_0]], ptr poison
; CHECK-PRESERVE-CFG-NEXT:    [[LOAD:%.*]] = load i32, ptr [[SELECT_SROA_SEL]], align 4
; CHECK-PRESERVE-CFG-NEXT:    ret i32 [[LOAD]]
;
; CHECK-MODIFY-CFG-LABEL: @test_sroa_select_gep_poison(
; CHECK-MODIFY-CFG-NEXT:  bb:
; CHECK-MODIFY-CFG-NEXT:    br i1 [[COND:%.*]], label [[BB_CONT:%.*]], label [[BB_ELSE:%.*]]
; CHECK-MODIFY-CFG:       bb.else:
; CHECK-MODIFY-CFG-NEXT:    [[LOAD_ELSE_VAL:%.*]] = load i32, ptr poison, align 4
; CHECK-MODIFY-CFG-NEXT:    br label [[BB_CONT]]
; CHECK-MODIFY-CFG:       bb.cont:
; CHECK-MODIFY-CFG-NEXT:    [[LOAD:%.*]] = phi i32 [ undef, [[BB:%.*]] ], [ [[LOAD_ELSE_VAL]], [[BB_ELSE]] ]
; CHECK-MODIFY-CFG-NEXT:    ret i32 [[LOAD]]
;
bb:
  %a = alloca %pair, align 4
  %select = select i1 %cond, ptr %a, ptr poison
  %gep = getelementptr inbounds %pair, ptr %select, i32 0, i32 1
  %load = load i32, ptr %gep, align 4
  ret i32 %load
}

define i32 @test_sroa_gep_select_gep(i1 %cond) {
; CHECK-LABEL: @test_sroa_gep_select_gep(
; CHECK-NEXT:  bb:
; CHECK-NEXT:    [[A_SROA_0:%.*]] = alloca i32, align 4
; CHECK-NEXT:    [[B_SROA_0:%.*]] = alloca i32, align 4
; CHECK-NEXT:    store i32 1, ptr [[A_SROA_0]], align 4
; CHECK-NEXT:    store i32 2, ptr [[B_SROA_0]], align 4
; CHECK-NEXT:    [[SELECT:%.*]] = select i1 [[COND:%.*]], ptr [[A_SROA_0]], ptr [[B_SROA_0]]
; CHECK-NEXT:    [[SELECT2:%.*]] = select i1 [[COND]], ptr [[SELECT]], ptr [[A_SROA_0]]
; CHECK-NEXT:    [[LOAD:%.*]] = load i32, ptr [[SELECT2]], align 4
; CHECK-NEXT:    ret i32 [[LOAD]]
;
bb:
  %a = alloca %pair, align 4
  %b = alloca %pair, align 4
  %gep_a = getelementptr inbounds %pair, ptr %a, i32 0, i32 1
  %gep_b = getelementptr inbounds %pair, ptr %b, i32 0, i32 1
  store i32 1, ptr %gep_a, align 4
  store i32 2, ptr %gep_b, align 4
  %select = select i1 %cond, ptr %gep_a, ptr %gep_b
  %select2 = select i1 %cond, ptr %select, ptr %gep_a
  %load = load i32, ptr %select2, align 4
  ret i32 %load
}

define i32 @test_sroa_gep_select_gep_nonconst_idx(i1 %cond, i32 %idx) {
; CHECK-LABEL: @test_sroa_gep_select_gep_nonconst_idx(
; CHECK-NEXT:  bb:
; CHECK-NEXT:    [[A:%.*]] = alloca [[PAIR:%.*]], align 4
; CHECK-NEXT:    [[B:%.*]] = alloca [[PAIR]], align 4
; CHECK-NEXT:    [[GEP_A:%.*]] = getelementptr inbounds [[PAIR]], ptr [[A]], i32 0, i32 1
; CHECK-NEXT:    [[GEP_B:%.*]] = getelementptr inbounds [[PAIR]], ptr [[B]], i32 0, i32 1
; CHECK-NEXT:    store i32 1, ptr [[GEP_A]], align 4
; CHECK-NEXT:    store i32 2, ptr [[GEP_B]], align 4
; CHECK-NEXT:    [[SELECT:%.*]] = select i1 [[COND:%.*]], ptr [[A]], ptr [[B]]
; CHECK-NEXT:    [[GEP:%.*]] = getelementptr inbounds [[PAIR]], ptr [[SELECT]], i32 [[IDX:%.*]], i32 1
; CHECK-NEXT:    [[LOAD:%.*]] = load i32, ptr [[GEP]], align 4
; CHECK-NEXT:    ret i32 [[LOAD]]
;
bb:
  %a = alloca %pair, align 4
  %b = alloca %pair, align 4
  %gep_a = getelementptr inbounds %pair, ptr %a, i32 0, i32 1
  %gep_b = getelementptr inbounds %pair, ptr %b, i32 0, i32 1
  store i32 1, ptr %gep_a, align 4
  store i32 2, ptr %gep_b, align 4
  %select = select i1 %cond, ptr %a, ptr %b
  %gep = getelementptr inbounds %pair, ptr %select, i32 %idx, i32 1
  %load = load i32, ptr %gep, align 4
  ret i32 %load
}

; Test gep of index select unfolding on an alloca that is splittable, but not
; promotable. The allocas here will be optimized away by subsequent passes.
define i32 @test_select_idx_memcpy(i1 %c, ptr %p) {
; CHECK-LABEL: @test_select_idx_memcpy(
; CHECK-NEXT:    [[ALLOCA_SROA_0:%.*]] = alloca [4 x i8], align 8
; CHECK-NEXT:    [[ALLOCA_SROA_2:%.*]] = alloca [20 x i8], align 4
; CHECK-NEXT:    [[ALLOCA_SROA_22:%.*]] = alloca [4 x i8], align 8
; CHECK-NEXT:    [[ALLOCA_SROA_3:%.*]] = alloca [132 x i8], align 4
; CHECK-NEXT:    call void @llvm.memcpy.p0.p0.i64(ptr align 8 [[ALLOCA_SROA_0]], ptr align 1 [[P:%.*]], i64 4, i1 false)
; CHECK-NEXT:    [[ALLOCA_SROA_2_0_P_SROA_IDX:%.*]] = getelementptr inbounds i8, ptr [[P]], i64 4
; CHECK-NEXT:    call void @llvm.memcpy.p0.p0.i64(ptr align 4 [[ALLOCA_SROA_2]], ptr align 1 [[ALLOCA_SROA_2_0_P_SROA_IDX]], i64 20, i1 false)
; CHECK-NEXT:    [[ALLOCA_SROA_22_0_P_SROA_IDX:%.*]] = getelementptr inbounds i8, ptr [[P]], i64 24
; CHECK-NEXT:    call void @llvm.memcpy.p0.p0.i64(ptr align 8 [[ALLOCA_SROA_22]], ptr align 1 [[ALLOCA_SROA_22_0_P_SROA_IDX]], i64 4, i1 false)
; CHECK-NEXT:    [[ALLOCA_SROA_3_0_P_SROA_IDX:%.*]] = getelementptr inbounds i8, ptr [[P]], i64 28
; CHECK-NEXT:    call void @llvm.memcpy.p0.p0.i64(ptr align 4 [[ALLOCA_SROA_3]], ptr align 1 [[ALLOCA_SROA_3_0_P_SROA_IDX]], i64 132, i1 false)
; CHECK-NEXT:    [[IDX:%.*]] = select i1 [[C:%.*]], i64 24, i64 0
; CHECK-NEXT:    [[IDX_SROA_SEL:%.*]] = select i1 [[C]], ptr [[ALLOCA_SROA_22]], ptr [[ALLOCA_SROA_0]]
; CHECK-NEXT:    [[RES:%.*]] = load i32, ptr [[IDX_SROA_SEL]], align 4
; CHECK-NEXT:    ret i32 [[RES]]
;
  %alloca = alloca [20 x i64], align 8
  call void @llvm.memcpy.p0.p0.i64(ptr %alloca, ptr %p, i64 160, i1 false)
  %idx = select i1 %c, i64 24, i64 0
  %gep = getelementptr inbounds i8, ptr %alloca, i64 %idx
  %res = load i32, ptr %gep, align 4
  ret i32 %res
}

; Test gep of index select unfolding on an alloca that is splittable and
; promotable.
define i32 @test_select_idx_mem2reg(i1 %c) {
; CHECK-LABEL: @test_select_idx_mem2reg(
; CHECK-NEXT:    [[IDX:%.*]] = select i1 [[C:%.*]], i64 24, i64 0
; CHECK-NEXT:    [[RES_SROA_SPECULATED:%.*]] = select i1 [[C]], i32 2, i32 1
; CHECK-NEXT:    ret i32 [[RES_SROA_SPECULATED]]
;
  %alloca = alloca [20 x i64], align 8
  store i32 1, ptr %alloca
  %gep1 = getelementptr inbounds i8, ptr %alloca, i64 24
  store i32 2, ptr %gep1
  %idx = select i1 %c, i64 24, i64 0
  %gep2 = getelementptr inbounds i8, ptr %alloca, i64 %idx
  %res = load i32, ptr %gep2, align 4
  ret i32 %res
}

; Test gep of index select unfolding on an alloca that escaped, and as such
; is not splittable or promotable.
; FIXME: Ideally, no transform would take place in this case.
define i32 @test_select_idx_escaped(i1 %c, ptr %p) {
; CHECK-LABEL: @test_select_idx_escaped(
; CHECK-NEXT:    [[ALLOCA:%.*]] = alloca [20 x i64], align 8
; CHECK-NEXT:    store ptr [[ALLOCA]], ptr [[P:%.*]], align 8
; CHECK-NEXT:    store i32 1, ptr [[ALLOCA]], align 4
; CHECK-NEXT:    [[GEP1:%.*]] = getelementptr inbounds i8, ptr [[ALLOCA]], i64 24
; CHECK-NEXT:    store i32 2, ptr [[GEP1]], align 4
; CHECK-NEXT:    [[IDX:%.*]] = select i1 [[C:%.*]], i64 24, i64 0
; CHECK-NEXT:    [[DOTSROA_GEP:%.*]] = getelementptr inbounds i8, ptr [[ALLOCA]], i64 24
; CHECK-NEXT:    [[DOTSROA_GEP1:%.*]] = getelementptr inbounds i8, ptr [[ALLOCA]], i64 0
; CHECK-NEXT:    [[IDX_SROA_SEL:%.*]] = select i1 [[C]], ptr [[DOTSROA_GEP]], ptr [[DOTSROA_GEP1]]
; CHECK-NEXT:    [[RES:%.*]] = load i32, ptr [[IDX_SROA_SEL]], align 4
; CHECK-NEXT:    ret i32 [[RES]]
;
  %alloca = alloca [20 x i64], align 8
  store ptr %alloca, ptr %p
  store i32 1, ptr %alloca
  %gep1 = getelementptr inbounds i8, ptr %alloca, i64 24
  store i32 2, ptr %gep1
  %idx = select i1 %c, i64 24, i64 0
  %gep2 = getelementptr inbounds i8, ptr %alloca, i64 %idx
  %res = load i32, ptr %gep2, align 4
  ret i32 %res
}

; FIXME: Should we allow recursive select unfolding if all the leaves are
; constants?
define i32 @test_select_idx_nested(i1 %c, i1 %c2) {
; CHECK-LABEL: @test_select_idx_nested(
; CHECK-NEXT:    [[ALLOCA:%.*]] = alloca [20 x i64], align 8
; CHECK-NEXT:    store i32 1, ptr [[ALLOCA]], align 4
; CHECK-NEXT:    [[GEP1:%.*]] = getelementptr inbounds i8, ptr [[ALLOCA]], i64 8
; CHECK-NEXT:    store i32 2, ptr [[GEP1]], align 4
; CHECK-NEXT:    [[GEP2:%.*]] = getelementptr inbounds i8, ptr [[ALLOCA]], i64 24
; CHECK-NEXT:    store i32 3, ptr [[GEP2]], align 4
; CHECK-NEXT:    [[IDX1:%.*]] = select i1 [[C:%.*]], i64 24, i64 0
; CHECK-NEXT:    [[IDX2:%.*]] = select i1 [[C2:%.*]], i64 [[IDX1]], i64 8
; CHECK-NEXT:    [[GEP3:%.*]] = getelementptr inbounds i8, ptr [[ALLOCA]], i64 [[IDX2]]
; CHECK-NEXT:    [[RES:%.*]] = load i32, ptr [[GEP3]], align 4
; CHECK-NEXT:    ret i32 [[RES]]
;
  %alloca = alloca [20 x i64], align 8
  store i32 1, ptr %alloca
  %gep1 = getelementptr inbounds i8, ptr %alloca, i64 8
  store i32 2, ptr %gep1
  %gep2 = getelementptr inbounds i8, ptr %alloca, i64 24
  store i32 3, ptr %gep2
  %idx1 = select i1 %c, i64 24, i64 0
  %idx2 = select i1 %c2, i64 %idx1, i64 8
  %gep3 = getelementptr inbounds i8, ptr %alloca, i64 %idx2
  %res = load i32, ptr %gep3, align 4
  ret i32 %res
}

; The following cases involve non-constant indices and should not be
; transformed.

define i32 @test_select_idx_not_constant1(i1 %c, ptr %p, i64 %arg) {
; CHECK-LABEL: @test_select_idx_not_constant1(
; CHECK-NEXT:    [[ALLOCA:%.*]] = alloca [20 x i64], align 8
; CHECK-NEXT:    call void @llvm.memcpy.p0.p0.i64(ptr [[ALLOCA]], ptr [[P:%.*]], i64 160, i1 false)
; CHECK-NEXT:    [[IDX:%.*]] = select i1 [[C:%.*]], i64 24, i64 [[ARG:%.*]]
; CHECK-NEXT:    [[GEP:%.*]] = getelementptr inbounds i8, ptr [[ALLOCA]], i64 [[IDX]]
; CHECK-NEXT:    [[RES:%.*]] = load i32, ptr [[GEP]], align 4
; CHECK-NEXT:    ret i32 [[RES]]
;
  %alloca = alloca [20 x i64], align 8
  call void @llvm.memcpy.p0.p0.i64(ptr %alloca, ptr %p, i64 160, i1 false)
  %idx = select i1 %c, i64 24, i64 %arg
  %gep = getelementptr inbounds i8, ptr %alloca, i64 %idx
  %res = load i32, ptr %gep, align 4
  ret i32 %res
}

define i32 @test_select_idx_not_constant2(i1 %c, ptr %p, i64 %arg) {
; CHECK-LABEL: @test_select_idx_not_constant2(
; CHECK-NEXT:    [[ALLOCA:%.*]] = alloca [20 x i64], align 8
; CHECK-NEXT:    call void @llvm.memcpy.p0.p0.i64(ptr [[ALLOCA]], ptr [[P:%.*]], i64 160, i1 false)
; CHECK-NEXT:    [[IDX:%.*]] = select i1 [[C:%.*]], i64 [[ARG:%.*]], i64 0
; CHECK-NEXT:    [[GEP:%.*]] = getelementptr inbounds i8, ptr [[ALLOCA]], i64 [[IDX]]
; CHECK-NEXT:    [[RES:%.*]] = load i32, ptr [[GEP]], align 4
; CHECK-NEXT:    ret i32 [[RES]]
;
  %alloca = alloca [20 x i64], align 8
  call void @llvm.memcpy.p0.p0.i64(ptr %alloca, ptr %p, i64 160, i1 false)
  %idx = select i1 %c, i64 %arg, i64 0
  %gep = getelementptr inbounds i8, ptr %alloca, i64 %idx
  %res = load i32, ptr %gep, align 4
  ret i32 %res
}

define i32 @test_select_idx_not_constant3(i1 %c, ptr %p, i64 %arg) {
; CHECK-LABEL: @test_select_idx_not_constant3(
; CHECK-NEXT:    [[ALLOCA:%.*]] = alloca [20 x i64], align 8
; CHECK-NEXT:    call void @llvm.memcpy.p0.p0.i64(ptr [[ALLOCA]], ptr [[P:%.*]], i64 160, i1 false)
; CHECK-NEXT:    [[IDX:%.*]] = select i1 [[C:%.*]], i64 24, i64 0
; CHECK-NEXT:    [[GEP:%.*]] = getelementptr inbounds [1 x i8], ptr [[ALLOCA]], i64 [[IDX]], i64 [[ARG:%.*]]
; CHECK-NEXT:    [[RES:%.*]] = load i32, ptr [[GEP]], align 4
; CHECK-NEXT:    ret i32 [[RES]]
;
  %alloca = alloca [20 x i64], align 8
  call void @llvm.memcpy.p0.p0.i64(ptr %alloca, ptr %p, i64 160, i1 false)
  %idx = select i1 %c, i64 24, i64 0
  %gep = getelementptr inbounds [1 x i8], ptr %alloca, i64 %idx, i64 %arg
  %res = load i32, ptr %gep, align 4
  ret i32 %res
}
