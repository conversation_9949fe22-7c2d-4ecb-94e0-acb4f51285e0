; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 3
; Compiler should not be broken with a dead user.
; RUN: opt -passes=sink -S < %s | FileCheck %s

define void @test(i16 %p1, i1 %arg) {
; CHECK-LABEL: define void @test(i16 %p1, i1 %arg) {
; CHECK-NEXT:  bb.0:
; CHECK-NEXT:    %conv = sext i16 %p1 to i32
; CHECK-NEXT:    br i1 %arg, label %bb.1, label %bb.3
; CHECK:       bb.1:
; CHECK-NEXT:    br label %bb.2
; CHECK:       bb.2:
; CHECK-NEXT:    %and.2 = and i32 undef, %conv
; CHECK-NEXT:    br label %bb.2
; CHECK:       bb.3:
; CHECK-NEXT:    %and.3 = and i32 undef, %conv
; CHECK-NEXT:    br label %bb.3
; CHECK:       dead:
; CHECK-NEXT:    %and.dead = and i32 undef, %conv
; CHECK-NEXT:    br label %dead
;
bb.0:
  %conv = sext i16 %p1 to i32
  br i1 %arg, label %bb.1, label %bb.3

bb.1:                                             ; preds = %bb.0
  br label %bb.2

bb.2:                                             ; preds = %bb.2, %bb.1
  %and.2 = and i32 undef, %conv
  br label %bb.2

bb.3:                                             ; preds = %bb.3, %bb.0
  %and.3 = and i32 undef, %conv
  br label %bb.3

dead:                                             ; preds = %dead
  %and.dead = and i32 undef, %conv
  br label %dead
}

define i32 @dead_from_phi(i32 %a) {
; CHECK-LABEL: define i32 @dead_from_phi(
; CHECK-SAME: i32 [[A:%.*]]) {
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[DOTNOT:%.*]] = icmp eq i32 [[A]], 0
; CHECK-NEXT:    br i1 [[DOTNOT]], label [[IF_END:%.*]], label [[IF_THEN:%.*]]
; CHECK:       if.then:
; CHECK-NEXT:    [[B:%.*]] = and i32 undef, 65535
; CHECK-NEXT:    br label [[IF_END]]
; CHECK:       dead:
; CHECK-NEXT:    br label [[IF_END]]
; CHECK:       if.end:
; CHECK-NEXT:    [[DOT0:%.*]] = phi i32 [ [[A]], [[ENTRY:%.*]] ], [ [[B]], [[IF_THEN]] ], [ [[B]], [[DEAD:%.*]] ]
; CHECK-NEXT:    ret i32 [[DOT0]]
;
entry:
  %.not = icmp eq i32 %a, 0
  br i1 %.not, label %if.end, label %if.then

if.then:                                                ; preds = %1
  %b = and i32 undef, 65535
  br label %if.end

dead:                                                ; No predecessors!
  br label %if.end

if.end:                                                ; preds = %4, %if.then, %1
  %.0 = phi i32 [ %a, %entry ], [ %b, %if.then ], [ %b, %dead ]
  ret i32 %.0
}
