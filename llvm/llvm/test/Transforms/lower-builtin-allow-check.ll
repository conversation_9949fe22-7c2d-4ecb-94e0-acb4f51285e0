; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 4
; RUN: opt < %s -passes='function(lower-allow-check)' -S | FileCheck %s --check-prefixes=NOPROFILE
; RUN: opt < %s -passes='function(lower-allow-check)' -lower-allow-check-random-rate=0 -S | FileCheck %s --check-prefixes=NONE
; RUN: opt < %s -passes='function(lower-allow-check)' -lower-allow-check-random-rate=1 -S | FileCheck %s --check-prefixes=ALL
;
; RUN: opt < %s -passes='require<profile-summary>,function(lower-allow-check<cutoffs[22]=990000>)' -S | FileCheck %s --check-prefixes=HOT99
; RUN: opt < %s -passes='require<profile-summary>,function(lower-allow-check<cutoffs[22]=700000>)' -S | FileCheck %s --check-prefixes=HOT70
; RUN: opt < %s -passes='require<profile-summary>,function(lower-allow-check<cutoffs[22]=990000>)' -lower-allow-check-random-rate=0 -S | FileCheck %s --check-prefixes=NONE99
; RUN: opt < %s -passes='require<profile-summary>,function(lower-allow-check<cutoffs[22]=700000>)' -lower-allow-check-random-rate=1 -S | FileCheck %s --check-prefixes=ALL70
;
; -lower-allow-check-percentile-cutoff is deprecated and will be removed in the future;
; use the cutoffs parameter to the lower-allow-check pass, as shown above.
; RUN: opt < %s -passes='require<profile-summary>,function(lower-allow-check)' -lower-allow-check-percentile-cutoff-hot=990000 -S | FileCheck %s --check-prefixes=HOT99
; RUN: opt < %s -passes='require<profile-summary>,function(lower-allow-check)' -lower-allow-check-percentile-cutoff-hot=700000 -S | FileCheck %s --check-prefixes=HOT70
; RUN: opt < %s -passes='require<profile-summary>,function(lower-allow-check)' -lower-allow-check-random-rate=0 -lower-allow-check-percentile-cutoff-hot=990000 -S | FileCheck %s --check-prefixes=NONE99
; RUN: opt < %s -passes='require<profile-summary>,function(lower-allow-check)' -lower-allow-check-random-rate=1 -lower-allow-check-percentile-cutoff-hot=700000 -S | FileCheck %s --check-prefixes=ALL70

target triple = "x86_64-pc-linux-gnu"

declare void @llvm.ubsantrap(i8 immarg)
declare i1 @llvm.allow.ubsan.check(i8 immarg)

define dso_local noundef i32 @simple(ptr noundef readonly %0) {
; NOPROFILE-LABEL: define dso_local noundef i32 @simple(
; NOPROFILE-SAME: ptr noundef readonly [[TMP0:%.*]]) {
; NOPROFILE-NEXT:    [[TMP2:%.*]] = icmp eq ptr [[TMP0]], null
; NOPROFILE-NEXT:    [[HOT:%.*]] = xor i1 true, true
; NOPROFILE-NEXT:    [[TMP6:%.*]] = or i1 [[TMP2]], [[HOT]]
; NOPROFILE-NEXT:    br i1 [[TMP6]], label [[TMP3:%.*]], label [[TMP4:%.*]]
; NOPROFILE:       3:
; NOPROFILE-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; NOPROFILE-NEXT:    unreachable
; NOPROFILE:       4:
; NOPROFILE-NEXT:    [[TMP5:%.*]] = load i32, ptr [[TMP0]], align 4
; NOPROFILE-NEXT:    ret i32 [[TMP5]]
;
; NONE-LABEL: define dso_local noundef i32 @simple(
; NONE-SAME: ptr noundef readonly [[TMP0:%.*]]) {
; NONE-NEXT:    [[TMP2:%.*]] = icmp eq ptr [[TMP0]], null
; NONE-NEXT:    [[HOT:%.*]] = xor i1 false, true
; NONE-NEXT:    [[TMP6:%.*]] = or i1 [[TMP2]], [[HOT]]
; NONE-NEXT:    br i1 [[TMP6]], label [[TMP3:%.*]], label [[TMP4:%.*]]
; NONE:       3:
; NONE-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; NONE-NEXT:    unreachable
; NONE:       4:
; NONE-NEXT:    [[TMP5:%.*]] = load i32, ptr [[TMP0]], align 4
; NONE-NEXT:    ret i32 [[TMP5]]
;
; ALL-LABEL: define dso_local noundef i32 @simple(
; ALL-SAME: ptr noundef readonly [[TMP0:%.*]]) {
; ALL-NEXT:    [[CHK:%.*]] = icmp eq ptr [[TMP0]], null
; ALL-NEXT:    [[HOT:%.*]] = xor i1 true, true
; ALL-NEXT:    [[TMP2:%.*]] = or i1 [[CHK]], [[HOT]]
; ALL-NEXT:    br i1 [[TMP2]], label [[TMP3:%.*]], label [[TMP4:%.*]]
; ALL:       3:
; ALL-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; ALL-NEXT:    unreachable
; ALL:       4:
; ALL-NEXT:    [[TMP5:%.*]] = load i32, ptr [[TMP0]], align 4
; ALL-NEXT:    ret i32 [[TMP5]]
;
; HOT99-LABEL: define dso_local noundef i32 @simple(
; HOT99-SAME: ptr noundef readonly [[TMP0:%.*]]) {
; HOT99-NEXT:    [[TMP2:%.*]] = icmp eq ptr [[TMP0]], null
; HOT99-NEXT:    [[HOT:%.*]] = xor i1 true, true
; HOT99-NEXT:    [[TMP6:%.*]] = or i1 [[TMP2]], [[HOT]]
; HOT99-NEXT:    br i1 [[TMP6]], label [[TMP3:%.*]], label [[TMP4:%.*]]
; HOT99:       3:
; HOT99-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; HOT99-NEXT:    unreachable
; HOT99:       4:
; HOT99-NEXT:    [[TMP5:%.*]] = load i32, ptr [[TMP0]], align 4
; HOT99-NEXT:    ret i32 [[TMP5]]
;
; HOT70-LABEL: define dso_local noundef i32 @simple(
; HOT70-SAME: ptr noundef readonly [[TMP0:%.*]]) {
; HOT70-NEXT:    [[TMP2:%.*]] = icmp eq ptr [[TMP0]], null
; HOT70-NEXT:    [[HOT:%.*]] = xor i1 true, true
; HOT70-NEXT:    [[TMP6:%.*]] = or i1 [[TMP2]], [[HOT]]
; HOT70-NEXT:    br i1 [[TMP6]], label [[TMP3:%.*]], label [[TMP4:%.*]]
; HOT70:       3:
; HOT70-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; HOT70-NEXT:    unreachable
; HOT70:       4:
; HOT70-NEXT:    [[TMP5:%.*]] = load i32, ptr [[TMP0]], align 4
; HOT70-NEXT:    ret i32 [[TMP5]]
;
; NONE99-LABEL: define dso_local noundef i32 @simple(
; NONE99-SAME: ptr noundef readonly [[TMP0:%.*]]) {
; NONE99-NEXT:    [[CHK:%.*]] = icmp eq ptr [[TMP0]], null
; NONE99-NEXT:    [[HOT:%.*]] = xor i1 false, true
; NONE99-NEXT:    [[TMP2:%.*]] = or i1 [[CHK]], [[HOT]]
; NONE99-NEXT:    br i1 [[TMP2]], label [[TMP3:%.*]], label [[TMP4:%.*]]
; NONE99:       3:
; NONE99-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; NONE99-NEXT:    unreachable
; NONE99:       4:
; NONE99-NEXT:    [[TMP5:%.*]] = load i32, ptr [[TMP0]], align 4
; NONE99-NEXT:    ret i32 [[TMP5]]
;
; ALL70-LABEL: define dso_local noundef i32 @simple(
; ALL70-SAME: ptr noundef readonly [[TMP0:%.*]]) {
; ALL70-NEXT:    [[CHK:%.*]] = icmp eq ptr [[TMP0]], null
; ALL70-NEXT:    [[HOT:%.*]] = xor i1 true, true
; ALL70-NEXT:    [[TMP2:%.*]] = or i1 [[CHK]], [[HOT]]
; ALL70-NEXT:    br i1 [[TMP2]], label [[TMP3:%.*]], label [[TMP4:%.*]]
; ALL70:       3:
; ALL70-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; ALL70-NEXT:    unreachable
; ALL70:       4:
; ALL70-NEXT:    [[TMP5:%.*]] = load i32, ptr [[TMP0]], align 4
; ALL70-NEXT:    ret i32 [[TMP5]]
;
  %chk = icmp eq ptr %0, null
  %allow = call i1 @llvm.allow.ubsan.check(i8 22)
  %hot = xor i1 %allow, true
  %2 = or i1 %chk, %hot
  br i1 %2, label %3, label %4

3:
  tail call void @llvm.ubsantrap(i8 22)
  unreachable

4:
  %5 = load i32, ptr %0, align 4
  ret i32 %5
}


define dso_local noundef i32 @hot(ptr noundef readonly %0) !prof !36 {
; NOPROFILE-LABEL: define dso_local noundef i32 @hot(
; NOPROFILE-SAME: ptr noundef readonly [[TMP0:%.*]]) !prof [[PROF16:![0-9]+]] {
; NOPROFILE-NEXT:    [[TMP2:%.*]] = icmp eq ptr [[TMP0]], null
; NOPROFILE-NEXT:    [[HOT:%.*]] = xor i1 true, true
; NOPROFILE-NEXT:    [[TMP6:%.*]] = or i1 [[TMP2]], [[HOT]]
; NOPROFILE-NEXT:    br i1 [[TMP6]], label [[TMP3:%.*]], label [[TMP4:%.*]]
; NOPROFILE:       3:
; NOPROFILE-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; NOPROFILE-NEXT:    unreachable
; NOPROFILE:       4:
; NOPROFILE-NEXT:    [[TMP5:%.*]] = load i32, ptr [[TMP0]], align 4
; NOPROFILE-NEXT:    ret i32 [[TMP5]]
;
; NONE-LABEL: define dso_local noundef i32 @hot(
; NONE-SAME: ptr noundef readonly [[TMP0:%.*]]) !prof [[PROF16:![0-9]+]] {
; NONE-NEXT:    [[TMP2:%.*]] = icmp eq ptr [[TMP0]], null
; NONE-NEXT:    [[HOT:%.*]] = xor i1 false, true
; NONE-NEXT:    [[TMP6:%.*]] = or i1 [[TMP2]], [[HOT]]
; NONE-NEXT:    br i1 [[TMP6]], label [[TMP3:%.*]], label [[TMP4:%.*]]
; NONE:       3:
; NONE-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; NONE-NEXT:    unreachable
; NONE:       4:
; NONE-NEXT:    [[TMP5:%.*]] = load i32, ptr [[TMP0]], align 4
; NONE-NEXT:    ret i32 [[TMP5]]
;
; ALL-LABEL: define dso_local noundef i32 @hot(
; ALL-SAME: ptr noundef readonly [[TMP0:%.*]]) !prof [[PROF16:![0-9]+]] {
; ALL-NEXT:    [[CHK:%.*]] = icmp eq ptr [[TMP0]], null
; ALL-NEXT:    [[HOT:%.*]] = xor i1 true, true
; ALL-NEXT:    [[TMP2:%.*]] = or i1 [[CHK]], [[HOT]]
; ALL-NEXT:    br i1 [[TMP2]], label [[TMP3:%.*]], label [[TMP4:%.*]]
; ALL:       3:
; ALL-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; ALL-NEXT:    unreachable
; ALL:       4:
; ALL-NEXT:    [[TMP5:%.*]] = load i32, ptr [[TMP0]], align 4
; ALL-NEXT:    ret i32 [[TMP5]]
;
; HOT99-LABEL: define dso_local noundef i32 @hot(
; HOT99-SAME: ptr noundef readonly [[TMP0:%.*]]) !prof [[PROF16:![0-9]+]] {
; HOT99-NEXT:    [[TMP2:%.*]] = icmp eq ptr [[TMP0]], null
; HOT99-NEXT:    [[HOT:%.*]] = xor i1 false, true
; HOT99-NEXT:    [[TMP6:%.*]] = or i1 [[TMP2]], [[HOT]]
; HOT99-NEXT:    br i1 [[TMP6]], label [[TMP3:%.*]], label [[TMP4:%.*]]
; HOT99:       3:
; HOT99-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; HOT99-NEXT:    unreachable
; HOT99:       4:
; HOT99-NEXT:    [[TMP5:%.*]] = load i32, ptr [[TMP0]], align 4
; HOT99-NEXT:    ret i32 [[TMP5]]
;
; HOT70-LABEL: define dso_local noundef i32 @hot(
; HOT70-SAME: ptr noundef readonly [[TMP0:%.*]]) !prof [[PROF16:![0-9]+]] {
; HOT70-NEXT:    [[TMP2:%.*]] = icmp eq ptr [[TMP0]], null
; HOT70-NEXT:    [[HOT:%.*]] = xor i1 true, true
; HOT70-NEXT:    [[TMP6:%.*]] = or i1 [[TMP2]], [[HOT]]
; HOT70-NEXT:    br i1 [[TMP6]], label [[TMP3:%.*]], label [[TMP4:%.*]]
; HOT70:       3:
; HOT70-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; HOT70-NEXT:    unreachable
; HOT70:       4:
; HOT70-NEXT:    [[TMP5:%.*]] = load i32, ptr [[TMP0]], align 4
; HOT70-NEXT:    ret i32 [[TMP5]]
;
; NONE99-LABEL: define dso_local noundef i32 @hot(
; NONE99-SAME: ptr noundef readonly [[TMP0:%.*]]) !prof [[PROF16:![0-9]+]] {
; NONE99-NEXT:    [[CHK:%.*]] = icmp eq ptr [[TMP0]], null
; NONE99-NEXT:    [[HOT:%.*]] = xor i1 false, true
; NONE99-NEXT:    [[TMP2:%.*]] = or i1 [[CHK]], [[HOT]]
; NONE99-NEXT:    br i1 [[TMP2]], label [[TMP3:%.*]], label [[TMP4:%.*]]
; NONE99:       3:
; NONE99-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; NONE99-NEXT:    unreachable
; NONE99:       4:
; NONE99-NEXT:    [[TMP5:%.*]] = load i32, ptr [[TMP0]], align 4
; NONE99-NEXT:    ret i32 [[TMP5]]
;
; ALL70-LABEL: define dso_local noundef i32 @hot(
; ALL70-SAME: ptr noundef readonly [[TMP0:%.*]]) !prof [[PROF16:![0-9]+]] {
; ALL70-NEXT:    [[CHK:%.*]] = icmp eq ptr [[TMP0]], null
; ALL70-NEXT:    [[HOT:%.*]] = xor i1 true, true
; ALL70-NEXT:    [[TMP2:%.*]] = or i1 [[CHK]], [[HOT]]
; ALL70-NEXT:    br i1 [[TMP2]], label [[TMP3:%.*]], label [[TMP4:%.*]]
; ALL70:       3:
; ALL70-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; ALL70-NEXT:    unreachable
; ALL70:       4:
; ALL70-NEXT:    [[TMP5:%.*]] = load i32, ptr [[TMP0]], align 4
; ALL70-NEXT:    ret i32 [[TMP5]]
;
  %chk = icmp eq ptr %0, null
  %allow = call i1 @llvm.allow.ubsan.check(i8 22)
  %hot = xor i1 %allow, true
  %2 = or i1 %chk, %hot
  br i1 %2, label %3, label %4

3:
  tail call void @llvm.ubsantrap(i8 22)
  unreachable

4:
  %5 = load i32, ptr %0, align 4
  ret i32 %5
}

define dso_local noundef i32 @veryHot(ptr noundef readonly %0) !prof !39 {
; NOPROFILE-LABEL: define dso_local noundef i32 @veryHot(
; NOPROFILE-SAME: ptr noundef readonly [[TMP0:%.*]]) !prof [[PROF17:![0-9]+]] {
; NOPROFILE-NEXT:    [[TMP2:%.*]] = icmp eq ptr [[TMP0]], null
; NOPROFILE-NEXT:    [[HOT:%.*]] = xor i1 true, true
; NOPROFILE-NEXT:    [[TMP6:%.*]] = or i1 [[TMP2]], [[HOT]]
; NOPROFILE-NEXT:    br i1 [[TMP6]], label [[TMP3:%.*]], label [[TMP4:%.*]]
; NOPROFILE:       3:
; NOPROFILE-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; NOPROFILE-NEXT:    unreachable
; NOPROFILE:       4:
; NOPROFILE-NEXT:    [[TMP5:%.*]] = load i32, ptr [[TMP0]], align 4
; NOPROFILE-NEXT:    ret i32 [[TMP5]]
;
; NONE-LABEL: define dso_local noundef i32 @veryHot(
; NONE-SAME: ptr noundef readonly [[TMP0:%.*]]) !prof [[PROF17:![0-9]+]] {
; NONE-NEXT:    [[TMP2:%.*]] = icmp eq ptr [[TMP0]], null
; NONE-NEXT:    [[HOT:%.*]] = xor i1 false, true
; NONE-NEXT:    [[TMP6:%.*]] = or i1 [[TMP2]], [[HOT]]
; NONE-NEXT:    br i1 [[TMP6]], label [[TMP3:%.*]], label [[TMP4:%.*]]
; NONE:       3:
; NONE-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; NONE-NEXT:    unreachable
; NONE:       4:
; NONE-NEXT:    [[TMP5:%.*]] = load i32, ptr [[TMP0]], align 4
; NONE-NEXT:    ret i32 [[TMP5]]
;
; ALL-LABEL: define dso_local noundef i32 @veryHot(
; ALL-SAME: ptr noundef readonly [[TMP0:%.*]]) !prof [[PROF17:![0-9]+]] {
; ALL-NEXT:    [[CHK:%.*]] = icmp eq ptr [[TMP0]], null
; ALL-NEXT:    [[HOT:%.*]] = xor i1 true, true
; ALL-NEXT:    [[TMP2:%.*]] = or i1 [[CHK]], [[HOT]]
; ALL-NEXT:    br i1 [[TMP2]], label [[TMP3:%.*]], label [[TMP4:%.*]]
; ALL:       3:
; ALL-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; ALL-NEXT:    unreachable
; ALL:       4:
; ALL-NEXT:    [[TMP5:%.*]] = load i32, ptr [[TMP0]], align 4
; ALL-NEXT:    ret i32 [[TMP5]]
;
; HOT99-LABEL: define dso_local noundef i32 @veryHot(
; HOT99-SAME: ptr noundef readonly [[TMP0:%.*]]) !prof [[PROF17:![0-9]+]] {
; HOT99-NEXT:    [[TMP2:%.*]] = icmp eq ptr [[TMP0]], null
; HOT99-NEXT:    [[HOT:%.*]] = xor i1 false, true
; HOT99-NEXT:    [[TMP6:%.*]] = or i1 [[TMP2]], [[HOT]]
; HOT99-NEXT:    br i1 [[TMP6]], label [[TMP3:%.*]], label [[TMP4:%.*]]
; HOT99:       3:
; HOT99-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; HOT99-NEXT:    unreachable
; HOT99:       4:
; HOT99-NEXT:    [[TMP5:%.*]] = load i32, ptr [[TMP0]], align 4
; HOT99-NEXT:    ret i32 [[TMP5]]
;
; HOT70-LABEL: define dso_local noundef i32 @veryHot(
; HOT70-SAME: ptr noundef readonly [[TMP0:%.*]]) !prof [[PROF17:![0-9]+]] {
; HOT70-NEXT:    [[TMP2:%.*]] = icmp eq ptr [[TMP0]], null
; HOT70-NEXT:    [[HOT:%.*]] = xor i1 false, true
; HOT70-NEXT:    [[TMP6:%.*]] = or i1 [[TMP2]], [[HOT]]
; HOT70-NEXT:    br i1 [[TMP6]], label [[TMP3:%.*]], label [[TMP4:%.*]]
; HOT70:       3:
; HOT70-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; HOT70-NEXT:    unreachable
; HOT70:       4:
; HOT70-NEXT:    [[TMP5:%.*]] = load i32, ptr [[TMP0]], align 4
; HOT70-NEXT:    ret i32 [[TMP5]]
;
; NONE99-LABEL: define dso_local noundef i32 @veryHot(
; NONE99-SAME: ptr noundef readonly [[TMP0:%.*]]) !prof [[PROF17:![0-9]+]] {
; NONE99-NEXT:    [[CHK:%.*]] = icmp eq ptr [[TMP0]], null
; NONE99-NEXT:    [[HOT:%.*]] = xor i1 false, true
; NONE99-NEXT:    [[TMP2:%.*]] = or i1 [[CHK]], [[HOT]]
; NONE99-NEXT:    br i1 [[TMP2]], label [[TMP3:%.*]], label [[TMP4:%.*]]
; NONE99:       3:
; NONE99-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; NONE99-NEXT:    unreachable
; NONE99:       4:
; NONE99-NEXT:    [[TMP5:%.*]] = load i32, ptr [[TMP0]], align 4
; NONE99-NEXT:    ret i32 [[TMP5]]
;
; ALL70-LABEL: define dso_local noundef i32 @veryHot(
; ALL70-SAME: ptr noundef readonly [[TMP0:%.*]]) !prof [[PROF17:![0-9]+]] {
; ALL70-NEXT:    [[CHK:%.*]] = icmp eq ptr [[TMP0]], null
; ALL70-NEXT:    [[HOT:%.*]] = xor i1 false, true
; ALL70-NEXT:    [[TMP2:%.*]] = or i1 [[CHK]], [[HOT]]
; ALL70-NEXT:    br i1 [[TMP2]], label [[TMP3:%.*]], label [[TMP4:%.*]]
; ALL70:       3:
; ALL70-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; ALL70-NEXT:    unreachable
; ALL70:       4:
; ALL70-NEXT:    [[TMP5:%.*]] = load i32, ptr [[TMP0]], align 4
; ALL70-NEXT:    ret i32 [[TMP5]]
;
  %chk = icmp eq ptr %0, null
  %allow = call i1 @llvm.allow.ubsan.check(i8 22)
  %hot = xor i1 %allow, true
  %2 = or i1 %chk, %hot
  br i1 %2, label %3, label %4

3:
  tail call void @llvm.ubsantrap(i8 22)
  unreachable

4:
  %5 = load i32, ptr %0, align 4
  ret i32 %5
}


define dso_local noundef i32 @branchColdFnHot(i32 noundef %0, ptr noundef readonly %1) !prof !39 {
; NOPROFILE-LABEL: define dso_local noundef i32 @branchColdFnHot(
; NOPROFILE-SAME: i32 noundef [[TMP0:%.*]], ptr noundef readonly [[TMP1:%.*]]) !prof [[PROF17]] {
; NOPROFILE-NEXT:    [[TMP3:%.*]] = icmp eq i32 [[TMP0]], 0
; NOPROFILE-NEXT:    br i1 [[TMP3]], label [[TMP9:%.*]], label [[TMP4:%.*]], !prof [[PROF18:![0-9]+]]
; NOPROFILE:       4:
; NOPROFILE-NEXT:    [[TMP5:%.*]] = icmp eq ptr [[TMP1]], null
; NOPROFILE-NEXT:    [[HOT:%.*]] = xor i1 true, true
; NOPROFILE-NEXT:    [[TMP11:%.*]] = or i1 [[TMP5]], [[HOT]]
; NOPROFILE-NEXT:    br i1 [[TMP11]], label [[TMP6:%.*]], label [[TMP7:%.*]]
; NOPROFILE:       6:
; NOPROFILE-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; NOPROFILE-NEXT:    unreachable
; NOPROFILE:       7:
; NOPROFILE-NEXT:    [[TMP8:%.*]] = load i32, ptr [[TMP1]], align 4
; NOPROFILE-NEXT:    br label [[TMP9]]
; NOPROFILE:       9:
; NOPROFILE-NEXT:    [[TMP10:%.*]] = phi i32 [ [[TMP8]], [[TMP7]] ], [ 0, [[TMP2:%.*]] ]
; NOPROFILE-NEXT:    ret i32 [[TMP10]]
;
; NONE-LABEL: define dso_local noundef i32 @branchColdFnHot(
; NONE-SAME: i32 noundef [[TMP0:%.*]], ptr noundef readonly [[TMP1:%.*]]) !prof [[PROF17]] {
; NONE-NEXT:    [[TMP3:%.*]] = icmp eq i32 [[TMP0]], 0
; NONE-NEXT:    br i1 [[TMP3]], label [[TMP9:%.*]], label [[TMP4:%.*]], !prof [[PROF18:![0-9]+]]
; NONE:       4:
; NONE-NEXT:    [[TMP5:%.*]] = icmp eq ptr [[TMP1]], null
; NONE-NEXT:    [[HOT:%.*]] = xor i1 false, true
; NONE-NEXT:    [[TMP11:%.*]] = or i1 [[TMP5]], [[HOT]]
; NONE-NEXT:    br i1 [[TMP11]], label [[TMP6:%.*]], label [[TMP7:%.*]]
; NONE:       6:
; NONE-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; NONE-NEXT:    unreachable
; NONE:       7:
; NONE-NEXT:    [[TMP8:%.*]] = load i32, ptr [[TMP1]], align 4
; NONE-NEXT:    br label [[TMP9]]
; NONE:       9:
; NONE-NEXT:    [[TMP10:%.*]] = phi i32 [ [[TMP8]], [[TMP7]] ], [ 0, [[TMP2:%.*]] ]
; NONE-NEXT:    ret i32 [[TMP10]]
;
; ALL-LABEL: define dso_local noundef i32 @branchColdFnHot(
; ALL-SAME: i32 noundef [[TMP0:%.*]], ptr noundef readonly [[TMP1:%.*]]) !prof [[PROF17]] {
; ALL-NEXT:    [[TMP3:%.*]] = icmp eq i32 [[TMP0]], 0
; ALL-NEXT:    br i1 [[TMP3]], label [[TMP9:%.*]], label [[TMP4:%.*]], !prof [[PROF18:![0-9]+]]
; ALL:       4:
; ALL-NEXT:    [[CHK:%.*]] = icmp eq ptr [[TMP1]], null
; ALL-NEXT:    [[HOT:%.*]] = xor i1 true, true
; ALL-NEXT:    [[TMP5:%.*]] = or i1 [[CHK]], [[HOT]]
; ALL-NEXT:    br i1 [[TMP5]], label [[TMP6:%.*]], label [[TMP7:%.*]]
; ALL:       6:
; ALL-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; ALL-NEXT:    unreachable
; ALL:       7:
; ALL-NEXT:    [[TMP8:%.*]] = load i32, ptr [[TMP1]], align 4
; ALL-NEXT:    br label [[TMP9]]
; ALL:       9:
; ALL-NEXT:    [[TMP10:%.*]] = phi i32 [ [[TMP8]], [[TMP7]] ], [ 0, [[TMP2:%.*]] ]
; ALL-NEXT:    ret i32 [[TMP10]]
;
; HOT99-LABEL: define dso_local noundef i32 @branchColdFnHot(
; HOT99-SAME: i32 noundef [[TMP0:%.*]], ptr noundef readonly [[TMP1:%.*]]) !prof [[PROF17]] {
; HOT99-NEXT:    [[TMP3:%.*]] = icmp eq i32 [[TMP0]], 0
; HOT99-NEXT:    br i1 [[TMP3]], label [[TMP9:%.*]], label [[TMP4:%.*]], !prof [[PROF18:![0-9]+]]
; HOT99:       4:
; HOT99-NEXT:    [[TMP5:%.*]] = icmp eq ptr [[TMP1]], null
; HOT99-NEXT:    [[HOT:%.*]] = xor i1 true, true
; HOT99-NEXT:    [[TMP11:%.*]] = or i1 [[TMP5]], [[HOT]]
; HOT99-NEXT:    br i1 [[TMP11]], label [[TMP6:%.*]], label [[TMP7:%.*]]
; HOT99:       6:
; HOT99-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; HOT99-NEXT:    unreachable
; HOT99:       7:
; HOT99-NEXT:    [[TMP8:%.*]] = load i32, ptr [[TMP1]], align 4
; HOT99-NEXT:    br label [[TMP9]]
; HOT99:       9:
; HOT99-NEXT:    [[TMP10:%.*]] = phi i32 [ [[TMP8]], [[TMP7]] ], [ 0, [[TMP2:%.*]] ]
; HOT99-NEXT:    ret i32 [[TMP10]]
;
; HOT70-LABEL: define dso_local noundef i32 @branchColdFnHot(
; HOT70-SAME: i32 noundef [[TMP0:%.*]], ptr noundef readonly [[TMP1:%.*]]) !prof [[PROF17]] {
; HOT70-NEXT:    [[TMP3:%.*]] = icmp eq i32 [[TMP0]], 0
; HOT70-NEXT:    br i1 [[TMP3]], label [[TMP9:%.*]], label [[TMP4:%.*]], !prof [[PROF18:![0-9]+]]
; HOT70:       4:
; HOT70-NEXT:    [[TMP5:%.*]] = icmp eq ptr [[TMP1]], null
; HOT70-NEXT:    [[HOT:%.*]] = xor i1 true, true
; HOT70-NEXT:    [[TMP11:%.*]] = or i1 [[TMP5]], [[HOT]]
; HOT70-NEXT:    br i1 [[TMP11]], label [[TMP6:%.*]], label [[TMP7:%.*]]
; HOT70:       6:
; HOT70-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; HOT70-NEXT:    unreachable
; HOT70:       7:
; HOT70-NEXT:    [[TMP8:%.*]] = load i32, ptr [[TMP1]], align 4
; HOT70-NEXT:    br label [[TMP9]]
; HOT70:       9:
; HOT70-NEXT:    [[TMP10:%.*]] = phi i32 [ [[TMP8]], [[TMP7]] ], [ 0, [[TMP2:%.*]] ]
; HOT70-NEXT:    ret i32 [[TMP10]]
;
; NONE99-LABEL: define dso_local noundef i32 @branchColdFnHot(
; NONE99-SAME: i32 noundef [[TMP0:%.*]], ptr noundef readonly [[TMP1:%.*]]) !prof [[PROF17]] {
; NONE99-NEXT:    [[TMP3:%.*]] = icmp eq i32 [[TMP0]], 0
; NONE99-NEXT:    br i1 [[TMP3]], label [[TMP9:%.*]], label [[TMP4:%.*]], !prof [[PROF18:![0-9]+]]
; NONE99:       4:
; NONE99-NEXT:    [[CHK:%.*]] = icmp eq ptr [[TMP1]], null
; NONE99-NEXT:    [[HOT:%.*]] = xor i1 false, true
; NONE99-NEXT:    [[TMP5:%.*]] = or i1 [[CHK]], [[HOT]]
; NONE99-NEXT:    br i1 [[TMP5]], label [[TMP6:%.*]], label [[TMP7:%.*]]
; NONE99:       6:
; NONE99-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; NONE99-NEXT:    unreachable
; NONE99:       7:
; NONE99-NEXT:    [[TMP8:%.*]] = load i32, ptr [[TMP1]], align 4
; NONE99-NEXT:    br label [[TMP9]]
; NONE99:       9:
; NONE99-NEXT:    [[TMP10:%.*]] = phi i32 [ [[TMP8]], [[TMP7]] ], [ 0, [[TMP2:%.*]] ]
; NONE99-NEXT:    ret i32 [[TMP10]]
;
; ALL70-LABEL: define dso_local noundef i32 @branchColdFnHot(
; ALL70-SAME: i32 noundef [[TMP0:%.*]], ptr noundef readonly [[TMP1:%.*]]) !prof [[PROF17]] {
; ALL70-NEXT:    [[TMP3:%.*]] = icmp eq i32 [[TMP0]], 0
; ALL70-NEXT:    br i1 [[TMP3]], label [[TMP9:%.*]], label [[TMP4:%.*]], !prof [[PROF18:![0-9]+]]
; ALL70:       4:
; ALL70-NEXT:    [[CHK:%.*]] = icmp eq ptr [[TMP1]], null
; ALL70-NEXT:    [[HOT:%.*]] = xor i1 true, true
; ALL70-NEXT:    [[TMP5:%.*]] = or i1 [[CHK]], [[HOT]]
; ALL70-NEXT:    br i1 [[TMP5]], label [[TMP6:%.*]], label [[TMP7:%.*]]
; ALL70:       6:
; ALL70-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; ALL70-NEXT:    unreachable
; ALL70:       7:
; ALL70-NEXT:    [[TMP8:%.*]] = load i32, ptr [[TMP1]], align 4
; ALL70-NEXT:    br label [[TMP9]]
; ALL70:       9:
; ALL70-NEXT:    [[TMP10:%.*]] = phi i32 [ [[TMP8]], [[TMP7]] ], [ 0, [[TMP2:%.*]] ]
; ALL70-NEXT:    ret i32 [[TMP10]]
;
  %3 = icmp eq i32 %0, 0
  br i1 %3, label %9, label %4, !prof !38

4:
  %chk = icmp eq ptr %1, null
  %allow = call i1 @llvm.allow.ubsan.check(i8 22)
  %hot = xor i1 %allow, true
  %5 = or i1 %chk, %hot
  br i1 %5, label %6, label %7

6:
  tail call void @llvm.ubsantrap(i8 22) #2
  unreachable

7:
  %8 = load i32, ptr %1, align 4
  br label %9

9:
  %10 = phi i32 [ %8, %7 ], [ 0, %2 ]
  ret i32 %10
}

define dso_local noundef i32 @branchHotFnCold(i32 noundef %0, ptr noundef readonly %1) !prof !36 {
; NOPROFILE-LABEL: define dso_local noundef i32 @branchHotFnCold(
; NOPROFILE-SAME: i32 noundef [[TMP0:%.*]], ptr noundef readonly [[TMP1:%.*]]) !prof [[PROF16]] {
; NOPROFILE-NEXT:    [[TMP3:%.*]] = icmp eq i32 [[TMP0]], 0
; NOPROFILE-NEXT:    br i1 [[TMP3]], label [[TMP9:%.*]], label [[TMP4:%.*]], !prof [[PROF19:![0-9]+]]
; NOPROFILE:       4:
; NOPROFILE-NEXT:    [[TMP5:%.*]] = icmp eq ptr [[TMP1]], null
; NOPROFILE-NEXT:    [[HOT:%.*]] = xor i1 true, true
; NOPROFILE-NEXT:    [[TMP11:%.*]] = or i1 [[TMP5]], [[HOT]]
; NOPROFILE-NEXT:    br i1 [[TMP11]], label [[TMP6:%.*]], label [[TMP7:%.*]]
; NOPROFILE:       6:
; NOPROFILE-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; NOPROFILE-NEXT:    unreachable
; NOPROFILE:       7:
; NOPROFILE-NEXT:    [[TMP8:%.*]] = load i32, ptr [[TMP1]], align 4
; NOPROFILE-NEXT:    br label [[TMP9]]
; NOPROFILE:       9:
; NOPROFILE-NEXT:    [[TMP10:%.*]] = phi i32 [ [[TMP8]], [[TMP7]] ], [ 0, [[TMP2:%.*]] ]
; NOPROFILE-NEXT:    ret i32 [[TMP10]]
;
; NONE-LABEL: define dso_local noundef i32 @branchHotFnCold(
; NONE-SAME: i32 noundef [[TMP0:%.*]], ptr noundef readonly [[TMP1:%.*]]) !prof [[PROF16]] {
; NONE-NEXT:    [[TMP3:%.*]] = icmp eq i32 [[TMP0]], 0
; NONE-NEXT:    br i1 [[TMP3]], label [[TMP9:%.*]], label [[TMP4:%.*]], !prof [[PROF19:![0-9]+]]
; NONE:       4:
; NONE-NEXT:    [[TMP5:%.*]] = icmp eq ptr [[TMP1]], null
; NONE-NEXT:    [[HOT:%.*]] = xor i1 false, true
; NONE-NEXT:    [[TMP11:%.*]] = or i1 [[TMP5]], [[HOT]]
; NONE-NEXT:    br i1 [[TMP11]], label [[TMP6:%.*]], label [[TMP7:%.*]]
; NONE:       6:
; NONE-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; NONE-NEXT:    unreachable
; NONE:       7:
; NONE-NEXT:    [[TMP8:%.*]] = load i32, ptr [[TMP1]], align 4
; NONE-NEXT:    br label [[TMP9]]
; NONE:       9:
; NONE-NEXT:    [[TMP10:%.*]] = phi i32 [ [[TMP8]], [[TMP7]] ], [ 0, [[TMP2:%.*]] ]
; NONE-NEXT:    ret i32 [[TMP10]]
;
; ALL-LABEL: define dso_local noundef i32 @branchHotFnCold(
; ALL-SAME: i32 noundef [[TMP0:%.*]], ptr noundef readonly [[TMP1:%.*]]) !prof [[PROF16]] {
; ALL-NEXT:    [[TMP3:%.*]] = icmp eq i32 [[TMP0]], 0
; ALL-NEXT:    br i1 [[TMP3]], label [[TMP9:%.*]], label [[TMP4:%.*]], !prof [[PROF19:![0-9]+]]
; ALL:       4:
; ALL-NEXT:    [[CHK:%.*]] = icmp eq ptr [[TMP1]], null
; ALL-NEXT:    [[HOT:%.*]] = xor i1 true, true
; ALL-NEXT:    [[TMP5:%.*]] = or i1 [[CHK]], [[HOT]]
; ALL-NEXT:    br i1 [[TMP5]], label [[TMP6:%.*]], label [[TMP7:%.*]]
; ALL:       6:
; ALL-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; ALL-NEXT:    unreachable
; ALL:       7:
; ALL-NEXT:    [[TMP8:%.*]] = load i32, ptr [[TMP1]], align 4
; ALL-NEXT:    br label [[TMP9]]
; ALL:       9:
; ALL-NEXT:    [[TMP10:%.*]] = phi i32 [ [[TMP8]], [[TMP7]] ], [ 0, [[TMP2:%.*]] ]
; ALL-NEXT:    ret i32 [[TMP10]]
;
; HOT99-LABEL: define dso_local noundef i32 @branchHotFnCold(
; HOT99-SAME: i32 noundef [[TMP0:%.*]], ptr noundef readonly [[TMP1:%.*]]) !prof [[PROF16]] {
; HOT99-NEXT:    [[TMP3:%.*]] = icmp eq i32 [[TMP0]], 0
; HOT99-NEXT:    br i1 [[TMP3]], label [[TMP9:%.*]], label [[TMP4:%.*]], !prof [[PROF19:![0-9]+]]
; HOT99:       4:
; HOT99-NEXT:    [[TMP5:%.*]] = icmp eq ptr [[TMP1]], null
; HOT99-NEXT:    [[HOT:%.*]] = xor i1 false, true
; HOT99-NEXT:    [[TMP11:%.*]] = or i1 [[TMP5]], [[HOT]]
; HOT99-NEXT:    br i1 [[TMP11]], label [[TMP6:%.*]], label [[TMP7:%.*]]
; HOT99:       6:
; HOT99-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; HOT99-NEXT:    unreachable
; HOT99:       7:
; HOT99-NEXT:    [[TMP8:%.*]] = load i32, ptr [[TMP1]], align 4
; HOT99-NEXT:    br label [[TMP9]]
; HOT99:       9:
; HOT99-NEXT:    [[TMP10:%.*]] = phi i32 [ [[TMP8]], [[TMP7]] ], [ 0, [[TMP2:%.*]] ]
; HOT99-NEXT:    ret i32 [[TMP10]]
;
; HOT70-LABEL: define dso_local noundef i32 @branchHotFnCold(
; HOT70-SAME: i32 noundef [[TMP0:%.*]], ptr noundef readonly [[TMP1:%.*]]) !prof [[PROF16]] {
; HOT70-NEXT:    [[TMP3:%.*]] = icmp eq i32 [[TMP0]], 0
; HOT70-NEXT:    br i1 [[TMP3]], label [[TMP9:%.*]], label [[TMP4:%.*]], !prof [[PROF19:![0-9]+]]
; HOT70:       4:
; HOT70-NEXT:    [[TMP5:%.*]] = icmp eq ptr [[TMP1]], null
; HOT70-NEXT:    [[HOT:%.*]] = xor i1 true, true
; HOT70-NEXT:    [[TMP11:%.*]] = or i1 [[TMP5]], [[HOT]]
; HOT70-NEXT:    br i1 [[TMP11]], label [[TMP6:%.*]], label [[TMP7:%.*]]
; HOT70:       6:
; HOT70-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; HOT70-NEXT:    unreachable
; HOT70:       7:
; HOT70-NEXT:    [[TMP8:%.*]] = load i32, ptr [[TMP1]], align 4
; HOT70-NEXT:    br label [[TMP9]]
; HOT70:       9:
; HOT70-NEXT:    [[TMP10:%.*]] = phi i32 [ [[TMP8]], [[TMP7]] ], [ 0, [[TMP2:%.*]] ]
; HOT70-NEXT:    ret i32 [[TMP10]]
;
; NONE99-LABEL: define dso_local noundef i32 @branchHotFnCold(
; NONE99-SAME: i32 noundef [[TMP0:%.*]], ptr noundef readonly [[TMP1:%.*]]) !prof [[PROF16]] {
; NONE99-NEXT:    [[TMP3:%.*]] = icmp eq i32 [[TMP0]], 0
; NONE99-NEXT:    br i1 [[TMP3]], label [[TMP9:%.*]], label [[TMP4:%.*]], !prof [[PROF19:![0-9]+]]
; NONE99:       4:
; NONE99-NEXT:    [[CHK:%.*]] = icmp eq ptr [[TMP1]], null
; NONE99-NEXT:    [[HOT:%.*]] = xor i1 false, true
; NONE99-NEXT:    [[TMP5:%.*]] = or i1 [[CHK]], [[HOT]]
; NONE99-NEXT:    br i1 [[TMP5]], label [[TMP6:%.*]], label [[TMP7:%.*]]
; NONE99:       6:
; NONE99-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; NONE99-NEXT:    unreachable
; NONE99:       7:
; NONE99-NEXT:    [[TMP8:%.*]] = load i32, ptr [[TMP1]], align 4
; NONE99-NEXT:    br label [[TMP9]]
; NONE99:       9:
; NONE99-NEXT:    [[TMP10:%.*]] = phi i32 [ [[TMP8]], [[TMP7]] ], [ 0, [[TMP2:%.*]] ]
; NONE99-NEXT:    ret i32 [[TMP10]]
;
; ALL70-LABEL: define dso_local noundef i32 @branchHotFnCold(
; ALL70-SAME: i32 noundef [[TMP0:%.*]], ptr noundef readonly [[TMP1:%.*]]) !prof [[PROF16]] {
; ALL70-NEXT:    [[TMP3:%.*]] = icmp eq i32 [[TMP0]], 0
; ALL70-NEXT:    br i1 [[TMP3]], label [[TMP9:%.*]], label [[TMP4:%.*]], !prof [[PROF19:![0-9]+]]
; ALL70:       4:
; ALL70-NEXT:    [[CHK:%.*]] = icmp eq ptr [[TMP1]], null
; ALL70-NEXT:    [[HOT:%.*]] = xor i1 true, true
; ALL70-NEXT:    [[TMP5:%.*]] = or i1 [[CHK]], [[HOT]]
; ALL70-NEXT:    br i1 [[TMP5]], label [[TMP6:%.*]], label [[TMP7:%.*]]
; ALL70:       6:
; ALL70-NEXT:    tail call void @llvm.ubsantrap(i8 22)
; ALL70-NEXT:    unreachable
; ALL70:       7:
; ALL70-NEXT:    [[TMP8:%.*]] = load i32, ptr [[TMP1]], align 4
; ALL70-NEXT:    br label [[TMP9]]
; ALL70:       9:
; ALL70-NEXT:    [[TMP10:%.*]] = phi i32 [ [[TMP8]], [[TMP7]] ], [ 0, [[TMP2:%.*]] ]
; ALL70-NEXT:    ret i32 [[TMP10]]
;
  %3 = icmp eq i32 %0, 0
  br i1 %3, label %9, label %4, !prof !37

4:
  %chk = icmp eq ptr %1, null
  %allow = call i1 @llvm.allow.ubsan.check(i8 22)
  %hot = xor i1 %allow, true
  %5 = or i1 %chk, %hot
  br i1 %5, label %6, label %7

6:
  tail call void @llvm.ubsantrap(i8 22) #2
  unreachable

7:
  %8 = load i32, ptr %1, align 4
  br label %9

9:
  %10 = phi i32 [ %8, %7 ], [ 0, %2 ]
  ret i32 %10
}

!llvm.module.flags = !{!6}
!6 = !{i32 1, !"ProfileSummary", !7}
!7 = !{!8, !9, !10, !11, !12, !13, !14, !17}
!8 = !{!"ProfileFormat", !"InstrProf"}
!9 = !{!"TotalCount", i64 30000}
!10 = !{!"MaxCount", i64 10000}
!11 = !{!"MaxInternalCount", i64 10000}
!12 = !{!"MaxFunctionCount", i64 10000}
!13 = !{!"NumCounts", i64 3}
!14 = !{!"NumFunctions", i64 5}
!17 = !{!"DetailedSummary", !18}
!18 = !{!19, !29, !30, !32, !34}
!19 = !{i32 10000, i64 10000, i32 3}
!29 = !{i32 950000, i64 5000, i32 3}
!30 = !{i32 990000, i64 500, i32 4}
!32 = !{i32 999900, i64 250, i32 4}
!34 = !{i32 999999, i64 1, i32 6}

!36 = !{!"function_entry_count", i64 1000}
!39 = !{!"function_entry_count", i64 7000}

!37 = !{!"branch_weights", i32 1, i32 1000}
!38 = !{!"branch_weights", i32 1000, i32 1}

;.
; NOPROFILE: [[PROF16]] = !{!"function_entry_count", i64 1000}
; NOPROFILE: [[PROF17]] = !{!"function_entry_count", i64 7000}
; NOPROFILE: [[PROF18]] = !{!"branch_weights", i32 1000, i32 1}
; NOPROFILE: [[PROF19]] = !{!"branch_weights", i32 1, i32 1000}
;.
; NONE: [[PROF16]] = !{!"function_entry_count", i64 1000}
; NONE: [[PROF17]] = !{!"function_entry_count", i64 7000}
; NONE: [[PROF18]] = !{!"branch_weights", i32 1000, i32 1}
; NONE: [[PROF19]] = !{!"branch_weights", i32 1, i32 1000}
;.
; ALL: [[PROF16]] = !{!"function_entry_count", i64 1000}
; ALL: [[PROF17]] = !{!"function_entry_count", i64 7000}
; ALL: [[PROF18]] = !{!"branch_weights", i32 1000, i32 1}
; ALL: [[PROF19]] = !{!"branch_weights", i32 1, i32 1000}
;.
; HOT99: [[PROF16]] = !{!"function_entry_count", i64 1000}
; HOT99: [[PROF17]] = !{!"function_entry_count", i64 7000}
; HOT99: [[PROF18]] = !{!"branch_weights", i32 1000, i32 1}
; HOT99: [[PROF19]] = !{!"branch_weights", i32 1, i32 1000}
;.
; HOT70: [[PROF16]] = !{!"function_entry_count", i64 1000}
; HOT70: [[PROF17]] = !{!"function_entry_count", i64 7000}
; HOT70: [[PROF18]] = !{!"branch_weights", i32 1000, i32 1}
; HOT70: [[PROF19]] = !{!"branch_weights", i32 1, i32 1000}
;.
; NONE99: [[PROF16]] = !{!"function_entry_count", i64 1000}
; NONE99: [[PROF17]] = !{!"function_entry_count", i64 7000}
; NONE99: [[PROF18]] = !{!"branch_weights", i32 1000, i32 1}
; NONE99: [[PROF19]] = !{!"branch_weights", i32 1, i32 1000}
;.
; ALL70: [[PROF16]] = !{!"function_entry_count", i64 1000}
; ALL70: [[PROF17]] = !{!"function_entry_count", i64 7000}
; ALL70: [[PROF18]] = !{!"branch_weights", i32 1000, i32 1}
; ALL70: [[PROF19]] = !{!"branch_weights", i32 1, i32 1000}
;.
