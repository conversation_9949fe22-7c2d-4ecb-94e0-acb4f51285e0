; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5

;; Test to ensure that memprof related metadata is not dropped when
;; instructions are combined. Currently the metadata from the first instruction
;; is kept, which prevents full loss of profile context information.

; RUN: opt < %s -passes=simplifycfg -S | FileCheck %s

target datalayout = "e-m:e-p270:32:32-p271:32:32-p272:64:64-i64:64-i128:128-f80:128-n8:16:32:64-S128"
target triple = "x86_64-unknown-linux-gnu"

define dso_local noundef nonnull ptr @_Z4testb(i1 noundef zeroext %b) local_unnamed_addr #0 {
; CHECK-LABEL: define dso_local noundef nonnull ptr @_Z4testb(
; CHECK-SAME: i1 noundef zeroext [[B:%.*]]) local_unnamed_addr {
; CHECK-NEXT:  [[ENTRY:.*:]]
; CHECK-NEXT:    [[CALL:%.*]] = call noalias noundef nonnull dereferenceable(4) ptr @_Znwm(i64 noundef 4), !memprof [[META0:![0-9]+]], !callsite [[META3:![0-9]+]]
; CHECK-NEXT:    ret ptr [[CALL]]
;
entry:
  br i1 %b, label %if.then, label %if.else

if.then:                                          ; preds = %entry
  %call = call noalias noundef nonnull dereferenceable(4) ptr @_Znwm(i64 noundef 4), !memprof !0, !callsite !3
  br label %if.end

if.else:                                          ; preds = %entry
  %call1 = call noalias noundef nonnull dereferenceable(4) ptr @_Znwm(i64 noundef 4), !memprof !4, !callsite !7
  br label %if.end

if.end:                                           ; preds = %if.else, %if.then
  %x.0 = phi ptr [ %call, %if.then ], [ %call1, %if.else ]
  ret ptr %x.0
}


declare ptr @_Znwm(i64) nounwind readonly

!0 = !{!1}
!1 = !{!2, !"notcold"}
!2 = !{i64 -852997907418798798, i64 -2101080423462424381, i64 5188446645037944434}
!3 = !{i64 -852997907418798798}
!4 = !{!5}
!5 = !{!6, !"cold"}
!6 = !{i64 123, i64 -2101080423462424381, i64 5188446645037944434}
!7 = !{i64 123}
;.
; CHECK: [[META0]] = !{[[META1:![0-9]+]]}
; CHECK: [[META1]] = !{[[META2:![0-9]+]], !"notcold"}
; CHECK: [[META2]] = !{i64 -852997907418798798, i64 -2101080423462424381, i64 5188446645037944434}
; CHECK: [[META3]] = !{i64 -852997907418798798}
;.
