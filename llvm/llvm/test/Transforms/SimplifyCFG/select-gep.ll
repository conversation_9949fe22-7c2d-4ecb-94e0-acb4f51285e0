; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -S -passes=simplifycfg -simplifycfg-require-and-preserve-domtree=1 < %s | FileCheck %s

%ST = type { i8, i8 }

define ptr @test1(ptr %x, ptr %y) nounwind {
; CHECK-LABEL: @test1(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[CMP:%.*]] = icmp eq ptr [[X:%.*]], null
; CHECK-NEXT:    [[INCDEC_PTR:%.*]] = getelementptr [[ST:%.*]], ptr [[X]], i32 0, i32 1
; CHECK-NEXT:    [[SPEC_SELECT:%.*]] = select i1 [[CMP]], ptr [[INCDEC_PTR]], ptr [[Y:%.*]]
; CHECK-NEXT:    ret ptr [[SPEC_SELECT]]
;
entry:
  %cmp = icmp eq ptr %x, null
  br i1 %cmp, label %if.then, label %if.end

if.then:
  %incdec.ptr = getelementptr %ST, ptr %x, i32 0, i32 1
  br label %if.end

if.end:
  %x.addr = phi ptr [ %incdec.ptr, %if.then ], [ %y, %entry ]
  ret ptr %x.addr

}
