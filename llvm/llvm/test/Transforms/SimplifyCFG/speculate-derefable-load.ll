; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt -S -passes=simplifycfg < %s | FileCheck %s

define i64 @align_deref_align(i1 %c, ptr %p) {
; CHECK-LABEL: define i64 @align_deref_align(
; CHECK-SAME: i1 [[C:%.*]], ptr [[P:%.*]]) {
; CHECK-NEXT:  [[ENTRY:.*:]]
; CHECK-NEXT:    call void @llvm.assume(i1 true) [ "dereferenceable"(ptr [[P]], i64 8), "align"(ptr [[P]], i64 8) ]
; CHECK-NEXT:    [[V:%.*]] = load i64, ptr [[P]], align 8
; CHECK-NEXT:    [[RES:%.*]] = select i1 [[C]], i64 [[V]], i64 0
; CHECK-NEXT:    ret i64 [[RES]]
;
entry:
  call void @llvm.assume(i1 true) [ "dereferenceable"(ptr %p, i64 8), "align"(ptr %p, i64 8) ]
  br i1 %c, label %if, label %exit

if:
  %v = load i64, ptr %p, align 8
  br label %exit

exit:
  %res = phi i64 [ %v, %if ], [ 0, %entry ]
  ret i64 %res
}

define i64 @assume_deref_align2(i1 %c1, i32 %x, ptr %p) {
; CHECK-LABEL: define i64 @assume_deref_align2(
; CHECK-SAME: i1 [[C1:%.*]], i32 [[X:%.*]], ptr [[P:%.*]]) {
; CHECK-NEXT:  [[ENTRY:.*:]]
; CHECK-NEXT:    call void @llvm.assume(i1 true) [ "dereferenceable"(ptr [[P]], i64 8), "align"(ptr [[P]], i64 8) ]
; CHECK-NEXT:    [[C2:%.*]] = icmp ugt i32 [[X]], 10
; CHECK-NEXT:    [[V:%.*]] = load i64, ptr [[P]], align 8
; CHECK-NEXT:    [[SPEC_SELECT:%.*]] = select i1 [[C2]], i64 [[V]], i64 1
; CHECK-NEXT:    [[RES:%.*]] = select i1 [[C1]], i64 [[SPEC_SELECT]], i64 0
; CHECK-NEXT:    ret i64 [[RES]]
;
entry:
  call void @llvm.assume(i1 true) [ "dereferenceable"(ptr %p, i64 8), "align"(ptr %p, i64 8) ]
  br i1 %c1, label %if1, label %exit

if1:
  %c2 = icmp ugt i32 %x, 10
  br i1 %c2, label %if2, label %exit

if2:
  %v = load i64, ptr %p, align 8
  br label %exit

exit:
  %res = phi i64 [ %v, %if2 ], [ 1, %if1 ], [ 0, %entry ]
  ret i64 %res
}

define i64 @assume_deref_align_not_dominating(i1 %c, ptr %p) {
; CHECK-LABEL: define i64 @assume_deref_align_not_dominating(
; CHECK-SAME: i1 [[C:%.*]], ptr [[P:%.*]]) {
; CHECK-NEXT:  [[ENTRY:.*]]:
; CHECK-NEXT:    br i1 [[C]], label %[[IF:.*]], label %[[EXIT:.*]]
; CHECK:       [[IF]]:
; CHECK-NEXT:    [[V:%.*]] = load i64, ptr [[P]], align 8
; CHECK-NEXT:    br label %[[EXIT]]
; CHECK:       [[EXIT]]:
; CHECK-NEXT:    [[RES:%.*]] = phi i64 [ [[V]], %[[IF]] ], [ 0, %[[ENTRY]] ]
; CHECK-NEXT:    call void @llvm.assume(i1 true) [ "dereferenceable"(ptr [[P]], i64 8), "align"(ptr [[P]], i64 8) ]
; CHECK-NEXT:    ret i64 [[RES]]
;
entry:
  br i1 %c, label %if, label %exit

if:
  %v = load i64, ptr %p, align 8
  br label %exit

exit:
  %res = phi i64 [ %v, %if ], [ 0, %entry ]
  call void @llvm.assume(i1 true) [ "dereferenceable"(ptr %p, i64 8), "align"(ptr %p, i64 8) ]
  ret i64 %res
}

define i64 @deref_no_hoist(i1 %c, ptr align 8 dereferenceable(8) %p1) {
; CHECK-LABEL: define i64 @deref_no_hoist(
; CHECK-SAME: i1 [[C:%.*]], ptr align 8 dereferenceable(8) [[P1:%.*]]) {
; CHECK-NEXT:  [[ENTRY:.*]]:
; CHECK-NEXT:    br i1 [[C]], label %[[IF:.*]], label %[[EXIT:.*]]
; CHECK:       [[IF]]:
; CHECK-NEXT:    [[P2:%.*]] = load ptr, ptr [[P1]], align 8, !dereferenceable [[META0:![0-9]+]], !align [[META0]]
; CHECK-NEXT:    [[V:%.*]] = load i64, ptr [[P2]], align 8
; CHECK-NEXT:    br label %[[EXIT]]
; CHECK:       [[EXIT]]:
; CHECK-NEXT:    [[RES:%.*]] = phi i64 [ [[V]], %[[IF]] ], [ 0, %[[ENTRY]] ]
; CHECK-NEXT:    ret i64 [[RES]]
;
entry:
  br i1 %c, label %if, label %exit

if:
  %p2 = load ptr, ptr %p1, align 8, !dereferenceable !0, !align !0
  %v = load i64, ptr %p2, align 8
  br label %exit

exit:
  %res = phi i64 [ %v, %if ], [ 0, %entry ]
  ret i64 %res
}

define i64 @deref_hoist(i1 %c, ptr align 8 dereferenceable(8) %p1) {
; CHECK-LABEL: define i64 @deref_hoist(
; CHECK-SAME: i1 [[C:%.*]], ptr align 8 dereferenceable(8) [[P1:%.*]]) {
; CHECK-NEXT:  [[ENTRY:.*:]]
; CHECK-NEXT:    [[P2:%.*]] = load ptr, ptr [[P1]], align 8, !dereferenceable [[META0]], !align [[META0]]
; CHECK-NEXT:    [[V:%.*]] = load i64, ptr [[P2]], align 8
; CHECK-NEXT:    [[RES:%.*]] = select i1 [[C]], i64 [[V]], i64 0
; CHECK-NEXT:    ret i64 [[RES]]
;
entry:
  %p2 = load ptr, ptr %p1, align 8, !dereferenceable !0, !align !0
  br i1 %c, label %if, label %exit

if:
  %v = load i64, ptr %p2, align 8
  br label %exit

exit:
  %res = phi i64 [ %v, %if ], [ 0, %entry ]
  ret i64 %res
}

define i64 @deref_no_hoist2(i1 %c1, i32 %x, ptr align 8 dereferenceable(8) %p1) {
; CHECK-LABEL: define i64 @deref_no_hoist2(
; CHECK-SAME: i1 [[C1:%.*]], i32 [[X:%.*]], ptr align 8 dereferenceable(8) [[P1:%.*]]) {
; CHECK-NEXT:  [[ENTRY:.*]]:
; CHECK-NEXT:    br i1 [[C1]], label %[[IF1:.*]], label %[[EXIT:.*]]
; CHECK:       [[IF1]]:
; CHECK-NEXT:    [[C2:%.*]] = icmp ugt i32 [[X]], 10
; CHECK-NEXT:    br i1 [[C2]], label %[[IF2:.*]], label %[[EXIT]]
; CHECK:       [[IF2]]:
; CHECK-NEXT:    [[P2:%.*]] = load ptr, ptr [[P1]], align 8, !dereferenceable [[META0]], !align [[META0]]
; CHECK-NEXT:    [[V:%.*]] = load i64, ptr [[P2]], align 8
; CHECK-NEXT:    br label %[[EXIT]]
; CHECK:       [[EXIT]]:
; CHECK-NEXT:    [[RES:%.*]] = phi i64 [ [[V]], %[[IF2]] ], [ 1, %[[IF1]] ], [ 0, %[[ENTRY]] ]
; CHECK-NEXT:    ret i64 [[RES]]
;
entry:
  br i1 %c1, label %if1, label %exit

if1:
  %c2 = icmp ugt i32 %x, 10
  br i1 %c2, label %if2, label %exit

if2:
  %p2 = load ptr, ptr %p1, align 8, !dereferenceable !0, !align !0
  %v = load i64, ptr %p2, align 8
  br label %exit

exit:
  %res = phi i64 [ %v, %if2 ], [ 1, %if1 ], [ 0, %entry ]
  ret i64 %res
}

define i64 @deref_hoist2(i1 %c1, i32 %x, ptr align 8 dereferenceable(8) %p1) {
; CHECK-LABEL: define i64 @deref_hoist2(
; CHECK-SAME: i1 [[C1:%.*]], i32 [[X:%.*]], ptr align 8 dereferenceable(8) [[P1:%.*]]) {
; CHECK-NEXT:  [[ENTRY:.*:]]
; CHECK-NEXT:    [[P2:%.*]] = load ptr, ptr [[P1]], align 8, !dereferenceable [[META0]], !align [[META0]]
; CHECK-NEXT:    [[C2:%.*]] = icmp ugt i32 [[X]], 10
; CHECK-NEXT:    [[V:%.*]] = load i64, ptr [[P2]], align 8
; CHECK-NEXT:    [[SPEC_SELECT:%.*]] = select i1 [[C2]], i64 [[V]], i64 1
; CHECK-NEXT:    [[RES:%.*]] = select i1 [[C1]], i64 [[SPEC_SELECT]], i64 0
; CHECK-NEXT:    ret i64 [[RES]]
;
entry:
  %p2 = load ptr, ptr %p1, align 8, !dereferenceable !0, !align !0
  br i1 %c1, label %if1, label %exit

if1:
  %c2 = icmp ugt i32 %x, 10
  br i1 %c2, label %if2, label %exit

if2:
  %v = load i64, ptr %p2, align 8
  br label %exit

exit:
  %res = phi i64 [ %v, %if2 ], [ 1, %if1 ], [ 0, %entry ]
  ret i64 %res
}

!0 = !{i64 8}
;.
; CHECK: [[META0]] = !{i64 8}
;.
