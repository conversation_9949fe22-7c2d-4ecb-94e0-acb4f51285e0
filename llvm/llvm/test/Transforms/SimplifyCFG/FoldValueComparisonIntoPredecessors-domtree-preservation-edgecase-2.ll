; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -S -passes=simplifycfg -simplifycfg-require-and-preserve-domtree=1 < %s | FileCheck %s

declare void @widget()
declare i16 @baz()
declare void @snork()
declare void @spam()

define void @zot() local_unnamed_addr align 2 personality ptr undef {
; CHECK-LABEL: @zot(
; CHECK-NEXT:  bb:
; CHECK-NEXT:    invoke void @widget()
; CHECK-NEXT:            to label [[BB14:%.*]] unwind label [[BB21:%.*]]
; CHECK:       bb14:
; CHECK-NEXT:    [[I0:%.*]] = invoke i16 @baz()
; CHECK-NEXT:            to label [[BB15:%.*]] unwind label [[BB21]]
; CHECK:       bb15:
; CHECK-NEXT:    switch i16 [[I0]], label [[BB19:%.*]] [
; CHECK-NEXT:      i16 42, label [[BB23:%.*]]
; CHECK-NEXT:      i16 21330, label [[BB23]]
; CHECK-NEXT:    ]
; CHECK:       bb19:
; CHECK-NEXT:    invoke void @snork()
; CHECK-NEXT:            to label [[BB20:%.*]] unwind label [[BB21]]
; CHECK:       bb20:
; CHECK-NEXT:    unreachable
; CHECK:       common.ret:
; CHECK-NEXT:    ret void
; CHECK:       bb21:
; CHECK-NEXT:    [[I22:%.*]] = landingpad { ptr, i32 }
; CHECK-NEXT:            cleanup
; CHECK-NEXT:    br label [[COMMON_RET:%.*]]
; CHECK:       bb23:
; CHECK-NEXT:    invoke void @spam()
; CHECK-NEXT:            to label [[COMMON_RET]] unwind label [[BB21]]
;
bb:
  invoke void @widget()
  to label %bb14 unwind label %bb21

bb14:                                             ; preds = %bb
  %i0 = invoke i16 @baz()
  to label %bb15 unwind label %bb25

bb15:                                             ; preds = %bb14
  %i16 = icmp eq i16 %i0, 42
  br i1 %i16, label %bb23, label %bb17

bb17:                                             ; preds = %bb15
  %i18 = icmp eq i16 %i0, 21330
  br i1 %i18, label %bb23, label %bb19

bb19:                                             ; preds = %bb17
  invoke void @snork()
  to label %bb20 unwind label %bb25

bb20:                                             ; preds = %bb19
  unreachable

bb21:                                             ; preds = %bb
  %i22 = landingpad { ptr, i32 }
  cleanup
  ret void

bb23:                                             ; preds = %bb17, %bb15
  invoke void @spam()
  to label %bb24 unwind label %bb25

bb24:                                             ; preds = %bb25, %bb23
  ret void

bb25:                                             ; preds = %bb23, %bb19, %bb14
  %i26 = landingpad { ptr, i32 }
  cleanup
  br label %bb24
}
