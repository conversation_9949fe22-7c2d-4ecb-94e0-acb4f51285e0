; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -S -passes=simplifycfg -simplifycfg-require-and-preserve-domtree=1 < %s | FileCheck %s

declare i8 @next_char();

define void @test_no_msan() {
; CHECK-LABEL: @test_no_msan(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    br label [[WHILE_BODY:%.*]]
; CHECK:       while.body:
; CHECK-NEXT:    br label [[WHILE_BODY_I:%.*]]
; CHECK:       while.body.i:
; CHECK-NEXT:    [[MAYBE_UNDEF:%.*]] = phi i1 [ undef, [[WHILE_BODY]] ], [ [[NEXT_MAYBE_UNDEF:%.*]], [[WHILE_BODY_I]] ]
; CHECK-NEXT:    [[C:%.*]] = call fastcc signext i8 @next_char()
; CHECK-NEXT:    [[C_10:%.*]] = icmp eq i8 [[C]], 10
; CHECK-NEXT:    [[C_13:%.*]] = icmp eq i8 [[C]], 13
; CHECK-NEXT:    [[C_10_OR_13:%.*]] = or i1 [[C_10]], [[C_13]]
; CHECK-NEXT:    [[NEXT_MAYBE_UNDEF]] = or i1 [[MAYBE_UNDEF]], [[C_10_OR_13]]
; CHECK-NEXT:    [[C_NOT_10_OR_13:%.*]] = xor i1 [[C_10_OR_13]], true
; CHECK-NEXT:    br i1 [[C_NOT_10_OR_13]], label [[WHILE_BODY_I]], label [[WHILE_BODY_I_BREAK:%.*]]
; CHECK:       while.body.i.break:
; CHECK-NEXT:    [[TMP0:%.*]] = freeze i1 [[MAYBE_UNDEF]]
; CHECK-NEXT:    br i1 [[TMP0]], label [[WHILE_BODY]], label [[SWITCH_EARLY_TEST:%.*]]
; CHECK:       switch.early.test:
; CHECK-NEXT:    switch i8 [[C]], label [[RETURN:%.*]] [
; CHECK-NEXT:      i8 13, label [[WHILE_BODY]]
; CHECK-NEXT:      i8 10, label [[WHILE_BODY]]
; CHECK-NEXT:    ]
; CHECK:       return:
; CHECK-NEXT:    ret void
;

entry:
  br label %while.body

while.body:
  br label %while.body.i

while.body.i:
  %maybe_undef = phi i1 [ undef, %while.body ], [ %next_maybe_undef, %while.body.i ]
  %c = call fastcc signext i8 @next_char()
  %c_10 = icmp eq i8 %c, 10
  %c_13 = icmp eq i8 %c, 13
  %c_10_or_13 = or i1 %c_10, %c_13
  %next_maybe_undef = or i1 %maybe_undef, %c_10_or_13
  %c_not_10_or_13 = xor i1 %c_10_or_13, true
  br i1 %c_not_10_or_13, label %while.body.i, label %while.body.i.break

while.body.i.break:
  ; NEXT_MAYBE_UNDEF is never undef if here
  br i1 %next_maybe_undef, label %while.body, label %return


return:
  ret void
}


define void @test_msan() sanitize_memory {
; CHECK-LABEL: @test_msan(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    br label [[WHILE_BODY:%.*]]
; CHECK:       while.body:
; CHECK-NEXT:    br label [[WHILE_BODY_I:%.*]]
; CHECK:       while.body.i:
; CHECK-NEXT:    [[MAYBE_UNDEF:%.*]] = phi i1 [ undef, [[WHILE_BODY]] ], [ [[NEXT_MAYBE_UNDEF:%.*]], [[WHILE_BODY_I]] ]
; CHECK-NEXT:    [[C:%.*]] = call fastcc signext i8 @next_char()
; CHECK-NEXT:    [[C_10:%.*]] = icmp eq i8 [[C]], 10
; CHECK-NEXT:    [[C_13:%.*]] = icmp eq i8 [[C]], 13
; CHECK-NEXT:    [[C_10_OR_13:%.*]] = or i1 [[C_10]], [[C_13]]
; CHECK-NEXT:    [[NEXT_MAYBE_UNDEF]] = or i1 [[MAYBE_UNDEF]], [[C_10_OR_13]]
; CHECK-NEXT:    [[C_NOT_10_OR_13:%.*]] = xor i1 [[C_10_OR_13]], true
; CHECK-NEXT:    br i1 [[C_NOT_10_OR_13]], label [[WHILE_BODY_I]], label [[WHILE_BODY_I_BREAK:%.*]]
; CHECK:       while.body.i.break:
; CHECK-NEXT:    [[TMP0:%.*]] = freeze i1 [[MAYBE_UNDEF]]
; CHECK-NEXT:    br i1 [[TMP0]], label [[WHILE_BODY]], label [[SWITCH_EARLY_TEST:%.*]]
; CHECK:       switch.early.test:
; CHECK-NEXT:    switch i8 [[C]], label [[RETURN:%.*]] [
; CHECK-NEXT:      i8 13, label [[WHILE_BODY]]
; CHECK-NEXT:      i8 10, label [[WHILE_BODY]]
; CHECK-NEXT:    ]
; CHECK:       return:
; CHECK-NEXT:    ret void
;

entry:
  br label %while.body

while.body:
  br label %while.body.i

while.body.i:
  %maybe_undef = phi i1 [ undef, %while.body ], [ %next_maybe_undef, %while.body.i ]
  %c = call fastcc signext i8 @next_char()
  %c_10 = icmp eq i8 %c, 10
  %c_13 = icmp eq i8 %c, 13
  %c_10_or_13 = or i1 %c_10, %c_13
  %next_maybe_undef = or i1 %maybe_undef, %c_10_or_13
  %c_not_10_or_13 = xor i1 %c_10_or_13, true
  br i1 %c_not_10_or_13, label %while.body.i, label %while.body.i.break

while.body.i.break:
  ; NEXT_MAYBE_UNDEF is never undef if here
  br i1 %next_maybe_undef, label %while.body, label %return


return:
  ret void
}
