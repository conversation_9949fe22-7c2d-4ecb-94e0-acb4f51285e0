; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -S -passes=simplifycfg -simplifycfg-require-and-preserve-domtree < %s | FileCheck %s

declare void @foo(i32)

define void @br_undef_simple(i1 %arg) {
; CHECK-LABEL: @br_undef_simple(
; CHECK-NEXT:    call void @foo(i32 0)
; CHECK-NEXT:    br i1 %arg, label [[IF:%.*]], label [[ELSE:%.*]]
; CHECK:       common.ret:
; CHECK-NEXT:    ret void
; CHECK:       if:
; CHECK-NEXT:    call void @foo(i32 1)
; CHECK-NEXT:    br label [[COMMON_RET:%.*]]
; CHECK:       else:
; CHECK-NEXT:    call void @foo(i32 2)
; CHECK-NEXT:    br label [[COMMON_RET]]
;
  call void @foo(i32 0)
  br i1 %arg, label %if, label %else

if:
  call void @foo(i32 1)
  ret void

else:
  call void @foo(i32 2)
  ret void
}

define void @br_poison_simple() {
; CHECK-LABEL: @br_poison_simple(
; CHECK-NEXT:    call void @foo(i32 0)
; CHECK-NEXT:    br i1 poison, label [[IF:%.*]], label [[ELSE:%.*]]
; CHECK:       common.ret:
; CHECK-NEXT:    ret void
; CHECK:       if:
; CHECK-NEXT:    call void @foo(i32 1)
; CHECK-NEXT:    br label [[COMMON_RET:%.*]]
; CHECK:       else:
; CHECK-NEXT:    call void @foo(i32 2)
; CHECK-NEXT:    br label [[COMMON_RET]]
;
  call void @foo(i32 0)
  br i1 poison, label %if, label %else

if:
  call void @foo(i32 1)
  ret void

else:
  call void @foo(i32 2)
  ret void
}

define void @br_poison_succs_used(i32 %v) {
; CHECK-LABEL: @br_poison_succs_used(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    switch i32 [[V:%.*]], label [[BR:%.*]] [
; CHECK-NEXT:      i32 1, label [[IF:%.*]]
; CHECK-NEXT:      i32 2, label [[ELSE:%.*]]
; CHECK-NEXT:    ]
; CHECK:       br:
; CHECK-NEXT:    call void @foo(i32 0)
; CHECK-NEXT:    br i1 poison, label [[IF]], label [[ELSE]]
; CHECK:       common.ret:
; CHECK-NEXT:    ret void
; CHECK:       if:
; CHECK-NEXT:    [[PHI1:%.*]] = phi i32 [ 1, [[BR]] ], [ 2, [[ENTRY:%.*]] ]
; CHECK-NEXT:    call void @foo(i32 [[PHI1]])
; CHECK-NEXT:    br label [[COMMON_RET:%.*]]
; CHECK:       else:
; CHECK-NEXT:    [[PHI2:%.*]] = phi i32 [ 3, [[BR]] ], [ 4, [[ENTRY]] ]
; CHECK-NEXT:    call void @foo(i32 [[PHI2]])
; CHECK-NEXT:    br label [[COMMON_RET]]
;
entry:
  switch i32 %v, label %br [
  i32 1, label %if
  i32 2, label %else
  ]

br:
  call void @foo(i32 0)
  br i1 poison, label %if, label %else

if:
  %phi1 = phi i32 [ 1, %br ], [ 2, %entry ]
  call void @foo(i32 %phi1)
  ret void

else:
  %phi2 = phi i32 [ 3, %br ], [ 4, %entry ]
  call void @foo(i32 %phi2)
  ret void
}
