; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 4
; RUN: opt %s -S -passes='simplifycfg<switch-to-lookup>' -simplifycfg-require-and-preserve-domtree=1 -switch-range-to-icmp | FileCheck %s

target datalayout = "e-m:e-p270:32:32-p271:32:32-p272:64:64-i64:64-f80:128-n8:16:32:64-S128"

define i64 @test_1(i64 %0) {
; CHECK-LABEL: define i64 @test_1(
; CHECK-SAME: i64 [[TMP0:%.*]]) {
; CHECK-NEXT:  switch.lookup:
; CHECK-NEXT:    [[TMP1:%.*]] = urem i64 [[TMP0]], 4
; CHECK-NEXT:    [[SWITCH_GEP:%.*]] = getelementptr inbounds [4 x i64], ptr @switch.table.test_1, i32 0, i64 [[TMP1]]
; CHECK-NEXT:    [[SWITCH_LOAD:%.*]] = load i64, ptr [[SWITCH_GEP]], align 8
; CHECK-NEXT:    ret i64 [[SWITCH_LOAD]]
;
  %2 = urem i64 %0, 4
  switch i64 %2, label %5 [
  i64 1, label %3
  i64 2, label %3
  i64 3, label %4
  ]

3:
  br label %5

4:
  br label %5

5:
  %.0 = phi i64 [ 2, %4 ], [ 1, %3 ], [ 0, %1 ]
  ret i64 %.0
}


define i64 @test_2(i64 %0) {
; CHECK-LABEL: define i64 @test_2(
; CHECK-SAME: i64 [[TMP0:%.*]]) {
; CHECK-NEXT:  switch.lookup:
; CHECK-NEXT:    [[TMP1:%.*]] = urem i64 [[TMP0]], 4
; CHECK-NEXT:    ret i64 [[TMP1]]
;
  %2 = urem i64 %0, 4
  switch i64 %2, label %6 [
  i64 1, label %3
  i64 2, label %4
  i64 3, label %5
  ]

3:
  br label %6

4:
  br label %6

5:
  br label %6

6:
  %.0 = phi i64 [ 0, %1 ], [ 1, %3 ], [ 2, %4 ], [ 3, %5 ]
  ret i64 %.0
}

