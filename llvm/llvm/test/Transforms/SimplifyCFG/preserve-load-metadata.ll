; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 5
; RUN: opt < %s -passes=simplifycfg -simplifycfg-require-and-preserve-domtree=1 -hoist-common-insts=true -S | FileCheck %s

declare void @bar(ptr)
declare void @baz(ptr)

; Check that align metadata is combined
define void @test_load_combine_metadata(i1 %c, ptr %p) {
; CHECK-LABEL: define void @test_load_combine_metadata(
; CHECK-SAME: i1 [[C:%.*]], ptr [[P:%.*]]) {
; CHECK-NEXT:    [[V1:%.*]] = load ptr, ptr [[P]], align 8, !align [[META0:![0-9]+]]
; CHECK-NEXT:    br i1 [[C]], label %[[T:.*]], label %[[F:.*]]
; CHECK:       [[T]]:
; CHECK-NEXT:    call void @bar(ptr [[V1]])
; CHECK-NEXT:    br label %[[CONT:.*]]
; CHECK:       [[F]]:
; CHECK-NEXT:    call void @baz(ptr [[V1]])
; CHECK-NEXT:    br label %[[CONT]]
; CHECK:       [[CONT]]:
; CHECK-NEXT:    ret void
;
  br i1 %c, label %t, label %f

t:
  %v1 = load ptr, ptr %p, !align !0
  call void @bar(ptr %v1)
  br label %cont

f:
  %v2 = load ptr, ptr %p, !align !1
  call void @baz(ptr %v2)
  br label %cont

cont:
  ret void
}

define i64 @test_intersect_access_grou_metadata(i1 %c, ptr %p) {
; CHECK-LABEL: define i64 @test_intersect_access_grou_metadata(
; CHECK-SAME: i1 [[C:%.*]], ptr [[P:%.*]]) {
; CHECK-NEXT:  [[ENTRY:.*:]]
; CHECK-NEXT:    [[L_1:%.*]] = load i64, ptr [[P]], align 8, !llvm.access.group [[ACC_GRP1:![0-9]+]]
; CHECK-NEXT:    ret i64 [[L_1]]
;
entry:
  br i1 %c, label %then, label %else

then:
  %l.1 = load i64, ptr %p, align 8, !llvm.access.group !2
  br label %exit

else:
  %l.2 = load i64, ptr %p, align 8, !llvm.access.group !5
  br label %exit

exit:
  %res = phi i64 [ %l.1, %then ], [ %l.2, %else ]
  ret i64 %res
}

!0 = !{i64 8}
!1 = !{i64 16}
!2 = !{!3, !4}
!3 = distinct !{}
!4 = distinct !{}
!5 = !{!3, !6}
!6 = distinct !{}
;.
; CHECK: [[META0]] = !{i64 8}
; CHECK: [[ACC_GRP1]] = distinct !{}
;.
