; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt < %s -passes=simplifycfg -simplifycfg-require-and-preserve-domtree=1 -S | FileCheck %s

declare void @bar()
declare void @baz()

define i8 @foo(i1 %c, i8 %v0, i8 %v1) {
; CHECK-LABEL: @foo(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[V0_V1:%.*]] = select i1 [[C:%.*]], i8 [[V0:%.*]], i8 [[V1:%.*]]
; CHECK-NEXT:    ret i8 [[V0_V1]]
;
entry:
  br i1 %c, label %true, label %false

true:
  ret i8 %v0

false:
  ret i8 %v1
}
