; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -passes=simplifycfg -simplifycfg-require-and-preserve-domtree=1 -S < %s | FileCheck %s

define noundef i32 @test_ret_noundef(i1 %cond) {
; CHECK-LABEL: @test_ret_noundef(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    ret i32 1
;
entry:
  br i1 %cond, label %bb1, label %bb2

bb1:
  br label %bb2

bb2:
  %r = phi i32 [ undef, %entry ], [ 1, %bb1 ]
  ret i32 %r
}

define i32 @test_ret(i1 %cond) {
; CHECK-LABEL: @test_ret(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[SPEC_SELECT:%.*]] = select i1 [[COND:%.*]], i32 1, i32 undef
; CHECK-NEXT:    ret i32 [[SPEC_SELECT]]
;
entry:
  br i1 %cond, label %bb1, label %bb2

bb1:
  br label %bb2

bb2:
  %r = phi i32 [ undef, %entry ], [ 1, %bb1 ]
  ret i32 %r
}

define nonnull noundef ptr @test_ret_ptr_nonnull_noundef(i1 %cond, ptr %x) {
; CHECK-LABEL: @test_ret_ptr_nonnull_noundef(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    call void @llvm.assume(i1 [[COND:%.*]])
; CHECK-NEXT:    ret ptr [[X:%.*]]
;
entry:
  br i1 %cond, label %bb1, label %bb2

bb1:
  br label %bb2

bb2:
  %r = phi ptr [ null, %entry ], [ %x, %bb1 ]
  ret ptr %r
}

define nonnull noundef ptr @test_ret_ptr_nonnull_noundef_gep_nonzero(i1 %cond, ptr %x) {
; CHECK-LABEL: @test_ret_ptr_nonnull_noundef_gep_nonzero(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[SPEC_SELECT:%.*]] = select i1 [[COND:%.*]], ptr [[X:%.*]], ptr null
; CHECK-NEXT:    [[GEP:%.*]] = getelementptr ptr, ptr [[SPEC_SELECT]], i64 12
; CHECK-NEXT:    ret ptr [[GEP]]
;
entry:
  br i1 %cond, label %bb1, label %bb2

bb1:
  br label %bb2

bb2:
  %phi = phi ptr [ null, %entry ], [ %x, %bb1 ]
  %gep = getelementptr ptr, ptr %phi, i64 12
  ret ptr %gep
}

define nonnull noundef ptr @test_ret_ptr_nonnull_noundef_gep_inbounds_nonzero(i1 %cond, ptr %x) {
; CHECK-LABEL: @test_ret_ptr_nonnull_noundef_gep_inbounds_nonzero(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    call void @llvm.assume(i1 [[COND:%.*]])
; CHECK-NEXT:    [[GEP:%.*]] = getelementptr inbounds ptr, ptr [[X:%.*]], i64 12
; CHECK-NEXT:    ret ptr [[GEP]]
;
entry:
  br i1 %cond, label %bb1, label %bb2

bb1:
  br label %bb2

bb2:
  %phi = phi ptr [ null, %entry ], [ %x, %bb1 ]
  %gep = getelementptr inbounds ptr, ptr %phi, i64 12
  ret ptr %gep
}

define nonnull ptr @test_ret_ptr_nonnull(i1 %cond, ptr %x) {
; CHECK-LABEL: @test_ret_ptr_nonnull(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[SPEC_SELECT:%.*]] = select i1 [[COND:%.*]], ptr [[X:%.*]], ptr null
; CHECK-NEXT:    ret ptr [[SPEC_SELECT]]
;
entry:
  br i1 %cond, label %bb1, label %bb2

bb1:
  br label %bb2

bb2:
  %r = phi ptr [ null, %entry ], [ %x, %bb1 ]
  ret ptr %r
}

define noundef ptr @test_ret_ptr_noundef(i1 %cond, ptr %x) {
; CHECK-LABEL: @test_ret_ptr_noundef(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[SPEC_SELECT:%.*]] = select i1 [[COND:%.*]], ptr [[X:%.*]], ptr null
; CHECK-NEXT:    ret ptr [[SPEC_SELECT]]
;
entry:
  br i1 %cond, label %bb1, label %bb2

bb1:
  br label %bb2

bb2:
  %r = phi ptr [ null, %entry ], [ %x, %bb1 ]
  ret ptr %r
}

define ptr @test_ret_ptr(i1 %cond, ptr %x) {
; CHECK-LABEL: @test_ret_ptr(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[SPEC_SELECT:%.*]] = select i1 [[COND:%.*]], ptr [[X:%.*]], ptr null
; CHECK-NEXT:    ret ptr [[SPEC_SELECT]]
;
entry:
  br i1 %cond, label %bb1, label %bb2

bb1:
  br label %bb2

bb2:
  %r = phi ptr [ null, %entry ], [ %x, %bb1 ]
  ret ptr %r
}
