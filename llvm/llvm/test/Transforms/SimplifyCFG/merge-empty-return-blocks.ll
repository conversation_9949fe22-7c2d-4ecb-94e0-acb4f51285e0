; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -passes=simplifycfg -simplifycfg-require-and-preserve-domtree=1 -S < %s | FileCheck %s

define void @t0(i1 %c) {
; CHECK-LABEL: @t0(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    ret void
;
entry:
  br i1 %c, label %end0, label %end1

end0:
  ret void

end1:
  ret void
}

define i8 @t1(i1 %c, i8 %v) {
; CHECK-LABEL: @t1(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    ret i8 [[V:%.*]]
;
entry:
  br i1 %c, label %end0, label %end1

end0:
  ret i8 %v

end1:
  ret i8 %v
}

define i8 @t2(i1 %c, i8 %v0, i8 %v1) {
; CHECK-LABEL: @t2(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[V0_V1:%.*]] = select i1 [[C:%.*]], i8 [[V0:%.*]], i8 [[V1:%.*]]
; CHECK-NEXT:    ret i8 [[V0_V1]]
;
entry:
  br i1 %c, label %end0, label %end1

end0:
  ret i8 %v0

end1:
  ret i8 %v1
}
