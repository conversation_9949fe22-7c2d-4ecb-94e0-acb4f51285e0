; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --prefix-filecheck-ir-name pref --version 5
; RUN: opt < %s -passes=simplifycfg -S | FileCheck %s

define i32 @foo.1(i32 %arg, ptr %arg1) {
; CHECK-LABEL: define i32 @foo.1(
; CHECK-SAME: i32 [[ARG:%.*]], ptr [[ARG1:%.*]]) {
; CHECK-NEXT:  [[BB:.*]]:
; CHECK-NEXT:    [[ALLOCA:%.*]] = alloca [2 x ptr], align 16
; CHECK-NEXT:    store ptr blockaddress(@foo.1, %[[BB2:.*]]), ptr [[ALLOCA]], align 16
; CHECK-NEXT:    [[GETELEMENTPTR:%.*]] = getelementptr inbounds [2 x ptr], ptr [[ALLOCA]], i64 0, i64 1
; CHECK-NEXT:    store ptr blockaddress(@foo.1, %[[BB16:.*]]), ptr [[GETELEMENTPTR]], align 8
; CHECK-NEXT:    br label %[[BB2]]
; CHECK:       [[BB2]]:
; CHECK-NEXT:    [[PHI:%.*]] = phi i32 [ 0, %[[BB]] ], [ 2, %[[BB18:.*]] ]
; CHECK-NEXT:    [[PHI3:%.*]] = phi i32 [ 0, %[[BB]] ], [ [[ARG]], %[[BB18]] ]
; CHECK-NEXT:    switch i32 [[PHI]], label %[[BB2_UNREACHABLEDEFAULT:.*]] [
; CHECK-NEXT:      i32 0, label %[[BB18]]
; CHECK-NEXT:      i32 2, label %[[PREFBB11:.*]]
; CHECK-NEXT:    ]
; CHECK:       [[PREFBB11]]:
; CHECK-NEXT:    [[CALL:%.*]] = call i32 @wombat(i32 noundef [[PHI3]])
; CHECK-NEXT:    [[ADD:%.*]] = add nsw i32 [[PHI3]], 1
; CHECK-NEXT:    br label %[[BB18]]
; CHECK:       [[BB2_UNREACHABLEDEFAULT]]:
; CHECK-NEXT:    unreachable
; CHECK:       [[BB16]]:
; CHECK-NEXT:    [[CALL17:%.*]] = call i32 @wombat(i32 noundef [[ARG]])
; CHECK-NEXT:    ret i32 0
; CHECK:       [[BB18]]:
; CHECK-NEXT:    [[LOAD:%.*]] = load ptr, ptr [[ARG1]], align 8
; CHECK-NEXT:    indirectbr ptr [[LOAD]], [label %[[BB2]], label %bb16]
;
bb:
  %alloca = alloca [2 x ptr], align 16
  store ptr blockaddress(@foo.1, %bb8), ptr %alloca, align 16
  %getelementptr = getelementptr inbounds [2 x ptr], ptr %alloca, i64 0, i64 1
  store ptr blockaddress(@foo.1, %bb16), ptr %getelementptr, align 8
  br label %bb2

bb2:                                              ; preds = %bb13, %bb
  %phi = phi i32 [ 0, %bb ], [ %phi14, %bb13 ]
  %phi3 = phi i32 [ 0, %bb ], [ %phi15, %bb13 ]
  switch i32 %phi, label %bb13 [
  i32 0, label %bb5
  i32 1, label %bb8
  i32 2, label %bb11
  ]

bb5:                                              ; preds = %bb2
  br label %bb18

bb8:                                              ; preds = %bb18, %bb2
  %phi10 = phi i32 [ %arg, %bb18 ], [ %phi3, %bb2 ]
  br label %bb13

bb11:                                             ; preds = %bb2
  %call = call i32 @wombat(i32 noundef %phi3)
  %add = add nsw i32 %phi3, 1
  br label %bb18

bb13:                                             ; preds = %bb8, %bb2
  %phi14 = phi i32 [ %phi, %bb2 ], [ 2, %bb8 ]
  %phi15 = phi i32 [ %phi3, %bb2 ], [ %phi10, %bb8 ]
  br label %bb2

bb16:                                             ; preds = %bb18
  %call17 = call i32 @wombat(i32 noundef %arg)
  ret i32 0

bb18:                                             ; preds = %bb11, %bb5
  %load = load ptr, ptr %arg1, align 8
  indirectbr ptr %load, [label %bb8, label %bb16]
}

declare i32 @wombat(i32)
