; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --check-globals
; RUN: opt < %s -passes=simplifycfg -switch-to-lookup=true -S | FileCheck %s
target datalayout = "e-p:32:32:32-i1:8:8-i8:8:8-i16:16:16-i32:32:32-i64:32:64-f32:32:32-f64:32:64-v64:64:64-v128:128:128-a0:0:64-f80:32:32-n8:16:32"
target triple = "i386-pc-linux-gnu"

; A dense switch with a reachable default case should be optimized into a lookup table with a bounds check
;.
; CHECK: @switch.table.reachable_default_dense_0to31 = private unnamed_addr constant [32 x i32] [i32 0, i32 7, i32 6, i32 5, i32 4, i32 3, i32 2, i32 1, i32 0, i32 7, i32 6, i32 5, i32 4, i32 3, i32 2, i32 1, i32 0, i32 7, i32 6, i32 5, i32 4, i32 3, i32 2, i32 1, i32 0, i32 7, i32 6, i32 5, i32 4, i32 3, i32 2, i32 1], align 4
; CHECK: @switch.table.unreachable_default_dense_0to31 = private unnamed_addr constant [32 x i32] [i32 0, i32 7, i32 6, i32 5, i32 4, i32 3, i32 2, i32 1, i32 0, i32 7, i32 6, i32 5, i32 4, i32 3, i32 2, i32 1, i32 0, i32 7, i32 6, i32 5, i32 4, i32 3, i32 2, i32 1, i32 0, i32 7, i32 6, i32 5, i32 4, i32 3, i32 2, i32 1], align 4
; CHECK: @switch.table.reachable_default_holes_0to31 = private unnamed_addr constant [32 x i32] [i32 0, i32 7, i32 6, i32 poison, i32 4, i32 3, i32 2, i32 1, i32 poison, i32 7, i32 6, i32 5, i32 4, i32 poison, i32 2, i32 1, i32 0, i32 7, i32 poison, i32 5, i32 4, i32 3, i32 2, i32 poison, i32 0, i32 7, i32 6, i32 5, i32 poison, i32 3, i32 2, i32 1], align 4
; CHECK: @switch.table.unreachable_default_holes_0to31 = private unnamed_addr constant [32 x i32] [i32 0, i32 7, i32 6, i32 poison, i32 4, i32 3, i32 2, i32 1, i32 poison, i32 7, i32 6, i32 5, i32 4, i32 poison, i32 2, i32 1, i32 0, i32 7, i32 poison, i32 5, i32 4, i32 3, i32 2, i32 poison, i32 0, i32 7, i32 6, i32 5, i32 poison, i32 3, i32 2, i32 1], align 4
; CHECK: @switch.table.reachable_default_dense_0to32 = private unnamed_addr constant [33 x i32] [i32 0, i32 7, i32 6, i32 5, i32 4, i32 3, i32 2, i32 1, i32 0, i32 7, i32 6, i32 5, i32 4, i32 3, i32 2, i32 1, i32 0, i32 7, i32 6, i32 5, i32 4, i32 3, i32 2, i32 1, i32 0, i32 7, i32 6, i32 5, i32 4, i32 3, i32 2, i32 1, i32 0], align 4
; CHECK: @switch.table.unreachable_default_dense_0to32 = private unnamed_addr constant [33 x i32] [i32 0, i32 7, i32 6, i32 5, i32 4, i32 3, i32 2, i32 1, i32 0, i32 7, i32 6, i32 5, i32 4, i32 3, i32 2, i32 1, i32 0, i32 7, i32 6, i32 5, i32 4, i32 3, i32 2, i32 1, i32 0, i32 7, i32 6, i32 5, i32 4, i32 3, i32 2, i32 1, i32 0], align 4
; CHECK: @switch.table.unreachable_default_holes_0to32 = private unnamed_addr constant [33 x i32] [i32 0, i32 7, i32 6, i32 poison, i32 4, i32 3, i32 2, i32 1, i32 poison, i32 7, i32 6, i32 5, i32 4, i32 poison, i32 2, i32 1, i32 0, i32 7, i32 poison, i32 5, i32 4, i32 3, i32 2, i32 poison, i32 0, i32 7, i32 6, i32 5, i32 poison, i32 3, i32 2, i32 1, i32 0], align 4
;.
define i32 @reachable_default_dense_0to31(i32 %x, i32 %y) {
; CHECK-LABEL: @reachable_default_dense_0to31(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP0:%.*]] = icmp ult i32 [[X:%.*]], 32
; CHECK-NEXT:    br i1 [[TMP0]], label [[SWITCH_LOOKUP:%.*]], label [[RETURN:%.*]]
; CHECK:       switch.lookup:
; CHECK-NEXT:    [[SWITCH_GEP:%.*]] = getelementptr inbounds [32 x i32], ptr @switch.table.reachable_default_dense_0to31, i32 0, i32 [[X]]
; CHECK-NEXT:    [[SWITCH_LOAD:%.*]] = load i32, ptr [[SWITCH_GEP]], align 4
; CHECK-NEXT:    br label [[RETURN]]
; CHECK:       return:
; CHECK-NEXT:    [[RES:%.*]] = phi i32 [ [[SWITCH_LOAD]], [[SWITCH_LOOKUP]] ], [ [[Y:%.*]], [[ENTRY:%.*]] ]
; CHECK-NEXT:    ret i32 [[RES]]
;
entry:
  switch i32 %x, label %sw.default [
  i32 0, label %bb0
  i32 1, label %bb7
  i32 2, label %bb6
  i32 3, label %bb5
  i32 4, label %bb4
  i32 5, label %bb3
  i32 6, label %bb2
  i32 7, label %bb1
  i32 8, label %bb0
  i32 9, label %bb7
  i32 10, label %bb6
  i32 11, label %bb5
  i32 12, label %bb4
  i32 13, label %bb3
  i32 14, label %bb2
  i32 15, label %bb1
  i32 16, label %bb0
  i32 17, label %bb7
  i32 18, label %bb6
  i32 19, label %bb5
  i32 20, label %bb4
  i32 21, label %bb3
  i32 22, label %bb2
  i32 23, label %bb1
  i32 24, label %bb0
  i32 25, label %bb7
  i32 26, label %bb6
  i32 27, label %bb5
  i32 28, label %bb4
  i32 29, label %bb3
  i32 30, label %bb2
  i32 31, label %bb1
  ]

sw.default: br label %return
bb0: br label %return
bb1: br label %return
bb2: br label %return
bb3: br label %return
bb4: br label %return
bb5: br label %return
bb6: br label %return
bb7: br label %return

return:
  %res = phi i32 [ %y, %sw.default ], [ 0, %bb0 ], [ 1, %bb1 ], [ 2, %bb2 ], [ 3, %bb3 ], [ 4, %bb4 ], [ 5, %bb5 ], [ 6, %bb6 ], [ 7, %bb7 ]
  ret i32 %res

}

; A dense switch with an unreachable default case should be optimized into a lookup table without bounds checks
define i32 @unreachable_default_dense_0to31(i32 %x, i32 %y) {
; CHECK-LABEL: @unreachable_default_dense_0to31(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[SWITCH_GEP:%.*]] = getelementptr inbounds [32 x i32], ptr @switch.table.unreachable_default_dense_0to31, i32 0, i32 [[X:%.*]]
; CHECK-NEXT:    [[SWITCH_LOAD:%.*]] = load i32, ptr [[SWITCH_GEP]], align 4
; CHECK-NEXT:    ret i32 [[SWITCH_LOAD]]
;
entry:
  switch i32 %x, label %sw.default [
  i32 0, label %bb0
  i32 1, label %bb7
  i32 2, label %bb6
  i32 3, label %bb5
  i32 4, label %bb4
  i32 5, label %bb3
  i32 6, label %bb2
  i32 7, label %bb1
  i32 8, label %bb0
  i32 9, label %bb7
  i32 10, label %bb6
  i32 11, label %bb5
  i32 12, label %bb4
  i32 13, label %bb3
  i32 14, label %bb2
  i32 15, label %bb1
  i32 16, label %bb0
  i32 17, label %bb7
  i32 18, label %bb6
  i32 19, label %bb5
  i32 20, label %bb4
  i32 21, label %bb3
  i32 22, label %bb2
  i32 23, label %bb1
  i32 24, label %bb0
  i32 25, label %bb7
  i32 26, label %bb6
  i32 27, label %bb5
  i32 28, label %bb4
  i32 29, label %bb3
  i32 30, label %bb2
  i32 31, label %bb1
  ]

sw.default: unreachable
bb0: br label %return
bb1: br label %return
bb2: br label %return
bb3: br label %return
bb4: br label %return
bb5: br label %return
bb6: br label %return
bb7: br label %return

return:
  %res = phi i32 [ 0, %bb0 ], [ 1, %bb1 ], [ 2, %bb2 ], [ 3, %bb3 ], [ 4, %bb4 ], [ 5, %bb5 ], [ 6, %bb6 ], [ 7, %bb7 ]
  ret i32 %res

}

; A sparse switch with a reachable default case should be optimized into a lookup table with a bounds check and a mask
define i32 @reachable_default_holes_0to31(i32 %x, i32 %y) {
; CHECK-LABEL: @reachable_default_holes_0to31(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP0:%.*]] = icmp ult i32 [[X:%.*]], 32
; CHECK-NEXT:    br i1 [[TMP0]], label [[SWITCH_HOLE_CHECK:%.*]], label [[RETURN:%.*]]
; CHECK:       switch.hole_check:
; CHECK-NEXT:    [[SWITCH_SHIFTED:%.*]] = lshr i32 -277094665, [[X]]
; CHECK-NEXT:    [[SWITCH_LOBIT:%.*]] = trunc i32 [[SWITCH_SHIFTED]] to i1
; CHECK-NEXT:    br i1 [[SWITCH_LOBIT]], label [[SWITCH_LOOKUP:%.*]], label [[RETURN]]
; CHECK:       switch.lookup:
; CHECK-NEXT:    [[SWITCH_GEP:%.*]] = getelementptr inbounds [32 x i32], ptr @switch.table.reachable_default_holes_0to31, i32 0, i32 [[X]]
; CHECK-NEXT:    [[SWITCH_LOAD:%.*]] = load i32, ptr [[SWITCH_GEP]], align 4
; CHECK-NEXT:    br label [[RETURN]]
; CHECK:       return:
; CHECK-NEXT:    [[RES:%.*]] = phi i32 [ [[SWITCH_LOAD]], [[SWITCH_LOOKUP]] ], [ [[Y:%.*]], [[SWITCH_HOLE_CHECK]] ], [ [[Y]], [[ENTRY:%.*]] ]
; CHECK-NEXT:    ret i32 [[RES]]
;
entry:
  switch i32 %x, label %sw.default [
  i32 0, label %bb0
  i32 1, label %bb7
  i32 2, label %bb6
  i32 4, label %bb4
  i32 5, label %bb3
  i32 6, label %bb2
  i32 7, label %bb1
  i32 9, label %bb7
  i32 10, label %bb6
  i32 11, label %bb5
  i32 12, label %bb4
  i32 14, label %bb2
  i32 15, label %bb1
  i32 16, label %bb0
  i32 17, label %bb7
  i32 19, label %bb5
  i32 20, label %bb4
  i32 21, label %bb3
  i32 22, label %bb2
  i32 24, label %bb0
  i32 25, label %bb7
  i32 26, label %bb6
  i32 27, label %bb5
  i32 29, label %bb3
  i32 30, label %bb2
  i32 31, label %bb1
  ]

sw.default: br label %return
bb0: br label %return
bb1: br label %return
bb2: br label %return
bb3: br label %return
bb4: br label %return
bb5: br label %return
bb6: br label %return
bb7: br label %return

return:
  %res = phi i32 [ %y, %sw.default ], [ 0, %bb0 ], [ 1, %bb1 ], [ 2, %bb2 ], [ 3, %bb3 ], [ 4, %bb4 ], [ 5, %bb5 ], [ 6, %bb6 ], [ 7, %bb7 ]
  ret i32 %res

}

; A sparse switch with an unreachable default case should be optimized into a lookup table without bounds checks
define i32 @unreachable_default_holes_0to31(i32 %x, i32 %y) {
; CHECK-LABEL: @unreachable_default_holes_0to31(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[SWITCH_GEP:%.*]] = getelementptr inbounds [32 x i32], ptr @switch.table.unreachable_default_holes_0to31, i32 0, i32 [[X:%.*]]
; CHECK-NEXT:    [[SWITCH_LOAD:%.*]] = load i32, ptr [[SWITCH_GEP]], align 4
; CHECK-NEXT:    ret i32 [[SWITCH_LOAD]]
;
entry:
  switch i32 %x, label %sw.default [
  i32 0, label %bb0
  i32 1, label %bb7
  i32 2, label %bb6
  i32 4, label %bb4
  i32 5, label %bb3
  i32 6, label %bb2
  i32 7, label %bb1
  i32 9, label %bb7
  i32 10, label %bb6
  i32 11, label %bb5
  i32 12, label %bb4
  i32 14, label %bb2
  i32 15, label %bb1
  i32 16, label %bb0
  i32 17, label %bb7
  i32 19, label %bb5
  i32 20, label %bb4
  i32 21, label %bb3
  i32 22, label %bb2
  i32 24, label %bb0
  i32 25, label %bb7
  i32 26, label %bb6
  i32 27, label %bb5
  i32 29, label %bb3
  i32 30, label %bb2
  i32 31, label %bb1
  ]

sw.default: unreachable
bb0: br label %return
bb1: br label %return
bb2: br label %return
bb3: br label %return
bb4: br label %return
bb5: br label %return
bb6: br label %return
bb7: br label %return

return:
  %res = phi i32 [ 0, %bb0 ], [ 1, %bb1 ], [ 2, %bb2 ], [ 3, %bb3 ], [ 4, %bb4 ], [ 5, %bb5 ], [ 6, %bb6 ], [ 7, %bb7 ]
  ret i32 %res

}

; A dense switch with a reachable default case should be optimized into a lookup table with a bounds check
define i32 @reachable_default_dense_0to32(i32 %x, i32 %y) {
; CHECK-LABEL: @reachable_default_dense_0to32(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[TMP0:%.*]] = icmp ult i32 [[X:%.*]], 33
; CHECK-NEXT:    br i1 [[TMP0]], label [[SWITCH_LOOKUP:%.*]], label [[RETURN:%.*]]
; CHECK:       switch.lookup:
; CHECK-NEXT:    [[SWITCH_GEP:%.*]] = getelementptr inbounds [33 x i32], ptr @switch.table.reachable_default_dense_0to32, i32 0, i32 [[X]]
; CHECK-NEXT:    [[SWITCH_LOAD:%.*]] = load i32, ptr [[SWITCH_GEP]], align 4
; CHECK-NEXT:    br label [[RETURN]]
; CHECK:       return:
; CHECK-NEXT:    [[RES:%.*]] = phi i32 [ [[SWITCH_LOAD]], [[SWITCH_LOOKUP]] ], [ [[Y:%.*]], [[ENTRY:%.*]] ]
; CHECK-NEXT:    ret i32 [[RES]]
;
entry:
  switch i32 %x, label %sw.default [
  i32 0, label %bb0
  i32 1, label %bb7
  i32 2, label %bb6
  i32 3, label %bb5
  i32 4, label %bb4
  i32 5, label %bb3
  i32 6, label %bb2
  i32 7, label %bb1
  i32 8, label %bb0
  i32 9, label %bb7
  i32 10, label %bb6
  i32 11, label %bb5
  i32 12, label %bb4
  i32 13, label %bb3
  i32 14, label %bb2
  i32 15, label %bb1
  i32 16, label %bb0
  i32 17, label %bb7
  i32 18, label %bb6
  i32 19, label %bb5
  i32 20, label %bb4
  i32 21, label %bb3
  i32 22, label %bb2
  i32 23, label %bb1
  i32 24, label %bb0
  i32 25, label %bb7
  i32 26, label %bb6
  i32 27, label %bb5
  i32 28, label %bb4
  i32 29, label %bb3
  i32 30, label %bb2
  i32 31, label %bb1
  i32 32, label %bb0
  ]

sw.default: br label %return
bb0: br label %return
bb1: br label %return
bb2: br label %return
bb3: br label %return
bb4: br label %return
bb5: br label %return
bb6: br label %return
bb7: br label %return

return:
  %res = phi i32 [ %y, %sw.default ], [ 0, %bb0 ], [ 1, %bb1 ], [ 2, %bb2 ], [ 3, %bb3 ], [ 4, %bb4 ], [ 5, %bb5 ], [ 6, %bb6 ], [ 7, %bb7 ]
  ret i32 %res

}

; A dense switch with an unreachable default case should be optimized into a lookup table without bounds checks
define i32 @unreachable_default_dense_0to32(i32 %x, i32 %y) {
; CHECK-LABEL: @unreachable_default_dense_0to32(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[SWITCH_GEP:%.*]] = getelementptr inbounds [33 x i32], ptr @switch.table.unreachable_default_dense_0to32, i32 0, i32 [[X:%.*]]
; CHECK-NEXT:    [[SWITCH_LOAD:%.*]] = load i32, ptr [[SWITCH_GEP]], align 4
; CHECK-NEXT:    ret i32 [[SWITCH_LOAD]]
;
entry:
  switch i32 %x, label %sw.default [
  i32 0, label %bb0
  i32 1, label %bb7
  i32 2, label %bb6
  i32 3, label %bb5
  i32 4, label %bb4
  i32 5, label %bb3
  i32 6, label %bb2
  i32 7, label %bb1
  i32 8, label %bb0
  i32 9, label %bb7
  i32 10, label %bb6
  i32 11, label %bb5
  i32 12, label %bb4
  i32 13, label %bb3
  i32 14, label %bb2
  i32 15, label %bb1
  i32 16, label %bb0
  i32 17, label %bb7
  i32 18, label %bb6
  i32 19, label %bb5
  i32 20, label %bb4
  i32 21, label %bb3
  i32 22, label %bb2
  i32 23, label %bb1
  i32 24, label %bb0
  i32 25, label %bb7
  i32 26, label %bb6
  i32 27, label %bb5
  i32 28, label %bb4
  i32 29, label %bb3
  i32 30, label %bb2
  i32 31, label %bb1
  i32 32, label %bb0
  ]

sw.default: unreachable
bb0: br label %return
bb1: br label %return
bb2: br label %return
bb3: br label %return
bb4: br label %return
bb5: br label %return
bb6: br label %return
bb7: br label %return

return:
  %res = phi i32 [ 0, %bb0 ], [ 1, %bb1 ], [ 2, %bb2 ], [ 3, %bb3 ], [ 4, %bb4 ], [ 5, %bb5 ], [ 6, %bb6 ], [ 7, %bb7 ]
  ret i32 %res

}

; A sparse switch with a reachable default case which would be optimized into a lookup table with a bounds check and a mask, but doesn't because
; it would require a 33-bit mask
define i32 @reachable_default_holes_0to32(i32 %x, i32 %y) {
; CHECK-LABEL: @reachable_default_holes_0to32(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    switch i32 [[X:%.*]], label [[RETURN:%.*]] [
; CHECK-NEXT:      i32 0, label [[BB0:%.*]]
; CHECK-NEXT:      i32 1, label [[BB7:%.*]]
; CHECK-NEXT:      i32 2, label [[BB6:%.*]]
; CHECK-NEXT:      i32 4, label [[BB4:%.*]]
; CHECK-NEXT:      i32 5, label [[BB3:%.*]]
; CHECK-NEXT:      i32 6, label [[BB2:%.*]]
; CHECK-NEXT:      i32 7, label [[BB1:%.*]]
; CHECK-NEXT:      i32 9, label [[BB7]]
; CHECK-NEXT:      i32 10, label [[BB6]]
; CHECK-NEXT:      i32 11, label [[BB5:%.*]]
; CHECK-NEXT:      i32 12, label [[BB4]]
; CHECK-NEXT:      i32 14, label [[BB2]]
; CHECK-NEXT:      i32 15, label [[BB1]]
; CHECK-NEXT:      i32 16, label [[BB0]]
; CHECK-NEXT:      i32 17, label [[BB7]]
; CHECK-NEXT:      i32 19, label [[BB5]]
; CHECK-NEXT:      i32 20, label [[BB4]]
; CHECK-NEXT:      i32 21, label [[BB3]]
; CHECK-NEXT:      i32 22, label [[BB2]]
; CHECK-NEXT:      i32 24, label [[BB0]]
; CHECK-NEXT:      i32 25, label [[BB7]]
; CHECK-NEXT:      i32 26, label [[BB6]]
; CHECK-NEXT:      i32 27, label [[BB5]]
; CHECK-NEXT:      i32 29, label [[BB3]]
; CHECK-NEXT:      i32 30, label [[BB2]]
; CHECK-NEXT:      i32 31, label [[BB1]]
; CHECK-NEXT:      i32 32, label [[BB0]]
; CHECK-NEXT:    ]
; CHECK:       bb0:
; CHECK-NEXT:    br label [[RETURN]]
; CHECK:       bb1:
; CHECK-NEXT:    br label [[RETURN]]
; CHECK:       bb2:
; CHECK-NEXT:    br label [[RETURN]]
; CHECK:       bb3:
; CHECK-NEXT:    br label [[RETURN]]
; CHECK:       bb4:
; CHECK-NEXT:    br label [[RETURN]]
; CHECK:       bb5:
; CHECK-NEXT:    br label [[RETURN]]
; CHECK:       bb6:
; CHECK-NEXT:    br label [[RETURN]]
; CHECK:       bb7:
; CHECK-NEXT:    br label [[RETURN]]
; CHECK:       return:
; CHECK-NEXT:    [[RES:%.*]] = phi i32 [ 0, [[BB0]] ], [ 1, [[BB1]] ], [ 2, [[BB2]] ], [ 3, [[BB3]] ], [ 4, [[BB4]] ], [ 5, [[BB5]] ], [ 6, [[BB6]] ], [ 7, [[BB7]] ], [ [[Y:%.*]], [[ENTRY:%.*]] ]
; CHECK-NEXT:    ret i32 [[RES]]
;
entry:
  switch i32 %x, label %sw.default [
  i32 0, label %bb0
  i32 1, label %bb7
  i32 2, label %bb6
  i32 4, label %bb4
  i32 5, label %bb3
  i32 6, label %bb2
  i32 7, label %bb1
  i32 9, label %bb7
  i32 10, label %bb6
  i32 11, label %bb5
  i32 12, label %bb4
  i32 14, label %bb2
  i32 15, label %bb1
  i32 16, label %bb0
  i32 17, label %bb7
  i32 19, label %bb5
  i32 20, label %bb4
  i32 21, label %bb3
  i32 22, label %bb2
  i32 24, label %bb0
  i32 25, label %bb7
  i32 26, label %bb6
  i32 27, label %bb5
  i32 29, label %bb3
  i32 30, label %bb2
  i32 31, label %bb1
  i32 32, label %bb0
  ]

sw.default: br label %return
bb0: br label %return
bb1: br label %return
bb2: br label %return
bb3: br label %return
bb4: br label %return
bb5: br label %return
bb6: br label %return
bb7: br label %return

return:
  %res = phi i32 [ %y, %sw.default ], [ 0, %bb0 ], [ 1, %bb1 ], [ 2, %bb2 ], [ 3, %bb3 ], [ 4, %bb4 ], [ 5, %bb5 ], [ 6, %bb6 ], [ 7, %bb7 ]
  ret i32 %res

}

; A sparse switch with an unreachable default case which can be optimized into a lookup table without bounds checks. Because the default case is
; unreachable, the fact that a 33-bit mask would be required doesn't prevent lookup table optimization.
define i32 @unreachable_default_holes_0to32(i32 %x, i32 %y) {
; CHECK-LABEL: @unreachable_default_holes_0to32(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[SWITCH_GEP:%.*]] = getelementptr inbounds [33 x i32], ptr @switch.table.unreachable_default_holes_0to32, i32 0, i32 [[X:%.*]]
; CHECK-NEXT:    [[SWITCH_LOAD:%.*]] = load i32, ptr [[SWITCH_GEP]], align 4
; CHECK-NEXT:    ret i32 [[SWITCH_LOAD]]
;
entry:
  switch i32 %x, label %sw.default [
  i32 0, label %bb0
  i32 1, label %bb7
  i32 2, label %bb6
  i32 4, label %bb4
  i32 5, label %bb3
  i32 6, label %bb2
  i32 7, label %bb1
  i32 9, label %bb7
  i32 10, label %bb6
  i32 11, label %bb5
  i32 12, label %bb4
  i32 14, label %bb2
  i32 15, label %bb1
  i32 16, label %bb0
  i32 17, label %bb7
  i32 19, label %bb5
  i32 20, label %bb4
  i32 21, label %bb3
  i32 22, label %bb2
  i32 24, label %bb0
  i32 25, label %bb7
  i32 26, label %bb6
  i32 27, label %bb5
  i32 29, label %bb3
  i32 30, label %bb2
  i32 31, label %bb1
  i32 32, label %bb0
  ]

sw.default: unreachable
bb0: br label %return
bb1: br label %return
bb2: br label %return
bb3: br label %return
bb4: br label %return
bb5: br label %return
bb6: br label %return
bb7: br label %return

return:
  %res = phi i32 [ 0, %bb0 ], [ 1, %bb1 ], [ 2, %bb2 ], [ 3, %bb3 ], [ 4, %bb4 ], [ 5, %bb5 ], [ 6, %bb6 ], [ 7, %bb7 ]
  ret i32 %res

}
