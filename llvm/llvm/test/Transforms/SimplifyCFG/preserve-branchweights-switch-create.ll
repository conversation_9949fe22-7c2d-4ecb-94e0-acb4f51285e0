; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -passes=simplifycfg -simplifycfg-require-and-preserve-domtree=1 -S -o - < %s | FileCheck %s

declare void @func2(i32)
declare void @func4(i32)
declare void @func6(i32)
declare void @func8(i32)

;; test1 - create a switch with case 2 and case 4 from two branches: N == 2
;; and N == 4.
define void @test1(i32 %N) nounwind uwtable {
; CHECK-LABEL: @test1(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    switch i32 [[N:%.*]], label [[IF_ELSE8:%.*]] [
; CHECK-NEXT:      i32 2, label [[IF_THEN:%.*]]
; CHECK-NEXT:      i32 4, label [[IF_THEN7:%.*]]
; CHECK-NEXT:    ], !prof [[PROF0:![0-9]+]]
; CHECK:       if.then:
; CHECK-NEXT:    call void @func2(i32 [[N]]) #[[ATTR1:[0-9]+]]
; CHECK-NEXT:    br label [[IF_END9:%.*]]
; CHECK:       if.then7:
; CHECK-NEXT:    call void @func4(i32 [[N]]) #[[ATTR1]]
; CHECK-NEXT:    br label [[IF_END9]]
; CHECK:       if.else8:
; CHECK-NEXT:    call void @func8(i32 [[N]]) #[[ATTR1]]
; CHECK-NEXT:    br label [[IF_END9]]
; CHECK:       if.end9:
; CHECK-NEXT:    ret void
;
entry:
  %cmp = icmp eq i32 %N, 2
  br i1 %cmp, label %if.then, label %if.else, !prof !0

if.then:
  call void @func2(i32 %N) nounwind
  br label %if.end9

if.else:
  %cmp2 = icmp eq i32 %N, 4
  br i1 %cmp2, label %if.then7, label %if.else8, !prof !1

if.then7:
  call void @func4(i32 %N) nounwind
  br label %if.end

if.else8:
  call void @func8(i32 %N) nounwind
  br label %if.end

if.end:
  br label %if.end9

if.end9:
  ret void
}

;; test2 - Merge two switches where PredDefault == BB.
define void @test2(i32 %M, i32 %N) nounwind uwtable {
; CHECK-LABEL: @test2(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[CMP:%.*]] = icmp sgt i32 [[M:%.*]], 2
; CHECK-NEXT:    br i1 [[CMP]], label [[SW1:%.*]], label [[SW2:%.*]]
; CHECK:       sw1:
; CHECK-NEXT:    switch i32 [[N:%.*]], label [[SW_EPILOG:%.*]] [
; CHECK-NEXT:      i32 2, label [[SW_BB:%.*]]
; CHECK-NEXT:      i32 3, label [[SW_BB1:%.*]]
; CHECK-NEXT:      i32 4, label [[SW_BB5:%.*]]
; CHECK-NEXT:    ], !prof [[PROF1:![0-9]+]]
; CHECK:       sw.bb:
; CHECK-NEXT:    call void @func2(i32 [[N]]) #[[ATTR1]]
; CHECK-NEXT:    br label [[SW_EPILOG]]
; CHECK:       sw.bb1:
; CHECK-NEXT:    call void @func4(i32 [[N]]) #[[ATTR1]]
; CHECK-NEXT:    br label [[SW_EPILOG]]
; CHECK:       sw2:
; CHECK-NEXT:    switch i32 [[N]], label [[SW_EPILOG]] [
; CHECK-NEXT:      i32 2, label [[SW_BB4:%.*]]
; CHECK-NEXT:      i32 4, label [[SW_BB5]]
; CHECK-NEXT:    ], !prof [[PROF2:![0-9]+]]
; CHECK:       sw.bb4:
; CHECK-NEXT:    call void @func6(i32 [[N]]) #[[ATTR1]]
; CHECK-NEXT:    br label [[SW_EPILOG]]
; CHECK:       sw.bb5:
; CHECK-NEXT:    call void @func8(i32 [[N]]) #[[ATTR1]]
; CHECK-NEXT:    br label [[SW_EPILOG]]
; CHECK:       sw.epilog:
; CHECK-NEXT:    ret void
;
entry:
  %cmp = icmp sgt i32 %M, 2
  br i1 %cmp, label %sw1, label %sw2

sw1:
  switch i32 %N, label %sw2 [
  i32 2, label %sw.bb
  i32 3, label %sw.bb1
  ], !prof !2

sw.bb:
  call void @func2(i32 %N) nounwind
  br label %sw.epilog

sw.bb1:
  call void @func4(i32 %N) nounwind
  br label %sw.epilog

sw2:
;; Here "case 2" is invalidated if control is transferred through default case
;; of the first switch.
  switch i32 %N, label %sw.epilog [
  i32 2, label %sw.bb4
  i32 4, label %sw.bb5
  ], !prof !3

sw.bb4:
  call void @func6(i32 %N) nounwind
  br label %sw.epilog

sw.bb5:
  call void @func8(i32 %N) nounwind
  br label %sw.epilog

sw.epilog:
  ret void
}

;; test3 - Merge two switches where PredDefault != BB.
define void @test3(i32 %M, i32 %N) nounwind uwtable {
; CHECK-LABEL: @test3(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[CMP:%.*]] = icmp sgt i32 [[M:%.*]], 2
; CHECK-NEXT:    br i1 [[CMP]], label [[SW1:%.*]], label [[SW2:%.*]]
; CHECK:       sw1:
; CHECK-NEXT:    switch i32 [[N:%.*]], label [[SW_BB:%.*]] [
; CHECK-NEXT:      i32 1, label [[SW_BB1:%.*]]
; CHECK-NEXT:      i32 3, label [[SW_BB4:%.*]]
; CHECK-NEXT:      i32 2, label [[SW_EPILOG:%.*]]
; CHECK-NEXT:    ], !prof [[PROF3:![0-9]+]]
; CHECK:       sw.bb:
; CHECK-NEXT:    call void @func2(i32 [[N]]) #[[ATTR1]]
; CHECK-NEXT:    br label [[SW_EPILOG]]
; CHECK:       sw.bb1:
; CHECK-NEXT:    call void @func4(i32 [[N]]) #[[ATTR1]]
; CHECK-NEXT:    br label [[SW_EPILOG]]
; CHECK:       sw2:
; CHECK-NEXT:    switch i32 [[N]], label [[SW_EPILOG]] [
; CHECK-NEXT:      i32 3, label [[SW_BB4]]
; CHECK-NEXT:      i32 4, label [[SW_BB5:%.*]]
; CHECK-NEXT:    ], !prof [[PROF4:![0-9]+]]
; CHECK:       sw.bb4:
; CHECK-NEXT:    call void @func6(i32 [[N]]) #[[ATTR1]]
; CHECK-NEXT:    br label [[SW_EPILOG]]
; CHECK:       sw.bb5:
; CHECK-NEXT:    call void @func8(i32 [[N]]) #[[ATTR1]]
; CHECK-NEXT:    br label [[SW_EPILOG]]
; CHECK:       sw.epilog:
; CHECK-NEXT:    ret void
;
entry:
  %cmp = icmp sgt i32 %M, 2
  br i1 %cmp, label %sw1, label %sw2

sw1:
  switch i32 %N, label %sw.bb [
  i32 2, label %sw2
  i32 3, label %sw2
  i32 1, label %sw.bb1
  ], !prof !4

sw.bb:
  call void @func2(i32 %N) nounwind
  br label %sw.epilog

sw.bb1:
  call void @func4(i32 %N) nounwind
  br label %sw.epilog

sw2:
  switch i32 %N, label %sw.epilog [
  i32 3, label %sw.bb4
  i32 4, label %sw.bb5
  ], !prof !5

sw.bb4:
  call void @func6(i32 %N) nounwind
  br label %sw.epilog

sw.bb5:
  call void @func8(i32 %N) nounwind
  br label %sw.epilog

sw.epilog:
  ret void
}

!0 = !{!"branch_weights", i32 64, i32 4}
!1 = !{!"branch_weights", i32 4, i32 64}
; CHECK: !0 = !{!"branch_weights", i32 256, i32 4352, i32 16}
!2 = !{!"branch_weights", i32 4, i32 4, i32 8}
!3 = !{!"branch_weights", i32 8, i32 8, i32 4}
; CHECK: !1 = !{!"branch_weights", i32 32, i32 48, i32 96, i32 16}
!4 = !{!"branch_weights", i32 7, i32 6, i32 4, i32 3}
!5 = !{!"branch_weights", i32 17, i32 13, i32 9}
; CHECK: !3 = !{!"branch_weights", i32 7, i32 3, i32 4, i32 6}
