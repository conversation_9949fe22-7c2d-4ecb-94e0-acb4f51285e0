; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt -S -passes=simplifycfg < %s | FileCheck %s
;; Test that we correct clone debug-info when folding in
;; FoldCondBranchOnValueKnownInPredecessorImpl. There should be two paths
;; through this program, assigning [0 1 2 3 4 5 6 7] to the variable down
;; one path, and [0 1 2 3 6 7] down the other.

declare void @foo()
declare void @bar()
declare void @use.i1(i1)
declare void @use.i32(i32)
declare void @llvm.dbg.value(metadata, metadata, metadata)

define void @test_phi_extra_use(i1 %c) {
; CHECK-LABEL: @test_phi_extra_use(
; CHECK-NEXT:    br i1 [[C:%.*]], label [[IF:%.*]], label [[ELSE:%.*]]
; CHECK:       if:
; CHECK-NEXT:    call void @foo()
; CHECK-NEXT:      #dbg_value(i32 0, [[META7:![0-9]+]], !DIExpression(), [[META13:![0-9]+]])
; CHECK-NEXT:      #dbg_value(i32 1, [[META7]], !DIExpression(), [[META13]])
; CHECK-NEXT:    call void @use.i1(i1 true)
; CHECK-NEXT:      #dbg_value(i32 2, [[META7]], !DIExpression(), [[META13]])
; CHECK-NEXT:      #dbg_value(i32 3, [[META7]], !DIExpression(), [[META13]])
; CHECK-NEXT:    call void @foo()
; CHECK-NEXT:    br label [[JOIN2:%.*]]
; CHECK:       else:
; CHECK-NEXT:    call void @bar()
; CHECK-NEXT:      #dbg_value(i32 0, [[META7]], !DIExpression(), [[META13]])
; CHECK-NEXT:      #dbg_value(i32 1, [[META7]], !DIExpression(), [[META13]])
; CHECK-NEXT:    call void @use.i1(i1 false)
; CHECK-NEXT:      #dbg_value(i32 2, [[META7]], !DIExpression(), [[META13]])
; CHECK-NEXT:      #dbg_value(i32 3, [[META7]], !DIExpression(), [[META13]])
; CHECK-NEXT:      #dbg_value(i32 4, [[META7]], !DIExpression(), [[META13]])
; CHECK-NEXT:      #dbg_value(i32 5, [[META7]], !DIExpression(), [[META13]])
; CHECK-NEXT:    call void @bar()
; CHECK-NEXT:    br label [[JOIN2]]
; CHECK:       join2:
; CHECK-NEXT:      #dbg_value(i32 6, [[META7]], !DIExpression(), [[META13]])
; CHECK-NEXT:      #dbg_value(i32 7, [[META7]], !DIExpression(), [[META13]])
; CHECK-NEXT:    ret void, !dbg [[META13]]
;
  br i1 %c, label %if, label %else

if:
  call void @foo()
  br label %join

else:
  call void @bar()
  br label %join

join:
  %c2 = phi i1 [ true, %if ], [ false, %else ]
  call void @llvm.dbg.value(metadata i32 0, metadata !12, metadata !DIExpression()), !dbg !15
  call void @llvm.dbg.value(metadata i32 1, metadata !12, metadata !DIExpression()), !dbg !15
  call void @use.i1(i1 %c2)
  call void @llvm.dbg.value(metadata i32 2, metadata !12, metadata !DIExpression()), !dbg !15
  call void @llvm.dbg.value(metadata i32 3, metadata !12, metadata !DIExpression()), !dbg !15
  br i1 %c2, label %if2, label %else2

if2:
  call void @foo()
  br label %join2

else2:
  call void @llvm.dbg.value(metadata i32 4, metadata !12, metadata !DIExpression()), !dbg !15
  call void @llvm.dbg.value(metadata i32 5, metadata !12, metadata !DIExpression()), !dbg !15
  call void @bar()
  br label %join2

join2:
  call void @llvm.dbg.value(metadata i32 6, metadata !12, metadata !DIExpression()), !dbg !15
  call void @llvm.dbg.value(metadata i32 7, metadata !12, metadata !DIExpression()), !dbg !15
  ret void, !dbg !15
}

!llvm.dbg.cu = !{!0}
!llvm.module.flags = !{!3, !4, !5}
!llvm.ident = !{!6}

!0 = distinct !DICompileUnit(language: DW_LANG_C99, file: !1, producer: "clang", isOptimized: true, runtimeVersion: 0, emissionKind: FullDebug, enums: !2)
!1 = !DIFile(filename: ".", directory: "/foo")
!2 = !{}
!3 = !{i32 2, !"Dwarf Version", i32 4}
!4 = !{i32 2, !"Debug Info Version", i32 3}
!5 = !{i32 1, !"wchar_size", i32 4}
!6 = !{!"clang"}
!7 = distinct !DISubprogram(name: "test1", scope: !1, file: !1, line: 1, type: !8, isLocal: false, isDefinition: true, scopeLine: 1, flags: DIFlagPrototyped, isOptimized: true, unit: !0, retainedNodes: !11)
!8 = !DISubroutineType(types: !9)
!9 = !{!10, !10, !10}
!10 = !DIBasicType(name: "int", size: 32, encoding: DW_ATE_signed)
!11 = !{!12}
!12 = !DILocalVariable(name: "getdirt", arg: 1, scope: !7, file: !1, line: 1, type: !10)
!15 = !DILocation(line: 1, column: 15, scope: !7)

