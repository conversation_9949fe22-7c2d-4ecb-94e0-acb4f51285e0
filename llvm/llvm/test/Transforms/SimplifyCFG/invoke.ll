; NOTE: Assertions have been autogenerated by utils/update_test_checks.py
; RUN: opt < %s -passes=simplifycfg -simplifycfg-require-and-preserve-domtree=1 -S | FileCheck %s
target datalayout = "e-p:64:64:64-i1:8:8-i8:8:8-i16:16:16-i32:32:32-i64:64:64-f32:32:32-f64:64:64-v64:64:64-v128:128:128-a0:0:64-s0:64:64-f80:128:128-n8:16:32:64-S128"

declare i32 @__gxx_personality_v0(...)
declare void @__cxa_call_unexpected(ptr)
declare void @purefn() nounwind readnone willreturn
declare i32 @readonly() nounwind readonly
declare i32 @readonly_willreturn() nounwind readonly willreturn
declare i32 @nounwind_fn() nounwind
declare i32 @fn()


define ptr @f1() nounwind uwtable ssp personality ptr @__gxx_personality_v0 {
; CHECK-LABEL: @f1(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    unreachable
;
entry:
  %call = invoke noalias ptr undef()
  to label %invoke.cont unwind label %lpad

invoke.cont:
  ret ptr %call

lpad:
  %0 = landingpad { ptr, i32 }
  filter [0 x ptr] zeroinitializer
  %1 = extractvalue { ptr, i32 } %0, 0
  tail call void @__cxa_call_unexpected(ptr %1) noreturn nounwind
  unreachable
}

define ptr @f2() nounwind uwtable ssp personality ptr @__gxx_personality_v0 {
; CHECK-LABEL: @f2(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    unreachable
;
entry:
  %call = invoke noalias ptr null()
  to label %invoke.cont unwind label %lpad

invoke.cont:
  ret ptr %call

lpad:
  %0 = landingpad { ptr, i32 }
  filter [0 x ptr] zeroinitializer
  %1 = extractvalue { ptr, i32 } %0, 0
  tail call void @__cxa_call_unexpected(ptr %1) noreturn nounwind
  unreachable
}

define ptr @f2_no_null_opt() nounwind uwtable ssp #0 personality ptr @__gxx_personality_v0 {
; CHECK-LABEL: @f2_no_null_opt(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[CALL:%.*]] = invoke noalias ptr null()
; CHECK-NEXT:            to label [[INVOKE_CONT:%.*]] unwind label [[LPAD:%.*]]
; CHECK:       invoke.cont:
; CHECK-NEXT:    ret ptr [[CALL]]
; CHECK:       lpad:
; CHECK-NEXT:    [[TMP0:%.*]] = landingpad { ptr, i32 }
; CHECK-NEXT:            filter [0 x ptr] zeroinitializer
; CHECK-NEXT:    [[TMP1:%.*]] = extractvalue { ptr, i32 } [[TMP0]], 0
; CHECK-NEXT:    tail call void @__cxa_call_unexpected(ptr [[TMP1]]) #[[ATTR7:[0-9]+]]
; CHECK-NEXT:    unreachable
;
entry:
  %call = invoke noalias ptr null()
  to label %invoke.cont unwind label %lpad

invoke.cont:
  ret ptr %call

lpad:
  %0 = landingpad { ptr, i32 }
  filter [0 x ptr] zeroinitializer
  %1 = extractvalue { ptr, i32 } %0, 0
  tail call void @__cxa_call_unexpected(ptr %1) noreturn nounwind
  unreachable
}

define i32 @invoke_readonly_may_not_return() nounwind personality ptr @__gxx_personality_v0 {
; CHECK-LABEL: @invoke_readonly_may_not_return(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[CALL:%.*]] = call i32 @readonly()
; CHECK-NEXT:    ret i32 3
;
entry:
  %call = invoke i32 @readonly()
  to label %invoke.cont unwind label %lpad

invoke.cont:
  ret i32 3

lpad:
  %0 = landingpad { ptr, i32 }
  filter [0 x ptr] zeroinitializer
  %1 = extractvalue { ptr, i32 } %0, 0
  tail call void @__cxa_call_unexpected(ptr %1) noreturn nounwind
  unreachable
}

define i32 @invoke_readonly_willreturn() nounwind personality ptr @__gxx_personality_v0 {
; CHECK-LABEL: @invoke_readonly_willreturn(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    ret i32 3
;
entry:
  %call = invoke i32 @readonly_willreturn()
  to label %invoke.cont unwind label %lpad

invoke.cont:
  ret i32 3

lpad:
  %0 = landingpad { ptr, i32 }
  filter [0 x ptr] zeroinitializer
  %1 = extractvalue { ptr, i32 } %0, 0
  tail call void @__cxa_call_unexpected(ptr %1) noreturn nounwind
  unreachable
}

define i32 @invoke_readonly_willreturn_with_used_retval() nounwind personality ptr @__gxx_personality_v0 {
; CHECK-LABEL: @invoke_readonly_willreturn_with_used_retval(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[CALL:%.*]] = call i32 @readonly_willreturn()
; CHECK-NEXT:    ret i32 [[CALL]]
;
entry:
  %call = invoke i32 @readonly_willreturn()
  to label %invoke.cont unwind label %lpad

invoke.cont:
  ret i32 %call

lpad:
  %0 = landingpad { ptr, i32 }
  filter [0 x ptr] zeroinitializer
  %1 = extractvalue { ptr, i32 } %0, 0
  tail call void @__cxa_call_unexpected(ptr %1) noreturn nounwind
  unreachable
}

define i32 @f5(i1 %cond, ptr %a, ptr %b) personality ptr @__gxx_personality_v0 {
; CHECK-LABEL: @f5(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    br i1 [[COND:%.*]], label [[X:%.*]], label [[Y:%.*]]
; CHECK:       x:
; CHECK-NEXT:    [[CALL:%.*]] = invoke i32 @fn()
; CHECK-NEXT:            to label [[CONT:%.*]] unwind label [[LPAD:%.*]]
; CHECK:       y:
; CHECK-NEXT:    [[CALL2:%.*]] = call i32 @nounwind_fn()
; CHECK-NEXT:    br label [[CONT]]
; CHECK:       cont:
; CHECK-NEXT:    [[PHI:%.*]] = phi i32 [ [[CALL]], [[X]] ], [ [[CALL2]], [[Y]] ]
; CHECK-NEXT:    ret i32 [[PHI]]
; CHECK:       lpad:
; CHECK-NEXT:    [[TMP0:%.*]] = landingpad { ptr, i32 }
; CHECK-NEXT:            filter [0 x ptr] zeroinitializer
; CHECK-NEXT:    tail call void @__cxa_call_unexpected(ptr [[A:%.*]]) #[[ATTR7]]
; CHECK-NEXT:    unreachable
;
entry:
  br i1 %cond, label %x, label %y

x:
  %call = invoke i32 @fn()
  to label %cont unwind label %lpad

y:
  %call2 = invoke i32 @nounwind_fn()
  to label %cont unwind label %lpad

cont:
  %phi = phi i32 [%call, %x], [%call2, %y]
  ret i32 %phi

lpad:
  %phi2 = phi ptr [%a, %x], [%b, %y]
  %0 = landingpad { ptr, i32 }
  filter [0 x ptr] zeroinitializer
  tail call void @__cxa_call_unexpected(ptr %phi2) noreturn nounwind
  unreachable
}

define void @f6() personality ptr @__gxx_personality_v0 {
; CHECK-LABEL: @f6(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    [[FOO:%.*]] = invoke i32 @fn()
; CHECK-NEXT:            to label [[COMMON_RET:%.*]] unwind label [[LPAD:%.*]]
; CHECK:       common.ret:
; CHECK-NEXT:    ret void
; CHECK:       lpad:
; CHECK-NEXT:    [[TMP0:%.*]] = landingpad { ptr, i32 }
; CHECK-NEXT:            cleanup
; CHECK-NEXT:    br label [[COMMON_RET]]
;
entry:
  invoke void @purefn()
  to label %invoke.cont1 unwind label %lpad

invoke.cont1:
  %foo = invoke i32 @fn()
  to label %invoke.cont2 unwind label %lpad

invoke.cont2:
  ret void

lpad:
  %tmp = phi ptr [ null, %invoke.cont1 ], [ null, %entry ]
  landingpad { ptr, i32 }
  cleanup
  ret void
}

define void @invoke_of_noreturn() personality ptr @__gxx_personality_v0 {
; CHECK-LABEL: @invoke_of_noreturn(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    invoke void @simple_throw()
; CHECK-NEXT:            to label [[INVOKE_CONT_UNREACHABLE:%.*]] unwind label [[LPAD:%.*]]
; CHECK:       invoke.cont.unreachable:
; CHECK-NEXT:    unreachable
; CHECK:       lpad:
; CHECK-NEXT:    [[EH:%.*]] = landingpad { ptr, i32 }
; CHECK-NEXT:            cleanup
; CHECK-NEXT:    call void @sideeffect(i32 1)
; CHECK-NEXT:    resume { ptr, i32 } [[EH]]
;
entry:
  invoke void @simple_throw() to label %invoke.cont unwind label %lpad

invoke.cont:
  call void @sideeffect(i32 0)
  ret void

lpad:
  %eh = landingpad { ptr, i32 } cleanup
  call void @sideeffect(i32 1)
  resume { ptr, i32 } %eh
}

define void @invoke_of_noreturn_with_shared_normal_destination(i1 %c) personality ptr @__gxx_personality_v0 {
; CHECK-LABEL: @invoke_of_noreturn_with_shared_normal_destination(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    br i1 [[C:%.*]], label [[INVOKE:%.*]], label [[INVOKE_CONT:%.*]]
; CHECK:       invoke:
; CHECK-NEXT:    invoke void @simple_throw()
; CHECK-NEXT:            to label [[INVOKE_CONT_UNREACHABLE:%.*]] unwind label [[LPAD:%.*]]
; CHECK:       invoke.cont.unreachable:
; CHECK-NEXT:    unreachable
; CHECK:       invoke.cont:
; CHECK-NEXT:    call void @sideeffect(i32 -1)
; CHECK-NEXT:    ret void
; CHECK:       lpad:
; CHECK-NEXT:    [[EH:%.*]] = landingpad { ptr, i32 }
; CHECK-NEXT:            cleanup
; CHECK-NEXT:    call void @sideeffect(i32 1)
; CHECK-NEXT:    resume { ptr, i32 } [[EH]]
;
entry:
  br i1 %c, label %invoke, label %invoke.cont

invoke:
  invoke void @simple_throw() to label %invoke.cont unwind label %lpad

invoke.cont:
  %r = phi i32 [ 0, %invoke ], [ -1, %entry ]
  call void @sideeffect(i32 %r)
  ret void

lpad:
  %eh = landingpad { ptr, i32 } cleanup
  call void @sideeffect(i32 1)
  resume { ptr, i32 } %eh
}

define void @invoke_of_nounwind() personality ptr @__gxx_personality_v0 {
; CHECK-LABEL: @invoke_of_nounwind(
; CHECK-NEXT:  entry:
; CHECK-NEXT:    call void @simple_return()
; CHECK-NEXT:    call void @sideeffect(i32 0)
; CHECK-NEXT:    ret void
;
entry:
  invoke void @simple_return() to label %invoke.cont unwind label %lpad

invoke.cont:
  call void @sideeffect(i32 0)
  ret void

lpad:
  %eh = landingpad { ptr, i32 } cleanup
  call void @sideeffect(i32 1)
  resume { ptr, i32 } %eh
}

declare void @simple_throw() noreturn
declare void @simple_return() nounwind
declare void @sideeffect(i32 )

attributes #0 = { null_pointer_is_valid }
