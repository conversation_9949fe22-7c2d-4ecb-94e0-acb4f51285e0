; NOTE: Assertions have been autogenerated by utils/update_test_checks.py UTC_ARGS: --version 3
; RUN: opt < %s -passes=simplifycfg -simplifycfg-require-and-preserve-domtree=1 -S | FileCheck %s

; Test a bunch of cases where the combination of phis in switch should be merged into one phi

declare void @use(i8)

define i8 @phis_of_switch_minimal(i8 noundef %arg) {
; CHECK-LABEL: define i8 @phis_of_switch_minimal(
; CHECK-SAME: i8 noundef [[ARG:%.*]]) {
; CHECK-NEXT:  start:
; CHECK-NEXT:    switch i8 [[ARG]], label [[UNREACHABLE:%.*]] [
; CHECK-NEXT:      i8 0, label [[CASE01:%.*]]
; CHECK-NEXT:      i8 1, label [[CASE1:%.*]]
; CHECK-NEXT:      i8 2, label [[END:%.*]]
; CHECK-NEXT:    ]
; CHECK:       unreachable:
; CHECK-NEXT:    unreachable
; CHECK:       case1:
; CHECK-NEXT:    br label [[END]]
; CHECK:       case01:
; CHECK-NEXT:    br label [[END]]
; CHECK:       end:
; CHECK-NEXT:    [[PHI2:%.*]] = phi i8 [ 3, [[START:%.*]] ], [ 2, [[CASE1]] ], [ 1, [[CASE01]] ]
; CHECK-NEXT:    ret i8 [[PHI2]]
;
start:
  switch i8 %arg, label %unreachable [
  i8 0, label %case01
  i8 1, label %case1
  i8 2, label %end
  ]
unreachable:
  unreachable
case1:
  br label %case01

case01:
  %phi1 = phi i8 [ 2, %case1 ], [1, %start]
  br label %end

end:
  %phi2 = phi i8 [ %phi1, %case01 ], [ 3, %start ]
  ret i8 %phi2
}

define i8 @phis_of_switch(i8 noundef %arg) {
; CHECK-LABEL: define i8 @phis_of_switch(
; CHECK-SAME: i8 noundef [[ARG:%.*]]) {
; CHECK-NEXT:  start:
; CHECK-NEXT:    switch i8 [[ARG]], label [[UNREACHABLE:%.*]] [
; CHECK-NEXT:      i8 0, label [[CASE012:%.*]]
; CHECK-NEXT:      i8 1, label [[CASE1:%.*]]
; CHECK-NEXT:      i8 2, label [[CASE2:%.*]]
; CHECK-NEXT:      i8 3, label [[END:%.*]]
; CHECK-NEXT:    ]
; CHECK:       unreachable:
; CHECK-NEXT:    unreachable
; CHECK:       case1:
; CHECK-NEXT:    br label [[END]]
; CHECK:       case2:
; CHECK-NEXT:    br label [[END]]
; CHECK:       case012:
; CHECK-NEXT:    br label [[END]]
; CHECK:       end:
; CHECK-NEXT:    [[PHI2:%.*]] = phi i8 [ 4, [[START:%.*]] ], [ 3, [[CASE2]] ], [ 2, [[CASE1]] ], [ 1, [[CASE012]] ]
; CHECK-NEXT:    ret i8 [[PHI2]]
;
start:
  switch i8 %arg, label %unreachable [
  i8 0, label %case012
  i8 1, label %case1
  i8 2, label %case2
  i8 3, label %end
  ]
unreachable:
  unreachable
case1:
  br label %case012

case2:
  br label %case012

case012:
  %phi1 = phi i8 [ 3, %case2 ], [ 2, %case1 ], [1, %start]
  br label %end

end:
  %phi2 = phi i8 [ %phi1, %case012 ], [ 4, %start ]
  ret i8 %phi2
}

define i8 @multiple_phis_of_switch(i8 noundef %arg) {
; CHECK-LABEL: define i8 @multiple_phis_of_switch(
; CHECK-SAME: i8 noundef [[ARG:%.*]]) {
; CHECK-NEXT:  start:
; CHECK-NEXT:    switch i8 [[ARG]], label [[UNREACHABLE:%.*]] [
; CHECK-NEXT:      i8 0, label [[CASE012:%.*]]
; CHECK-NEXT:      i8 1, label [[CASE1:%.*]]
; CHECK-NEXT:      i8 2, label [[CASE2:%.*]]
; CHECK-NEXT:      i8 3, label [[END:%.*]]
; CHECK-NEXT:    ]
; CHECK:       unreachable:
; CHECK-NEXT:    unreachable
; CHECK:       case1:
; CHECK-NEXT:    br label [[END]]
; CHECK:       case2:
; CHECK-NEXT:    br label [[END]]
; CHECK:       case012:
; CHECK-NEXT:    br label [[END]]
; CHECK:       end:
; CHECK-NEXT:    [[PHI2_1:%.*]] = phi i8 [ 4, [[START:%.*]] ], [ 3, [[CASE2]] ], [ 2, [[CASE1]] ], [ 1, [[CASE012]] ]
; CHECK-NEXT:    [[PHI2_2:%.*]] = phi i8 [ 5, [[START]] ], [ 3, [[CASE2]] ], [ 2, [[CASE1]] ], [ 1, [[CASE012]] ]
; CHECK-NEXT:    [[PHI2_3:%.*]] = phi i8 [ 3, [[START]] ], [ 6, [[CASE2]] ], [ 5, [[CASE1]] ], [ 4, [[CASE012]] ]
; CHECK-NEXT:    call void @use(i8 [[PHI2_1]])
; CHECK-NEXT:    call void @use(i8 [[PHI2_2]])
; CHECK-NEXT:    call void @use(i8 [[PHI2_3]])
; CHECK-NEXT:    ret i8 [[PHI2_1]]
;
start:
  switch i8 %arg, label %unreachable [
  i8 0, label %case012
  i8 1, label %case1
  i8 2, label %case2
  i8 3, label %end
  ]
unreachable:
  unreachable
case1:
  br label %case012

case2:
  br label %case012

case012:
  %phi1_1 = phi i8 [ 3, %case2 ], [ 2, %case1 ], [1, %start]
  %phi1_2 = phi i8 [ 6, %case2 ], [ 5, %case1 ], [4, %start]
  br label %end

end:
  %phi2_1 = phi i8 [ %phi1_1, %case012 ], [ 4, %start ]
  %phi2_2 = phi i8 [ %phi1_1, %case012 ], [ 5, %start ]
  %phi2_3 = phi i8 [ %phi1_2, %case012 ], [ 3, %start ]
  call void @use(i8 %phi2_1)
  call void @use(i8 %phi2_2)
  call void @use(i8 %phi2_3)
  ret i8 %phi2_1
}

define i8 @phis_of_switch_multiple_stage0(i8 noundef %arg) {
; CHECK-LABEL: define i8 @phis_of_switch_multiple_stage0(
; CHECK-SAME: i8 noundef [[ARG:%.*]]) {
; CHECK-NEXT:  start:
; CHECK-NEXT:    switch i8 [[ARG]], label [[UNREACHABLE:%.*]] [
; CHECK-NEXT:      i8 0, label [[CASE0:%.*]]
; CHECK-NEXT:      i8 1, label [[CASE1:%.*]]
; CHECK-NEXT:      i8 2, label [[CASE2:%.*]]
; CHECK-NEXT:      i8 3, label [[CASE0123:%.*]]
; CHECK-NEXT:      i8 4, label [[CASE01234:%.*]]
; CHECK-NEXT:      i8 5, label [[END:%.*]]
; CHECK-NEXT:    ]
; CHECK:       unreachable:
; CHECK-NEXT:    unreachable
; CHECK:       case0:
; CHECK-NEXT:    br label [[END]]
; CHECK:       case1:
; CHECK-NEXT:    br label [[END]]
; CHECK:       case2:
; CHECK-NEXT:    br label [[END]]
; CHECK:       case0123:
; CHECK-NEXT:    br label [[END]]
; CHECK:       case01234:
; CHECK-NEXT:    br label [[END]]
; CHECK:       end:
; CHECK-NEXT:    [[PHI3:%.*]] = phi i8 [ 6, [[START:%.*]] ], [ 3, [[CASE2]] ], [ 2, [[CASE1]] ], [ 1, [[CASE0]] ], [ 4, [[CASE0123]] ], [ 5, [[CASE01234]] ]
; CHECK-NEXT:    ret i8 [[PHI3]]
;
start:
  switch i8 %arg, label %unreachable [
  i8 0, label %case0
  i8 1, label %case1
  i8 2, label %case2
  i8 3, label %case0123
  i8 4, label %case01234
  i8 5, label %end
  ]
unreachable:
  unreachable

case0:
  br label %case0123

case1:
  br label %case0123

case2:
  br label %case0123

case0123:
  %phi1 = phi i8 [4, %start], [ 3, %case2 ], [ 2, %case1 ], [ 1, %case0 ]
  br label %case01234

case01234:
  %phi2 = phi i8 [ %phi1, %case0123 ], [ 5, %start ]
  br label %end

end:
  %phi3 = phi i8 [ %phi2, %case01234 ], [6, %start]
  ret i8 %phi3
}

define i8 @phis_of_switch_multiple_stage1(i8 noundef %arg) {
; CHECK-LABEL: define i8 @phis_of_switch_multiple_stage1(
; CHECK-SAME: i8 noundef [[ARG:%.*]]) {
; CHECK-NEXT:  start:
; CHECK-NEXT:    switch i8 [[ARG]], label [[UNREACHABLE:%.*]] [
; CHECK-NEXT:      i8 0, label [[CASE0:%.*]]
; CHECK-NEXT:      i8 1, label [[CASE1:%.*]]
; CHECK-NEXT:      i8 2, label [[CASE012:%.*]]
; CHECK-NEXT:      i8 3, label [[CASE3:%.*]]
; CHECK-NEXT:      i8 4, label [[CASE4:%.*]]
; CHECK-NEXT:      i8 5, label [[CASE345:%.*]]
; CHECK-NEXT:      i8 6, label [[CASE0123456:%.*]]
; CHECK-NEXT:    ]
; CHECK:       unreachable:
; CHECK-NEXT:    unreachable
; CHECK:       case0:
; CHECK-NEXT:    br label [[CASE0123456]]
; CHECK:       case1:
; CHECK-NEXT:    br label [[CASE0123456]]
; CHECK:       case012:
; CHECK-NEXT:    br label [[CASE0123456]]
; CHECK:       case3:
; CHECK-NEXT:    br label [[CASE0123456]]
; CHECK:       case4:
; CHECK-NEXT:    br label [[CASE0123456]]
; CHECK:       case345:
; CHECK-NEXT:    br label [[CASE0123456]]
; CHECK:       case0123456:
; CHECK-NEXT:    [[PHI1234567:%.*]] = phi i8 [ 7, [[START:%.*]] ], [ 2, [[CASE1]] ], [ 1, [[CASE0]] ], [ 3, [[CASE012]] ], [ 5, [[CASE4]] ], [ 4, [[CASE3]] ], [ 6, [[CASE345]] ]
; CHECK-NEXT:    ret i8 [[PHI1234567]]
;
start:
  switch i8 %arg, label %unreachable [
  i8 0, label %case0
  i8 1, label %case1
  i8 2, label %case012
  i8 3, label %case3
  i8 4, label %case4
  i8 5, label %case345
  i8 6, label %case0123456
  ]
unreachable:
  unreachable
case0:
  br label %case012

case1:
  br label %case012

case012:
  %phi123 = phi i8 [3, %start], [ 2, %case1 ], [ 1, %case0 ]
  br label %case0123456

case3:
  br label %case345

case4:
  br label %case345

case345:
  %phi456 = phi i8 [6, %start], [ 5, %case4 ], [ 4, %case3 ]
  br label %case0123456

case0123456:
  %phi1234567 = phi i8 [7, %start], [ %phi456, %case345 ], [ %phi123, %case012 ]
  ret i8 %phi1234567

}

define i8 @phis_of_switch_extra_use_fail(i8 noundef %arg) {
; CHECK-LABEL: define i8 @phis_of_switch_extra_use_fail(
; CHECK-SAME: i8 noundef [[ARG:%.*]]) {
; CHECK-NEXT:  start:
; CHECK-NEXT:    switch i8 [[ARG]], label [[UNREACHABLE:%.*]] [
; CHECK-NEXT:      i8 0, label [[CASE012:%.*]]
; CHECK-NEXT:      i8 1, label [[CASE1:%.*]]
; CHECK-NEXT:      i8 2, label [[CASE2:%.*]]
; CHECK-NEXT:      i8 3, label [[END:%.*]]
; CHECK-NEXT:    ]
; CHECK:       unreachable:
; CHECK-NEXT:    unreachable
; CHECK:       case1:
; CHECK-NEXT:    br label [[CASE012]]
; CHECK:       case2:
; CHECK-NEXT:    br label [[CASE012]]
; CHECK:       case012:
; CHECK-NEXT:    [[PHI1:%.*]] = phi i8 [ 3, [[CASE2]] ], [ 2, [[CASE1]] ], [ 1, [[START:%.*]] ]
; CHECK-NEXT:    call void @use(i8 [[PHI1]])
; CHECK-NEXT:    br label [[END]]
; CHECK:       end:
; CHECK-NEXT:    [[PHI2:%.*]] = phi i8 [ [[PHI1]], [[CASE012]] ], [ 4, [[START]] ]
; CHECK-NEXT:    ret i8 [[PHI2]]
;
start:
  switch i8 %arg, label %unreachable [
  i8 0, label %case012
  i8 1, label %case1
  i8 2, label %case2 i8 3, label %end
  ]
unreachable:
  unreachable
case1:
  br label %case012

case2:
  br label %case012

case012:
  %phi1 = phi i8 [ 3, %case2 ], [ 2, %case1 ], [1, %start]
  call void @use(i8 %phi1)
  br label %end

end:
  %phi2 = phi i8 [ %phi1, %case012 ], [ 4, %start ]
  ret i8 %phi2
}
