name: Save Stage
description: >-
  Upload the source and binary directories from a build stage so that they
  can be re-used in the next stage.  This action is used to the release
  binaries workflow into multiple stages to avoid the 6 hour timeout on
  the GitHub hosted runners.
inputs:
  build-prefix:
    description: "Directory containing the build directory."
    required: true
    type: 'string'

permissions:
  contents: read

runs:
  using: "composite"
  steps:
    # We need to create an archive of the build directory, because it has too
    # many files to upload.
    - name: Package Build and Source Directories
      shell: bash
      run: |
        # Remove .git/config to avoid leaking GITHUB_TOKEN stored there.
        # See https://unit42.paloaltonetworks.com/github-repo-artifacts-leak-tokens/
        rm -Rf .git/config
        # Windows does not support symlinks, so we need to dereference them.
        tar --exclude build/ ${{ (runner.os == 'Windows' && '-h') || '' }} -c . | zstd -T0 -c > ../llvm-project.tar.zst
        mv ../llvm-project.tar.zst .
        tar -C ${{ inputs.build-prefix }} -c build/ | zstd -T0 -c > build.tar.zst

    - name: Upload Stage 1 Source
      uses: actions/upload-artifact@26f96dfa697d77e81fd5907df203aa23a56210a8 #v4.3.0
      with:
        name: ${{ runner.os }}-${{ runner.arch }}-${{ github.job }}-source
        path: llvm-project.tar.zst
        retention-days: 2

    - name: Upload Stage 1 Build Dir
      uses: actions/upload-artifact@26f96dfa697d77e81fd5907df203aa23a56210a8 #v4.3.0
      with:
        name: ${{ runner.os}}-${{ runner.arch }}-${{ github.job }}-build
        path: build.tar.zst
        retention-days: 2
