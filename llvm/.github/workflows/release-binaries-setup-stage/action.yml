name: Setup Stage
description: >-
  Setup the next stage of the release binaries workflow.  This sets up the
  environment correctly for a new stage of the release binaries workflow
  and also restores the source and build directory from the previous stage.

inputs:
  previous-artifact:
    description: >-
      A unique descriptor for the artifact from the previous stage.  This will
      be used to construct the final artifact pattern, which is:
      $RUNNER_OS-$RUNNER_ARCH-$PREVIOUS_ARTIFACT-*
    required: false
    type: 'string'

outputs:
  build-prefix:
    description: "Directory containing the build directory."
    value: ${{ steps.build-prefix.outputs.build-prefix }}

runs:
  using: "composite"
  steps:
    - name: Install Ninja
      uses: llvm/actions/install-ninja@22e9f909d35b50bd1181709564bfe816eaeaae81 # main
   
    - name: Setup Windows
      if: startsWith(runner.os, 'Windows')
      uses: llvm/actions/setup-windows@main
      with:
        arch: amd64

    - name: Set Build Prefix
      id: build-prefix
      shell: bash
      run: |
        build_prefix=`pwd`
        if [ "${{ runner.os }}" = "Linux" ]; then
          sudo chown $USER:$USER /mnt/
          build_prefix=/mnt/
        fi
        echo "build-prefix=$build_prefix" >> $GITHUB_OUTPUT

    - name: Download Previous Stage Artifact
      if: ${{ inputs.previous-artifact }}
      id: download
      uses: actions/download-artifact@6b208ae046db98c579e8a3aa621ab581ff575935 # v4.1.1
      with:
        pattern: ${{ runner.os }}-${{ runner.arch }}-${{ inputs.previous-artifact }}-*
        merge-multiple: true

    - name: Unpack Artifact
      if: ${{ steps.download.outputs.download-path }}
      shell: bash
      run: |
        tar --zstd -xf llvm-project.tar.zst
        rm llvm-project.tar.zst
        tar --zstd -C ${{ steps.build-prefix.outputs.build-prefix}} -xf build.tar.zst
        rm build.tar.zst
