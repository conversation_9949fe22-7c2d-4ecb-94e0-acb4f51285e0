name: LLVM Project Version Check

on:
  push:
    branches:
      - 'release/**'
  pull_request:
    branches:
      - 'release/**'

permissions:
  contents: read

jobs:
  version_check:
    if: github.repository_owner == 'llvm'
    runs-on: ubuntu-latest
    steps:
      - name: Fetch LLVM sources
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install dependencies
        run: |
          pip install --require-hashes -r ./llvm/utils/git/requirements.txt

      - name: Version Check
        run: |
          version=$(grep -o 'LLVM_VERSION_\(MAJOR\|MINOR\|PATCH\) [0-9]\+' cmake/Modules/LLVMVersion.cmake  | cut -d ' ' -f 2 | tr "\n" "." | sed 's/.$//g')
          .github/workflows/version-check.py "$version"
