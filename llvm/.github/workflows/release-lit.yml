name: Release Lit

permissions:
  contents: read

on:
  workflow_dispatch:
    inputs:
      release-version:
        description: 'Release Version'
        required: true
        type: string

  workflow_call:
    inputs:
      release-version:
        description: 'Release Version'
        required: true
        type: string
    secrets:
      RELEASE_TASKS_USER_TOKEN:
        description: "Secret used to check user permissions."
        required: false

jobs:
  release-lit:
    name: Release Lit
    runs-on: ubuntu-latest
    steps:
      - name: Checkout LLVM
        uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
        with:
          ref: "llvmorg-${{ inputs.release-version }}"

      - name: Install dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y python3-setuptools python3-psutil python3-github

      - name: Check Permissions
        env:
          GITHUB_TOKEN: ${{ github.token }}
          USER_TOKEN: ${{ secrets.RELEASE_TASKS_USER_TOKEN }}
        run: |
          ./llvm/utils/release/./github-upload-release.py --token "$GITHUB_TOKEN" --user ${{ github.actor }} --user-token "$USER_TOKEN" check-permissions

      - name: Setup Cpp
        uses: aminya/setup-cpp@v1
        with:
          compiler: llvm-16.0.6
          cmake: true
          ninja: true

      - name: Test lit
        run: |
          mkdir build && cd build
          export FILECHECK_OPTS='-dump-input-filter=all -vv -color'
          cmake ../llvm -DCMAKE_BUILD_TYPE=Release -G Ninja
          ninja -v -j $(nproc) check-lit

      - name: Package lit
        run: |
          cd llvm/utils/lit
          # Remove 'dev' suffix from lit version.
          sed -i 's/ + "dev"//g' lit/__init__.py
          python3 setup.py sdist bdist_wheel

      - name: Upload lit to test.pypi.org
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          password: ${{ secrets.LLVM_LIT_TEST_PYPI_API_TOKEN }}
          repository-url: https://test.pypi.org/legacy/
          packages-dir: llvm/utils/lit/dist/

      - name: Upload lit to pypi.org
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          password: ${{ secrets.LLVM_LIT_PYPI_API_TOKEN }}
          packages-dir: llvm/utils/lit/dist/
