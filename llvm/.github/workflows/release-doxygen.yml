name: Release Doxygen

permissions:
  contents: read

on:
  workflow_dispatch:
    inputs:
      release-version:
        description: 'Release Version'
        required: true
        type: string
      upload:
        description: 'Upload documentation'
        required: false
        type: boolean

  workflow_call:
    inputs:
      release-version:
        description: 'Release Version'
        required: true
        type: string
      upload:
        description: 'Upload documentation'
        required: false
        type: boolean
    secrets:
      RELEASE_TASKS_USER_TOKEN:
        description: "Secret used to check user permissions."
        required: false

jobs:
  release-doxygen:
    name: Build and Upload Release Doxygen
    runs-on: ubuntu-latest
    permissions:
      contents: write
    env:
      upload: ${{ inputs.upload && !contains(inputs.release-version, 'rc') }}
    steps:
      - name: Checkout LLVM
        uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1

      - name: Setup Python env
        uses: actions/setup-python@v5
        with:
          cache: 'pip'
          cache-dependency-path: './llvm/docs/requirements.txt'

      - name: Install Dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y \
              doxygen \
              graphviz \
              python3-github \
              ninja-build \
              texlive-font-utils
          pip3 install --user -r ./llvm/docs/requirements.txt

      - name: Build Doxygen
        run: |
          ./llvm/utils/release/build-docs.sh -release "${{ inputs.release-version }}" -no-sphinx

      - name: Upload Doxygen
        if: env.upload
        env:
          GITHUB_TOKEN: ${{ github.token }}
          USER_TOKEN: ${{ secrets.RELEASE_TASKS_USER_TOKEN }}
        run: |
          ./llvm/utils/release/github-upload-release.py --token "$GITHUB_TOKEN" --release "${{ inputs.release-version }}" --user "${{ github.actor }}" --user-token "$USER_TOKEN" upload --files ./*doxygen*.tar.xz
