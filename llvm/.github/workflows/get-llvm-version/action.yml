name: Get LLVM Version
description: >-
  Get the LLVM version from the llvm-project source tree.  This action assumes
  the llvm-project sources have already been checked out into GITHUB_WORKSPACE.

outputs:
  major:
    description: LLVM major version
    value: ${{ steps.version.outputs.major }}
  minor:
    description: LLVM minor version
    value: ${{ steps.version.outputs.minor }}
  patch:
    description: LLVM patch version
    value: ${{ steps.version.outputs.patch }}

runs:
  using: "composite"
  steps:
    - name: Get Version
      shell: bash
      id: version
      run: |
        for v in major minor patch; do
          echo "$v=`llvm/utils/release/get-llvm-version.sh --$v`" >> $GITHUB_OUTPUT
        done
